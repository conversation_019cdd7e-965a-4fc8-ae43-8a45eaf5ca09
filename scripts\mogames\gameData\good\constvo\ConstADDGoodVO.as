package mogames.gameData.good.constvo
{
   import mogames.gameData.base.Sint;
   
   public class ConstADDGoodVO extends ConstGoodVO
   {
      public var countType:Sint;
      
      public var addType:Sint;
      
      public var value:Sint;
      
      public function ConstADDGoodVO(param1:int, param2:String, param3:String, param4:int, param5:int, param6:int, param7:int, param8:int, param9:int, param10:String)
      {
         super(param1,param2,param3,param7,param8,param9,param10);
         this.countType = new Sint(param4);
         this.addType = new Sint(param5);
         this.value = new Sint(param6);
      }
   }
}

