package mogames.gameData.online
{
   import file.RewardLineConfig;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   import utils.TxtUtil;
   
   public class RewardLineProxy
   {
      private static var _instance:RewardLineProxy;
      
      private var _timer:Timer;
      
      private var _sec:Sint;
      
      public var activeVO:RewardLineVO;
      
      public var isReward:<PERSON>olean;
      
      public function RewardLineProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this._sec = new Sint();
         this._timer = new Timer(1000);
         this._timer.addEventListener(TimerEvent.TIMER,this.onTimer);
      }
      
      public static function instance() : RewardLineProxy
      {
         if(!_instance)
         {
            _instance = new RewardLineProxy();
         }
         return _instance;
      }
      
      public function init() : void
      {
         if(FlagProxy.instance().isComplete(204))
         {
            return;
         }
         this.startTimer();
      }
      
      private function startTimer() : void
      {
         this._sec.v = 0;
         this.isReward = false;
         this.activeVO = RewardLineConfig.instance().findReward(FlagProxy.instance().findFlag(204).cur);
         if(this.activeVO)
         {
            this._timer.start();
         }
      }
      
      private function onTimer(param1:TimerEvent) : void
      {
         this._sec.v += ConstData.DATA_NUM1.v;
         if(this._sec.v >= this.activeVO.sec)
         {
            this.isReward = true;
            this._timer.stop();
         }
      }
      
      public function get timeStamp() : String
      {
         if(!this.activeVO)
         {
            return "";
         }
         return TxtUtil.initTimeFormat(this.activeVO.sec - this._sec.v);
      }
   }
}

