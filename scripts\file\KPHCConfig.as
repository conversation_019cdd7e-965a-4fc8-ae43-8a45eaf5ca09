package file
{
   import mogames.gameData.card.KPHCVO;
   import mogames.gameData.forge.NeedVO;
   
   public class KPHCConfig
   {
      private static var _instance:KPHCConfig;
      
      private var _list:Array;
      
      public function KPHCConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : KPHCConfig
      {
         if(!_instance)
         {
            _instance = new KPHCConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list.push(new KPHCVO(71001,71002,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71002,71003,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71003,71004,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71004,71005,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71006,71007,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71007,71008,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71008,71009,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71009,71010,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71011,71012,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71012,71013,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71013,71014,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71014,71015,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71016,71017,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71017,71018,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71018,71019,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71019,71020,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71021,71022,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71022,71023,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71023,71024,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71024,71025,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71026,71027,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71027,71028,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71028,71029,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71029,71030,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71031,71032,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71032,71033,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71033,71034,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71034,71035,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71036,71037,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71037,71038,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71038,71039,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71039,71040,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71041,71042,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71042,71043,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71043,71044,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71044,71045,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71046,71047,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71047,71048,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71048,71049,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71049,71050,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71051,71052,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71052,71053,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71053,71054,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71054,71055,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71056,71057,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71057,71058,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71058,71059,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71059,71060,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71061,71062,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71062,71063,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71063,71064,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71064,71065,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71066,71067,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71067,71068,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71068,71069,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71069,71070,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71071,71072,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71072,71073,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71073,71074,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71074,71075,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71076,71077,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71077,71078,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71078,71079,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71079,71080,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71081,71082,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71082,71083,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71083,71084,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71084,71085,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71086,71087,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71087,71088,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71088,71089,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71089,71090,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71091,71092,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71092,71093,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71093,71094,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71094,71095,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71096,71097,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71097,71098,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71098,71099,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71099,71100,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71101,71102,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71102,71103,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71103,71104,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71104,71105,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71106,71107,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71107,71108,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71108,71109,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71109,71110,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71111,71112,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71112,71113,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71113,71114,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71114,71115,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71116,71117,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71117,71118,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71118,71119,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71119,71120,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71121,71122,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71122,71123,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71123,71124,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71124,71125,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71126,71127,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71127,71128,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71128,71129,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71129,71130,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71131,71132,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71132,71133,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71133,71134,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71134,71135,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71136,71137,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71137,71138,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71138,71139,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71139,71140,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71141,71142,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71142,71143,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71143,71144,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71144,71145,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71146,71147,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71147,71148,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71148,71149,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71149,71150,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71151,71152,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71152,71153,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71153,71154,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71154,71155,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71156,71157,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71157,71158,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71158,71159,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71159,71160,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71161,71162,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71162,71163,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71163,71164,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71164,71165,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71166,71167,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71167,71168,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71168,71169,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71169,71170,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71171,71172,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71172,71173,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71173,71174,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71174,71175,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71176,71177,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71177,71178,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71178,71179,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71179,71180,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71181,71182,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71182,71183,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71183,71184,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71184,71185,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71186,71187,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71187,71188,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71188,71189,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71189,71190,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71191,71192,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71192,71193,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71193,71194,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71194,71195,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(71196,71197,10000,[new NeedVO(50000,1)]));
         this._list.push(new KPHCVO(71197,71198,20000,[new NeedVO(50000,2)]));
         this._list.push(new KPHCVO(71198,71199,30000,[new NeedVO(50000,3)]));
         this._list.push(new KPHCVO(71199,71200,50000,[new NeedVO(50000,4)]));
         this._list.push(new KPHCVO(70001,70002,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70002,70003,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70003,70004,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70004,70005,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70006,70007,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70007,70008,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70008,70009,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70009,70010,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70011,70012,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70012,70013,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70013,70014,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70014,70015,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70016,70017,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70017,70018,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70018,70019,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70019,70020,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70021,70022,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70022,70023,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70023,70024,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70024,70025,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70026,70027,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70027,70028,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70028,70029,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70029,70030,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70031,70032,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70032,70033,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70033,70034,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70034,70035,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70036,70037,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70037,70038,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70038,70039,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70039,70040,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70041,70042,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70042,70043,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70043,70044,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70044,70045,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70046,70047,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70047,70048,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70048,70049,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70049,70050,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70051,70052,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70052,70053,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70053,70054,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70054,70055,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70056,70057,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70057,70058,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70058,70059,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70059,70060,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70061,70062,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70062,70063,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70063,70064,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70064,70065,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70066,70067,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70067,70068,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70068,70069,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70069,70070,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70071,70072,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70072,70073,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70073,70074,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70074,70075,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70076,70077,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70077,70078,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70078,70079,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70079,70080,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70081,70082,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70082,70083,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70083,70084,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70084,70085,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70086,70087,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70087,70088,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70088,70089,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70089,70090,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70091,70092,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70092,70093,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70093,70094,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70094,70095,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70096,70097,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70097,70098,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70098,70099,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70099,70100,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70101,70102,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70102,70103,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70103,70104,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70104,70105,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70106,70107,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70107,70108,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70108,70109,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70109,70110,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70111,70112,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70112,70113,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70113,70114,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70114,70115,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70116,70117,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70117,70118,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70118,70119,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70119,70120,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70121,70122,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70122,70123,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70123,70124,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70124,70125,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70126,70127,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70127,70128,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70128,70129,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70129,70130,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70131,70132,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70132,70133,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70133,70134,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70134,70135,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70136,70137,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70137,70138,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70138,70139,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70139,70140,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70141,70142,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70142,70143,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70143,70144,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70144,70145,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70146,70147,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70147,70148,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70148,70149,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70149,70150,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70151,70152,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70152,70153,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70153,70154,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70154,70155,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70156,70157,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70157,70158,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70158,70159,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70159,70160,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70161,70162,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70162,70163,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70163,70164,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70164,70165,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70166,70167,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70167,70168,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70168,70169,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70169,70170,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70171,70172,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70172,70173,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70173,70174,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70174,70175,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70176,70177,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70177,70178,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70178,70179,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70179,70180,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70181,70182,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70182,70183,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70183,70184,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70184,70185,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70186,70187,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70187,70188,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70188,70189,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70189,70190,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70191,70192,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70192,70193,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70193,70194,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70194,70195,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70196,70197,20000,[new NeedVO(50005,1)]));
         this._list.push(new KPHCVO(70197,70198,40000,[new NeedVO(50005,2)]));
         this._list.push(new KPHCVO(70198,70199,60000,[new NeedVO(50005,3)]));
         this._list.push(new KPHCVO(70199,70200,80000,[new NeedVO(50005,4)]));
         this._list.push(new KPHCVO(70951,70952,40000,[new NeedVO(50008,1)]));
         this._list.push(new KPHCVO(70952,70953,60000,[new NeedVO(50008,2)]));
         this._list.push(new KPHCVO(70953,70954,80000,[new NeedVO(50008,3)]));
         this._list.push(new KPHCVO(70954,70955,100000,[new NeedVO(50008,4)]));
         this._list.push(new KPHCVO(70956,70957,40000,[new NeedVO(50008,1)]));
         this._list.push(new KPHCVO(70957,70958,60000,[new NeedVO(50008,2)]));
         this._list.push(new KPHCVO(70958,70959,80000,[new NeedVO(50008,3)]));
         this._list.push(new KPHCVO(70959,70960,100000,[new NeedVO(50008,4)]));
         this._list.push(new KPHCVO(70961,70962,40000,[new NeedVO(50008,1)]));
         this._list.push(new KPHCVO(70962,70963,60000,[new NeedVO(50008,2)]));
         this._list.push(new KPHCVO(70963,70964,80000,[new NeedVO(50008,3)]));
         this._list.push(new KPHCVO(70964,70965,100000,[new NeedVO(50008,4)]));
         this._list.push(new KPHCVO(70966,70967,40000,[new NeedVO(50008,1)]));
         this._list.push(new KPHCVO(70967,70968,60000,[new NeedVO(50008,2)]));
         this._list.push(new KPHCVO(70968,70969,80000,[new NeedVO(50008,3)]));
         this._list.push(new KPHCVO(70969,70970,100000,[new NeedVO(50008,4)]));
         this._list.push(new KPHCVO(70971,70972,40000,[new NeedVO(50008,1)]));
         this._list.push(new KPHCVO(70972,70973,60000,[new NeedVO(50008,2)]));
         this._list.push(new KPHCVO(70973,70974,80000,[new NeedVO(50008,3)]));
         this._list.push(new KPHCVO(70974,70975,100000,[new NeedVO(50008,4)]));
         this._list.push(new KPHCVO(70976,70977,40000,[new NeedVO(50008,1)]));
         this._list.push(new KPHCVO(70977,70978,60000,[new NeedVO(50008,2)]));
         this._list.push(new KPHCVO(70978,70979,80000,[new NeedVO(50008,3)]));
         this._list.push(new KPHCVO(70979,70980,100000,[new NeedVO(50008,4)]));
         this._list.push(new KPHCVO(70981,70982,40000,[new NeedVO(50008,1)]));
         this._list.push(new KPHCVO(70982,70983,60000,[new NeedVO(50008,2)]));
         this._list.push(new KPHCVO(70983,70984,80000,[new NeedVO(50008,3)]));
         this._list.push(new KPHCVO(70984,70985,100000,[new NeedVO(50008,4)]));
      }
      
      public function findHC(param1:int) : KPHCVO
      {
         var _loc2_:KPHCVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.oldID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

