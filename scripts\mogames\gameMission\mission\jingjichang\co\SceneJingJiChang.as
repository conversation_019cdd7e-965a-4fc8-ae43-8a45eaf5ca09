package mogames.gameMission.mission.jingjichang.co
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.utils.setTimeout;
   import mogames.Layers;
   import mogames.gameData.ConstData;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.flag.FBFlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.pk.RolePKProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.jingjichang.PKMissionManager;
   import mogames.gamePKG.PKGManager;
   import mogames.gameSystem.CitrusRender;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.FontUtil;
   import utils.MethodUtil;
   
   public class SceneJingJi<PERSON>hang extends BaseScene
   {
      private var _pkMission:JingJiChangMission;
      
      private var _panel:MovieClip;
      
      private var _btnLeave:SimpleButton;
      
      public function SceneJingJiChang(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_BATTLE1");
         this._pkMission = param2 as JingJiChangMission;
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this._panel = GameInLoader.instance().findAsset("SCENE_PK_INFOR_PANEL") as MovieClip;
         this._panel.x = 691;
         this._panel.y = 3;
         Layers.frameLayer.addChild(this._panel);
         MethodUtil.setMousable(this._panel,false);
         FontUtil.setText(this._panel.txtMode,ConstData.PK_MODE[RolePKProxy.instance().activeType] + "模式");
         FontUtil.setText(this._panel.txtFriend,MasterProxy.instance().serverNick);
         FontUtil.setText(this._panel.txtEnemy,RolePKProxy.instance().activeVO.nick);
         this._pkMission.layoutEnemy();
         if(!RolePKProxy.instance().isQieCuo)
         {
            FBFlagProxy.instance().findFlag(112).useFree();
         }
         CitrusRender.instance().add(this.checkWin);
         PKGManager.saveWithTip();
         this.initLeaveBtn();
      }
      
      private function layoutEnemy() : void
      {
         this._pkMission.layoutEnemy();
         this.initLeaveBtn();
      }
      
      private function initLeaveBtn() : void
      {
         this._btnLeave = GameInLoader.instance().findAsset("BTN_PK_LEAVE") as SimpleButton;
         this._btnLeave.x = 895;
         this._btnLeave.y = 545;
         Layers.frameLayer.addChild(this._btnLeave);
         this._btnLeave.addEventListener(MouseEvent.CLICK,this.onLeave,false,0,true);
      }
      
      private function removeLeaveBtn() : void
      {
         this._btnLeave.removeEventListener(MouseEvent.CLICK,this.onLeave);
         MethodUtil.removeMe(this._btnLeave);
         this._btnLeave = null;
      }
      
      private function onLeave(param1:MouseEvent) : void
      {
         var okFunc:Function = null;
         var e:MouseEvent = param1;
         okFunc = function():void
         {
            Layers.ceLayer.resumeGame();
            setTimeout(PKMissionManager.instance().quitMission,20);
         };
         if(PKMissionManager.instance().isWin || PKMissionManager.instance().isLose)
         {
            return;
         }
         Layers.ceLayer.pauseGame();
         PromptMediator.instance().showPrompt("正在挑战中，直接离开？",okFunc,Layers.ceLayer.resumeGame);
      }
      
      private function checkWin() : void
      {
         if(!this._pkMission.enemySwitcher.allDead)
         {
            return;
         }
         this.listenLeave();
         CitrusRender.instance().remove(this.checkWin);
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         PKMissionManager.instance().handlerWin();
      }
      
      override public function destroy() : void
      {
         this.removeLeaveBtn();
         MethodUtil.removeMe(this._panel);
         this._panel = null;
         CitrusRender.instance().remove(this.checkWin);
         this._pkMission = null;
         super.destroy();
      }
   }
}

