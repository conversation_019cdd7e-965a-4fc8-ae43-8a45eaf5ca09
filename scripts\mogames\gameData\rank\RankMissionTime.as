package mogames.gameData.rank
{
   import mogames.gameData.flag.TimeFlagProxy;
   import mogames.gameData.flag.mission.TimeMissionFlag;
   import mogames.gameData.rank.vo.RankTimeVO;
   
   public class RankMissionTime extends RankHandler
   {
      public var index:int;
      
      public function RankMissionTime()
      {
         super([1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1785,1789,1790,1791,1792,1793,1794,1795,1849,1868,1869,1871]);
         _msg = "初始化通关时间排行榜";
      }
      
      override public function parseRank(param1:Array) : Array
      {
         var _loc5_:RankTimeVO = null;
         if(!param1 || param1.length <= 0)
         {
            return [];
         }
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         var _loc4_:Array = [];
         while(_loc2_ < _loc3_)
         {
            _loc5_ = new RankTimeVO();
            _loc5_.parseData(param1[_loc2_]);
            _loc4_[_loc2_] = _loc5_;
            _loc2_++;
         }
         _loc4_.sortOn("sortIndex",Array.NUMERIC);
         return _loc4_;
      }
      
      override public function submitRank(param1:Function) : void
      {
         if(this.isCheat)
         {
            if(param1 != null)
            {
               param1();
            }
            return;
         }
         var _loc2_:TimeMissionFlag = this.findTimeFlag();
         if(_loc2_.minTime.v == 0)
         {
            param1();
         }
         else
         {
            super.submitRank(param1);
         }
      }
      
      override protected function get rankData() : Array
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.newRankData();
         return _loc1_;
      }
      
      protected function newRankData() : Object
      {
         var _loc1_:TimeMissionFlag = this.findTimeFlag();
         var _loc2_:Object = new Object();
         _loc2_.rId = this.curRankID;
         _loc2_.score = 10000 - _loc1_.minTime.v;
         _loc2_.extra = [1000 - _loc1_.minHurt.v];
         return _loc2_;
      }
      
      public function findTimeFlag() : TimeMissionFlag
      {
         return TimeFlagProxy.instance().findFlag(TimeFlagProxy.missionIDs[this.index]);
      }
      
      public function get curRankID() : int
      {
         return rankIDs[this.index];
      }
      
      private function get isCheat() : Boolean
      {
         return false;
      }
   }
}

