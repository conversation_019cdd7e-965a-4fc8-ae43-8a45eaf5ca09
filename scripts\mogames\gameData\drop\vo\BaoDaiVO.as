package mogames.gameData.drop.vo
{
   import mogames.gameData.base.Sint;
   
   public class BaoDaiVO
   {
      private var _rate:Sint;
      
      public var list:Array;
      
      public function BaoDaiVO(param1:int, param2:Array)
      {
         super();
         this._rate = new Sint(param1);
         this.list = param2;
      }
      
      public function get rate() : int
      {
         return this._rate.v;
      }
      
      public function get randReward() : BaseRewardVO
      {
         return this.list[int(Math.random() * this.list.length)];
      }
   }
}

