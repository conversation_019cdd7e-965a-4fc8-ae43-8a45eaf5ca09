package mogames.gameMission.mission.pipadong
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gamePKG.PKGManager;
   import mogames.gameRole.enemy.BOSSJuYanJing;
   import mogames.gameRole.enemy.BOSSNiBaGuai;
   import mogames.gameRole.enemy.BOSSYanShiJiang;
   import mogames.gameRole.enemy.BOSSYanShuJing;
   import mogames.gameRole.hero.BaseHero;
   
   public class SceneZhenBei extends BossScene
   {
      public function SceneZhenBei(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(270,342,446,110),new Point(480,360));
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.refreshHero();
         this.addBOSS();
      }
      
      override protected function addBOSS() : void
      {
         var _loc1_:PiPaDongMission = _mission as PiPaDongMission;
         switch(_loc1_.zhenbeiVO.bossID.v)
         {
            case 4005:
               _boss = new BOSSNiBaGuai();
               _boss.initData(40051,40051,70);
               break;
            case 4006:
               _boss = new BOSSYanShuJing();
               _boss.initData(40061,40061,71);
               break;
            case 4007:
               _boss = new BOSSJuYanJing();
               _boss.initData(40071,40071,72);
               break;
            case 4008:
               _boss = new BOSSYanShiJiang();
               _boss.initData(40081,40081,73);
         }
         _boss.x = 780;
         _boss.y = 80;
         _boss.target = _mission.onePlayer;
         add(_boss);
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,780,80);
         startBossBattle();
      }
      
      override protected function listenLeave() : void
      {
         this.refreshHero();
         (_mission as PiPaDongMission).leaveZhenBei();
         PKGManager.saveData();
      }
      
      private function refreshHero() : void
      {
         var _loc2_:HeroGameVO = null;
         var _loc3_:BaseHero = null;
         var _loc1_:Vector.<BaseHero> = _mission.heros;
         for each(_loc3_ in _loc1_)
         {
            _loc3_.handlerRevive();
            _loc2_ = _loc3_.roleVO as HeroGameVO;
            _loc2_.refresh();
         }
      }
   }
}

