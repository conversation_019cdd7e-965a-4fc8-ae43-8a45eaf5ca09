package citrus.objects
{
   import Box2D.Collision.Shapes.b2CircleShape;
   import Box2D.Collision.Shapes.b2PolygonShape;
   import Box2D.Collision.Shapes.b2Shape;
   import Box2D.Collision.b2Manifold;
   import Box2D.Common.Math.b2Mat22;
   import Box2D.Common.Math.b2Transform;
   import Box2D.Common.Math.b2Vec2;
   import Box2D.Dynamics.Contacts.b2Contact;
   import Box2D.Dynamics.b2Body;
   import Box2D.Dynamics.b2BodyDef;
   import Box2D.Dynamics.b2ContactImpulse;
   import Box2D.Dynamics.b2Fixture;
   import Box2D.Dynamics.b2FixtureDef;
   import citrus.core.CitrusEngine;
   import citrus.physics.PhysicsCollisionCategories;
   import citrus.physics.box2d.Box2D;
   import citrus.physics.box2d.IBox2DPhysicsObject;
   import citrus.view.ISpriteView;
   
   public class Box2DPhysicsObject extends APhysicsObject implements ISpriteView, IBox2DPhysicsObject
   {
      protected var _box2D:Box2D = _ce.state.getFirstObjectByType(Box2D) as Box2D;
      
      protected var _bodyDef:b2BodyDef;
      
      protected var _body:b2Body;
      
      protected var _shape:b2Shape;
      
      protected var _fixtureDef:b2FixtureDef;
      
      protected var _fixture:b2Fixture;
      
      protected var _width:Number = 1;
      
      protected var _height:Number = 1;
      
      protected var _beginContactCallEnabled:Boolean = false;
      
      protected var _endContactCallEnabled:Boolean = false;
      
      protected var _preContactCallEnabled:Boolean = false;
      
      protected var _postContactCallEnabled:Boolean = false;
      
      public var points:Array;
      
      protected var _vertices:Array;
      
      public function Box2DPhysicsObject(param1:String, param2:Object = null)
      {
         _ce = CitrusEngine.getInstance();
         super(param1,param2);
      }
      
      override public function addPhysics() : void
      {
         if(!this._box2D)
         {
            throw new Error("Cannot create a Box2DPhysicsObject when a Box2D object has not been added to the state.");
         }
         this.defineBody();
         this.createBody();
         this.createShape();
         this.defineFixture();
         this.createFixture();
         this.defineJoint();
         this.createJoint();
      }
      
      override public function destroy() : void
      {
         this._box2D.world.DestroyBody(this._body);
         this._body.SetUserData(null);
         this._shape = null;
         this._bodyDef = null;
         this._fixtureDef = null;
         this._fixture = null;
         this._box2D = null;
         super.destroy();
      }
      
      protected function defineBody() : void
      {
         this._bodyDef = new b2BodyDef();
         this._bodyDef.type = b2Body.b2_dynamicBody;
         this._bodyDef.position = new b2Vec2(_x,_y);
         this._bodyDef.angle = _rotation;
      }
      
      protected function createBody() : void
      {
         this._body = this._box2D.world.CreateBody(this._bodyDef);
         this._body.SetUserData(this);
      }
      
      protected function createShape() : void
      {
         if(_radius != 0)
         {
            this._shape = new b2CircleShape();
            b2CircleShape(this._shape).SetRadius(_radius);
         }
         else
         {
            this._shape = new b2PolygonShape();
            b2PolygonShape(this._shape).SetAsBox(this._width / 2,this._height / 2);
         }
      }
      
      protected function defineFixture() : void
      {
         var _loc1_:b2PolygonShape = null;
         var _loc2_:* = 0;
         var _loc3_:Number = 0;
         this._fixtureDef = new b2FixtureDef();
         this._fixtureDef.shape = this._shape;
         this._fixtureDef.density = 1;
         this._fixtureDef.friction = 0.6;
         this._fixtureDef.restitution = 0.3;
         this._fixtureDef.filter.categoryBits = PhysicsCollisionCategories.Get("Level");
         this._fixtureDef.filter.maskBits = PhysicsCollisionCategories.GetAll();
         if(Boolean(this.points) && this.points.length > 1)
         {
            this._createVerticesFromPoint();
            _loc2_ = this._vertices.length;
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               _loc1_ = new b2PolygonShape();
               _loc1_.SetAsArray(this._vertices[_loc3_]);
               this._fixtureDef.shape = _loc1_;
               this._body.CreateFixture(this._fixtureDef);
               _loc3_++;
            }
         }
      }
      
      protected function createFixture() : void
      {
         this._fixture = this._body.CreateFixture(this._fixtureDef);
      }
      
      protected function defineJoint() : void
      {
      }
      
      protected function createJoint() : void
      {
      }
      
      public function handleBeginContact(param1:b2Contact) : void
      {
      }
      
      public function handleEndContact(param1:b2Contact) : void
      {
      }
      
      public function handlePreSolve(param1:b2Contact, param2:b2Manifold) : void
      {
      }
      
      public function handlePostSolve(param1:b2Contact, param2:b2ContactImpulse) : void
      {
      }
      
      protected function _createVerticesFromPoint() : void
      {
         this._vertices = [];
         var _loc1_:Array = [];
         var _loc2_:uint = this.points.length;
         var _loc3_:Number = 0;
         while(_loc3_ < _loc2_)
         {
            _loc1_.push(new b2Vec2(this.points[_loc3_].x / this._box2D.scale,this.points[_loc3_].y / this._box2D.scale));
            _loc3_++;
         }
         this._vertices.push(_loc1_);
         _loc1_ = [];
      }
      
      public function get x() : Number
      {
         if(this._body)
         {
            return this._body.GetPosition().x * this._box2D.scale;
         }
         return _x * this._box2D.scale;
      }
      
      public function set x(param1:Number) : void
      {
         var _loc2_:b2Vec2 = null;
         _x = param1 / this._box2D.scale;
         if(this._body)
         {
            _loc2_ = this._body.GetPosition();
            _loc2_.x = _x;
            this._body.SetTransform(new b2Transform(_loc2_,b2Mat22.FromAngle(this._body.GetAngle())));
         }
      }
      
      public function get y() : Number
      {
         if(this._body)
         {
            return this._body.GetPosition().y * this._box2D.scale;
         }
         return _y * this._box2D.scale;
      }
      
      public function set y(param1:Number) : void
      {
         var _loc2_:b2Vec2 = null;
         _y = param1 / this._box2D.scale;
         if(this._body)
         {
            _loc2_ = this._body.GetPosition();
            _loc2_.y = _y;
            this._body.SetTransform(new b2Transform(_loc2_,b2Mat22.FromAngle(this._body.GetAngle())));
         }
      }
      
      public function get z() : Number
      {
         return 0;
      }
      
      public function get rotation() : Number
      {
         if(this._body)
         {
            return this._body.GetAngle() * 180 / Math.PI;
         }
         return _rotation * 180 / Math.PI;
      }
      
      public function set rotation(param1:Number) : void
      {
         var _loc2_:b2Transform = null;
         _rotation = param1 * Math.PI / 180;
         if(this._body)
         {
            _loc2_ = this._body.GetTransform();
            _loc2_.R = b2Mat22.FromAngle(_rotation);
            this._body.SetTransform(_loc2_);
         }
      }
      
      public function get width() : Number
      {
         return this._width * this._box2D.scale;
      }
      
      public function set width(param1:Number) : void
      {
         this._width = param1 / this._box2D.scale;
         if(_initialized && !hideParamWarnings)
         {
         }
      }
      
      public function get height() : Number
      {
         return this._height * this._box2D.scale;
      }
      
      public function set height(param1:Number) : void
      {
         this._height = param1 / this._box2D.scale;
         if(_initialized && !hideParamWarnings)
         {
         }
      }
      
      public function get depth() : Number
      {
         return 0;
      }
      
      public function get radius() : Number
      {
         return _radius * this._box2D.scale;
      }
      
      public function set radius(param1:Number) : void
      {
         _radius = param1 / this._box2D.scale;
         if(_initialized)
         {
         }
      }
      
      public function get body() : b2Body
      {
         return this._body;
      }
      
      override public function getBody() : *
      {
         return this._body;
      }
      
      public function get velocity() : Array
      {
         return [this._body.GetLinearVelocity().x,this._body.GetLinearVelocity().y,0];
      }
      
      public function set velocity(param1:Array) : void
      {
         this._body.SetLinearVelocity(new b2Vec2(param1[0],param1[1]));
      }
      
      public function get beginContactCallEnabled() : Boolean
      {
         return this._beginContactCallEnabled;
      }
      
      public function set beginContactCallEnabled(param1:Boolean) : void
      {
         this._beginContactCallEnabled = param1;
      }
      
      public function get endContactCallEnabled() : Boolean
      {
         return this._endContactCallEnabled;
      }
      
      public function set endContactCallEnabled(param1:Boolean) : void
      {
         this._endContactCallEnabled = param1;
      }
      
      public function get preContactCallEnabled() : Boolean
      {
         return this._preContactCallEnabled;
      }
      
      public function set preContactCallEnabled(param1:Boolean) : void
      {
         this._preContactCallEnabled = param1;
      }
      
      public function get postContactCallEnabled() : Boolean
      {
         return this._postContactCallEnabled;
      }
      
      public function set postContactCallEnabled(param1:Boolean) : void
      {
         this._postContactCallEnabled = param1;
      }
   }
}

