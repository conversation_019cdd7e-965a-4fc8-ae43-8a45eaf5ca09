package mogames.gameMission.mission.pipadong
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameObj.trap.TrapShiZhui;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BOSSXieZiJing;
   import mogames.gameRole.enemy.BOSSZhenXieZi;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.dialog.StoryDialogModule;
   import mogames.gameUI.fuben.zhenbei.ZhenBeiEnterModule;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.TxtUtil;
   
   public class ScenePiPaDong extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _pTrigger5:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _eTrigger4:EnemyTrigger;
      
      private var _failBoss:BOSSXieZiJing;
      
      private var _failPos:Point;
      
      private var _mubei:CitrusMCSprite;
      
      private var _trapTimer:CitrusTimer;
      
      private var _trapPos:Array = [3878,4035,4192,4350,4507,4664];
      
      private var _trapData:Object = {
         "interval":new Snum(8),
         "hurt":new Sint(150)
      };
      
      public function ScenePiPaDong(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER0");
         setWinPosition(new Rectangle(4100,150,514,148),new Point(4283,420));
         this._trapTimer = new CitrusTimer();
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2033,1);
            TaskProxy.instance().addTask(11020);
         };
         _mission.cleanLoadUI();
         this.initMuBei();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2033))
         {
            showDialog("STORY0059",func);
         }
      }
      
      private function initMuBei() : void
      {
         this._mubei = new CitrusMCSprite("MC_MU_BEI_CLIP",{
            "width":210,
            "height":284,
            "group":2
         });
         this._mubei.x = 460;
         this._mubei.y = 200;
         this._mubei.addMouseListener(this.mubeiFunc);
         this._mubei.changeAnimation("idle",true);
         add(this._mubei);
      }
      
      private function mubeiFunc() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2034,1);
            PromptMediator.instance().showPrompt(TxtUtil.setColor("【木系镇碑】","99ff00") + "已经开放！<br>（猪八戒单人副本）",null,null,true,"NEW_FUNCTION",false);
         };
         if(Layers.hasEnemy)
         {
            MiniMsgMediator.instance().showAutoMsg("请先消灭场景里的所有怪物！");
            return;
         }
         if(!FlagProxy.instance().isComplete(2034))
         {
            showDialog("STORY0060",func);
         }
         else
         {
            if(!HeroProxy.instance().isOnlyPig)
            {
               MiniMsgMediator.instance().showAutoMsg("猪八戒单人副本，队伍条件不符！");
               return;
            }
            ZhenBeiEnterModule.instance().init((_mission as PiPaDongMission).enterZhenBei);
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[246,345,150,100],[753,345,150,100]],16011,this.triggerEnd1);
         this._eTrigger1 = new EnemyTrigger(_mission,[[980,345,150,100],[1434,345,150,100]],16012,this.handlerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1684,345,150,100],[2166,345,150,100]],16013,this.handlerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[2522,345,150,100],[2990,345,150,100]],16014,this.triggerEnd3);
         this._eTrigger4 = new EnemyTrigger(_mission,[[3302,345,150,100],[3612,345,150,100]],16015,this.triggerEnd4);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(568,465),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1212,465),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2133,465),1,new Rectangle(960,0,1720,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2757,465),1,new Rectangle(1640,0,1720,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(3521,465),1);
         this._pTrigger5 = new LocalTriggerX(_mission.onePlayer,new Point(4237,465),1,new Rectangle(3840,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger5.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger4.trigger = this._eTrigger4;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this._pTrigger5.start;
         this._pTrigger5.okFunc = this.activeFailBOSS;
         this._pTrigger0.start();
      }
      
      private function triggerEnd4() : void
      {
         this.handlerEnd();
         this.addFailBOSS();
      }
      
      private function triggerEnd3() : void
      {
         this.handlerEnd();
         CitrusLock.instance().lock(new Rectangle(1640,0,2200,600),false,false,true,true);
      }
      
      private function triggerEnd1() : void
      {
         this.handlerEnd();
         CitrusLock.instance().lock(new Rectangle(0,0,1920,600),false,false,false,true);
      }
      
      private function handlerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function startBattle() : void
      {
         this.activeFailBOSS();
      }
      
      private function addFailBOSS() : void
      {
         this._failBoss = new BOSSXieZiJing();
         this._failBoss.x = 4624;
         this._failBoss.y = 330;
         this._failBoss.initData(30261,30261);
         this._failBoss.target = _mission.onePlayer;
         add(this._failBoss);
         this._failBoss.onRole.add(this.listenDead);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSZhenXieZi();
         _boss.x = this._failPos.x;
         _boss.y = 200;
         _boss.initData(30271,30271,75);
         _boss.target = _mission.onePlayer;
         add(_boss);
         this._failBoss.kill = true;
         startBossBattle();
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,_boss.x,_boss.y);
      }
      
      private function listenDead(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         this._failPos = new Point(this._failBoss.x,this._failBoss.y);
         StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(680,87),"嘶嘶————你们惹怒老娘了！","BODY_XIE_ZI",this.addBOSS,2,false);
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      private function activeFailBOSS() : void
      {
         setHeroEnable(true);
         this._failBoss.activeEnemy(false);
         EffectManager.instance().playBGM("BGM_BATTLE0");
         this._trapTimer.setTimeOut(this._trapData.interval.v,this.addShiZhui);
      }
      
      private function addShiZhui() : void
      {
         var _loc2_:Array = null;
         var _loc4_:int = 0;
         var _loc5_:TrapShiZhui = null;
         var _loc1_:int = Math.random() * 5 + 2;
         _loc2_ = this._trapPos.slice();
         var _loc3_:int = 0;
         while(_loc3_ < _loc1_)
         {
            _loc4_ = int(Math.random() * _loc2_.length);
            _loc5_ = new TrapShiZhui();
            _loc5_.endY = 610;
            _loc5_.speed = 10;
            _loc5_.x = _loc2_[_loc4_];
            _loc5_.y = -200;
            _loc5_.createHitEffect("EFFECT_HU_GUAI_ATK","GUNHURT",2,2);
            _loc5_.createHitHurt(this._trapData.hurt.v,0.5,false,1);
            Layers.addCEChild(_loc5_);
            _loc5_.isActive = true;
            _loc2_.splice(_loc4_,1);
            _loc3_++;
         }
         EffectManager.instance().playAudio("FALL");
         this._trapTimer.setTimeOut(this._trapData.interval.v,this.addShiZhui);
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11020);
         super.handlerWin();
         this._trapTimer.pause();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._eTrigger4)
         {
            this._eTrigger4.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         if(this._pTrigger5)
         {
            this._pTrigger5.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._eTrigger4 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._pTrigger5 = null;
         this._trapTimer.destroy();
         this._trapTimer = null;
         this._mubei.removeListener();
         this._mubei = null;
         super.destroy();
      }
   }
}

