package mogames.gameData.rank.vo
{
   import mogames.gameData.base.Sint;
   
   public class RankPetVO
   {
      public var uid:String;
      
      public var nick:String;
      
      public var score:Sint;
      
      public var rank:Sint;
      
      public var saveIndex:int;
      
      public var petName:String;
      
      public var petLv:int;
      
      public var petGrow:String;
      
      public var petTalent:int;
      
      public var petWit:int;
      
      public var petLearn:int;
      
      public var petQuality:int;
      
      public function RankPetVO()
      {
         super();
         this.score = new Sint();
         this.rank = new Sint();
      }
      
      public function parseData(param1:Object) : void
      {
         this.uid = param1.uId;
         this.nick = param1.userName;
         this.score.v = param1.score;
         this.rank.v = param1.rank;
         this.saveIndex = param1.index;
         if(param1.extra is Array)
         {
            this.parseExtra(param1.extra);
         }
      }
      
      private function parseExtra(param1:Array) : void
      {
         this.petName = param1[0];
         this.petLv = int(param1[1]);
         this.petGrow = (int(param1[2]) * 0.01).toFixed(2);
         this.petTalent = int(param1[3]);
         this.petWit = int(param1[4]);
         this.petLearn = int(param1[5]);
         this.petQuality = int(param1[6]);
      }
      
      public function get sortIndex() : int
      {
         return this.rank.v;
      }
   }
}

