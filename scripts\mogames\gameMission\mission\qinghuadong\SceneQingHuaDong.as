package mogames.gameMission.mission.qinghuadong
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.TrapDuMoGu;
   import mogames.gameRole.enemy.BOSSBaiLuJing;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneQingHuaDong extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _bossTrigger:EnemyTrigger;
      
      public function SceneQingHuaDong(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER2");
         setWinPosition(new Rectangle(4130,134,512,118),new Point(4390,416));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2051,1);
            TaskProxy.instance().addTask(11033);
         };
         super.handlerInit();
         this.layoutEnemies();
         this.layoutTraps();
         if(!FlagProxy.instance().isComplete(2051))
         {
            showDialog("STORY0078",func);
         }
      }
      
      private function layoutTraps() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 9)
         {
            this.createTrap(1105 + _loc1_ * 322,477);
            _loc1_++;
         }
         this.createTrap(4078,477);
         this.createTrap(4586,477);
      }
      
      protected function createTrap(param1:int, param2:int) : void
      {
         var _loc3_:TrapDuMoGu = new TrapDuMoGu();
         Layers.addCEChild(_loc3_);
         _loc3_.x = param1;
         _loc3_.y = param2;
         _loc3_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",0,0);
         _loc3_.createHitHurt(1000,1,false);
         _loc3_.createOtherBuff([new BuffVO(1004,5)]);
         _loc3_.initTimer(15);
      }
      
      private function layoutEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[202,216,150,100],[626,216,150,100]],25011,this.handlerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1159,216,150,100],[1583,216,150,100]],25012,this.handlerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[2113,216,150,100],[2537,216,150,100]],25013,this.handlerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[3103,216,150,100],[3528,216,150,100]],25014,this.triggerEnd3);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,428),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1440,480),1,new Rectangle(960,0,960,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2400,480),1,new Rectangle(1920,0,960,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(3360,480),1,new Rectangle(2880,0,960,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(4238,480),1,new Rectangle(3840,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function handlerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd3() : void
      {
         this.handlerEnd();
         this.addBOSS();
      }
      
      private function startBattle() : void
      {
         startBossBattle("BGM_BATTLE2");
         this.startTrigger();
      }
      
      private function startTrigger() : void
      {
         if(this._bossTrigger)
         {
            this._bossTrigger.destroy();
         }
         this._bossTrigger = null;
         this._bossTrigger = new EnemyTrigger(_mission,[[4122,200,150,100],[4422,200,150,100]],25014,this.startTrigger);
         this._bossTrigger.showTip = false;
         this._bossTrigger.autoDestroy = false;
         this._bossTrigger.start();
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSBaiLuJing();
         _boss.x = 4640;
         _boss.y = 280;
         _boss.initData(30421,30421,136);
         _boss.target = _mission.curTarget;
         add(_boss);
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11033);
         super.handlerWin();
         if(this._bossTrigger)
         {
            this._bossTrigger.removeListener();
            this._bossTrigger.destroy();
         }
         this._bossTrigger = null;
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._bossTrigger)
         {
            this._bossTrigger.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._bossTrigger = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         super.destroy();
      }
   }
}

