package mogames.gameMission.mission.huoyundong
{
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameObj.box2d.Door;
   import mogames.gameObj.trap.TrapYanJiang;
   import mogames.gameRole.enemy.BOSSHongHaiEr;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneHuoYunDong1102 extends BossScene
   {
      private var _eTrigger0:EnemyTrigger;
      
      private var _wxTrigger:EnemyTrigger;
      
      private var _door:CitrusMCSprite;
      
      private var _switch:DoorSwitch;
      
      private var _trapPos:Array = [155,490,830,1500];
      
      private var _trapHurt:int = 20;
      
      private var _enemyArgs:Array = [{
         "id":new Sint(2013),
         "attID":new Sint(20131),
         "aiID":new Sint(20131),
         "dropID":new Sint(45)
      },{
         "id":new Sint(2014),
         "attID":new Sint(20141),
         "aiID":new Sint(20141),
         "dropID":new Sint(46)
      },{
         "id":new Sint(2013),
         "attID":new Sint(20131),
         "aiID":new Sint(20131),
         "dropID":new Sint(45)
      }];
      
      private var _enemies:Array = [11022,11023,11024,11025,11026];
      
      private var _ballSkins:Array = ["MC_BALL_JIN_CLIP","MC_BALL_MU_CLIP","MC_BALL_SHUI_CLIP","MC_BALL_HUO_CLIP","MC_BALL_TU_CLIP"];
      
      private var _ballPos:Array = [[1056,288],[1100,230],[1174,200],[1250,231],[1294,288]];
      
      private var _balls:Array;
      
      private var _counter:int;
      
      private var _doorActive:Boolean;
      
      public function SceneHuoYunDong1102(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         _dropRect = new Rectangle(310,190,435,116);
         EffectManager.instance().playBGM("BGM_DANGER0");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            initEnemies();
            FlagProxy.instance().setValue(2022,1);
         };
         super.handlerInit();
         this.initTrap();
         this.initDoor();
         this.addBOSS();
         if(!FlagProxy.instance().isComplete(2022))
         {
            showDialog("STORY0047",func);
         }
         else
         {
            this.initEnemies();
         }
      }
      
      private function initTrap() : void
      {
         var _loc3_:TrapYanJiang = null;
         var _loc1_:int = 0;
         var _loc2_:int = int(this._trapPos.length);
         while(_loc1_ < _loc2_)
         {
            _loc3_ = new TrapYanJiang();
            _loc3_.x = this._trapPos[_loc1_];
            _loc3_.y = 500;
            _loc3_.createHitHurt(this._trapHurt,0,false,0);
            _loc3_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",5,5);
            Layers.addCEChild(_loc3_);
            _loc1_++;
         }
      }
      
      private function initDoor() : void
      {
         var _loc2_:CitrusMCSprite = null;
         this._door = new CitrusMCSprite("MC_DOOR_WU_XING_CLIP",{
            "width":507,
            "height":350,
            "x":940,
            "y":153,
            "group":2
         });
         this._door.changeAnimation("idle");
         add(this._door);
         this._balls = [];
         var _loc1_:int = 0;
         while(_loc1_ < 5)
         {
            _loc2_ = new CitrusMCSprite(this._ballSkins[_loc1_],{
               "width":35,
               "height":36,
               "x":this._ballPos[_loc1_][0],
               "y":this._ballPos[_loc1_][1],
               "group":2
            });
            this._balls[_loc1_] = _loc2_;
            add(_loc2_);
            _loc1_++;
         }
         this._switch = new DoorSwitch("MC_SWITCH_CLIP",102,100,this.changeWuxing);
         this._switch.x = 1400;
         this._switch.y = 408;
         add(this._switch);
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,1020,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[238,335,150,100],[480,335,150,100]],11021);
         this._eTrigger0.start();
         startBossBattle("BGM_BATTLE0");
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHongHaiEr();
         _boss.x = 747;
         _boss.y = 384;
         _boss.initData(30151,30151,48);
         _boss.target = _mission.onePlayer;
         (_boss as BOSSHongHaiEr).enemyList = this._enemyArgs.slice();
         add(_boss);
      }
      
      private function changeWuxing() : void
      {
         var _loc1_:int = 0;
         if(this._counter >= 3)
         {
            _loc1_ = 3;
         }
         else
         {
            ++this._counter;
            _loc1_ = Math.random() * 5;
         }
         if(_loc1_ == 3)
         {
            this._doorActive = true;
         }
         this._balls[_loc1_].changeAnimation("active");
         this._wxTrigger = new EnemyTrigger(_mission,[[931,335,150,100],[1512,335,150,100]],this._enemies[_loc1_],this.enemyEnd);
         this._wxTrigger.start();
      }
      
      private function enemyEnd() : void
      {
         this._wxTrigger.destroy();
         this._wxTrigger = null;
         if(this._doorActive)
         {
            this._door.changeAnimation("open",false);
            (getObjectByName("door1103") as Door).setActive();
         }
         else
         {
            this.resetBall();
            this._switch.changeAnimation("idle",true);
         }
      }
      
      override protected function handlerWin() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2023,1);
            triggerEnd();
         };
         EffectManager.instance().playBGM("BGM_DANGER0");
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
         _boss.kill = true;
         if(!FlagProxy.instance().isComplete(2023))
         {
            showDialog("STORY0048",func);
         }
         else
         {
            this.triggerEnd();
         }
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function resetBall() : void
      {
         var _loc1_:CitrusMCSprite = null;
         for each(_loc1_ in this._balls)
         {
            _loc1_.changeAnimation("idle",true);
         }
      }
      
      override public function destroy() : void
      {
         this._eTrigger0.destroy();
         this._eTrigger0 = null;
         if(this._wxTrigger)
         {
            this._wxTrigger.destroy();
         }
         this._wxTrigger = null;
         super.destroy();
      }
   }
}

