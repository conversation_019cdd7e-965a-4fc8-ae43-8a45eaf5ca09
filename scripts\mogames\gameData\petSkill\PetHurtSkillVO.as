package mogames.gameData.petSkill
{
   import mogames.gameData.role.HeroGameVO;
   import utils.TxtUtil;
   
   public class PetHurtSkillVO extends PetSkillVO
   {
      private var _qualityArg:Array = [1,1.25,1.5,1.75,2];
      
      private var _multi:int;
      
      public function PetHurtSkillVO(param1:int, param2:HeroGameVO, param3:int = 1)
      {
         this._multi = param3;
         super(param1,param2);
      }
      
      public function get hurt() : int
      {
         return countVO.argDic.hurt.v * level.v * this._qualityArg[curQuality.v] * (1 + _pet.curWit.v * 0.05) + _pet.totalATK.v * 0.2;
      }
      
      override public function get tipInfor() : String
      {
         if(this._multi != 1)
         {
            return constVO.infor + TxtUtil.setColor("<br>（造成" + this.hurt * this._multi + "的总伤害）","99ff00");
         }
         return constVO.infor + TxtUtil.setColor("<br>造成" + this.hurt + "的单次伤害","99ff00");
      }
   }
}

