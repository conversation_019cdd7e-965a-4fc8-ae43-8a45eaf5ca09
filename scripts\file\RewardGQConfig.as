package file
{
   import mogames.gameData.activity.RewardGQVO;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.forge.NeedVO;
   
   public class RewardGQConfig
   {
      private static var _instance:RewardGQConfig;
      
      public var list:Array;
      
      public function RewardGQConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : RewardGQConfig
      {
         if(!_instance)
         {
            _instance = new RewardGQConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.list = [];
         this.list[0] = new RewardGQVO(229,new BaseRewardVO(14002,5),[new NeedVO(10027,3)]);
         this.list[1] = new RewardGQVO(230,new BaseRewardVO(16721,2),[new NeedVO(10027,3),new NeedVO(10028,3)]);
         this.list[2] = new RewardGQVO(231,new BaseRewardVO(16711,1),[new NeedVO(10027,3),new NeedVO(10028,3),new NeedVO(10029,3)]);
         this.list[3] = new RewardGQVO(232,new BaseRewardVO(14101,1),[new NeedVO(10027,3),new NeedVO(10028,3),new NeedVO(10029,3),new NeedVO(10030,3)]);
      }
   }
}

