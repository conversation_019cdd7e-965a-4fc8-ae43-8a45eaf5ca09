package citrus.utils.objectmakers.tmx
{
   public class TmxObjectGroup
   {
      public var map:TmxMap;
      
      public var name:String;
      
      public var x:int;
      
      public var y:int;
      
      public var width:int;
      
      public var height:int;
      
      public var opacity:Number;
      
      public var visible:Boolean;
      
      public var properties:TmxPropertySet = null;
      
      public var objects:Array;
      
      public function TmxObjectGroup(param1:XML, param2:TmxMap)
      {
         var _loc3_:XML = null;
         this.objects = [];
         super();
         this.map = param2;
         this.name = param1.@name;
         this.x = param1.@x;
         this.y = param1.@y;
         this.width = param1.@width;
         this.height = param1.@height;
         this.visible = !param1.@visible || param1.@visible != 0;
         this.opacity = param1.@opacity;
         for each(_loc3_ in param1.properties)
         {
            this.properties = !!this.properties ? this.properties.extend(_loc3_) : new TmxPropertySet(_loc3_);
         }
         for each(_loc3_ in param1.object)
         {
            this.objects.push(new TmxObject(_loc3_,this));
         }
      }
   }
}

