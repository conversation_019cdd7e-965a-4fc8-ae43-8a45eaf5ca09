package mogames.gameData.danyao
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class DYHCVO extends BaseUseVO
   {
      public var danyaoID:Sint;
      
      public function DYHCVO(param1:int, param2:int, param3:Array = null)
      {
         this.danyaoID = new Sint(param1);
         super(param2,param3);
      }
      
      public function get constDanyao() : ConstGoodVO
      {
         return GoodConfig.instance().findConstGood(this.danyaoID.v);
      }
   }
}

