package file
{
   import mogames.gameData.role.vo.HeroCountVO;
   import mogames.gameData.role.vo.RoleAssetVO;
   import mogames.gameData.role.vo.RoleConstVO;
   
   public class HeroConfig
   {
      private static var _instance:HeroConfig;
      
      private var _constVec:Vector.<RoleConstVO>;
      
      private var _countVec:Vector.<HeroCountVO>;
      
      private var _assetVec:Vector.<RoleAssetVO>;
      
      public function HeroConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : HeroConfig
      {
         if(!_instance)
         {
            _instance = new HeroConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._constVec = new Vector.<RoleConstVO>();
         this._countVec = new Vector.<HeroCountVO>();
         this._assetVec = new Vector.<RoleAssetVO>();
         this._constVec.push(new RoleConstVO(1001,1,11,10,26,110));
         this._constVec.push(new RoleConstVO(1003,2,10,10,28,110));
         this._constVec.push(new RoleConstVO(1002,3,12,10,26,110));
         this._constVec.push(new RoleConstVO(1005,4,10,9,28,106));
         this._constVec.push(new RoleConstVO(1004,5,10,11,28,110));
         this._countVec.push(new HeroCountVO(1001,1.5,1.4,1.6,3,3,5,5,6,8,0,2.5,4.5));
         this._countVec.push(new HeroCountVO(1003,1.9,1.3,1.2,2,2,5,5,7,7,0,2.5,4.5));
         this._countVec.push(new HeroCountVO(1002,1.25,1.4,1.3,2,2,5,5,14,12,0,2.5,4.5));
         this._countVec.push(new HeroCountVO(1005,1.15,1.6,1.5,2,7,5,5,6,6,0,2.5,4.5));
         this._countVec.push(new HeroCountVO(1004,1.4,1.3,1.45,7,2,5,5,10,6,0,2.5,4.5));
         this._assetVec.push(new RoleAssetVO(1001,"孙悟空","ICON_SUNWUKONG","EFFECT_GUN_ATK","SWK_ATK_HURT","","BODY_SUNWUKONG","WUQI_SUNWUKONG","SKILL_SUNWUKONG"));
         this._assetVec.push(new RoleAssetVO(1002,"小白龙","ICON_XIAOBAILONG","EFFECT_SWORD_HIT_CLIP","XBL_ATK_HURT","","BODY_XIAOBAILONG","WEAPON_XIAOBAILONG","SKILL_XIAOBAILONG"));
         this._assetVec.push(new RoleAssetVO(1003,"猪八戒","ICON_ZHUBAJIE","EFFECT_PUNCH_HURT_CLIP","ZBJ_ATK_HURT","","BODY_ZHUBAJIE","WUQI_ZHUBAJIE","SKILL_ZHUBAJIE"));
         this._assetVec.push(new RoleAssetVO(1004,"沙僧","ICON_SHASENG","EFFECT_GUN_ATK","SS_ATK_HURT","","BODY_SHASENG","WUQI_SHASENG","SKILL_SHASENG"));
         this._assetVec.push(new RoleAssetVO(1005,"小龙女","ICON_XIAOLONGNV","SEQ_XIAOLONGNV_BALL_BOOM","FIRE_EXPLORE","","BODY_XIAOLONGNV","WUQI_XIAOLONGNV","SKILL_XIAOLONGNV"));
      }
      
      public function findConstRole(param1:int) : RoleConstVO
      {
         var _loc2_:RoleConstVO = null;
         for each(_loc2_ in this._constVec)
         {
            if(_loc2_.roleID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findRoleCount(param1:int) : HeroCountVO
      {
         var _loc2_:HeroCountVO = null;
         if(param1 == 0)
         {
            return null;
         }
         for each(_loc2_ in this._countVec)
         {
            if(_loc2_.roleID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findRoleAsset(param1:int) : RoleAssetVO
      {
         var _loc2_:RoleAssetVO = null;
         for each(_loc2_ in this._assetVec)
         {
            if(_loc2_.roleID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

