package mogames.gameData.flag.fuben
{
   import mogames.gameData.base.Sint;
   
   public class FBMoneyFlag extends FBBase<PERSON>lag
   {
      protected var _curBuy:Sint;
      
      protected var _totalBuy:Sint;
      
      public function FBMoneyFlag(param1:int, param2:int, param3:int)
      {
         super(param1,param2);
         this._curBuy = new Sint();
         this._totalBuy = new Sint(param3);
      }
      
      override public function dailyRefresh() : void
      {
         super.dailyRefresh();
         this._curBuy.v = 0;
      }
      
      public function useBuy() : void
      {
         this._curBuy.v += 1;
      }
      
      public function get isBuy() : <PERSON><PERSON>an
      {
         return this._curBuy.v < this.totalBuy;
      }
      
      public function get leftBuy() : int
      {
         return this.totalBuy - this._curBuy.v;
      }
      
      public function get totalBuy() : int
      {
         return this._totalBuy.v;
      }
      
      override public function get status() : String
      {
         if(isFree)
         {
            return "免费挑战剩余：" + leftFree + "次";
         }
         return "购买挑战剩余：" + this.leftBuy + "次";
      }
      
      override public function get saveData() : String
      {
         return [id.v,_curFree.v,this._curBuy.v].join("H");
      }
      
      override public function set loadData(param1:Array) : void
      {
         _curFree.v = int(param1[1]);
         this._curBuy.v = int(param1[2]);
      }
   }
}

