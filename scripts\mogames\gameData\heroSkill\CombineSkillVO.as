package mogames.gameData.heroSkill
{
   import file.SkillConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.IconVO;
   import mogames.gameData.heroSkill.constvo.ConstCombineVO;
   
   public class CombineSkillVO extends IconVO
   {
      public static const MAX_LEVEL:Sint = new Sint(20);
      
      public var id:Sint;
      
      public var level:Sint;
      
      public var constVO:ConstCombineVO;
      
      public function CombineSkillVO(param1:int)
      {
         super();
         this.id = new Sint(param1);
         this.level = new Sint();
         this.constVO = SkillConfig.instance().findConstCombine(param1);
      }
      
      public function setLevel(param1:int) : void
      {
         this.level.v = param1;
      }
      
      public function get needStr() : String
      {
         return "铜钱X999999";
      }
      
      public function get isActive() : Boolean
      {
         return true;
      }
      
      public function get isMaxLevel() : Boolean
      {
         return this.level.v >= MAX_LEVEL.v;
      }
      
      public function parseLoadData(param1:Array) : void
      {
         this.setLevel(int(param1[1]));
      }
      
      public function collectSaveData() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.constVO.id.v;
         _loc1_[1] = this.level.v;
         return _loc1_.join("A");
      }
   }
}

