package mogames.gameFabao.view
{
   import mogames.gameData.ConstRole;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoDongHuangZhong;
   
   public class FabaoDHZView extends FabaoBaseView
   {
      private var _fabao:FabaoDongHuangZhong;
      
      public function FabaoDHZView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         addSound(param1);
         super.updateCurrentFrame(param1);
         if(!mcATK)
         {
            return;
         }
         if(curStatus == ConstRole.SKILL_ONE)
         {
            this._fabao.dispatchSkillOne();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as <PERSON>abao<PERSON>ong<PERSON><PERSON><PERSON><PERSON>;
      }
   }
}

