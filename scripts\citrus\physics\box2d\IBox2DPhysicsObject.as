package citrus.physics.box2d
{
   import Box2D.Collision.b2Manifold;
   import Box2D.Dynamics.Contacts.b2Contact;
   import Box2D.Dynamics.b2Body;
   import Box2D.Dynamics.b2ContactImpulse;
   
   public interface IBox2DPhysicsObject
   {
      function handleBeginContact(param1:b2Contact) : void;
      
      function handleEndContact(param1:b2Contact) : void;
      
      function handlePreSolve(param1:b2Contact, param2:b2Manifold) : void;
      
      function handlePostSolve(param1:b2Contact, param2:b2ContactImpulse) : void;
      
      function fixedUpdate() : void;
      
      function get x() : Number;
      
      function set x(param1:Number) : void;
      
      function get y() : Number;
      
      function set y(param1:Number) : void;
      
      function get z() : Number;
      
      function get rotation() : Number;
      
      function set rotation(param1:Number) : void;
      
      function get width() : Number;
      
      function set width(param1:Number) : void;
      
      function get height() : Number;
      
      function set height(param1:Number) : void;
      
      function get depth() : Number;
      
      function get radius() : Number;
      
      function set radius(param1:Number) : void;
      
      function get body() : b2Body;
      
      function getBody() : *;
      
      function get beginContactCallEnabled() : Boolean;
      
      function set beginContactCallEnabled(param1:Boolean) : void;
      
      function get endContactCallEnabled() : Boolean;
      
      function set endContactCallEnabled(param1:Boolean) : void;
      
      function get preContactCallEnabled() : Boolean;
      
      function set preContactCallEnabled(param1:Boolean) : void;
      
      function get postContactCallEnabled() : Boolean;
      
      function set postContactCallEnabled(param1:Boolean) : void;
   }
}

