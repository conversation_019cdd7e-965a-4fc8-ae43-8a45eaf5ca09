package file
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.card.KPFJVO;
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.good.GameGoodVO;
   import utils.MathUtil;
   
   public class KPFJConfig
   {
      private static var _instance:KPFJConfig;
      
      private var _list:Array;
      
      public function KPFJConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : KPFJConfig
      {
         if(!_instance)
         {
            _instance = new KPFJConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list.push(new KPFJVO(71001,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71002,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71003,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71004,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71005,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71006,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71007,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71008,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71009,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71010,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71011,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71012,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71013,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71014,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71015,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71016,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71017,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71018,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71019,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71020,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71021,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71022,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71023,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71024,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71025,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71026,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71027,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71028,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71029,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71030,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71031,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71032,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71033,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71034,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71035,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71036,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71037,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71038,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71039,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71040,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71041,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71042,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71043,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71044,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71045,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71046,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71047,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71048,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71049,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71050,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71051,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71052,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71053,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71054,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71055,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71056,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71057,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71058,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71059,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71060,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71061,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71062,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71063,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71064,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71065,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71066,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71067,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71068,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71069,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71070,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71071,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71072,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71073,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71074,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71075,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71076,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71077,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71078,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71079,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71080,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71081,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71082,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71083,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71084,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71085,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71086,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71087,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71088,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71089,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71090,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71091,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71092,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71093,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71094,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71095,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71096,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71097,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71098,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71099,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71100,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71101,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71102,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71103,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71104,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71105,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71106,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71107,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71108,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71109,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71110,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71111,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71112,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71113,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71114,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71115,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71116,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71117,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71118,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71119,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71120,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71121,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71122,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71123,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71124,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71125,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71126,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71127,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71128,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71129,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71130,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71131,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71132,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71133,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71134,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71135,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71136,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71137,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71138,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71139,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71140,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71141,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71142,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71143,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71144,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71145,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71146,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71147,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71148,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71149,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71150,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71151,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71152,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71153,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71154,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71155,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71156,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71157,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71158,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71159,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71160,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71161,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71162,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71163,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71164,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71165,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71166,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71167,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71168,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71169,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71170,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71171,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71172,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71173,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71174,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71175,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71176,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71177,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71178,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71179,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71180,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71181,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71182,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71183,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71184,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71185,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71186,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71187,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71188,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71189,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71190,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71191,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71192,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71193,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71194,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71195,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(71196,10000,[new BaseRewardVO(50000,1)]));
         this._list.push(new KPFJVO(71197,15000,[new BaseRewardVO(50000,2)]));
         this._list.push(new KPFJVO(71198,20000,[new BaseRewardVO(50000,3)]));
         this._list.push(new KPFJVO(71199,25000,[new BaseRewardVO(50000,4)]));
         this._list.push(new KPFJVO(71200,30000,[new BaseRewardVO(50000,5)]));
         this._list.push(new KPFJVO(70001,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70006,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70011,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70016,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70021,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70026,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70031,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70036,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70041,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70046,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70051,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70056,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70061,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70066,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70071,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70076,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70081,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70086,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70091,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70096,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70101,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70106,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70111,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70116,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70121,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70126,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70131,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70136,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70141,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70146,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70151,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70156,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70161,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70166,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70161,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70166,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70171,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70176,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70181,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70186,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70191,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70196,20000,[new BaseRewardVO(50005,1)]));
         this._list.push(new KPFJVO(70951,50000,[new BaseRewardVO(50008,1)]));
         this._list.push(new KPFJVO(70956,50000,[new BaseRewardVO(50008,1)]));
         this._list.push(new KPFJVO(70961,50000,[new BaseRewardVO(50008,1)]));
         this._list.push(new KPFJVO(70966,50000,[new BaseRewardVO(50008,1)]));
         this._list.push(new KPFJVO(70971,50000,[new BaseRewardVO(50008,1)]));
         this._list.push(new KPFJVO(70976,50000,[new BaseRewardVO(50008,1)]));
         this._list.push(new KPFJVO(70981,50000,[new BaseRewardVO(50008,1)]));
      }
      
      public function findCost(param1:int) : KPFJVO
      {
         var _loc2_:KPFJVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function countRewards(param1:KPFJVO, param2:int = 1) : Array
      {
         var _loc4_:GameGoodVO = null;
         var _loc3_:Array = RewardHandler.instance().newGiftReward(param1.rewards);
         for each(_loc4_ in _loc3_)
         {
            _loc4_.amount.v *= param2;
            if(MasterProxy.instance().gameVIP.v >= 7)
            {
               if(MathUtil.checkOdds(350))
               {
                  _loc4_.amount.v *= 2;
               }
            }
            else if(MathUtil.checkOdds(250) && MasterProxy.instance().gameVIP.v >= 5)
            {
               _loc4_.amount.v *= 2;
            }
         }
         return _loc3_;
      }
   }
}

