package file
{
   import mogames.gameData.FJCJ.ZBCJVO;
   import mogames.gameData.forge.NeedVO;
   
   public class ZBCJConfig
   {
      private static var _instance:ZBCJConfig;
      
      private var _cjVec:Vector.<ZBCJVO>;
      
      public function ZBCJConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : ZBCJConfig
      {
         if(!_instance)
         {
            _instance = new ZBCJConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._cjVec = new Vector.<ZBCJVO>();
         this._cjVec.push(new ZBCJVO(20001,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(20002,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(20003,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(20004,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(20005,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(21001,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(21002,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(21003,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(21004,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(21005,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(22001,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(22002,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(22003,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(22004,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(22005,1000,new NeedVO(14011,2)));
         this._cjVec.push(new ZBCJVO(20006,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(20007,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(20008,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(20009,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(20010,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(21006,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(21007,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(21008,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(21009,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(21010,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(22006,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(22007,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(22008,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(22009,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(22010,2000,new NeedVO(14011,3)));
         this._cjVec.push(new ZBCJVO(20011,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(20012,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(20013,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(20014,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(20015,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(21011,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(21012,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(21013,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(21014,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(21015,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(22011,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(22012,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(22013,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(22014,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(22015,5000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(20016,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(20017,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(20018,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(20019,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(20020,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(21016,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(21017,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(21018,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(21019,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(21020,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(22016,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(22017,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(22018,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(22019,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(22020,10000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(20021,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(20022,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(20023,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(20024,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(20025,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(21021,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(21022,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(21023,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(21024,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(21025,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(22021,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(22022,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(22023,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(22024,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(22025,20000,new NeedVO(14011,6)));
         this._cjVec.push(new ZBCJVO(23001,10000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(23002,10000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(23003,10000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(23004,10000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(23005,10000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(23006,20000,new NeedVO(14011,4)));
         this._cjVec.push(new ZBCJVO(23007,20000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(23008,20000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(23009,20000,new NeedVO(14011,5)));
         this._cjVec.push(new ZBCJVO(23010,20000,new NeedVO(14011,5)));
      }
      
      public function findVO(param1:int) : ZBCJVO
      {
         var _loc2_:ZBCJVO = null;
         for each(_loc2_ in this._cjVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

