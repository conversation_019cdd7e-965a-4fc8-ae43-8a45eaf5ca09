package mogames.gameData.pet
{
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetHurtSkillVO;
   
   public class PetSheLingVO extends PetGameVO
   {
      public function PetSheLingVO()
      {
         allSkills = this.newActiveSkills();
         super(1102);
      }
      
      override public function newActiveSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new PetHurtSkillVO(11021,this));
         _loc1_.push(new PetHurtSkillVO(11022,this));
         _loc1_.push(new PetHurtSkillVO(11023,this));
         _loc1_.push(new PetHurtSkillVO(11024,this));
         _loc1_.push(new PetHurtSkillVO(11025,this));
         _loc1_.push(new PetHurtSkillVO(11026,this));
         return _loc1_;
      }
   }
}

