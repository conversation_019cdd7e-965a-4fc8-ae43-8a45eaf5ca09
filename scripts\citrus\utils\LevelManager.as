package citrus.utils
{
   import flash.display.Loader;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   import flash.system.SecurityDomain;
   import org.osflash.signals.Signal;
   
   public class LevelManager
   {
      private static var _instance:LevelManager;
      
      public var onLevelChanged:Signal;
      
      public var checkPolicyFile:Boolean = false;
      
      public var applicationDomain:ApplicationDomain = null;
      
      public var securityDomain:SecurityDomain = null;
      
      public var levels:Array;
      
      public var currentLevel:Object;
      
      public var enableSwfCaching:Boolean = false;
      
      private var _ALevel:Class;
      
      private var _currentIndex:uint;
      
      private var _levelData:Array;
      
      public function LevelManager(param1:Class)
      {
         super();
         _instance = this;
         this._ALevel = param1;
         this._levelData = new Array();
         this.onLevelChanged = new Signal(this._ALevel);
         this._currentIndex = 0;
      }
      
      public static function getInstance() : LevelManager
      {
         return _instance;
      }
      
      public function destroy() : void
      {
         this.onLevelChanged.removeAll();
         this.currentLevel = null;
      }
      
      public function nextLevel() : void
      {
         if(this._currentIndex < this.levels.length - 1)
         {
            ++this._currentIndex;
         }
         this.gotoLevel();
      }
      
      public function prevLevel() : void
      {
         if(this._currentIndex > 0)
         {
            --this._currentIndex;
         }
         this.gotoLevel();
      }
      
      public function gotoLevel(param1:uint = 0) : void
      {
         var _loc2_:String = null;
         var _loc3_:URLLoader = null;
         var _loc4_:Loader = null;
         var _loc5_:LoaderContext = null;
         if(param1 != 0)
         {
            this._currentIndex = param1 - 1;
         }
         if(this.levels[this._currentIndex][0] == undefined)
         {
            this.currentLevel = this._ALevel(new this.levels[this._currentIndex]());
            this.onLevelChanged.dispatch(this.currentLevel);
         }
         else if(this.levels[this._currentIndex][1] is Class || this.levels[this._currentIndex][1] is XML)
         {
            this.currentLevel = this.levels[this._currentIndex][1] is Class ? this._ALevel(new this.levels[this._currentIndex][0](new this.levels[this._currentIndex][1]())) : this._ALevel(new this.levels[this._currentIndex][0](this.levels[this._currentIndex][1]));
            this.onLevelChanged.dispatch(this.currentLevel);
         }
         else
         {
            _loc2_ = this.levels[this._currentIndex][1].substring(this.levels[this._currentIndex][1].length - 4).toLowerCase();
            if(_loc2_ == ".xml" || _loc2_ == ".lev" || _loc2_ == ".tmx")
            {
               _loc3_ = new URLLoader();
               _loc3_.load(new URLRequest(this.levels[this._currentIndex][1]));
               _loc3_.addEventListener(Event.COMPLETE,this._levelLoaded);
            }
            else if(this.enableSwfCaching && this._levelData.length > this._currentIndex && this._levelData[this._currentIndex] != null)
            {
               this.createLevelFromCache();
            }
            else
            {
               _loc4_ = new Loader();
               _loc5_ = new LoaderContext(this.checkPolicyFile,this.applicationDomain,this.securityDomain);
               _loc4_.load(new URLRequest(this.levels[this._currentIndex][1]),_loc5_);
               _loc4_.contentLoaderInfo.addEventListener(Event.COMPLETE,this._levelLoaded);
               _loc4_.contentLoaderInfo.addEventListener(IOErrorEvent.IO_ERROR,this._handleLoaderError);
            }
         }
      }
      
      private function _levelLoaded(param1:Event) : void
      {
         if(param1.target is URLLoader)
         {
            this.currentLevel = this._ALevel(new this.levels[this._currentIndex][0](XML(param1.target.data)));
         }
         else
         {
            if(this.enableSwfCaching)
            {
               this._levelData[this._currentIndex] = param1.target.loader.content;
            }
            this.currentLevel = this._ALevel(new this.levels[this._currentIndex][0](param1.target.loader.content));
         }
         this.onLevelChanged.dispatch(this.currentLevel);
         if(param1.target is Loader)
         {
            param1.target.contentLoaderInfo.removeEventListener(Event.COMPLETE,this._levelLoaded);
            param1.target.contentLoaderInfo.removeEventListener(IOErrorEvent.IO_ERROR,this._handleLoaderError);
            param1.target.loader.unloadAndStop();
         }
         else if(param1.target is URLLoader)
         {
            param1.target.removeEventListener(Event.COMPLETE,this._levelLoaded);
         }
      }
      
      private function createLevelFromCache() : void
      {
         this.currentLevel = this._ALevel(new this.levels[this._currentIndex][0](this._levelData[this._currentIndex]));
         this.onLevelChanged.dispatch(this.currentLevel);
      }
      
      private function _handleLoaderError(param1:IOErrorEvent) : void
      {
      }
      
      public function get nameCurrentLevel() : String
      {
         return this.currentLevel.nameLevel;
      }
      
      public function get currentIndex() : uint
      {
         return this._currentIndex;
      }
   }
}

