package mogames.gameData.pk.vo
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class PKExchangeVO
   {
      public var shopID:Sint;
      
      private var _goodID:Sint;
      
      public var flag:Sint;
      
      public var need:Sint;
      
      public function PKExchangeVO(param1:int, param2:int, param3:int)
      {
         super();
         this.shopID = new Sint(param1);
         this._goodID = new Sint(param2);
         this.need = new Sint(param3);
         this.flag = new Sint();
      }
      
      public function handlerBuy() : void
      {
         this.flag.v = 1;
      }
      
      public function get hasBuy() : <PERSON><PERSON>an
      {
         return this.flag.v == 1;
      }
      
      public function get goodVO() : GameGoodVO
      {
         return GoodConfig.instance().newGood(this._goodID.v);
      }
      
      public function get constGood() : ConstGoodVO
      {
         return GoodConfig.instance().findConstGood(this._goodID.v);
      }
      
      public function get price() : String
      {
         return "荣誉令X" + this.need.v;
      }
      
      public function get saveData() : String
      {
         return this.shopID.v + "H" + this.flag.v;
      }
   }
}

