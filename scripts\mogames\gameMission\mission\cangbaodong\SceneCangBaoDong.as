package mogames.gameMission.mission.cangbaodong
{
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.enemy.BaseEnemy;
   import mogames.gameRole.enemy.EnemyBaoXiangGuai;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.MathUtil;
   
   public class SceneCangBaoDong extends BaseScene
   {
      private var _enemyTimer:CitrusTimer;
      
      private var _enemyList:Array;
      
      private var _rect:Rectangle = new Rectangle(90,-228,1260,636);
      
      public function SceneCangBaoDong(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         this._enemyTimer = new CitrusTimer();
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this._enemyTimer.setInterval(1,120,this.onTimer,this.onTimeOut);
         this.layoutEnemies();
      }
      
      private function layoutEnemies() : void
      {
         this._enemyList = [];
         this.addNewEnemy();
      }
      
      private function addNewEnemy() : void
      {
         var _loc1_:BaseEnemy = null;
         _loc1_ = new EnemyBaoXiangGuai(100,1300,MathUtil.checkOdds(500));
         _loc1_.x = int(this._rect.x + Math.random() * this._rect.width);
         _loc1_.y = int(this._rect.y + Math.random() * this._rect.height);
         _loc1_.initData(20271,20271,89);
         _loc1_.target = _mission.curTarget;
         _loc1_.onRole.addOnce(this.listenDead);
         Layers.addCEChild(_loc1_);
         _loc1_.activeEnemy();
         this._enemyList.push(_loc1_);
      }
      
      private function listenDead(param1:String) : void
      {
         if(param1 != "onDead" || !this._enemyTimer.running)
         {
            return;
         }
         if(this.checkAllDead())
         {
            this._enemyTimer.pause();
            this.onTimeOut();
         }
      }
      
      private function checkAllDead() : Boolean
      {
         var _loc1_:BaseEnemy = null;
         for each(_loc1_ in this._enemyList)
         {
            if(!_loc1_.isDead)
            {
               return false;
            }
         }
         return true;
      }
      
      private function onTimer() : void
      {
         MiniMsgMediator.instance().showMsg("剩余时间：" + this._enemyTimer.leftTime + "秒",480,50);
      }
      
      private function onTimeOut() : void
      {
         this.destroyEnemies();
         addLeaveDoor(480,650);
         MiniMsgMediator.clean();
      }
      
      private function destroyEnemies() : void
      {
         var _loc1_:BaseEnemy = null;
         for each(_loc1_ in this._enemyList)
         {
            if(!(!_loc1_ || _loc1_.isDead))
            {
               _loc1_.visible = false;
               _loc1_.onRole.removeAll();
               _loc1_.setSysDead();
            }
         }
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         MissionManager.instance().handlerWin(false);
      }
      
      override public function destroy() : void
      {
         MiniMsgMediator.clean();
         if(this._enemyTimer.running)
         {
            this.destroyEnemies();
         }
         this._enemyTimer.destroy();
         this._enemyTimer = null;
         this._enemyList = null;
         super.destroy();
      }
   }
}

