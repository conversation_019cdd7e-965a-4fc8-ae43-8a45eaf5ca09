package file
{
   import mogames.gameData.drop.vo.EquipRewardVO;
   import mogames.gameData.fashion.SZHCVO;
   import mogames.gameData.good.GameEquipVO;
   
   public class SZHCConfig
   {
      private static var _instance:SZHCConfig;
      
      private var _list:Vector.<SZHCVO>;
      
      public function SZHCConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : SZHCConfig
      {
         if(!_instance)
         {
            _instance = new SZHCConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._list = new Vector.<SZHCVO>();
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(34131,4),[new EquipRewardVO(34101,3),new EquipRewardVO(34111,3),new EquipRewardVO(34121,3)]);
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(35031,4),[new EquipRewardVO(35001,3),new EquipRewardVO(35011,3),new EquipRewardVO(35021,3)]);
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(34132,4),[new EquipRewardVO(34102,3),new EquipRewardVO(34112,3),new EquipRewardVO(34122,3)]);
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(35032,4),[new EquipRewardVO(35002,3),new EquipRewardVO(35012,3),new EquipRewardVO(35022,3)]);
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(34133,4),[new EquipRewardVO(34103,3),new EquipRewardVO(34113,3),new EquipRewardVO(34123,3)]);
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(35033,4),[new EquipRewardVO(35003,3),new EquipRewardVO(35013,3),new EquipRewardVO(35023,3)]);
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(34134,4),[new EquipRewardVO(34104,3),new EquipRewardVO(34114,3),new EquipRewardVO(34124,3)]);
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(35034,4),[new EquipRewardVO(35004,3),new EquipRewardVO(35014,3),new EquipRewardVO(35024,3)]);
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(34135,4),[new EquipRewardVO(34105,3),new EquipRewardVO(34115,3),new EquipRewardVO(34125,3)]);
         this._list[this._list.length] = new SZHCVO(new EquipRewardVO(35035,4),[new EquipRewardVO(35005,3),new EquipRewardVO(35015,3),new EquipRewardVO(35025,3)]);
      }
      
      public function checkEquip(param1:GameEquipVO) : Boolean
      {
         var _loc2_:SZHCVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.inList(param1))
            {
               return true;
            }
         }
         return false;
      }
      
      public function checkHC(param1:Array) : SZHCVO
      {
         var _loc2_:SZHCVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.checkList(param1))
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

