package citrus.events
{
   import citrus.core.citrus_internal;
   import flash.utils.Dictionary;
   
   use namespace citrus_internal;
   
   public class CitrusEventDispatcher
   {
      protected var listeners:Dictionary;
      
      protected var dispatchParent:CitrusEventDispatcher;
      
      protected var dispatchChildren:Vector.<CitrusEventDispatcher>;
      
      public function CitrusEventDispatcher()
      {
         super();
         this.listeners = new Dictionary();
      }
      
      citrus_internal function addDispatchChild(param1:CitrusEventDispatcher) : CitrusEventDispatcher
      {
         if(!this.dispatchChildren)
         {
            this.dispatchChildren = new Vector.<CitrusEventDispatcher>();
         }
         param1.dispatchParent = this;
         this.dispatchChildren.push(param1);
         return param1;
      }
      
      citrus_internal function removeDispatchChild(param1:CitrusEventDispatcher) : void
      {
         var _loc2_:int = int(this.dispatchChildren.indexOf(param1));
         if(_loc2_ < 0)
         {
            return;
         }
         param1.dispatchParent = null;
         this.dispatchChildren.splice(_loc2_,1);
         if(this.dispatchChildren.length == 0)
         {
            this.dispatchChildren = null;
         }
      }
      
      citrus_internal function removeDispatchChildren() : void
      {
         var _loc1_:CitrusEventDispatcher = null;
         for each(_loc1_ in this.dispatchChildren)
         {
            this.citrus_internal::removeDispatchChild(_loc1_);
         }
      }
      
      public function addEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         if(param1 in this.listeners)
         {
            this.listeners[param1].push({
               "func":param2,
               "useCapture":param3
            });
         }
         else
         {
            this.listeners[param1] = new Vector.<Object>();
            this.listeners[param1].push({
               "func":param2,
               "useCapture":param3
            });
         }
      }
      
      public function removeEventListener(param1:String, param2:Function) : void
      {
         var _loc3_:String = null;
         var _loc4_:Vector.<Object> = null;
         if(param1 in this.listeners)
         {
            _loc4_ = this.listeners[param1];
            for(_loc3_ in _loc4_)
            {
               if(_loc4_[_loc3_].func == param2)
               {
                  _loc4_.splice(int(_loc3_),1);
               }
            }
         }
      }
      
      public function willTrigger(param1:Function) : Boolean
      {
         var _loc2_:String = null;
         var _loc3_:Vector.<Function> = null;
         var _loc4_:Object = null;
         for(_loc2_ in this.listeners)
         {
            _loc3_ = this.listeners[_loc2_];
            for each(_loc4_ in _loc3_)
            {
               if(_loc4_.func == param1)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function dispatchEvent(param1:CitrusEvent) : void
      {
         var _loc4_:Object = null;
         var _loc5_:Vector.<Object> = null;
         var _loc6_:CitrusEventDispatcher = null;
         if(!param1._target)
         {
            param1._target = this;
         }
         param1._currentTarget = this;
         var _loc2_:int = param1._phase;
         var _loc3_:Boolean = false;
         if(this == param1._target)
         {
            param1._phase = CitrusEvent.AT_TARGET;
         }
         if(param1._type in this.listeners)
         {
            _loc5_ = this.listeners[param1.type];
            for each(_loc4_ in _loc5_)
            {
               param1._currentListener = _loc4_.func;
               if(_loc4_.func.length == 0)
               {
                  _loc4_.func.apply();
               }
               else
               {
                  _loc4_.func.apply(null,[param1]);
               }
               _loc3_ = true;
            }
         }
         if(param1._phase == CitrusEvent.AT_TARGET && param1._bubbles)
         {
            _loc2_ = param1._phase = CitrusEvent.BUBBLE_PHASE;
         }
         if(Boolean(this.dispatchChildren) && _loc2_ == CitrusEvent.CAPTURE_PHASE)
         {
            for each(_loc6_ in this.dispatchChildren)
            {
               _loc6_.dispatchEvent(param1);
            }
         }
         if(Boolean(this.dispatchParent) && _loc2_ == CitrusEvent.BUBBLE_PHASE)
         {
            this.dispatchParent.dispatchEvent(param1);
         }
      }
      
      public function hasEventListener(param1:String) : Boolean
      {
         return param1 in this.listeners;
      }
      
      public function removeListenersOf(param1:String) : void
      {
         if(param1 in this.listeners)
         {
            delete this.listeners[param1];
         }
      }
      
      public function removeListener(param1:Function) : void
      {
         var _loc2_:String = null;
         var _loc3_:String = null;
         var _loc4_:Vector.<Object> = null;
         for(_loc2_ in this.listeners)
         {
            _loc4_ = this.listeners[_loc2_];
            for(_loc3_ in _loc4_)
            {
               if(param1 == _loc4_[_loc3_].func)
               {
                  _loc4_.splice(int(_loc3_),1);
               }
            }
         }
      }
      
      public function removeEventListeners() : void
      {
         this.listeners = new Dictionary();
      }
   }
}

