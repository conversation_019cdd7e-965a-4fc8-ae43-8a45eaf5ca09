package mogames.gameData.flag
{
   import file.FlagConfig;
   import mogames.gameData.flag.vo.NumFlag;
   import utils.MathUtil;
   
   public class FlagProxy
   {
      private static var _instance:FlagProxy;
      
      private var _flags:Vector.<NumFlag>;
      
      private var wd:Array = [1,2,3,4,5,6,7,8,9,10,11,15,18];
      
      public function FlagProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : FlagProxy
      {
         if(!_instance)
         {
            _instance = new FlagProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._flags = null;
         this._flags = FlagConfig.instance().newFlags();
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:Array = null;
         var _loc4_:NumFlag = null;
         var _loc5_:* = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc5_ in _loc2_)
         {
            _loc3_ = _loc5_.split("H");
            _loc4_ = this.findFlag(int(_loc3_[0]));
            if(_loc4_)
            {
               _loc4_.setValue(int(_loc3_[1]));
            }
         }
      }
      
      public function get saveData() : String
      {
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         var _loc3_:int = int(this._flags.length);
         while(_loc2_ < _loc3_)
         {
            _loc1_[_loc2_] = this._flags[_loc2_].saveStr;
            _loc2_++;
         }
         return _loc1_.join("T");
      }
      
      public function dailyRefresh() : void
      {
         var _loc1_:int = 0;
         var _loc2_:int = int(this._flags.length);
         while(_loc1_ < _loc2_)
         {
            if(this._flags[_loc1_].isDaily)
            {
               this._flags[_loc1_].dailyRefresh();
            }
            _loc1_++;
         }
      }
      
      public function setValue(param1:int, param2:int) : void
      {
         var _loc3_:NumFlag = this.findFlag(param1);
         if(!_loc3_)
         {
            return;
         }
         _loc3_.setValue(param2);
      }
      
      public function changeValue(param1:int, param2:int) : void
      {
         var _loc3_:NumFlag = this.findFlag(param1);
         if(!_loc3_)
         {
            return;
         }
         _loc3_.changeValue(param2);
      }
      
      public function isComplete(param1:int) : Boolean
      {
         var _loc2_:NumFlag = this.findFlag(param1);
         if(!_loc2_)
         {
            return false;
         }
         return _loc2_.isComplete();
      }
      
      public function findFlag(param1:int) : NumFlag
      {
         var _loc4_:NumFlag = null;
         var _loc3_:int = int(this._flags.length);
         for each(_loc4_ in this._flags)
         {
            if(_loc4_.flagID.v == param1)
            {
               return _loc4_;
            }
         }
         return null;
      }
      
      public function findFlagCur(param1:int) : int
      {
         var _loc2_:NumFlag = this.findFlag(param1);
         if(_loc2_)
         {
            return _loc2_.cur;
         }
         return 0;
      }
      
      public function countTongGuanWD() : int
      {
         var _loc1_:int = 0;
         var _loc2_:NumFlag = null;
         for each(_loc2_ in this._flags)
         {
            if(!(_loc2_.flagID.v < 1001 || _loc2_.flagID.v > 1018))
            {
               if(_loc2_.isComplete())
               {
                  _loc1_++;
               }
            }
         }
         return MathUtil.checkWD(_loc1_,this.wd);
      }
   }
}

