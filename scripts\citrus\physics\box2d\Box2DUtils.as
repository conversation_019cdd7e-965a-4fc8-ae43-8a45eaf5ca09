package citrus.physics.box2d
{
   import Box2D.Common.Math.b2Vec2;
   import Box2D.Dynamics.Contacts.b2Contact;
   
   public class Box2DUtils
   {
      public function Box2DUtils()
      {
         super();
      }
      
      public static function CollisionGetOther(param1:IBox2DPhysicsObject, param2:b2Contact) : IBox2DPhysicsObject
      {
         return param1 == param2.GetFixtureA().GetBody().GetUserData() ? param2.GetFixtureB().GetBody().GetUserData() : param2.GetFixtureA().GetBody().GetUserData();
      }
      
      public static function CollisionGetSelf(param1:IBox2DPhysicsObject, param2:b2Contact) : IBox2DPhysicsObject
      {
         return param1 == param2.GetFixtureA().GetBody().GetUserData() ? param2.GetFixtureA().GetBody().GetUserData() : param2.GetFixtureB().GetBody().GetUserData();
      }
      
      public static function CollisionGetObjectByType(param1:Class, param2:b2Contact) : IBox2DPhysicsObject
      {
         return param2.GetFixtureA().GetBody().GetUserData() is param1 ? param2.GetFixtureA().GetBody().GetUserData() : param2.GetFixtureB().GetBody().GetUserData();
      }
      
      public static function Rotateb2Vec2(param1:b2Vec2, param2:Number) : b2Vec2
      {
         var _loc3_:Number = Math.cos(param2);
         var _loc4_:Number = Math.sin(param2);
         return new b2Vec2(param1.x * _loc3_ - param1.y * _loc4_,param1.x * _loc4_ + param1.y * _loc3_);
      }
      
      public static function Multiply2(param1:b2Vec2, param2:b2Vec2) : b2Vec2
      {
         param1.x *= param2.x;
         param1.y *= param2.y;
         return param1;
      }
   }
}

