package file
{
   import citrus.sounds.CitrusSoundGroup;
   import mogames.Layers;
   
   public class SoundConfig
   {
      private static var _instance:SoundConfig;
      
      public function SoundConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : SoundConfig
      {
         if(!_instance)
         {
            _instance = new SoundConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         Layers.ceLayer.sound.addSound("BGM_TITLE",{
            "sound":"sound/bgm/BGM_TITLE320.mp3",
            "loops":-1,
            "volume":0.5,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_COMIC",{
            "sound":"sound/bgm/BGM_COMIC.mp3",
            "loops":-1,
            "volume":0.5,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_FALL",{
            "sound":"sound/bgm/BGM_FALL.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_BIRD",{
            "sound":"sound/bgm/BGM_BIRD.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_TANGSENG",{
            "sound":"sound/bgm/BGM_TANGSENG.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_BATTLE0",{
            "sound":"sound/bgm/BGM_BATTLE0.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_BATTLE1",{
            "sound":"sound/bgm/BGM_BATTLE1.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_BATTLE2",{
            "sound":"sound/bgm/BGM_BATTLE2.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_TITLE900",{
            "sound":"sound/bgm/BGM_TITLE900.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_WORLD",{
            "sound":"sound/bgm/BGM_WORLD.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_MAP_ONE",{
            "sound":"sound/bgm/BGM_MAP_ONE.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_MAP_TWO",{
            "sound":"sound/bgm/BGM_MAP_TWO.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_MAP_THREE",{
            "sound":"sound/bgm/BGM_MAP_THREE.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_MAP_FOUR",{
            "sound":"sound/bgm/BGM_MAP_FOUR.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_MAP_FIVE",{
            "sound":"sound/bgm/BGM_MAP_FIVE.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_DXBD",{
            "sound":"sound/bgm/BGM_DXBD.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_SUNSHINE",{
            "sound":"sound/bgm/BGM_SUNSHINE.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_HUAGUOSHAN",{
            "sound":"sound/bgm/BGM_HUAGUOSHAN1.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_WUZHUANGGUAN",{
            "sound":"sound/bgm/BGM_HUAGUOSHAN.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_GAOLAOZHUANG",{
            "sound":"sound/bgm/BGM_GAOLAOZHUANG.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_JINGJILING",{
            "sound":"sound/bgm/BGM_JINGJILING.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_DANGER0",{
            "sound":"sound/bgm/BGM_DANGER0.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_DANGER1",{
            "sound":"sound/bgm/BGM_DANGER1.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_DANGER2",{
            "sound":"sound/bgm/BGM_DANGER2.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_DANGER3",{
            "sound":"sound/bgm/BGM_DANGER3.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_DANGER4",{
            "sound":"sound/bgm/BGM_DANGER4.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_PK",{
            "sound":"sound/bgm/BGM_PK.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_NIAN",{
            "sound":"sound/bgm/BGM_NIAN.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("BGM_LONG_GONG",{
            "sound":"sound/bgm/BGM_LONG_GONG.mp3",
            "loops":-1,
            "volume":1,
            "group":CitrusSoundGroup.BGM
         });
         Layers.ceLayer.sound.addSound("CLICK",{
            "sound":"sound/interface/click.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("TAB_CLICK",{
            "sound":"sound/interface/tabclick.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ERROR",{
            "sound":"sound/interface/error.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BREAK",{
            "sound":"sound/interface/break.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("MISSION_WIN",{
            "sound":"sound/interface/win.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("MISSION_LOSE",{
            "sound":"sound/interface/lose.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("GET_OFF",{
            "sound":"sound/interface/getoff.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("PUT_ON",{
            "sound":"sound/interface/puton.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SELL",{
            "sound":"sound/interface/sell.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SORT",{
            "sound":"sound/interface/sort.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SUCCESS",{
            "sound":"sound/interface/forge_success.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SUCCESS1",{
            "sound":"sound/interface/success.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("FAIL",{
            "sound":"sound/interface/forge_fail.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("NEW_FUNCTION",{
            "sound":"sound/interface/new_function.mp3",
            "volume":2,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("GET_REWARD",{
            "sound":"sound/interface/get_reward.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHAOMU_FAIL",{
            "sound":"sound/interface/zhaomu_fail.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("FBHC_SUCCESS",{
            "sound":"sound/interface/hc_success.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SIGN",{
            "sound":"sound/interface/sign.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("NEAR100",{
            "sound":"sound/npc/near100.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CLOSE100",{
            "sound":"sound/npc/close100.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("NEAR101",{
            "sound":"sound/npc/near101.mp3",
            "volume":2,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CLOSE101",{
            "sound":"sound/npc/close101.mp3",
            "volume":1,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("NEAR102",{
            "sound":"sound/npc/near102.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CLOSE102",{
            "sound":"sound/npc/close102.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("NEAR103",{
            "sound":"sound/npc/near103.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CLOSE103",{
            "sound":"sound/npc/close103.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("NEAR104",{
            "sound":"sound/npc/near104.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CLOSE104",{
            "sound":"sound/npc/close104.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("NEAR105",{
            "sound":"sound/npc/near105.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CLOSE105",{
            "sound":"sound/npc/close105.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("NEAR107",{
            "sound":"sound/npc/near107.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DING",{
            "sound":"sound/effect/ding.mp3",
            "volume":2,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BOOM0",{
            "sound":"sound/effect/boom0.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BOOM1",{
            "sound":"sound/effect/boom1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("LIGHT",{
            "sound":"sound/effect/light.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("STONE",{
            "sound":"sound/effect/stone.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("FALL",{
            "sound":"sound/effect/fall.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("EFFECT_WIN",{
            "sound":"sound/effect/effect_win.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DANGER",{
            "sound":"sound/effect/danger.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SWITCH",{
            "sound":"sound/effect/switch.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("THROW",{
            "sound":"sound/effect/throw.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("FIRE_EXPLORE",{
            "sound":"sound/effect/fire_explore.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("WATER_EXPLORE",{
            "sound":"sound/effect/water_explore.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CURE",{
            "sound":"sound/effect/cure.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("LEVEL_UP",{
            "sound":"sound/effect/level_up.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ADD_HP",{
            "sound":"sound/effect/add_hp.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ADD_MP",{
            "sound":"sound/effect/add_mp.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ADD_GOOD",{
            "sound":"sound/effect/add_good.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ADD_GOLD",{
            "sound":"sound/effect/add_gold.mp3",
            "volume":0.2,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("OPEN_LOCK",{
            "sound":"sound/effect/open_lock.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("KEY_DROP",{
            "sound":"sound/effect/key_drop.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XUAN_FENG",{
            "sound":"sound/effect/xuanfeng.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("REDUCE_HP",{
            "sound":"sound/effect/reduce_hp.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("TRANS",{
            "sound":"sound/effect/trans.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("TRAP_TUCI",{
            "sound":"sound/effect/trap_tuci.mp3",
            "volume":0.2,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BAO_ZHU",{
            "sound":"sound/effect/bao_zhu.mp3",
            "volume":2,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SKILL_FU_YAO_SUO",{
            "sound":"sound/fabao/SKILL_FU_YAO_SUO.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SKILL_ZHAO_YAO_JING",{
            "sound":"sound/fabao/SKILL_ZHAO_YAO_JING.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SKILL_YU_JIN_PING",{
            "sound":"sound/fabao/SKILL_YU_JIN_PING.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("JUMP0",{
            "sound":"sound/role/jump0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DOUBLEJUMP0",{
            "sound":"sound/role/doublejump0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("RUN",{
            "sound":"sound/role/run.mp3",
            "loops":-1,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("GUN",{
            "sound":"sound/role/gun.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("GUNRUN",{
            "sound":"sound/role/gun_run.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("GUNAIR",{
            "sound":"sound/role/gun_air.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("GUNCRIT",{
            "sound":"sound/role/gun_crit.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SWK_ATK_HURT",{
            "sound":"sound/role/swk_atk_hurt.mp3",
            "volume":1,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SWK_RUN_HURT",{
            "sound":"sound/role/swk_run_hurt.mp3",
            "volume":1,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("GUN_CRIT_HIT",{
            "sound":"sound/role/GUN_CRIT_HIT.mp3",
            "volume":1,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("GUNHURT",{
            "sound":"sound/role/gun_hurt.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SWK_SKILL_TWO",{
            "sound":"sound/role/swk_skill_two.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_ATK_ONE",{
            "sound":"sound/role/xbl_atk_one.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_ATK_TWO",{
            "sound":"sound/role/xbl_atk_two.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_ATK_THREE",{
            "sound":"sound/role/xbl_atk_three.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SWORD_CRIT_HIT",{
            "sound":"sound/role/SWORD_CRIT_HIT.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_ATK_RUN",{
            "sound":"sound/role/xbl_atk_run.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_ATK_AIR",{
            "sound":"sound/role/xbl_atk_air.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_ATK_CRIT",{
            "sound":"sound/role/xbl_atk_crit.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_ATK_HURT",{
            "sound":"sound/role/xbl_atk_hurt.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_SKILL_ONE",{
            "sound":"sound/role/xbl_skill_one.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_SKILL_TWO",{
            "sound":"sound/role/xbl_skill_two.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_SKILL_THREE",{
            "sound":"sound/role/xbl_skill_three.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_SKILL_FOUR",{
            "sound":"sound/role/xbl_skill_four.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XBL_SKILL_FIVE",{
            "sound":"sound/role/xbl_skill_five.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_ATK_ONE",{
            "sound":"sound/role/zbj_atk_one.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_ATK_TWO",{
            "sound":"sound/role/zbj_atk_two.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_ATK_RUN",{
            "sound":"sound/role/zbj_atk_run.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_ATK_AIR",{
            "sound":"sound/role/zbj_atk_air.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_ATK_CRIT",{
            "sound":"sound/role/zbj_atk_crit.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_ATK_HURT",{
            "sound":"sound/role/zbj_atk_hurt.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_CRIT_HURT",{
            "sound":"sound/role/zbj_crit_hurt.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_SKILL_ONE",{
            "sound":"sound/role/zbj_skill_one.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_SKILL_TWO",{
            "sound":"sound/role/zbj_skill_two.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_SKILL_THREE",{
            "sound":"sound/role/zbj_skill_three.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_SKILL_FOUR",{
            "sound":"sound/role/zbj_skill_four.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZBJ_SKILL_FIVE",{
            "sound":"sound/role/zbj_skill_five.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_ATK_ONE",{
            "sound":"sound/role/SS_ATK_ONE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_ATK_TWO",{
            "sound":"sound/role/SS_ATK_TWO.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_ATK_THREE",{
            "sound":"sound/role/SS_ATK_THREE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_ATK_RUN",{
            "sound":"sound/role/SS_ATK_RUN.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_ATK_AIR",{
            "sound":"sound/role/SS_ATK_AIR.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_ATK_CRIT",{
            "sound":"sound/role/SS_ATK_CRIT.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_ATK_HURT",{
            "sound":"sound/role/SS_ATK_HURT.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_CRIT_HURT",{
            "sound":"sound/role/SS_ATK_HURT.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_SKILL_ONE",{
            "sound":"sound/role/SS_SKILL_ONE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_SKILL_TWO",{
            "sound":"sound/role/SS_SKILL_TWO.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_SKILL_THREE",{
            "sound":"sound/role/SS_SKILL_THREE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_SKILL_FOUR",{
            "sound":"sound/role/SS_SKILL_FOUR.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SS_SKILL_FIVE",{
            "sound":"sound/role/SS_SKILL_FIVE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_ATK_ONE",{
            "sound":"sound/role/xln_attack0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_ATK_TWO",{
            "sound":"sound/role/xln_attack1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_ATK_THREE",{
            "sound":"sound/role/xln_attack2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_ATK_RUN",{
            "sound":"sound/role/xln_runattack.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_ATK_CRIT",{
            "sound":"sound/role/xln_critattack.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_ATK_AIR",{
            "sound":"sound/role/xln_airattack.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_SKILL_ONE",{
            "sound":"sound/role/xln_skillone.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_SKILL_TWO",{
            "sound":"sound/role/xln_skilltwo.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_SKILL_TWO_RELEASE",{
            "sound":"sound/role/xln_skilltwo_release.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_SKILL_THREE",{
            "sound":"sound/role/xln_skillthree.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_SKILL_FOUR",{
            "sound":"sound/role/xln_skillfour.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_SKILL_FIVE",{
            "sound":"sound/role/xln_skillfive.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XLN_SKILL_FIVE_RELEASE",{
            "sound":"sound/role/xln_skillfive_release.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ATK_FO_SHOU",{
            "sound":"sound/role/ATK_FO_SHOU.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HIT_HU_GUAI",{
            "sound":"sound/role/HIT_HU_GUAI.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HU_GUAI_DEAD",{
            "sound":"sound/role/HU_GUAI_DEAD.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HIT_YIN_JIANG_JUN",{
            "sound":"sound/role/HIT_YIN_JIANG_JUN.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XIONG_GUAI_SKILL_COLLECT",{
            "sound":"sound/role/XIONG_GUAI_SKILL_COLLECT.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XIONG_GUAI_HIT",{
            "sound":"sound/role/XIONG_GUAI_HIT.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HEI_XIONG_JING_ATTACK",{
            "sound":"sound/role/HEI_XIONG_JING_ATTACK.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HEI_XIONG_JING_FIRE",{
            "sound":"sound/role/HEI_XIONG_JING_FIRE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HEI_XIONG_JING_CHUIQI",{
            "sound":"sound/role/HEI_XIONG_JING_CHUIQI.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HEI_XIONG_JING_SKILL_STUN",{
            "sound":"sound/role/HEI_XIONG_JING_SKILL_STUN.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_FENG_GUAI_SMILE",{
            "sound":"sound/role/HUANG_FENG_GUAI_SMILE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_FENG_GUAI_ATTACK0",{
            "sound":"sound/role/HUANG_FENG_GUAI_ATK0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_FENG_GUAI_ATTACK1",{
            "sound":"sound/role/HUANG_FENG_GUAI_ATK1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_FENG_GUAI_SKILL0",{
            "sound":"sound/role/HUANG_FENG_GUAI_SKILL0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_FENG_GUAI_SKILL1",{
            "sound":"sound/role/HUANG_FENG_GUAI_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_FENG_GUAI_HIT",{
            "sound":"sound/role/HUANG_FENG_GUAI_HIT.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HU_FA_SHI_SKILL",{
            "sound":"sound/role/HU_FA_SHI_SKILL.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BONE_DEAD",{
            "sound":"sound/role/BONE_DEAD.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BONE_FOOT_SKILL",{
            "sound":"sound/role/BONE_FOOT_SKILL.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BONE_FOOT_HIT",{
            "sound":"sound/role/BONE_FOOT_HIT.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BONE_ARROW_SKILL",{
            "sound":"sound/role/BONE_ARROW_SKILL.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BONE_ARROW_ATK",{
            "sound":"sound/role/BONE_ARROW_ATK.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BONE_FASHI_SKILL",{
            "sound":"sound/role/BONE_FASHI_SKILL.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BONE_FASHI_ATK",{
            "sound":"sound/role/BONE_FASHI_ATK.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BONE_BALL_HIT",{
            "sound":"sound/role/BONE_BALL_HIT.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BAI_GU_JING_DEAD",{
            "sound":"sound/role/BAI_GU_JING_DEAD.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("BAI_GU_JING_LAUGH",{
            "sound":"sound/role/BAI_GU_JING_LAUGH.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DUN_PAI_HIT",{
            "sound":"sound/role/DUN_PAI_HIT.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_PAO_GUAI_ATK0",{
            "sound":"sound/role/HUANG_PAO_GUAI_ATK0.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_PAO_GUAI_ATK1",{
            "sound":"sound/role/HUANG_PAO_GUAI_ATK1.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_PAO_GUAI_ATK2",{
            "sound":"sound/role/HUANG_PAO_GUAI_ATK2.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_PAO_GUAI_SKILL1",{
            "sound":"sound/role/HUANG_PAO_GUAI_SKILL1.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_PAO_GUAI_RENG",{
            "sound":"sound/role/HUANG_PAO_GUAI_RENG.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_PAO_GUAI_SWORD",{
            "sound":"sound/role/HUANG_PAO_GUAI_SWORD.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_PAO_GUAI_SKILL3",{
            "sound":"sound/role/HUANG_PAO_GUAI_SKILL3.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUA_SHEN_LAOTOU_HURT",{
            "sound":"sound/role/HUA_SHEN_LAOTOU_HURT.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUA_SHEN_LAOTAI_HURT",{
            "sound":"sound/role/HUA_SHEN_LAOTAI_HURT.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUA_SHEN_NVHAI_HURT",{
            "sound":"sound/role/HUA_SHEN_NVHAI_HURT.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("JIU_WEI_SKILL_ONE",{
            "sound":"sound/role/JIU_WEI_SKILL_ONE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("JIU_WEI_SKILL_TWO",{
            "sound":"sound/role/JIU_WEI_SKILL_TWO.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("JIU_WEI_SKILL_THREE",{
            "sound":"sound/role/JIU_WEI_SKILL_THREE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SHI_LI_WANG_ATK0",{
            "sound":"sound/role/SHI_LI_WANG_ATK0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SHI_LI_WANG_ATK1",{
            "sound":"sound/role/SHI_LI_WANG_ATK1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SHI_LI_WANG_SKILL_ONE",{
            "sound":"sound/role/SHI_LI_WANG_SKILL_ONE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SHI_LI_WANG_SKILL_TWO",{
            "sound":"sound/role/SHI_LI_WANG_SKILL_TWO.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("SHI_LI_WANG_SKILL_THREE",{
            "sound":"sound/role/SHI_LI_WANG_SKILL_THREE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_SLW_ATK0",{
            "sound":"sound/role/ZHEN_SLW_ATK0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_SLW_SKILL_ONE",{
            "sound":"sound/role/ZHEN_SLW_SKILL_ONE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_SLW_SKILL_TWO",{
            "sound":"sound/role/ZHEN_SLW_SKILL_TWO.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_SLW_SKILL_THREE",{
            "sound":"sound/role/ZHEN_SLW_SKILL_THREE.mp3",
            "volume":0.5,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CHAN_CHU_ATK0",{
            "sound":"sound/role/CHAN_CHU_ATK0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CHAN_CHU_ATK1",{
            "sound":"sound/role/CHAN_CHU_ATK1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CHAN_CHU_SKILL_ONE",{
            "sound":"sound/role/CHAN_CHU_SKILL_ONE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CHAN_CHU_SKILL_TWO1",{
            "sound":"sound/role/CHAN_CHU_SKILL_TWO1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("CHAN_CHU_SKILL_TWO2",{
            "sound":"sound/role/CHAN_CHU_SKILL_TWO2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DENG_YU_ATK0",{
            "sound":"sound/role/DENG_YU_ATK0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DENG_YU_SKILL_ONE0",{
            "sound":"sound/role/DENG_YU_SKILL_ONE0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DENG_YU_SKILL_ONE1",{
            "sound":"sound/role/DENG_YU_SKILL_ONE1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DENG_YU_SKILL_ONE2",{
            "sound":"sound/role/DENG_YU_SKILL_ONE2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DENG_YU_SKILL_ONE3",{
            "sound":"sound/role/DENG_YU_SKILL_ONE3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DENG_YU_SKILL_TWO0",{
            "sound":"sound/role/DENG_YU_SKILL_TWO0.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DENG_YU_SKILL_TWO1",{
            "sound":"sound/role/DENG_YU_SKILL_TWO1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DENG_YU_SKILL_THREE",{
            "sound":"sound/role/DENG_YU_SKILL_THREE.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HONG_HAI_ATK1",{
            "sound":"sound/role/HONG_HAI_ATK1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HONG_HAI_ATK2",{
            "sound":"sound/role/HONG_HAI_ATK2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HONG_HAI_SKILL1",{
            "sound":"sound/role/HONG_HAI_SKILL1.mp3",
            "volume":2,
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HONG_HAI_SKILL2",{
            "sound":"sound/role/HONG_HAI_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HONG_HAI_SKILL3",{
            "sound":"sound/role/HONG_HAI_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HONG_HAI_SKILL4",{
            "sound":"sound/role/HONG_HAI_SKILL4.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HU_DA_XIAN_ATK",{
            "sound":"sound/role/HU_DA_XIAN_ATK.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HU_DA_XIAN_SKILL1",{
            "sound":"sound/role/HU_DA_XIAN_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HU_DA_XIAN_SKILL2",{
            "sound":"sound/role/HU_DA_XIAN_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("LU_DA_XIAN_ATK",{
            "sound":"sound/role/LU_DA_XIAN_ATK.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("LU_DA_XIAN_SKILL1",{
            "sound":"sound/role/LU_DA_XIAN_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("LU_DA_XIAN_SKILL2",{
            "sound":"sound/role/LU_DA_XIAN_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("YANG_DA_XIAN_ATK",{
            "sound":"sound/role/YANG_DA_XIAN_ATK.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("YANG_DA_XIAN_SKILL1",{
            "sound":"sound/role/YANG_DA_XIAN_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("YANG_DA_XIAN_SKILL2",{
            "sound":"sound/role/YANG_DA_XIAN_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HE_TI_SKILL1",{
            "sound":"sound/role/HE_TI_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HE_TI_SKILL2",{
            "sound":"sound/role/HE_TI_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HE_TI_SKILL3",{
            "sound":"sound/role/HE_TI_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HE_TI_SKILL4",{
            "sound":"sound/role/HE_TI_SKILL4.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("LING_GAN_SKILL1",{
            "sound":"sound/role/LING_GAN_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("LING_GAN_SKILL2",{
            "sound":"sound/role/LING_GAN_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("LING_GAN_SKILL3",{
            "sound":"sound/role/LING_GAN_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("LING_GAN_SKILL4",{
            "sound":"sound/role/LING_GAN_SKILL4.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DU_JIAO_SKILL1",{
            "sound":"sound/role/DU_JIAO_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DU_JIAO_SKILL2",{
            "sound":"sound/role/DU_JIAO_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("DU_JIAO_SKILL3",{
            "sound":"sound/role/DU_JIAO_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_DU_JIAO_SKILL1",{
            "sound":"sound/role/ZHEN_DU_JIAO_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_DU_JIAO_SKILL2",{
            "sound":"sound/role/ZHEN_DU_JIAO_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_DU_JIAO_SKILL3",{
            "sound":"sound/role/ZHEN_DU_JIAO_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_DU_JIAO_SKILL4",{
            "sound":"sound/role/ZHEN_DU_JIAO_SKILL4.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XIE_ZI_SKILL1",{
            "sound":"sound/role/XIE_ZI_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XIE_ZI_SKILL2",{
            "sound":"sound/role/XIE_ZI_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XIE_ZI_SKILL3",{
            "sound":"sound/role/XIE_ZI_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XIE_ZI_SKILL3_END",{
            "sound":"sound/role/XIE_ZI_SKILL3_END.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_XIE_ZI_SKILL1",{
            "sound":"sound/role/ZHEN_XIE_ZI_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_XIE_ZI_SKILL2",{
            "sound":"sound/role/ZHEN_XIE_ZI_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_XIE_ZI_SKILL3",{
            "sound":"sound/role/ZHEN_XIE_ZI_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_XIE_ZI_SKILL4",{
            "sound":"sound/role/ZHEN_XIE_ZI_SKILL4.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("JIU_TOU_ATK",{
            "sound":"sound/role/JIU_TOU_ATK.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("JIU_TOU_SKILL1",{
            "sound":"sound/role/JIU_TOU_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("JIU_TOU_SKILL2",{
            "sound":"sound/role/JIU_TOU_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("JIU_TOU_SKILL3",{
            "sound":"sound/role/JIU_TOU_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_JIU_TOU_SKILL1",{
            "sound":"sound/role/ZHEN_JIU_TOU_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_JIU_TOU_SKILL2",{
            "sound":"sound/role/ZHEN_JIU_TOU_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_JIU_TOU_SKILL21",{
            "sound":"sound/role/ZHEN_JIU_TOU_SKILL21.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_JIU_TOU_SKILL3",{
            "sound":"sound/role/ZHEN_JIU_TOU_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("ZHEN_JIU_TOU_SKILL4",{
            "sound":"sound/role/ZHEN_JIU_TOU_SKILL4.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XIE_WU_SENG_ATK",{
            "sound":"sound/role/XIE_WU_SENG_ATK.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XIE_WU_SENG_SKILL1",{
            "sound":"sound/role/XIE_WU_SENG_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("XIE_FA_SENG_SKILL1",{
            "sound":"sound/role/XIE_FA_SENG_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_MEI_ATK1",{
            "sound":"sound/role/HUANG_MEI_ATK1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_MEI_ATK2",{
            "sound":"sound/role/HUANG_MEI_ATK2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_MEI_SKILL1",{
            "sound":"sound/role/HUANG_MEI_SKILL1.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_MEI_SKILL2",{
            "sound":"sound/role/HUANG_MEI_SKILL2.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_MEI_SKILL3",{
            "sound":"sound/role/HUANG_MEI_SKILL3.mp3",
            "group":CitrusSoundGroup.SFX
         });
         Layers.ceLayer.sound.addSound("HUANG_MEI_SKILL4",{
            "sound":"sound/role/HUANG_MEI_SKILL4.mp3",
            "group":CitrusSoundGroup.SFX
         });
         _instance = null;
      }
   }
}

