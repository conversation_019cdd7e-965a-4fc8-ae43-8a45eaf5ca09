package mogames.gameMission.co
{
   import citrus.core.State;
   import citrus.physics.box2d.Box2D;
   import citrus.utils.objectmakers.ObjectMaker2D;
   import citrus.view.spriteview.SpriteView;
   import flash.display.MovieClip;
   import mogames.Layers;
   import utils.MethodUtil;
   
   public class Box2DScene extends State
   {
      protected var _levelMC:MovieClip;
      
      public function Box2DScene(param1:MovieClip)
      {
         this._levelMC = param1;
         super();
      }
      
      override public function initialize() : void
      {
         super.initialize();
         Layers.ceInterView = (view as SpriteView).viewRoot;
         var _loc1_:Box2D = new Box2D("box2dWorld");
         add(_loc1_);
         this.initHero();
         ObjectMaker2D.FromMovieClip(this._levelMC);
         MethodUtil.setMousable(Layers.findCEInter(4),false);
      }
      
      protected function initHero() : void
      {
      }
      
      override public function update(param1:Number) : void
      {
         super.update(param1);
         if(!Layers.ceInterView)
         {
            return;
         }
         Layers.ceEffectLayer.x = Layers.ceInterView.x;
         Layers.ceEffectLayer.y = Layers.ceInterView.y;
      }
      
      override public function destroy() : void
      {
         MethodUtil.removeAllChildren(Layers.bgLayer);
         Layers.ceInterView = null;
         super.destroy();
      }
   }
}

