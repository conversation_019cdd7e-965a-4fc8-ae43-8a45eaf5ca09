package citrus.physics.box2d
{
   import Box2D.Common.Math.b2Vec2;
   import Box2D.Dynamics.b2World;
   import citrus.physics.APhysicsEngine;
   import citrus.physics.PhysicsCollisionCategories;
   import citrus.view.ISpriteView;
   
   public class Box2D extends APhysicsEngine implements ISpriteView
   {
      public var timeStep:Number = 0.05;
      
      public var velocityIterations:uint = 8;
      
      public var positionIterations:uint = 8;
      
      private var _scale:Number = 30;
      
      private var _world:b2World;
      
      private var _gravity:b2Vec2 = new b2Vec2(0,12);
      
      private var _contactListener:Box2DContactListener;
      
      public function Box2D(param1:String, param2:Object = null)
      {
         if(Boolean(param2) && param2.view == undefined)
         {
            param2.view = Box2DDebugArt;
         }
         else if(param2 == null)
         {
            param2 = {"view":Box2DDebugArt};
         }
         super(param1,param2);
      }
      
      override public function initialize(param1:Object = null) : void
      {
         super.initialize();
         _realDebugView = _view;
         this._world = new b2World(this._gravity,true);
         this._contactListener = new Box2DContactListener();
         this._world.SetContactListener(this._contactListener);
         PhysicsCollisionCategories.Add("GoodGuys");
         PhysicsCollisionCategories.Add("BadGuys");
         PhysicsCollisionCategories.Add("Level");
         PhysicsCollisionCategories.Add("Items");
         PhysicsCollisionCategories.Add("Effect");
      }
      
      override public function destroy() : void
      {
         super.destroy();
      }
      
      public function get world() : b2World
      {
         return this._world;
      }
      
      public function get scale() : Number
      {
         return this._scale;
      }
      
      public function get gravity() : b2Vec2
      {
         return this._gravity;
      }
      
      public function set gravity(param1:b2Vec2) : void
      {
         this._gravity = param1;
         if(this._world)
         {
            this._world.SetGravity(this._gravity);
         }
      }
      
      override public function update(param1:Number) : void
      {
         super.update(param1);
         this.refresh();
      }
      
      public function refresh() : void
      {
         this._world.Step(this.timeStep,this.velocityIterations,this.positionIterations);
         this._world.ClearForces();
      }
   }
}

