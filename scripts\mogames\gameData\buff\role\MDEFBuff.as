package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class MDEFBuff extends BaseBuff
   {
      private var addMDEF:int;
      
      public function MDEFBuff()
      {
         super(1009);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         this.addMDEF = _roleVO.totalMDEF.v * _buffVO.argDic.per * 0.01;
         _roleVO.skillMDEF.v += this.addMDEF;
         _roleVO.updateMDEF();
         if(_role.isReady)
         {
            EffectManager.instance().addHeadWord("魔防提升！",_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override protected function onEnd() : void
      {
         _roleVO.skillMDEF.v -= this.addMDEF;
         _roleVO.updateMDEF();
         destroy();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         if(!_buffVO.argDic.skin)
         {
            return null;
         }
         _skin.setupSequence(AssetManager.findAnimation(_buffVO.argDic.skin),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

