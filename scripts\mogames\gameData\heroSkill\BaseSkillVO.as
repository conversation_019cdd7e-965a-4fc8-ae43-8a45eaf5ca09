package mogames.gameData.heroSkill
{
   import file.SkillConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   import mogames.gameData.heroSkill.constvo.ConstSkillVO;
   import mogames.gameData.role.HeroGameVO;
   
   public class BaseSkillVO
   {
      public static const MAX_LEVEL:Sint = new Sint(20);
      
      public var id:Sint;
      
      public var keyIndex:int;
      
      public var level:Sint;
      
      public var needMP:Sint;
      
      public var constVO:ConstSkillVO;
      
      public var countVO:SkillCountVO;
      
      protected var _owner:HeroGameVO;
      
      protected var _levelVO:SkillLevelVO;
      
      public function BaseSkillVO(param1:int, param2:HeroGameVO, param3:int = 0)
      {
         super();
         this.id = new Sint(param1);
         this.level = new Sint();
         this.needMP = new Sint();
         this.keyIndex = param3;
         this._owner = param2;
         this.initSkill(param1);
      }
      
      protected function initSkill(param1:int) : void
      {
         this.countVO = SkillConfig.instance().findSkillCount(param1);
         this.constVO = SkillConfig.instance().findConstSkill(param1);
         this.setLevel(0);
      }
      
      public function setLevel(param1:int, param2:Boolean = false) : void
      {
         this.level.v = param1;
         this._levelVO = this.constVO.findlvVO(param1);
         this.updateNeedMP();
         if(param2)
         {
            this._owner.onUpdate.dispatch("updateSkill");
         }
      }
      
      protected function updateNeedMP() : void
      {
         this.needMP.v = Math.ceil(this.countVO.baseMP.v + (this.level.v - ConstData.DATA_NUM1.v) * this.countVO.baseMP.v * ConstData.DATA_NUM06.v);
      }
      
      public function levelup() : void
      {
         this._levelVO.useStuff();
         this.setLevel(this.level.v + ConstData.DATA_NUM1.v,true);
      }
      
      public function useMP() : void
      {
         this._owner.changeMP(-this.needMP.v);
      }
      
      public function get needStr() : String
      {
         if(!this._levelVO || this.isMaxLevel)
         {
            return "";
         }
         return this._levelVO.parseNeedStr(this._owner);
      }
      
      public function get canLearn() : Boolean
      {
         return Boolean(this._levelVO) && this._levelVO.checkLearn(this._owner) && !this.isMaxLevel;
      }
      
      public function get isActivate() : Boolean
      {
         return this.level.v > ConstData.DATA_NUM0.v;
      }
      
      public function get isMaxLevel() : Boolean
      {
         return this.level.v >= MAX_LEVEL.v;
      }
      
      public function get isLackMP() : Boolean
      {
         return this.needMP.v > this._owner.curMP.v;
      }
      
      public function parseLoadData(param1:Array) : void
      {
         this.setLevel(int(param1[1]));
         this.keyIndex = int(param1[2]);
      }
      
      public function collectSaveData() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.constVO.id.v;
         _loc1_[1] = this.level.v;
         _loc1_[2] = this.keyIndex;
         return _loc1_.join("A");
      }
   }
}

