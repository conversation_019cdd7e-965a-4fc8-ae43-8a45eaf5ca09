package mogames.gameMission.mission.baihuling
{
   import mogames.gameData.base.Sint;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.mission.BaseMission;
   
   public class BaiHuLingMission extends BaseMission
   {
      public var destroyNum:Sint;
      
      public function BaiHuLingMission()
      {
         super();
         this.destroyNum = new Sint();
      }
      
      public function playerBGM() : void
      {
         EffectManager.instance().playBGM("BGM_DANGER2");
      }
      
      public function addDead() : void
      {
         if(this.destroyNum.v >= 3)
         {
            return;
         }
         this.destroyNum.v += 1;
      }
   }
}

