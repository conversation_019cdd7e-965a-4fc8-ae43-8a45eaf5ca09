package mogames.gameFabao.fabao
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameData.tower.BossNeedVO;
   import mogames.gameData.tower.TowerProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.view.FabaoZhenYaoTaView;
   import mogames.gameMission.BattleMediator;
   import mogames.gameRole.hero.BaseHero;
   import org.osflash.signals.Signal;
   import utils.MathUtil;
   
   public class FabaoZhenYaoTa extends BaseFabao
   {
      private var _hitVO:BaseHitVO;
      
      public var onZhuaBu:Signal;
      
      public function FabaoZhenYaoTa(param1:GameFabaoVO)
      {
         super(param1,52,88);
         this.createFabaoData();
         this.createVO();
         createView(new FabaoZhenYaoTaView());
         this.onZhuaBu = new Signal(int,Number);
         this.onZhuaBu.add(this.handlerZhuaBu);
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(50),
            "hurt":new Sint(750)
         },{
            "cdTime":new Snum(50),
            "hurt":new Sint(975)
         },{
            "cdTime":new Snum(50),
            "hurt":new Sint(1267)
         },{
            "cdTime":new Snum(50),
            "hurt":new Sint(1647)
         },{
            "cdTime":new Snum(50),
            "hurt":new Sint(2141)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      private function createVO() : void
      {
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._hitVO.HURT_X = 0;
         this._hitVO.HURT_Y = 0;
         this._hitVO.HURT_SKIN = "EFFECT_GUN_ATK";
         this._hitVO.HURT_SOUND = "GUNHURT";
         this._hitVO.WU_XING.v = _fabaoVO.constFabao.wuxing.v;
      }
      
      override public function followTarget() : void
      {
         if(_fabaoView.isSkill)
         {
            return;
         }
         super.followTarget();
      }
      
      override public function handlerSkill() : void
      {
         this.x = owner.x + owner.width * owner.dirX * 5;
         super.handlerSkill();
      }
      
      public function dispatchSkillOne() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         this._hitVO.init(_fabaoView.mcATK,_fabaoData.hurt.v,1,false);
         this._hitVO.isMiss = false;
         if(owner is BaseHero)
         {
            BattleMediator.instance().onHeroATK.dispatch(this._hitVO);
         }
         else
         {
            BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
         }
      }
      
      private function handlerZhuaBu(param1:int, param2:Number) : void
      {
         var _loc3_:BossNeedVO = TowerProxy.instance().findTowerBoss(param1);
         if(!_loc3_ || _loc3_.hasGet)
         {
            return;
         }
         if(this.checkOddGet(param2))
         {
            _loc3_.setGet();
            EffectManager.instance().addPicPopWord("PIC_ZHUA_BU_SUCCESS",x,y - height,1);
            EffectManager.instance().playAudio("SUCCESS1");
         }
         else
         {
            EffectManager.instance().addPicPopWord("PIC_ZHUA_BU_FAIL",x,y - height,1);
         }
      }
      
      private function checkOddGet(param1:Number) : Boolean
      {
         if(MasterProxy.instance().gameVIP.v >= 3)
         {
            return true;
         }
         if(param1 <= 0.1)
         {
            return MathUtil.checkOdds(1000);
         }
         if(param1 <= 0.15)
         {
            return MathUtil.checkOdds(850);
         }
         if(param1 <= 0.2)
         {
            return MathUtil.checkOdds(750);
         }
         if(param1 <= 0.25)
         {
            return MathUtil.checkOdds(500);
         }
         if(param1 <= 0.3)
         {
            return MathUtil.checkOdds(350);
         }
         if(param1 <= 0.4)
         {
            return MathUtil.checkOdds(200);
         }
         return false;
      }
      
      override public function destroy() : void
      {
         this.onZhuaBu.removeAll();
         this.onZhuaBu = null;
         super.destroy();
      }
   }
}

