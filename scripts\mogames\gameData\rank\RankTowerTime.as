package mogames.gameData.rank
{
   import mogames.gameData.rank.vo.RankTimeVO;
   import mogames.gameData.tower.TowerFloorVO;
   import mogames.gameData.tower.TowerProxy;
   
   public class RankTowerTime extends RankHandler
   {
      public var index:int;
      
      private var towerIDs:Array = [[0,0],[0,1],[0,2]];
      
      public function RankTowerTime()
      {
         super([1765,1766,1767]);
         _msg = "初始化通关时间排行榜";
      }
      
      override public function parseRank(param1:Array) : Array
      {
         var _loc5_:RankTimeVO = null;
         if(!param1 || param1.length <= 0)
         {
            return [];
         }
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         var _loc4_:Array = [];
         while(_loc2_ < _loc3_)
         {
            _loc5_ = new RankTimeVO();
            _loc5_.parseData(param1[_loc2_]);
            _loc4_[_loc2_] = _loc5_;
            _loc2_++;
         }
         _loc4_.sortOn("sortIndex",Array.NUMERIC);
         return _loc4_;
      }
      
      override public function submitRank(param1:Function) : void
      {
         if(this.isCheat)
         {
            if(param1 != null)
            {
               param1();
            }
            return;
         }
         var _loc2_:TowerFloorVO = this.findFloorVO();
         if(_loc2_.time.v == 0)
         {
            param1();
         }
         else
         {
            super.submitRank(param1);
         }
      }
      
      override protected function get rankData() : Array
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.newRankData();
         return _loc1_;
      }
      
      protected function newRankData() : Object
      {
         var _loc1_:TowerFloorVO = this.findFloorVO();
         var _loc2_:Object = new Object();
         _loc2_.rId = this.curRankID;
         _loc2_.score = 10000 - _loc1_.time.v;
         _loc2_.extra = [1000 - _loc1_.hit.v];
         return _loc2_;
      }
      
      public function findFloorVO() : TowerFloorVO
      {
         return TowerProxy.instance().findFloorVO(this.towerIDs[this.index][0],this.towerIDs[this.index][1]);
      }
      
      public function get curRankID() : int
      {
         return rankIDs[this.index];
      }
      
      private function get isCheat() : Boolean
      {
         return false;
      }
   }
}

