package mogames.gameData.drop.vo
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.task.TaskProxy;
   import utils.MathUtil;
   
   public class BaseDropVO
   {
      public var id:Sint;
      
      public var num:Sint;
      
      protected var _per:Sint;
      
      public function BaseDropVO(param1:int, param2:int, param3:int)
      {
         super();
         this.id = new Sint(param1);
         this.num = new Sint(param2);
         this._per = new Sint(param3);
      }
      
      public function get isDrop() : Boolean
      {
         return MathUtil.checkOdds(this._per.v);
      }
      
      public function get isTaskDrop() : Boolean
      {
         if(TaskProxy.instance().checkNeedGood(this.id.v))
         {
            return MathUtil.checkOdds(this._per.v);
         }
         return false;
      }
      
      public function get per() : int
      {
         return this._per.v;
      }
      
      public function newGood() : GameGoodVO
      {
         var _loc1_:GameGoodVO = GoodConfig.instance().newGood(this.id.v);
         _loc1_.amount.v = this.num.v;
         return _loc1_;
      }
   }
}

