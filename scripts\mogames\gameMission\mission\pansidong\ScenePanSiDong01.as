package mogames.gameMission.mission.pansidong
{
   import citrus.objects.CitrusSprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameRole.enemy.BOSSZhiZhuJing;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   
   public class ScenePanSiDong01 extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _eggs:Array;
      
      private var _spiders:Array = [[4064,18],[4332,18],[4600,18]];
      
      private var _trapTimer:CitrusTimer;
      
      private var _activeSpider:CitrusSprite;
      
      public function ScenePanSiDong01(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER2");
         setWinPosition(new Rectangle(4130,134,512,118),new Point(4390,416));
         this._trapTimer = new CitrusTimer(true);
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2047,1);
            TaskProxy.instance().addTask(11030);
         };
         super.handlerInit();
         this.layoutEnemies();
         this.layoutTraps();
         if(!FlagProxy.instance().isComplete(2047))
         {
            showDialog("STORY0075",func);
         }
      }
      
      private function layoutTraps() : void
      {
         var _loc1_:int = 0;
         var _loc2_:TrapSpiderEgg = null;
         this._eggs = [];
         _loc1_ = 0;
         while(_loc1_ < 8)
         {
            _loc2_ = new TrapSpiderEgg(10,this.addDuWu);
            Layers.addCEChild(_loc2_);
            _loc2_.x = 251 + _loc1_ * 477;
            _loc2_.y = 425;
            _loc2_.maxCount = 15;
            this._eggs[_loc1_] = _loc2_;
            _loc1_++;
         }
      }
      
      private function addDuWu(param1:int, param2:int) : void
      {
         var _loc3_:TrapLargeDuWu = null;
         _loc3_ = new TrapLargeDuWu();
         _loc3_.x = param1;
         _loc3_.y = param2 + _loc3_.height * 0.5;
         _loc3_.initTimer(5);
         _loc3_.createHitEffect("EFFECT_SHE_BOOM_CLIP","GUNHURT",2,2);
         _loc3_.createHitHurt(900,0,false,1);
         Layers.addCEChild(_loc3_);
      }
      
      private function layoutEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[202,216,150,100],[626,216,150,100]],22011,this.handlerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1159,216,150,100],[1583,216,150,100]],22012,this.handlerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[2113,216,150,100],[2537,216,150,100]],22013,this.handlerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[3103,216,150,100],[3528,216,150,100]],22014,this.triggerEnd3);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,428),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1440,480),1,new Rectangle(960,0,960,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2400,480),1,new Rectangle(1920,0,960,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(3360,480),1,new Rectangle(2880,0,960,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(4238,480),1,new Rectangle(3840,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this.onTrigger0;
         this._pTrigger1.okFunc = this.onTrigger1;
         this._pTrigger2.okFunc = this.onTrigger2;
         this._pTrigger3.okFunc = this.onTrigger3;
         this._pTrigger4.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function onTrigger0() : void
      {
         this._pTrigger1.start();
         this._eggs[0].startTimer();
         this._eggs[1].startTimer();
      }
      
      private function onTrigger1() : void
      {
         this._pTrigger2.start();
         this._eggs[2].startTimer();
         this._eggs[3].startTimer();
      }
      
      private function onTrigger2() : void
      {
         this._pTrigger3.start();
         this._eggs[4].startTimer();
         this._eggs[5].startTimer();
      }
      
      private function onTrigger3() : void
      {
         this._pTrigger4.start();
         this._eggs[6].startTimer();
         this._eggs[7].startTimer();
      }
      
      private function handlerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd3() : void
      {
         this.handlerEnd();
         this.addBOSS();
      }
      
      private function startBattle() : void
      {
         startBossBattle("BGM_BATTLE2");
         this._trapTimer.setInterval(5,0,this.addSpider);
         this._trapTimer.startNone();
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSZhiZhuJing();
         _boss.x = 4640;
         _boss.y = 280;
         _boss.initData(30371,30371,118);
         _boss.target = _mission.curTarget;
         add(_boss);
      }
      
      private function addSpider() : void
      {
         if(this._activeSpider != null)
         {
            return;
         }
         var _loc1_:int = Math.random() * 3;
         if(_loc1_ == 0)
         {
            this.addWhiteSpider(_loc1_);
         }
         else if(_loc1_ == 1)
         {
            this.addRedSpider(_loc1_);
         }
         else if(_loc1_ == 2)
         {
            this.addPurpleSpider(_loc1_);
         }
      }
      
      private function addWhiteSpider(param1:int) : void
      {
         var _loc2_:TrapWhiteSpider = new TrapWhiteSpider(this.cleanSpider);
         Layers.addCEChild(_loc2_);
         _loc2_.x = this._spiders[param1][0];
         _loc2_.y = this._spiders[param1][1];
         _loc2_.createHitHurt(700,1);
         this._activeSpider = _loc2_;
      }
      
      private function addRedSpider(param1:int) : void
      {
         var _loc2_:TrapRedSpider = new TrapRedSpider(this.cleanSpider);
         Layers.addCEChild(_loc2_);
         _loc2_.x = this._spiders[param1][0];
         _loc2_.y = this._spiders[param1][1];
         _loc2_.createHitHurt(800);
         this._activeSpider = _loc2_;
      }
      
      private function addPurpleSpider(param1:int) : void
      {
         var _loc2_:TrapPurpleSpider = new TrapPurpleSpider(this.cleanSpider);
         Layers.addCEChild(_loc2_);
         _loc2_.x = this._spiders[param1][0];
         _loc2_.y = this._spiders[param1][1];
         this._activeSpider = _loc2_;
      }
      
      private function cleanSpider() : void
      {
         this._activeSpider = null;
      }
      
      override protected function handlerWin() : void
      {
         this._trapTimer.pause();
         TaskProxy.instance().setComplete(11030);
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._trapTimer.destroy();
         this._trapTimer = null;
         this._eggs = null;
         super.destroy();
      }
   }
}

