package mogames.gameMission.mission.bujinchansi
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.huanghuaguan.TrapGongNu;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BOSSYuTu;
   import mogames.gameRole.enemy.BOSSZhenYuTu;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameUI.dialog.StoryDialogModule;
   
   public class SceneBuJinChanSi extends BossScene
   {
      private var _trapArgs:Array = [{
         "posX":2945,
         "posY":30,
         "inverted":false,
         "num":3,
         "hurt":500,
         "interval":6
      },{
         "posX":3755,
         "posY":30,
         "inverted":true,
         "num":3,
         "hurt":500,
         "interval":6
      }];
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _fake:BOSSYuTu;
      
      private var _fakeP:Point;
      
      public function SceneBuJinChanSi(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER0");
         setWinPosition(new Rectangle(3108,170,480,140),new Point(3214,416));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2058,1);
            TaskProxy.instance().addTask(11039);
         };
         _mission.cleanLoadUI();
         this.layoutTraps();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2058))
         {
            showDialog("STORY0085",func);
         }
      }
      
      private function layoutTraps() : void
      {
         var _loc2_:TrapGongNu = null;
         var _loc1_:int = 0;
         while(_loc1_ < 2)
         {
            _loc2_ = new TrapGongNu();
            Layers.addCEChild(_loc2_);
            _loc2_.x = this._trapArgs[_loc1_].posX;
            _loc2_.y = this._trapArgs[_loc1_].posY;
            _loc2_.initData(this._trapArgs[_loc1_]);
            _loc1_++;
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[282,306,150,100],[493,306,150,100]],31011,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[727,306,150,100],[1000,306,150,100]],31012,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1226,306,150,100],[1556,306,150,100]],31013,this.triggerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[1837,306,150,100],[2167,306,150,100]],31014,this.triggerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(220,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(955,450),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1475,450),1,new Rectangle(440,0,1380,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2088,450),1,new Rectangle(1040,0,1380,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(3380,450),1,new Rectangle(2880,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this.addFakeBoss;
         this._pTrigger0.start();
      }
      
      private function triggerEnd0() : void
      {
         this.triggerEnd();
         CitrusLock.instance().lock(new Rectangle(0,0,1380,600),false,false,false,true);
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function addFakeBoss() : void
      {
         this._fake = new BOSSYuTu();
         this._fake.x = 3356;
         this._fake.y = 450;
         this._fake.initData(30511,30511);
         this._fake.target = _mission.onePlayer;
         add(this._fake);
         setHeroEnable(true);
         this._fake.activeEnemy(false);
         this._fake.aiEnabled = true;
         this._fake.onRole.add(this.listenDead);
         EffectManager.instance().playBGM("BGM_BATTLE1");
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSZhenYuTu();
         _boss.x = this._fakeP.x;
         _boss.y = this._fakeP.y;
         _boss.initData(30521,30521,161);
         _boss.target = _mission.onePlayer;
         add(_boss);
         this._fake.kill = true;
         startBossBattle();
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,_boss.x,_boss.y);
      }
      
      private function listenDead(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         this._fakeP = new Point(this._fake.x,this._fake.y);
         StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(680,87),"哼，你把老娘惹怒了！","BODY_YU_TU",this.addBOSS,2,false);
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11039);
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._fake = null;
         super.destroy();
      }
   }
}

