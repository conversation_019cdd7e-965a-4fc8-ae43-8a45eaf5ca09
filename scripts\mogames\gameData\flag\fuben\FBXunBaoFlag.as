package mogames.gameData.flag.fuben
{
   import mogames.gameData.MasterProxy;
   
   public class FBXunBaoFlag extends FBBaseFlag
   {
      public function FBXunBaoFlag(param1:int, param2:int)
      {
         super(param1,param2);
      }
      
      override public function get totalFree() : int
      {
         if(MasterProxy.instance().gameVIP.v >= 7)
         {
            return _totalFree.v + 2;
         }
         if(MasterProxy.instance().gameVIP.v >= 6)
         {
            return _totalFree.v + 1;
         }
         return _totalFree.v;
      }
      
      override public function get status() : String
      {
         return leftFree + "/" + this.totalFree;
      }
   }
}

