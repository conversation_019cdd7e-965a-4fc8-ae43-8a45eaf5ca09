package mogames.gameFabao.fabao
{
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.view.FabaoZhenBJSView;
   import mogames.gameObj.trap.TrapZhenBJS;
   import mogames.gameRole.hero.BaseHero;
   
   public class FabaoZhenBJS extends BaseFabao
   {
      public function FabaoZhenBJS(param1:GameFabaoVO)
      {
         super(param1,28,62);
         this.createFabaoData();
         createView(new FabaoZhenBJSView());
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(35),
            "hurt":new Sint(350),
            "keepTime":new Snum(3)
         },{
            "cdTime":new Snum(35),
            "hurt":new Sint(545),
            "keepTime":new Snum(3)
         },{
            "cdTime":new Snum(35),
            "hurt":new Sint(798),
            "keepTime":new Snum(3)
         },{
            "cdTime":new Snum(35),
            "hurt":new Sint(928),
            "keepTime":new Snum(3)
         },{
            "cdTime":new Snum(35),
            "hurt":new Sint(1156),
            "keepTime":new Snum(3)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      public function addXuanFeng() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         var _loc1_:TrapZhenBJS = new TrapZhenBJS();
         _loc1_.x = owner.x - _loc1_.width * 0.5;
         _loc1_.y = owner.y - _loc1_.height;
         _loc1_.initTimer(_fabaoData.keepTime.v);
         _loc1_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",0,15);
         _loc1_.createHitHurt(_fabaoData.hurt.v,0.5,false);
         _loc1_.isFriend = owner is BaseHero;
         Layers.addCEChild(_loc1_);
         EffectManager.instance().playAudio("XUAN_FENG");
      }
   }
}

