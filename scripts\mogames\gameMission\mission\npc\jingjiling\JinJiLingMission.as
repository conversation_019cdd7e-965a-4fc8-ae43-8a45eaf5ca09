package mogames.gameMission.mission.npc.jingjiling
{
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.HeroManager;
   import mogames.gameUI.mission.BattleHeroTopModule;
   import mogames.gameUI.mission.BattleMenuModule;
   import mogames.gameUI.mission.BossHPManager;
   import mogames.gameUI.mission.HeroHitModule;
   import mogames.gameUI.mission.HeroLeftModule;
   import mogames.gameUI.mission.HeroRightModule;
   
   public class JinJiLingMission extends BaseMission
   {
      public function JinJiLingMission()
      {
         super();
      }
      
      override protected function layoutUI() : void
      {
         HeroLeftModule.instance().init();
         if(HeroManager.isDouble)
         {
            HeroRightModule.instance().init();
         }
         BattleMenuModule.instance().init(null);
         BattleHeroTopModule.instance().init();
         HeroHitModule.instance().init();
         BossHPManager.instance().init();
      }
      
      public function onLeaveDoor() : void
      {
         this.handlerTouchDoor(5,1220,390);
      }
      
      override protected function handlerTouchDoor(param1:int, param2:int, param3:int) : void
      {
         if(param1 == 0)
         {
            throw new Error("未能找到场景！");
         }
         if(param1 == 9999)
         {
            quitMission();
            return;
         }
         EffectManager.instance().addTransEffect();
         _heroPos.x = param2;
         _heroPos.y = param3;
         changeScene(param1);
      }
   }
}

