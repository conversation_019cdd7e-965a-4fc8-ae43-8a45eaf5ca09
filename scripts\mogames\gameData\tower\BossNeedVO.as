package mogames.gameData.tower
{
   import mogames.gameData.base.Sint;
   
   public class BossNeedVO
   {
      public var bossID:Sint;
      
      private var _isGet:Sint;
      
      public function BossNeedVO(param1:int)
      {
         super();
         this.bossID = new Sint(param1);
         this._isGet = new Sint();
      }
      
      public function setGet() : void
      {
         this._isGet.v = 1;
      }
      
      public function set isGet(param1:int) : void
      {
         this._isGet.v = param1;
      }
      
      public function get hasGet() : Boolean
      {
         return this._isGet.v == 1;
      }
      
      public function get saveData() : String
      {
         return this.bossID.v + "B" + this._isGet.v;
      }
   }
}

