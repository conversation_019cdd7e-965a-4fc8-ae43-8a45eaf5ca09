package mogames.gameData.fabao
{
   import mogames.gameData.base.Sint;
   
   public class RongLianVO
   {
      public var id:Sint;
      
      public var list:Array;
      
      public function RongLianVO(param1:int, param2:Array)
      {
         super();
         this.id = new Sint(param1);
         this.list = param2;
      }
      
      public function findVO(param1:int) : RLUseVO
      {
         var _loc2_:RLUseVO = null;
         for each(_loc2_ in this.list)
         {
            if(_loc2_)
            {
               if(_loc2_.quality.v == param1)
               {
                  return _loc2_;
               }
            }
         }
         return null;
      }
   }
}

