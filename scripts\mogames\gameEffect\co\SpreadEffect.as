package mogames.gameEffect.co
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.geom.Point;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.gameObj.box2d.SpreadObject;
   import org.osflash.signals.Signal;
   import utils.MathUtil;
   
   public class SpreadEffect
   {
      private var _spreadList:Vector.<SpreadObject>;
      
      public var onDestroy:Signal;
      
      public function SpreadEffect()
      {
         super();
         this.onDestroy = new Signal(SpreadEffect);
         this._spreadList = new Vector.<SpreadObject>();
      }
      
      public function addSkinSpread(param1:int, param2:Point, param3:Array, param4:Number = 1) : void
      {
         var _loc5_:int = 0;
         var _loc6_:Array = param3.slice();
         var _loc7_:int = 0;
         while(_loc7_ < param1)
         {
            if(_loc6_.length <= 0)
            {
               _loc6_ = param3.slice();
            }
            _loc5_ = int(Math.random() * _loc6_.length);
            this.newSpreadGrain(param2,AssetManager.newBitmap(_loc6_[_loc5_]),param4);
            _loc6_.splice(_loc5_,1);
            _loc7_++;
         }
      }
      
      public function addCircleSpread(param1:int, param2:Point, param3:int) : void
      {
         var _loc4_:int = 0;
         while(_loc4_ < param1)
         {
            this.newSpreadGrain(param2,new Bitmap(this.newBitmapData(param3)));
            _loc4_++;
         }
      }
      
      private function newSpreadGrain(param1:Point, param2:Bitmap, param3:Number = 1) : void
      {
         var _loc4_:SpreadObject = null;
         param2.smoothing = true;
         _loc4_ = new SpreadObject("spread",{
            "width":param2.width,
            "height":param2.height
         });
         _loc4_.x = param1.x;
         _loc4_.y = param1.y;
         _loc4_.view = param2;
         _loc4_.group = Layers.GROUP_MIDDLE;
         _loc4_.rotation = Math.random() * 180;
         _loc4_.isShadow = false;
         Layers.addCEChild(_loc4_);
         this._spreadList.push(_loc4_);
         _loc4_.applyForce(this.randomVX,this.randomVY * param3);
         _loc4_.onKill.add(this.removeSpread);
      }
      
      private function newBitmapData(param1:int) : BitmapData
      {
         var _loc2_:Sprite = new Sprite();
         _loc2_.graphics.beginFill(param1,Math.random() * 0.9 + 0.1);
         _loc2_.graphics.drawCircle(5,5,5);
         _loc2_.graphics.endFill();
         var _loc3_:BitmapData = new BitmapData(10,10,true,0);
         _loc3_.draw(_loc2_);
         return _loc3_;
      }
      
      private function removeSpread(param1:SpreadObject) : void
      {
         var _loc2_:int = int(this._spreadList.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._spreadList.splice(_loc2_,1);
         }
         if(this._spreadList.length <= 0)
         {
            this.destroy();
         }
      }
      
      private function get randomVX() : Number
      {
         var _loc1_:int = MathUtil.checkOdds(500) ? 1 : -1;
         return _loc1_ * Math.random() * 10;
      }
      
      private function get randomVY() : Number
      {
         return -(Math.random() * 10 + 10);
      }
      
      private function destroy() : void
      {
         this.onDestroy.removeAll();
         this._spreadList = null;
      }
   }
}

