package citrus.utils.objectmakers.tmx
{
   import flash.display.BitmapData;
   import flash.geom.Rectangle;
   
   public class TmxTileSet
   {
      private var _tileProps:Array;
      
      private var _image:BitmapData = null;
      
      public var firstGID:int = 0;
      
      public var map:TmxMap;
      
      public var name:String;
      
      public var tileWidth:int;
      
      public var tileHeight:int;
      
      public var spacing:int;
      
      public var margin:int;
      
      public var imageSource:String;
      
      public var numTiles:int = 16777215;
      
      public var numRows:int = 1;
      
      public var numCols:int = 1;
      
      public function TmxTileSet(param1:XML, param2:TmxMap)
      {
         var _loc3_:XML = null;
         this._tileProps = [];
         super();
         this.firstGID = param1.@firstgid;
         this.imageSource = param1.image.@source;
         this.map = param2;
         this.name = param1.@name;
         this.tileWidth = param1.@tilewidth;
         this.tileHeight = param1.@tileheight;
         this.spacing = param1.@spacing;
         this.margin = param1.@margin;
         for each(_loc3_ in param1.tile)
         {
            if(_loc3_.properties[0])
            {
               this._tileProps[int(_loc3_.@id)] = new TmxPropertySet(_loc3_.properties[0]);
            }
         }
      }
      
      public function get image() : BitmapData
      {
         return this._image;
      }
      
      public function set image(param1:BitmapData) : void
      {
         this._image = param1;
         this.numCols = Math.floor(param1.width / this.tileWidth);
         this.numRows = Math.floor(param1.height / this.tileHeight);
         this.numTiles = this.numRows * this.numCols;
      }
      
      public function hasGid(param1:int) : Boolean
      {
         return param1 >= this.firstGID && param1 < this.firstGID + this.numTiles;
      }
      
      public function fromGid(param1:int) : int
      {
         return param1 - this.firstGID;
      }
      
      public function toGid(param1:int) : int
      {
         return this.firstGID + param1;
      }
      
      public function getPropertiesByGid(param1:int) : TmxPropertySet
      {
         return this._tileProps[param1 - this.firstGID];
      }
      
      public function getProperties(param1:int) : TmxPropertySet
      {
         return this._tileProps[param1];
      }
      
      public function getRect(param1:int) : Rectangle
      {
         return new Rectangle((param1 - this.firstGID) % this.numCols * this.tileWidth,Math.floor((param1 - this.firstGID) / this.numCols) * this.tileHeight,this.tileWidth,this.tileHeight);
      }
   }
}

