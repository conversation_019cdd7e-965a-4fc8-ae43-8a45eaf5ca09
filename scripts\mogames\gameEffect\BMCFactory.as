package mogames.gameEffect
{
   import mogames.gameObj.display.BMCSprite;
   
   public class BMCFactory
   {
      private static var _instance:BMCFactory;
      
      private var _pool:Vector.<BMCSprite>;
      
      public function BMCFactory()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this._pool = new Vector.<BMCSprite>();
         var _loc1_:int = 0;
         while(_loc1_ < 20)
         {
            this._pool[_loc1_] = new BMCSprite();
            _loc1_++;
         }
      }
      
      public static function instance() : BMCFactory
      {
         if(!_instance)
         {
            _instance = new BMCFactory();
         }
         return _instance;
      }
      
      public function newBMC() : BMCSprite
      {
         return this._pool.length > 0 ? this._pool.pop() : new BMCSprite();
      }
      
      public function recyle(param1:BMCSprite) : void
      {
         var _loc2_:BMCSprite = null;
         for each(_loc2_ in this._pool)
         {
            if(_loc2_ == param1)
            {
               return;
            }
         }
         this._pool.push(param1);
      }
   }
}

