package mogames.gameMission.mission.baihuling.scene
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.baihuling.BaiHuLingMission;
   import mogames.gameMission.mission.baihuling.npc.NPCHuaShen;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.StoneTrap;
   import mogames.gameRole.enemy.BOSSYinJiangJun;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneBaiHuLing02 extends HuaShenScene
   {
      protected var _eTrigger0:EnemyTrigger;
      
      protected var _eTrigger1:EnemyTrigger;
      
      protected var _pTrigger0:LocalTriggerX;
      
      protected var _pTrigger1:LocalTriggerX;
      
      protected var _bossTrigger:LocalTriggerX;
      
      public function SceneBaiHuLing02(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [StoneTrap];
         super(param1,param2);
      }
      
      override protected function createHuaShen() : void
      {
         if(_mission.hasMark("laotouDead"))
         {
            return;
         }
         _npc = new NPCHuaShen(1500,"HUA_SHEN_LAOTOU_HURT","MC_HUA_SHEN_LAO_TOU_CLIP",87,116);
         _npc.x = 1560;
         _npc.y = 372;
         add(_npc);
         if(_mission.hasMark("laotouSave"))
         {
            _npc.changeAnimation("stand",true);
         }
         _npc.setFunc(this.saveFunc,this.deadFunc);
      }
      
      override protected function saveFunc() : void
      {
         _mission.setMark("laotouSave");
      }
      
      override protected function deadFunc(param1:int) : void
      {
         super.deadFunc(param1);
         (_mission as BaiHuLingMission).addDead();
         _mission.setMark("laotouDead");
      }
      
      override protected function initEnemies() : void
      {
         if(_mission.hasMark("enemy702"))
         {
            return;
         }
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[230,200,152,100]],7021,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[960,200,900,100]],7022,null);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(570,1053),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1530,1053),1);
         this._bossTrigger = new LocalTriggerX(_mission.onePlayer,new Point(960,450),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._bossTrigger.okFunc = this.handlerStart;
         this._pTrigger0.start();
         this._bossTrigger.start();
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSYinJiangJun();
         _boss.x = 1433;
         _boss.y = 450;
         _boss.initData(30022,30022,16);
         _boss.target = _mission.curTarget;
         add(_boss);
         _dropRect = new Rectangle(930,370,900,100);
      }
      
      private function triggerEnd0() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
         this.addBOSS();
         _mission.setMark("enemy702");
      }
      
      private function handlerStart() : void
      {
         startBossBattle("BGM_BATTLE1");
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._bossTrigger)
         {
            this._bossTrigger.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._bossTrigger = null;
         super.destroy();
      }
   }
}

