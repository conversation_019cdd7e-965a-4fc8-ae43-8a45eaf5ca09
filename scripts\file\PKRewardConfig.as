package file
{
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.drop.vo.CardRewardVO;
   import mogames.gameData.pk.vo.PKRewardVO;
   
   public class PKRewardConfig
   {
      private static var _instance:PKRewardConfig;
      
      public var rewards:Array;
      
      public function PKRewardConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : PKRewardConfig
      {
         if(!_instance)
         {
            _instance = new PKRewardConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.rewards = [];
         this.rewards.push(new PKRewardVO(1,1,500,[new BaseRewardVO(14074,1),new BaseRewardVO(70911,1),new BaseRewardVO(14170,60)],[new CardRewardVO(14062,1),new BaseRewardVO(14059,1),new BaseRewardVO(14170,50)]));
         this.rewards.push(new PKRewardVO(2,2,500,[new CardRewardVO(70965,1),new BaseRewardVO(14063,1),new BaseRewardVO(14170,55)],[new CardRewardVO(14204,1),new BaseRewardVO(14063,1),new BaseRewardVO(14170,45)]));
         this.rewards.push(new PKRewardVO(3,3,500,[new CardRewardVO(70964,1),new BaseRewardVO(14124,1),new BaseRewardVO(14170,50)],[new CardRewardVO(14214,1),new BaseRewardVO(14124,1),new BaseRewardVO(14170,40)]));
         this.rewards.push(new PKRewardVO(4,10,500,[new CardRewardVO(70963,1),new BaseRewardVO(14059,1),new BaseRewardVO(14170,45)],[new CardRewardVO(14224,1),new BaseRewardVO(14058,1),new BaseRewardVO(14170,35)]));
         this.rewards.push(new PKRewardVO(11,50,500,[new CardRewardVO(70962,1),new BaseRewardVO(14101,5),new BaseRewardVO(14170,40)],[new CardRewardVO(14203,2),new BaseRewardVO(14101,3),new BaseRewardVO(14170,30)]));
         this.rewards.push(new PKRewardVO(51,100,500,[new CardRewardVO(14203,5),new BaseRewardVO(14101,3),new BaseRewardVO(14170,35)],[new CardRewardVO(14213,2),new BaseRewardVO(14101,2),new BaseRewardVO(14170,25)]));
         this.rewards.push(new PKRewardVO(101,500,500,[new CardRewardVO(14213,5),new BaseRewardVO(14150,10),new BaseRewardVO(14170,30)],[new CardRewardVO(14223,2),new BaseRewardVO(14150,3),new BaseRewardVO(14170,25)]));
         this.rewards.push(new PKRewardVO(501,1000,500,[new CardRewardVO(14223,5),new BaseRewardVO(14150,5),new BaseRewardVO(14170,25)],[new CardRewardVO(71131,2),new BaseRewardVO(14150,2),new BaseRewardVO(14170,15)]));
      }
      
      public function findReward(param1:int, param2:int) : Array
      {
         var _loc3_:PKRewardVO = null;
         for each(_loc3_ in this.rewards)
         {
            if(_loc3_.inRate(param1))
            {
               return _loc3_.countReward(param2);
            }
         }
         return [];
      }
   }
}

