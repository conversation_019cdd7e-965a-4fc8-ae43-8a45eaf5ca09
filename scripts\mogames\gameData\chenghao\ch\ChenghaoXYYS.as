package mogames.gameData.chenghao.ch
{
   import mogames.gameData.chenghao.BaseChenghaoVO;
   import mogames.gameData.flag.FlagProxy;
   
   public class ChenghaoXYYS extends BaseChenghaoVO
   {
      public function ChenghaoXYYS(param1:int, param2:int, param3:String)
      {
         super(param1,param2,param3);
      }
      
      override public function get hasGet() : Boolean
      {
         return FlagProxy.instance().isComplete(1026);
      }
   }
}

