package mogames.gameData.heroSkill.constvo
{
   import mogames.gameData.heroSkill.SkillLevelVO;
   
   public class ConstSkillFive extends ConstSkillVO
   {
      public function ConstSkillFive(param1:int, param2:String, param3:String, param4:String, param5:String)
      {
         super(param1,param2,param3,param4,param5);
      }
      
      override protected function initCondition() : void
      {
         super.initCondition();
         _lvVec.push(new SkillLevelVO(0,5,1000));
         _lvVec.push(new SkillLevelVO(1,10,2000));
         _lvVec.push(new SkillLevelVO(2,15,3000));
         _lvVec.push(new SkillLevelVO(3,20,4000));
         _lvVec.push(new SkillLevelVO(4,25,5000));
         _lvVec.push(new SkillLevelVO(5,30,6000));
         _lvVec.push(new SkillLevelVO(6,35,7000));
         _lvVec.push(new SkillLevelVO(7,40,8000));
         _lvVec.push(new SkillLevelVO(8,45,9000));
         _lvVec.push(new SkillLevelVO(9,50,1000));
      }
   }
}

