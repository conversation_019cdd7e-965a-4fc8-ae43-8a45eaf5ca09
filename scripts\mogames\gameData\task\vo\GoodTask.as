package mogames.gameData.task.vo
{
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameData.task.constvo.ConstTask;
   
   public class GoodTask extends BaseTask
   {
      public function GoodTask(param1:ConstTask)
      {
         super(param1);
      }
      
      override public function checkComplete() : void
      {
         if(!this.isComplete)
         {
            return;
         }
         this.setComplete();
      }
      
      override public function setComplete() : void
      {
         EventManager.dispatchEvent(UIEvent.MENU_TIP_EVENT,{"type":"updateTaskBtn"});
         TaskProxy.instance().taskHandler.handlerComplete(constTask.id.v);
      }
      
      override public function get isComplete() : Boolean
      {
         var _loc1_:TaskGoodVO = null;
         for each(_loc1_ in constTask.needList)
         {
            if(!_loc1_.isComplete)
            {
               return false;
            }
         }
         return true;
      }
      
      override public function handlerGet() : void
      {
         var _loc1_:TaskGoodVO = null;
         super.handlerGet();
         for each(_loc1_ in constTask.needList)
         {
            _loc1_.handlerGet();
         }
      }
      
      public function checkNeed(param1:int) : Boolean
      {
         var _loc2_:TaskGoodVO = null;
         for each(_loc2_ in constTask.needList)
         {
            if(_loc2_.goodID.v == param1)
            {
               return true;
            }
         }
         return false;
      }
      
      override public function get targetStr() : String
      {
         var _loc2_:TaskGoodVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in constTask.needList)
         {
            _loc1_.push(_loc2_.infor);
         }
         return "获得：" + _loc1_.join("，");
      }
      
      override public function get saveData() : String
      {
         return [constTask.id.v,_isGet.v].join("H");
      }
      
      override public function set loadData(param1:Array) : void
      {
         _isGet.v = int(param1[1]);
      }
   }
}

