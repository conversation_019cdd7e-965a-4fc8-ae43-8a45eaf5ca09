package mogames.gameMission.mission.wujiguo.scene
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.wujiguo.WuJiGuoMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameObj.box2d.Door;
   import mogames.gamePKG.PKGManager;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.TxtUtil;
   
   public class SceneWuJiGuo1001 extends BaseScene
   {
      private var _stone:CitrusMCSprite;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _trapPos:Array = [[634,450],[1130,450],[1624,450],[2336,450]];
      
      public function SceneWuJiGuo1001(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER4");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            PromptMediator.instance().showPrompt(TxtUtil.setColor("【炼丹】","99ff00") + "功能已经开放！<br>（花果山找太上老君）",null,null,true,"");
            TaskProxy.instance().addTask(11011);
            FlagProxy.instance().setValue(2019,1);
            FlagProxy.instance().setValue(107,1);
            PKGManager.saveWithTip();
         };
         super.handlerInit();
         this.initEnemies();
         this.initTrap();
         this.initStone();
         if(!FlagProxy.instance().isComplete(2019))
         {
            showDialog("STORY0040",func);
         }
      }
      
      private function initStone() : void
      {
         if(FlagProxy.instance().isComplete(401))
         {
            this.activeDoor();
            return;
         }
         this._stone = new CitrusMCSprite("MC_WU_JI_GUO_STONE_CLIP",{
            "width":134,
            "height":130,
            "group":2
         });
         this._stone.x = 79;
         this._stone.y = 353;
         add(this._stone);
         if(FlagProxy.instance().isComplete(400))
         {
            this._stone.changeAnimation("idle1",true);
            this._stone.addMouseListener(this.handlerOpen);
         }
         else
         {
            this._stone.changeAnimation("idle0",true);
         }
      }
      
      private function handlerOpen() : void
      {
         this._stone.kill = true;
         this.activeDoor();
         EffectManager.instance().playAudio("STONE");
         EffectManager.instance().addSkinSpread(3,new Point(this._stone.x + this._stone.width * 0.5,this._stone.y + this._stone.height * 0.5),["PIC_STONE_TRAP3","PIC_STONE_TRAP0","PIC_STONE_TRAP2"]);
      }
      
      private function activeDoor() : void
      {
         (getObjectByName("stoneDoor") as Door).setActive();
      }
      
      private function initTrap() : void
      {
         var _loc1_:WuJiGuoMission = _mission as WuJiGuoMission;
         var _loc2_:int = 0;
         while(_loc2_ < 4)
         {
            _loc1_.createTrap(this._trapPos[_loc2_][0],this._trapPos[_loc2_][1]);
            _loc2_++;
         }
      }
      
      private function initEnemies() : void
      {
         if(_mission.hasMark("enemy10011"))
         {
            return;
         }
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[136,358,150,100],[775,358,150,100]],10011,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1195,150,150,100],[1700,358,150,100]],10012,this.triggerEnd1);
         this._eTrigger2 = new EnemyTrigger(_mission,[[2105,218,150,100],[2490,358,150,100]],10013,this.triggerEnd2);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(337,490),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1577,490),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1954,490),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd0() : void
      {
         unlock();
         CitrusLock.instance().lock(new Rectangle(0,0,1920,600),false,false,false,true);
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd1() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd2() : void
      {
         _mission.setMark("enemy10011");
      }
      
      override public function destroy() : void
      {
         this._stone = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         super.destroy();
      }
   }
}

