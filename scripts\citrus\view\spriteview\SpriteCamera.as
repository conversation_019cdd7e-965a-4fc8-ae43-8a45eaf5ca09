package citrus.view.spriteview
{
   import citrus.math.MathUtils;
   import citrus.view.ACitrusCamera;
   import flash.display.Sprite;
   import flash.geom.Point;
   
   public class SpriteCamera extends ACitrusCamera
   {
      public function SpriteCamera(param1:Sprite)
      {
         super(param1);
      }
      
      override protected function initialize() : void
      {
         super.initialize();
         _aabbData = MathUtils.createAABBData(0,0,cameraLensWidth / _camProxy.scale,cameraLensHeight / _camProxy.scale,_camProxy.rotation,_aabbData);
      }
      
      override public function zoom(param1:Number) : void
      {
         if(_allowZoom)
         {
            _zoom *= param1;
            return;
         }
         throw new Error("[object SpriteCamera]is not allowed to zoom. please set allowZoom to true.");
      }
      
      override public function zoomFit(param1:Number, param2:Number, param3:Boolean = false) : Number
      {
         var _loc4_:Number = NaN;
         if(_allowZoom)
         {
            if(cameraLensHeight / cameraLensWidth > param2 / param1)
            {
               _loc4_ = cameraLensWidth / param1;
            }
            else
            {
               _loc4_ = cameraLensHeight / param2;
            }
            if(param3)
            {
               baseZoom = _loc4_;
               _zoom = 1;
               return _loc4_;
            }
            return _zoom = _loc4_;
         }
         throw new Error("[object SpriteCamera] is not allowed to zoom. please set allowZoom to true.");
      }
      
      override public function rotate(param1:Number) : void
      {
         if(_allowRotation)
         {
            _rotation += param1;
            return;
         }
         throw new Error("[object SpriteCamera]is not allowed to rotate. please set allowRotation to true.");
      }
      
      override public function setRotation(param1:Number) : void
      {
         if(_allowRotation)
         {
            _rotation = param1;
            return;
         }
         throw new Error("[object SpriteCamera]is not allowed to rotate. please set allowRotation to true.");
      }
      
      override public function setZoom(param1:Number) : void
      {
         if(_allowZoom)
         {
            _zoom = param1;
            return;
         }
         throw new Error("[object SpriteCamera]is not allowed to zoom. please set allowZoom to true.");
      }
      
      override public function getZoom() : Number
      {
         return _zoom;
      }
      
      override public function getRotation() : Number
      {
         return _rotation;
      }
      
      public function resetAABBData() : void
      {
         if(!_allowZoom && !_allowRotation)
         {
            _aabbData.offsetX = _aabbData.offsetY = 0;
            _aabbData.rect.setTo(_ghostTarget.x,_ghostTarget.y,cameraLensWidth,cameraLensHeight);
            return;
         }
         if(_allowZoom && !_allowRotation)
         {
            _aabbData.offsetX = _aabbData.offsetY = 0;
            _aabbData.rect.setTo(_ghostTarget.x,_ghostTarget.y,cameraLensWidth / _camProxy.scale,cameraLensHeight / _camProxy.scale);
            return;
         }
         if(_allowRotation && _allowZoom)
         {
            _aabbData = MathUtils.createAABBData(_ghostTarget.x,_ghostTarget.y,cameraLensWidth / _camProxy.scale,cameraLensHeight / _camProxy.scale,-_camProxy.rotation,_aabbData);
            return;
         }
         if(!_allowZoom && _allowRotation)
         {
            _aabbData = MathUtils.createAABBData(_ghostTarget.x,_ghostTarget.y,cameraLensWidth,cameraLensHeight,-_camProxy.rotation,_aabbData);
            return;
         }
      }
      
      override public function update() : void
      {
         var _loc1_:Number = NaN;
         var _loc2_:Number = NaN;
         super.update();
         offset.setTo(cameraLensWidth * center.x,cameraLensHeight * center.y);
         if(Boolean(_target) && followTarget)
         {
            if(_target.x <= camPos.x - deadZone.width * 0.5 / _camProxy.scale || _target.x >= camPos.x + deadZone.width * 0.5 / _camProxy.scale)
            {
               _targetPos.x = _target.x;
            }
            if(_target.y <= camPos.y - deadZone.height * 0.5 / _camProxy.scale || _target.y >= camPos.y + deadZone.height * 0.5 / _camProxy.scale)
            {
               _targetPos.y = _target.y;
            }
            _ghostTarget.x += (_targetPos.x - _ghostTarget.x) * easing.x;
            _ghostTarget.y += (_targetPos.y - _ghostTarget.y) * easing.y;
         }
         else if(_manualPosition)
         {
            _ghostTarget.x = _manualPosition.x;
            _ghostTarget.y = _manualPosition.y;
         }
         if(_allowRotation)
         {
            _camProxy.rotation += (_rotation - _camProxy.rotation) * rotationEasing;
         }
         this.resetAABBData();
         if(_allowZoom)
         {
            _camProxy.scale += (mzoom - _camProxy.scale) * zoomEasing;
            if(Boolean(bounds) && (boundsMode == BOUNDS_MODE_AABB || boundsMode == BOUNDS_MODE_ADVANCED))
            {
               _loc1_ = _aabbData.rect.width * _camProxy.scale / bounds.width;
               _loc2_ = _aabbData.rect.height * _camProxy.scale / bounds.height;
               if(_aabbData.rect.width >= bounds.width)
               {
                  _camProxy.scale = mzoom = _loc1_;
               }
               else if(_aabbData.rect.height >= bounds.height)
               {
                  _camProxy.scale = mzoom = _loc2_;
               }
            }
         }
         _camProxy.x += (ghostTarget.x - _camProxy.x) * easing.x;
         _camProxy.y += (ghostTarget.y - _camProxy.y) * easing.y;
         MathUtils.rotatePoint(offset.x / _camProxy.scale,offset.y / _camProxy.scale,_camProxy.rotation,_b.rotoffset);
         if(bounds)
         {
            if(boundsMode == BOUNDS_MODE_AABB)
            {
               _b.w2 = _aabbData.rect.width - _b.rotoffset.x + _aabbData.offsetX;
               _b.h2 = _aabbData.rect.height - _b.rotoffset.y + _aabbData.offsetY;
               _b.bl = bounds.left + (MathUtils.abs(_aabbData.offsetX) + _b.rotoffset.x);
               _b.bt = bounds.top + (MathUtils.abs(_aabbData.offsetY) + _b.rotoffset.y);
               _b.br = bounds.right - (_aabbData.offsetX + _aabbData.rect.width - _b.rotoffset.x);
               _b.bb = bounds.bottom - (_aabbData.offsetY + _aabbData.rect.height - _b.rotoffset.y);
               if(_camProxy.x < _b.bl)
               {
                  _camProxy.x = _b.bl;
               }
               if(_camProxy.x > _b.br)
               {
                  _camProxy.x = _b.br;
               }
               if(_camProxy.y < _b.bt)
               {
                  _camProxy.y = _b.bt;
               }
               if(_camProxy.y > _b.bb)
               {
                  _camProxy.y = _b.bb;
               }
            }
            else if(boundsMode == BOUNDS_MODE_OFFSET)
            {
               if(_camProxy.x < bounds.left)
               {
                  _camProxy.x = bounds.left;
               }
               if(_camProxy.x > bounds.right)
               {
                  _camProxy.x = bounds.right;
               }
               if(_camProxy.y < bounds.top)
               {
                  _camProxy.y = bounds.top;
               }
               if(_camProxy.y > bounds.bottom)
               {
                  _camProxy.y = bounds.bottom;
               }
            }
            else if(boundsMode == BOUNDS_MODE_ADVANCED)
            {
               if(offset.x <= cameraLensWidth * 0.5)
               {
                  if(offset.y <= cameraLensHeight * 0.5)
                  {
                     _b.diag2 = MathUtils.DistanceBetweenTwoPoints(offset.x,cameraLensWidth,offset.y,cameraLensHeight);
                  }
                  else
                  {
                     _b.diag2 = MathUtils.DistanceBetweenTwoPoints(offset.x,cameraLensWidth,offset.y,0);
                  }
               }
               else if(offset.y <= cameraLensHeight * 0.5)
               {
                  _b.diag2 = MathUtils.DistanceBetweenTwoPoints(offset.x,0,offset.y,cameraLensHeight);
               }
               else
               {
                  _b.diag2 = offset.length;
               }
               _b.diag2 /= _camProxy.scale;
               if(_camProxy.x < bounds.left + _b.diag2)
               {
                  _camProxy.x = bounds.left + _b.diag2;
               }
               if(_camProxy.x > bounds.right - _b.diag2)
               {
                  _camProxy.x = bounds.right - _b.diag2;
               }
               if(_camProxy.y < bounds.top + _b.diag2)
               {
                  _camProxy.y = bounds.top + _b.diag2;
               }
               if(_camProxy.y > bounds.bottom - _b.diag2)
               {
                  _camProxy.y = bounds.bottom - _b.diag2;
               }
            }
         }
         if(parallaxMode == PARALLAX_MODE_TOPLEFT)
         {
            _m.identity();
            _m.rotate(_camProxy.rotation);
            _m.scale(1 / _camProxy.scale,1 / _camProxy.scale);
            _camProxy.offset = _m.transformPoint(offset);
            _camProxy.offset.x *= -1;
            _camProxy.offset.y *= -1;
         }
         _aabbData.rect.x = _camProxy.x + _aabbData.offsetX - _b.rotoffset.x;
         _aabbData.rect.y = _camProxy.y + _aabbData.offsetY - _b.rotoffset.y;
         _m.identity();
         _m.translate(-_camProxy.x,-_camProxy.y);
         _m.rotate(_camProxy.rotation);
         _m.scale(_camProxy.scale,_camProxy.scale);
         _m.translate(offset.x,offset.y);
         this.pointFromLocal(offset.x,offset.y,_camPos);
         (_viewRoot as Sprite).transform.matrix = _m;
      }
      
      public function pointFromLocal(param1:Number, param2:Number, param3:Point = null) : Point
      {
         _p.setTo(param1,param2);
         if(param3)
         {
            param3.copyFrom((_viewRoot as Sprite).globalToLocal(_p));
            return null;
         }
         return (_viewRoot as Sprite).globalToLocal(_p);
      }
      
      public function pointToLocal(param1:Point) : Point
      {
         return (_viewRoot as Sprite).localToGlobal(param1);
      }
      
      override public function get allowZoom() : Boolean
      {
         return _allowZoom;
      }
      
      override public function get allowRotation() : Boolean
      {
         return _allowRotation;
      }
      
      override public function set allowZoom(param1:Boolean) : void
      {
         if(!param1)
         {
            _zoom = 1;
            _camProxy.scale = 1;
         }
         _allowZoom = param1;
      }
      
      override public function set allowRotation(param1:Boolean) : void
      {
         if(!param1)
         {
            _rotation = 0;
            _camProxy.rotation = 0;
         }
         _allowRotation = param1;
      }
   }
}

