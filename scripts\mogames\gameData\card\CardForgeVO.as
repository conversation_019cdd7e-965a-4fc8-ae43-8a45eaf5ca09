package mogames.gameData.card
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.forge.NeedVO;
   import mogames.gameData.good.GameCardVO;
   
   public class CardForgeVO extends BaseUseVO
   {
      private var _oldID:Sint;
      
      private var _newID:Sint;
      
      private var _gold:Sint;
      
      private var _list:Array;
      
      private var _rate:Sint;
      
      private var _hunNum:NeedVO;
      
      public function CardForgeVO(param1:int, param2:int, param3:int, param4:NeedVO, param5:Array, param6:int)
      {
         this._oldID = new Sint(param1);
         this._newID = new Sint(param2);
         this._hunNum = param4;
         this._rate = new Sint(param6);
         super(param3,param5);
      }
      
      public function get oldID() : int
      {
         return this._oldID.v;
      }
      
      public function get newID() : int
      {
         return this._newID.v;
      }
      
      public function get rate() : int
      {
         return this._rate.v;
      }
      
      public function get hunNeed() : NeedVO
      {
         return this._hunNum;
      }
      
      public function get newCard() : GameCardVO
      {
         return GoodConfig.instance().newGood(this.newID) as GameCardVO;
      }
   }
}

