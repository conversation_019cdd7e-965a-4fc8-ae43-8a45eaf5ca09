package file
{
   import mogames.gameData.fuben.TZJS.TZJSLimitVO;
   import mogames.gameData.fuben.TZJS.TZJSVO;
   
   public class TZJSConfig
   {
      private static var _instance:TZJSConfig;
      
      public var list:Vector.<TZJSVO>;
      
      public function TZJSConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : TZJSConfig
      {
         if(!_instance)
         {
            _instance = new TZJSConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.list = new Vector.<TZJSVO>();
         this.list[0] = new TZJSVO(100,[new TZJSLimitVO("降服小白龙（不消耗次数）",1),new TZJSLimitVO("挑战小白龙【普通】",12),new TZJSLimitVO("挑战小白龙【困难】",22),new TZJSLimitVO("挑战小白龙【噩梦】",32)],[10000,14001,13105,13303,13317,16752]);
         this.list[1] = new TZJSVO(101,[new TZJSLimitVO("降服猪八戒（不消耗次数）",1),new TZJSLimitVO("挑战猪八戒【普通】",16),new TZJSLimitVO("挑战猪八戒【困难】",28),new TZJSLimitVO("挑战猪八戒【噩梦】",40)],[10000,16108,16761,13315,16755,13326]);
         this.list[2] = new TZJSVO(102,[new TZJSLimitVO("降服沙僧（不消耗次数）",1),new TZJSLimitVO("挑战沙僧【普通】",24),new TZJSLimitVO("挑战沙僧【困难】",34),new TZJSLimitVO("挑战沙僧【噩梦】",44)],[10000,16115,16016,16756,14028,16018]);
      }
   }
}

