package mogames.gameData.fashion
{
   import mogames.gameData.drop.vo.EquipRewardVO;
   import mogames.gameData.good.GameEquipVO;
   
   public class SZHCVO
   {
      private var _rewardVO:EquipRewardVO;
      
      private var _list:Array;
      
      public function SZHCVO(param1:EquipRewardVO, param2:Array)
      {
         super();
         this._rewardVO = param1;
         this._list = param2;
      }
      
      public function get rewardVO() : EquipRewardVO
      {
         return this._rewardVO;
      }
      
      public function checkList(param1:Array) : Bo<PERSON>an
      {
         var _loc2_:GameEquipVO = null;
         if(param1.length != this._list.length)
         {
            return false;
         }
         for each(_loc2_ in param1)
         {
            if(!this.inList(_loc2_))
            {
               return false;
            }
         }
         return true;
      }
      
      public function inList(param1:GameEquipVO) : Bo<PERSON>an
      {
         var _loc2_:EquipRewardVO = null;
         for each(_loc2_ in this._list)
         {
            if(param1.quality == _loc2_.quality && param1.constEquip.id.v == _loc2_.id.v)
            {
               return true;
            }
         }
         return false;
      }
   }
}

