package mogames.gameEffect
{
   import flash.display.BitmapData;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.AssetManager;
   import mogames.gameEffect.co.FallBitmap;
   
   public class FallFactory
   {
      private static var _instance:FallFactory;
      
      private var _numList:Array;
      
      private var _pool:Vector.<FallBitmap>;
      
      public function FallFactory()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this._pool = new Vector.<FallBitmap>();
         this._numList = [];
         this._numList[0] = {
            "name":"PIC_ENEMY_HURT_NUM",
            "width":40,
            "height":40,
            "coord":20
         };
         this._numList[1] = {
            "name":"PIC_ENEMY_CRIT_HURT_NUM",
            "width":60,
            "height":60,
            "coord":35
         };
         this._numList[2] = {
            "name":"PIC_HERO_HURT_NUM",
            "width":40,
            "height":40,
            "coord":20
         };
      }
      
      public static function instance() : FallFactory
      {
         if(!_instance)
         {
            _instance = new FallFactory();
         }
         return _instance;
      }
      
      public function newHurtNumber(param1:int, param2:String) : BitmapData
      {
         var _loc3_:Object = this._numList[param1];
         var _loc4_:int = 0;
         var _loc5_:int = param2.length;
         var _loc6_:BitmapData = AssetManager.findPicRes(_loc3_.name);
         var _loc7_:BitmapData = new BitmapData(_loc5_ * _loc3_.width,_loc3_.height,true,0);
         var _loc8_:Point = new Point();
         var _loc9_:Rectangle = new Rectangle(0,0,_loc3_.width,_loc3_.height);
         while(_loc4_ < _loc5_)
         {
            _loc9_.x = int(param2.charAt(_loc4_)) * _loc3_.width;
            _loc8_.x = _loc4_ * _loc3_.coord;
            _loc7_.copyPixels(_loc6_,_loc9_,_loc8_,null,null,true);
            _loc4_++;
         }
         return _loc7_;
      }
      
      public function newFallBitmap() : FallBitmap
      {
         return this._pool.length > 0 ? this._pool.pop() : new FallBitmap();
      }
      
      public function recyle(param1:FallBitmap) : void
      {
         if(param1.bitmapData != null && AssetManager.noCache(param1.bitmapData))
         {
            param1.bitmapData.dispose();
         }
         param1.bitmapData = null;
         param1.alpha = 1;
         this._pool.push(param1);
      }
      
      public function clean() : void
      {
         this._pool = null;
         _instance = null;
      }
   }
}

