package mogames.gameData.role
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.constvo.ConstDanYaoVO;
   
   public class HeroDanYaoVO
   {
      public var constVO:ConstDanYaoVO;
      
      public var owner:HeroGameVO;
      
      public var total:Sint;
      
      public function HeroDanYaoVO(param1:int)
      {
         super();
         this.total = new Sint();
         this.constVO = GoodConfig.instance().findConstGood(param1) as ConstDanYaoVO;
      }
      
      public function setValue(param1:int) : void
      {
         this.total.v = param1;
      }
      
      public function changeValue(param1:int) : void
      {
         this.setValue(this.total.v + param1);
      }
      
      public function addATT() : void
      {
         switch(this.constVO.addType.v)
         {
            case 0:
               this.owner.extraHP.v += this.constVO.addValue.v * this.total.v;
               break;
            case 1:
               this.owner.extraMP.v += this.constVO.addValue.v * this.total.v;
               break;
            case 2:
               this.owner.extraATK.v += this.constVO.addValue.v * this.total.v;
         }
      }
   }
}

