package mogames.gameMission.mission.heisonglin
{
   import flash.geom.Point;
   import mogames.gameData.dialog.DialogVO;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameUI.dialog.DialogMediator;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class SceneHeiSongLin04 extends BaseScene
   {
      private var _eTrigger:EnemyTrigger;
      
      private var _pTrigger:LocalTriggerX;
      
      private var _jiguan:CitrusMCSprite;
      
      public function SceneHeiSongLin04(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this.initEnemies();
         this.initJiGuan();
      }
      
      private function initJiGuan() : void
      {
         if(_mission.hasMark("isOpen"))
         {
            return;
         }
         this._jiguan = new CitrusMCSprite("MC_JI_GUAN_OPEN_CLIP",{
            "width":105,
            "height":152,
            "group":2
         });
         this._jiguan.x = 816;
         this._jiguan.y = 328;
         this._jiguan.changeAnimation("idle",true);
         this._jiguan.addMouseListener(this.handlerOpen);
         add(this._jiguan);
      }
      
      private function initEnemies() : void
      {
         if(_mission.hasMark("enemy804"))
         {
            return;
         }
         this._eTrigger = new EnemyTrigger(_mission,[[15,370,150,100],[535,190,150,100],[306,116,150,100]],8041,this.triggerEnd);
         this._pTrigger = new LocalTriggerX(_mission.onePlayer,new Point(851,470),-1);
         this._pTrigger.trigger = this._eTrigger;
         this._pTrigger.start();
      }
      
      private function triggerEnd() : void
      {
         _mission.setMark("enemy804");
      }
      
      private function handlerOpen() : void
      {
         var func:Function = null;
         func = function():void
         {
            _mission.setMark("isOpen");
            EffectManager.instance().addSkinSpread(3,new Point(_jiguan.x,_jiguan.y),["PIC_JIGUAN0","PIC_JIGUAN1","PIC_JIGUAN2","PIC_JIGUAN3","PIC_JIGUAN4"]);
            _jiguan.kill = true;
            MiniMsgMediator.instance().showAutoMsg("右图的铁闸打开了。",480,100);
         };
         var list:Vector.<DialogVO> = new Vector.<DialogVO>();
         list.push(new DialogVO(0,"MASTER","5","咦，这个东西似乎可以旋转，我且试试看。"));
         DialogMediator.instance().init(list,func);
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger)
         {
            this._eTrigger.destroy();
         }
         if(this._pTrigger)
         {
            this._pTrigger.destroy();
         }
         this._eTrigger = null;
         this._pTrigger = null;
         this._jiguan = null;
         super.destroy();
      }
   }
}

