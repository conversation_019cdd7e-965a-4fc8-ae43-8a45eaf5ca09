package mogames.gameMission.mission.pindingshan.scene
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameMission.trigger.LocalTriggerY;
   import mogames.gameSystem.CitrusLock;
   
   public class ScenePingDingShan02 extends BaseScene
   {
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerY;
      
      private var _pTrigger2:LocalTriggerY;
      
      public function ScenePingDingShan02(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         this.initEnemy9023();
         this.initEnemy9022();
         this.initEnemy9021();
      }
      
      private function initEnemy9023() : void
      {
         if(_mission.hasMark("enemy9023"))
         {
            return;
         }
         this._eTrigger2 = new EnemyTrigger(_mission,[[88,334,150,100],[603,452,150,100],[301,53,150,100]],9023,this.triggerEnd2);
         this._pTrigger2 = new LocalTriggerY(_mission.onePlayer,new Point(250,400),1);
         this._pTrigger2.trigger = this._eTrigger2;
         if(_mission.hasMark("enemy9022"))
         {
            this._pTrigger2.start();
         }
      }
      
      private function initEnemy9022() : void
      {
         if(_mission.hasMark("enemy9022"))
         {
            return;
         }
         this._eTrigger1 = new EnemyTrigger(_mission,[[76,652,150,100],[597,912,150,100]],9022,this.triggerEnd1);
         this._pTrigger1 = new LocalTriggerY(_mission.onePlayer,new Point(255,970),1);
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         if(_mission.hasMark("enemy9021"))
         {
            this._pTrigger1.start();
         }
      }
      
      private function initEnemy9021() : void
      {
         if(_mission.hasMark("enemy9021"))
         {
            return;
         }
         _mission.setMark("enemy9021");
         CitrusLock.instance().lock(new Rectangle(0,1153,1160,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[308,1427,150,100],[740,1277,150,100],[950,1438,150,100]],9021,this.triggerEnd0);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(230,1590),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd0() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(0);
         CitrusLock.instance().lock(new Rectangle(0,485,1160,1270),true,false,true,false);
      }
      
      private function triggerEnd1() : void
      {
         _mission.setMark("enemy9022");
         unlock();
         EffectManager.instance().addGOEffect(0);
      }
      
      private function triggerEnd2() : void
      {
         _mission.setMark("enemy9023");
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         super.destroy();
      }
   }
}

