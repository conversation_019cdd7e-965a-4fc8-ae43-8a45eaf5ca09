package citrus.events
{
   import citrus.sounds.CitrusSound;
   import citrus.sounds.CitrusSoundInstance;
   
   public class CitrusSoundEvent extends CitrusEvent
   {
      public static const SOUND_ERROR:String = "SOUND_ERROR";
      
      public static const SOUND_LOADED:String = "SOUND_LOADED";
      
      public static const ALL_SOUNDS_LOADED:String = "ALL_SOUNDS_LOADED";
      
      public static const SOUND_START:String = "SOUND_START";
      
      public static const SOUND_PAUSE:String = "SOUND_PAUSE";
      
      public static const SOUND_RESUME:String = "SOUND_RESUME";
      
      public static const SOUND_LOOP:String = "SOUND_LOOP";
      
      public static const SOUND_END:String = "SOUND_END";
      
      public static const NO_CHANNEL_AVAILABLE:String = "NO_CHANNEL_AVAILABLE";
      
      public static const FORCE_STOP:String = "FORCE_STOP";
      
      public static const SOUND_NOT_READY:String = "SOUND_NOT_READY";
      
      public static const EVENT:String = "EVENT";
      
      public var soundName:String;
      
      public var soundID:int;
      
      public var sound:CitrusSound;
      
      public var soundInstance:CitrusSoundInstance;
      
      public var loops:int = 0;
      
      public var loopCount:int = 0;
      
      public var loadedRatio:Number;
      
      public var loaded:Boolean;
      
      public var error:Boolean;
      
      public function CitrusSoundEvent(param1:String, param2:CitrusSound, param3:CitrusSoundInstance, param4:int = -1, param5:Boolean = true, param6:Boolean = false)
      {
         super(param1,param5,param6);
         if(param2)
         {
            this.sound = param2;
            this.soundName = param2.name;
            this.loadedRatio = param2.loadedRatio;
            this.loaded = param2.loaded;
            this.error = param2.ioerror;
         }
         if(param3)
         {
            this.soundInstance = param3;
            this.loops = param3.loops;
            this.loopCount = param3.loopCount;
         }
         this.soundID = param4;
         if(param1 == SOUND_ERROR || param1 == SOUND_LOADED || param1 == ALL_SOUNDS_LOADED)
         {
            setTarget(param2);
         }
         else
         {
            setTarget(param3);
         }
      }
      
      override public function clone() : CitrusEvent
      {
         return new CitrusSoundEvent(type,this.sound,this.soundInstance,this.soundID,bubbles,cancelable) as CitrusEvent;
      }
      
      override public function toString() : String
      {
         return "[CitrusSoundEvent type: " + type + " sound: \"" + this.soundName + "\" ID: " + this.soundID + " loopCount: " + this.loopCount + " loops: " + this.loops + " ]";
      }
   }
}

