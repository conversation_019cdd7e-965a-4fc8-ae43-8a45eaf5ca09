package mogames.gameData.good
{
   import file.GoodConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   
   public class HoleVO
   {
      public var index:Sint;
      
      public var flag:Sint;
      
      public var attVO:GameATTVO;
      
      public var type:int;
      
      public function HoleVO(param1:int, param2:int)
      {
         super();
         this.index = new Sint(param1);
         this.flag = new Sint();
         this.type = param2;
      }
      
      public function openHole() : void
      {
         this.flag.v = ConstData.DATA_NUM1.v;
      }
      
      public function addHole(param1:int) : void
      {
         this.attVO = GoodConfig.instance().newGood(param1) as GameATTVO;
      }
      
      public function get isOpen() : Boolean
      {
         return this.flag.v == ConstData.DATA_NUM1.v;
      }
      
      public function get infor() : String
      {
         if(this.attVO)
         {
            return this.attVO.attInfor;
         }
         if(!this.isOpen)
         {
            if(this.type == 0)
            {
               return "未开槽";
            }
            return "未开孔";
         }
         if(this.type == 0)
         {
            return "未插入卡片";
         }
         return "未镶嵌宝石";
      }
      
      public function parseLoadData(param1:String) : void
      {
         var _loc2_:Array = param1.split("A");
         this.flag.v = int(_loc2_[1]);
         if(_loc2_[2] != "none")
         {
            this.addHole(int(_loc2_[2]));
         }
      }
      
      public function collectSaveData() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.index.v;
         _loc1_[1] = this.flag.v;
         if(this.attVO)
         {
            _loc1_[2] = this.attVO.constVO.id.v;
         }
         else
         {
            _loc1_[2] = "none";
         }
         return _loc1_.join("A");
      }
   }
}

