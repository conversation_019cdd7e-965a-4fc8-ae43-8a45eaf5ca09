package file
{
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.online.RewardLineVO;
   
   public class RewardLineConfig
   {
      private static var _instance:RewardLineConfig;
      
      private var _list:Vector.<RewardLineVO>;
      
      public function RewardLineConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : RewardLineConfig
      {
         if(!_instance)
         {
            _instance = new RewardLineConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<RewardLineVO>();
         this._list.push(new RewardLineVO(0,1,new BaseRewardVO(10000,1000)));
         this._list.push(new RewardLineVO(1,2,new BaseRewardVO(14001,1)));
         this._list.push(new RewardLineVO(2,3,new BaseRewardVO(13109,1)));
         this._list.push(new RewardLineVO(3,4,new BaseRewardVO(10000,1500)));
         this._list.push(new RewardLineVO(4,5,new BaseRewardVO(14001,2)));
         this._list.push(new RewardLineVO(5,6,new BaseRewardVO(14002,2)));
      }
      
      public function findReward(param1:int) : RewardLineVO
      {
         var _loc2_:RewardLineVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.index.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

