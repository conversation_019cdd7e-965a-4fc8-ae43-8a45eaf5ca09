package mogames.gameMission.mission.heisonglin
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.StoneTrap;
   
   public class SceneHeiSongLin07 extends BaseScene
   {
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      public function SceneHeiSongLin07(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [StoneTrap];
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this.initLeftEnemies();
         this.initRightEnemies();
      }
      
      private function initRightEnemies() : void
      {
         if(_mission.hasMark("enemy8072"))
         {
            return;
         }
         this._eTrigger1 = new EnemyTrigger(_mission,[[1160,164,150,100],[1574,164,150,100]],8072,unlock);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1520,470),1,new Rectangle(960,0,960,600));
         this._pTrigger1.setBlock(false,false,true,false);
         this._pTrigger1.isNearX = true;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger1.okFunc = this.okFunc1;
         this._pTrigger1.start();
      }
      
      private function initLeftEnemies() : void
      {
         if(_mission.hasMark("enemy8071"))
         {
            return;
         }
         this._eTrigger0 = new EnemyTrigger(_mission,[[24,334,150,100],[650,334,150,100]],8071,unlock);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,470),1,new Rectangle(0,0,1400,600));
         this._pTrigger0.setBlock(false,false,false,true);
         this._pTrigger0.isNearX = true;
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger0.okFunc = this.okFunc0;
         this._pTrigger0.start();
      }
      
      private function okFunc1() : void
      {
         _mission.setMark("enemy8072");
      }
      
      private function okFunc0() : void
      {
         _mission.setMark("enemy8071");
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         this._eTrigger0 = null;
         this._pTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger1 = null;
         super.destroy();
      }
   }
}

