package mogames.gameData.pet.vo
{
   import file.PetConfig;
   import file.PetSkillConfig;
   import flash.display.MovieClip;
   import mogames.AssetManager;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.SArray;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.GameEquipVO;
   import mogames.gameData.heroSkill.BaseSkillVO;
   import mogames.gameData.huoli.HuoLiProxy;
   import mogames.gameData.petSkill.PetSkillVO;
   import mogames.gameData.petSkill.passive.PetSkillJianRenVO;
   import mogames.gameData.petSkill.passive.PetSkillManLiVO;
   import mogames.gameData.petSkill.passive.PetSkillRenXingVO;
   import mogames.gameData.role.HeroGameVO;
   import utils.MathUtil;
   import utils.TxtUtil;
   
   public class PetGameVO extends HeroGameVO
   {
      public var petConst:PetConstVO;
      
      public var petAsset:PetAssetVO;
      
      public var petCount:PetCountVO;
      
      public var ownerID:int;
      
      public var curName:String;
      
      public var isMutate:Sint = new Sint();
      
      public var curEvolve:Sint = new Sint();
      
      public var curQuality:Sint = new Sint();
      
      public var curGrow:Sint = new Sint();
      
      public var curWit:Sint = new Sint();
      
      public var curLearn:Sint = new Sint();
      
      public var curTalent:Sint = new Sint();
      
      public var curLife:Sint = new Sint(100);
      
      public var curLoyal:Sint = new Sint(100);
      
      public var skillPoint:Sint = new Sint();
      
      public var talentHP:Sint = new Sint();
      
      public var talentATK:Sint = new Sint();
      
      public var talentPDEF:Sint = new Sint();
      
      public var talentMDEF:Sint = new Sint();
      
      public var shoukui:GameEquipVO;
      
      public var shouzhua:GameEquipVO;
      
      public var shoujia:GameEquipVO;
      
      public var shouzhui:GameEquipVO;
      
      public var activeSkills:Array;
      
      public var passiveSkills:Array;
      
      protected var _maxLevels:SArray = new SArray([15,30,45,60]);
      
      public var talents:Array = [this.talentHP,this.talentATK,this.talentPDEF,this.talentMDEF];
      
      public function PetGameVO(param1:int = 0)
      {
         super(param1);
      }
      
      override protected function initConstData(param1:int) : void
      {
         constVO = this.petConst = PetConfig.instance().findConstPet(param1);
         assetVO = this.petAsset = PetConfig.instance().findPetAsset(param1);
         this.petCount = PetConfig.instance().findPetCount(param1);
         baseMOVE.v = this.petCount.baseMOVE.v;
      }
      
      override public function initBattleData() : void
      {
         curHP.v = totalHP.v;
      }
      
      override public function setLevel(param1:int, param2:Boolean = true) : void
      {
         level.v = param1;
         baseHP.v = (this.petCount.baseHP.v * 5 + level.v * this.curGrow.v) * (1 + this.talentHP.v * 0.001) * 1.1;
         baseATK.v = (this.petCount.baseATK.v * 5 + level.v * this.curGrow.v) * (1 + this.talentATK.v * 0.001) * 0.15;
         basePDEF.v = (this.petCount.basePDEF.v * 5 + level.v * this.curGrow.v) * (1 + this.talentPDEF.v * 0.001) * 0.05;
         baseMDEF.v = (this.petCount.baseMDEF.v * 5 + level.v * this.curGrow.v) * (1 + this.talentMDEF.v * 0.001) * 0.05;
         baseMISS.v = this.petCount.baseMISS.v + level.v * this.curGrow.v * 0.01;
         baseCRIT.v = this.petCount.baseCRIT.v + level.v * this.curGrow.v * 0.01;
         totalEXP.v = this.countTotalExp(level.v);
         this.updateExtra();
         if(param2)
         {
            updateAll();
         }
      }
      
      override public function addEXP(param1:int, param2:Boolean = true) : void
      {
         lastLv = level.v;
         curEXP.v += param1;
         if(curEXP.v >= totalEXP.v && this.isMaxLevel)
         {
            curEXP.v = totalEXP.v;
         }
         else
         {
            while(curEXP.v >= totalEXP.v)
            {
               curEXP.v -= totalEXP.v;
               if(!this.isMaxLevel)
               {
                  this.setLevel(level.v + ConstData.DATA_NUM1.v);
                  this.skillPoint.v += ConstData.DATA_NUM1.v;
                  HuoLiProxy.instance().setNum(1011);
               }
            }
         }
         onUpdate.dispatch("updateEXP");
         if(lastLv != level.v)
         {
            onUpdate.dispatch("updateLevel");
         }
      }
      
      override protected function countTotalExp(param1:int) : int
      {
         return Math.pow(param1,3) * 1.2 + 80 * (param1 + 1);
      }
      
      override public function setBodyEquip(param1:GameEquipVO, param2:int) : void
      {
         if(param2 < 6 || param2 > 9)
         {
            return;
         }
         if(param1)
         {
            param1.owner = this;
         }
         switch(param2)
         {
            case 6:
               this.shoukui = param1;
               break;
            case 7:
               this.shouzhua = param1;
               break;
            case 8:
               this.shoujia = param1;
               break;
            case 9:
               this.shouzhui = param1;
         }
      }
      
      override public function get battleRate() : int
      {
         return this.ownHP * 0.2 + ownATK * 3 + this.ownPDEF * 2 + this.ownMDEF * 2 + ownCRIT * 3 + ownMISS * 3 + this.curLife.v * 2 + this.curLoyal.v * 2 + this.countColorSkill(4) * 20 + this.countColorSkill(3) * 16 + this.countColorSkill(2) * 12 + this.countColorSkill(1) * 8 + this.countColorSkill(0) * 4 + (this.curTalent.v + this.curLearn.v + this.curWit.v) * 15;
      }
      
      override public function updateSkill() : void
      {
      }
      
      public function updateExtra(param1:Boolean = false) : void
      {
         var _loc2_:PetSkillVO = null;
         extraATK.v = 0;
         extraPDEF.v = 0;
         extraMDEF.v = 0;
         for each(_loc2_ in this.passiveSkills)
         {
            if(_loc2_ is PetSkillManLiVO)
            {
               extraATK.v += (_loc2_ as PetSkillManLiVO).addATK;
            }
            else if(_loc2_ is PetSkillJianRenVO)
            {
               extraPDEF.v += (_loc2_ as PetSkillJianRenVO).addPDEF;
            }
            else if(_loc2_ is PetSkillRenXingVO)
            {
               extraMDEF.v += (_loc2_ as PetSkillRenXingVO).addMDEF;
            }
         }
         if(param1)
         {
            updateAll();
         }
      }
      
      override public function get ownHP() : int
      {
         var _loc1_:int = baseHP.v + equipHP.v + extraHP.v;
         if(perHP.v != 0)
         {
            _loc1_ += _loc1_ * perHP.v * ConstData.DATA_NUM001.v;
         }
         return _loc1_;
      }
      
      override public function get ownPDEF() : int
      {
         var _loc1_:int = basePDEF.v + equipPDEF.v + extraPDEF.v;
         if(perPDEF.v != 0)
         {
            _loc1_ += _loc1_ * perPDEF.v * ConstData.DATA_NUM001.v;
         }
         return _loc1_;
      }
      
      override public function get ownMDEF() : int
      {
         var _loc1_:int = baseMDEF.v + equipMDEF.v + extraMDEF.v;
         if(perMDEF.v != 0)
         {
            _loc1_ += _loc1_ * perMDEF.v * ConstData.DATA_NUM001.v;
         }
         return _loc1_;
      }
      
      public function handlerDead() : void
      {
         this.curLife.v -= MathUtil.randomNum(1,2);
         if(this.curLife.v <= 0)
         {
            this.curLife.v = 0;
         }
         this.curLoyal.v -= MathUtil.randomNum(1,3);
         if(this.curLoyal.v <= 0)
         {
            this.curLoyal.v = 0;
         }
      }
      
      override public function getEquipList() : Array
      {
         return [this.shoukui,this.shouzhua,this.shoujia,this.shouzhui];
      }
      
      override public function get bodySkin() : MovieClip
      {
         return AssetManager.newMCRes(assetVO.bodySkin + this.curEvolve.v);
      }
      
      public function get nextBodySkin() : MovieClip
      {
         return AssetManager.newMCRes(assetVO.bodySkin + (this.curEvolve.v + 1));
      }
      
      public function countMaxTalent(param1:int) : int
      {
         return this.petCount.findBase(param1).v * this.curGrow.v * this.curTalent.v * 0.04;
      }
      
      public function countMinTalent(param1:int) : int
      {
         return this.countMaxTalent(param1) * 0.7;
      }
      
      public function countColorSkill(param1:int) : int
      {
         var _loc4_:PetSkillVO = null;
         var _loc2_:int = 0;
         var _loc3_:Array = this.activeSkills.concat(this.passiveSkills);
         for each(_loc4_ in _loc3_)
         {
            if(_loc4_)
            {
               if(_loc4_.curQuality.v == param1)
               {
                  _loc2_++;
               }
            }
         }
         return 0;
      }
      
      override public function get isMaxLevel() : Boolean
      {
         return level.v >= this._maxLevels.findElement(this.curEvolve.v);
      }
      
      public function get curMaxLevel() : int
      {
         return this._maxLevels.findElement(this.curEvolve.v);
      }
      
      public function get isATK() : Boolean
      {
         if(this.curLoyal.v <= 0)
         {
            return false;
         }
         return MathUtil.checkOdds(300 + this.curLoyal.v * 7);
      }
      
      public function get qualityName() : String
      {
         var _loc1_:* = ConstData.PET_QUALITY[this.curQuality.v];
         if(this.mutate)
         {
            _loc1_ += "（变异）";
         }
         return TxtUtil.setColor(_loc1_,ConstData.PET_COLOR[this.curQuality.v]);
      }
      
      public function get isBattle() : Boolean
      {
         return this.ownerID != 0;
      }
      
      public function get isATKRate() : Boolean
      {
         return MathUtil.checkOdds(300 + this.curLoyal.v * 7);
      }
      
      public function get bodyURL() : String
      {
         return (assetVO as PetAssetVO).mcURL + this.curEvolve.v + ".swf";
      }
      
      public function get iconName() : String
      {
         return assetVO.iname + this.curEvolve.v;
      }
      
      public function get petName() : String
      {
         if(this.curName == "")
         {
            this.curName = assetVO.name;
         }
         return TxtUtil.setColor(this.curName,ConstData.PET_COLOR[this.curQuality.v]);
      }
      
      public function get hasEquip() : Boolean
      {
         var _loc2_:GameEquipVO = null;
         var _loc1_:Array = this.getEquipList();
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_ != null)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get mutate() : Boolean
      {
         return this.isMutate.v == 1;
      }
      
      public function findTalent(param1:int) : int
      {
         return this.talents[param1].v;
      }
      
      public function isMaxTalent(param1:int) : Boolean
      {
         return this.talents[param1].v >= this.countMaxTalent(param1);
      }
      
      public function get isAllMaxTalent() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < 4)
         {
            if(!this.isMaxTalent(_loc1_))
            {
               return false;
            }
            _loc1_++;
         }
         return true;
      }
      
      public function findPassiveVO(param1:int) : PetSkillVO
      {
         var _loc2_:PetSkillVO = null;
         for each(_loc2_ in this.passiveSkills)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findActiveInAll(param1:int) : PetSkillVO
      {
         var _loc2_:int = int(allSkills.length);
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_)
         {
            if(allSkills[_loc3_].id.v == param1)
            {
               return allSkills[_loc3_];
            }
            _loc3_++;
         }
         return null;
      }
      
      public function newActiveSkills() : Array
      {
         return [];
      }
      
      public function get width() : int
      {
         return this.petConst.parseWidth(this.curEvolve.v);
      }
      
      public function get height() : int
      {
         return this.petConst.parseHeight(this.curEvolve.v);
      }
      
      override public function parseLoadData(param1:String) : void
      {
         var _loc2_:Array = param1.split("H");
         this.ownerID = int(_loc2_[1]);
         this.curName = _loc2_[2];
         level.v = int(_loc2_[3]);
         curEXP.v = int(_loc2_[4]);
         this.isMutate.v = int(_loc2_[5]);
         this.curEvolve.v = int(_loc2_[6]);
         this.curQuality.v = int(_loc2_[7]);
         this.curGrow.v = int(_loc2_[8]);
         this.curWit.v = int(_loc2_[9]);
         this.curLearn.v = int(_loc2_[10]);
         this.curTalent.v = int(_loc2_[11]);
         this.curLife.v = int(_loc2_[12]);
         this.curLoyal.v = int(_loc2_[13]);
         this.talentHP.v = int(_loc2_[14]);
         this.talentATK.v = int(_loc2_[15]);
         this.talentPDEF.v = int(_loc2_[16]);
         this.talentMDEF.v = int(_loc2_[17]);
         this.skillPoint.v = int(_loc2_[18]);
         parseEquipData(_loc2_[19]);
         parseSkillData(_loc2_[20]);
         this.parsePassiveSkill(_loc2_[21]);
         this.parseActiveSkill(_loc2_[22]);
         this.setLevel(level.v,false);
         updateEquip(false);
         updateAll();
      }
      
      override public function collectSaveData() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = id.v;
         _loc1_[1] = this.ownerID;
         _loc1_[2] = this.curName;
         _loc1_[3] = level.v;
         _loc1_[4] = curEXP.v;
         _loc1_[5] = this.isMutate.v;
         _loc1_[6] = this.curEvolve.v;
         _loc1_[7] = this.curQuality.v;
         _loc1_[8] = this.curGrow.v;
         _loc1_[9] = this.curWit.v;
         _loc1_[10] = this.curLearn.v;
         _loc1_[11] = this.curTalent.v;
         _loc1_[12] = this.curLife.v;
         _loc1_[13] = this.curLoyal.v;
         _loc1_[14] = this.talentHP.v;
         _loc1_[15] = this.talentATK.v;
         _loc1_[16] = this.talentPDEF.v;
         _loc1_[17] = this.talentMDEF.v;
         _loc1_[18] = this.skillPoint.v;
         _loc1_[19] = collectEquipData();
         _loc1_[20] = collectSkillData();
         _loc1_[21] = this.collectPassiveSkill();
         _loc1_[22] = this.collectActiveSKill();
         return _loc1_.join("H");
      }
      
      protected function collectPassiveSkill() : String
      {
         var _loc2_:BaseSkillVO = null;
         var _loc1_:Array = [];
         var _loc3_:int = 0;
         while(_loc3_ < 5)
         {
            _loc2_ = this.passiveSkills[_loc3_];
            if(!_loc2_)
            {
               _loc1_[_loc3_] = "none";
            }
            else
            {
               _loc1_[_loc3_] = _loc2_.collectSaveData();
            }
            _loc3_++;
         }
         return _loc1_.join("P");
      }
      
      protected function parsePassiveSkill(param1:String) : void
      {
         var _loc3_:String = null;
         var _loc5_:Array = null;
         var _loc6_:BaseSkillVO = null;
         this.passiveSkills = new Array(5);
         var _loc2_:Array = param1.split("P");
         var _loc4_:int = 0;
         while(_loc4_ < 5)
         {
            _loc3_ = _loc2_[_loc4_];
            if(_loc3_ != "none")
            {
               _loc5_ = _loc3_.split("A");
               _loc6_ = PetSkillConfig.instance().newPassive(int(_loc5_[0]),this);
               _loc6_.parseLoadData(_loc5_);
               this.passiveSkills[_loc4_] = _loc6_;
            }
            _loc4_++;
         }
      }
      
      protected function collectActiveSKill() : String
      {
         var _loc2_:BaseSkillVO = null;
         var _loc1_:Array = [];
         var _loc3_:int = 0;
         while(_loc3_ < 4)
         {
            _loc2_ = this.activeSkills[_loc3_];
            if(!_loc2_)
            {
               _loc1_[_loc3_] = "none";
            }
            else
            {
               _loc1_[_loc3_] = _loc2_.id.v;
            }
            _loc3_++;
         }
         return _loc1_.join("Q");
      }
      
      protected function parseActiveSkill(param1:String) : void
      {
         var _loc3_:String = null;
         this.activeSkills = new Array(4);
         var _loc2_:Array = param1.split("Q");
         var _loc4_:int = 0;
         while(_loc4_ < 4)
         {
            _loc3_ = _loc2_[_loc4_];
            if(_loc3_ != "none")
            {
               this.activeSkills[_loc4_] = findSkill(int(_loc3_));
            }
            _loc4_++;
         }
      }
   }
}

