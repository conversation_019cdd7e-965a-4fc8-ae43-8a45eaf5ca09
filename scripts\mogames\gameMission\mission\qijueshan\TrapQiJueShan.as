package mogames.gameMission.mission.qijueshan
{
   import Box2D.Dynamics.Contacts.b2Contact;
   import Box2D.Dynamics.b2Body;
   import citrus.objects.Box2DPhysicsObject;
   import citrus.physics.PhysicsCollisionCategories;
   import citrus.physics.box2d.Box2DUtils;
   import citrus.physics.box2d.IBox2DPhysicsObject;
   import flash.display.Bitmap;
   import mogames.Layers;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.hero.BaseHero;
   import utils.TxtUtil;
   
   public class TrapQiJueShan extends Box2DPhysicsObject
   {
      private var _trapPer:int;
      
      private var _type:int;
      
      private var _hitSkin:String;
      
      private var _hitSound:String;
      
      public function TrapQiJueShan(param1:Bitmap)
      {
         _beginContactCallEnabled = true;
         super("TrapQiJueShan",{
            "width":param1.width,
            "height":param1.height,
            "group":2
         });
         this.view = param1;
      }
      
      public function createHurt(param1:int, param2:int, param3:String, param4:String) : void
      {
         this._trapPer = param2;
         this._type = param1;
         this._hitSkin = param3;
         this._hitSound = param4;
      }
      
      override protected function defineBody() : void
      {
         super.defineBody();
         _bodyDef.type = b2Body.b2_staticBody;
      }
      
      override protected function defineFixture() : void
      {
         super.defineFixture();
         _fixtureDef.filter.categoryBits = PhysicsCollisionCategories.Get("BadGuys");
         _fixtureDef.filter.maskBits = PhysicsCollisionCategories.Get("GoodGuys");
      }
      
      override public function handleBeginContact(param1:b2Contact) : void
      {
         super.handleBeginContact(param1);
         var _loc2_:IBox2DPhysicsObject = Box2DUtils.CollisionGetOther(this,param1);
         if(_loc2_ is BaseHero)
         {
            this.handlerHitHero(_loc2_ as BaseHero);
         }
      }
      
      private function handlerHitHero(param1:BaseHero) : void
      {
         if(param1.isDead || !param1.visible)
         {
            return;
         }
         if(this._type == 0)
         {
            param1.roleVO.changeHP(-param1.roleVO.totalHP.v * this._trapPer * 0.01);
            EffectManager.instance().addHeadWord(TxtUtil.setColor("生命减少" + this._trapPer + "%","ff0000"),param1.x,param1.y - param1.height);
         }
         else if(this._type == 1)
         {
            param1.roleVO.changeMP(-param1.roleVO.totalMP.v * this._trapPer * 0.01);
            EffectManager.instance().addHeadWord(TxtUtil.setColor("法力减少" + this._trapPer + "%","00ffff"),param1.x,param1.y - param1.height);
         }
         if(param1.isDead)
         {
            param1.handlerDead(0);
         }
         else
         {
            param1.handlerHurtAnim(1);
         }
         param1.addHurtFlyEffect(-param1.dirX,10,1);
         EffectManager.instance().addBMCEffect(this._hitSkin,Layers.ceEffectLayer,param1.x,param1.y);
         EffectManager.instance().playAudio(this._hitSound);
      }
   }
}

