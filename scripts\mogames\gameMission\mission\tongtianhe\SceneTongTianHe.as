package mogames.gameMission.mission.tongtianhe
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.TrapPosionBeiKe;
   import mogames.gameRole.enemy.BOSSLingGanWang;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameTask.TaskStoryNpc109;
   
   public class SceneTongTianHe extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _pTrigger5:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _eTrigger4:EnemyTrigger;
      
      private var _npcStory:TaskStoryNpc109;
      
      private var _enemyArgs:Array = [{
         "id":new Sint(2020),
         "attID":new Sint(20201),
         "aiID":new Sint(20201),
         "dropID":new Sint(64)
      },{
         "id":new Sint(2021),
         "attID":new Sint(20211),
         "aiID":new Sint(20211),
         "dropID":new Sint(64)
      }];
      
      private var _beikeArg:Object = {
         "interval":new Snum(8),
         "hurt":new Sint(350),
         "keepTime":new Snum(5),
         "keepHurt":new Sint(200)
      };
      
      private var _beikePos:Array = [1342,2352,3137,3611,4048,4500];
      
      private var _traps:Array;
      
      public function SceneTongTianHe(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER0");
         sceneType = 1;
         setWinPosition(new Rectangle(4127,222,396,128),new Point(4360,440));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2030,1);
            TaskProxy.instance().addTask(11018);
         };
         _mission.cleanLoadUI();
         this.initTrap();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2030))
         {
            showDialog("STORY0054",func);
         }
      }
      
      private function initTrap() : void
      {
         var _loc2_:TrapPosionBeiKe = null;
         this._traps = [];
         var _loc1_:int = 0;
         while(_loc1_ < 6)
         {
            _loc2_ = new TrapPosionBeiKe();
            _loc2_.x = this._beikePos[_loc1_];
            _loc2_.y = 438;
            _loc2_.createHitHurt(this._beikeArg.hurt.v,0,false,1);
            _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",2,2);
            _loc2_.createOtherBuff([new BuffVO(1001,this._beikeArg.keepTime.v,{"hurt":this._beikeArg.keepHurt})]);
            _loc2_.initTimer(this._beikeArg.interval.v);
            Layers.addCEChild(_loc2_);
            this._traps.push(_loc2_);
            _loc1_++;
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[284,325,150,100],[528,325,150,100]],14011,this.handlerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[980,325,150,100],[1170,325,150,100]],14012,this.handlerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1550,325,150,100],[1950,325,150,100]],14013,this.handlerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[2136,325,150,100],[2700,325,150,100]],14014,this.triggerEnd3);
         this._eTrigger4 = new EnemyTrigger(_mission,[[2904,325,150,100],[3382,325,150,100]],14015,this.triggerEnd4);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(218,480),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(982,480),1,new Rectangle(500,0,1270,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1878,480),1,new Rectangle(1250,0,1270,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2617,480),1,new Rectangle(2122,0,1270,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(3715,480),1);
         this._pTrigger5 = new LocalTriggerX(_mission.onePlayer,new Point(4200,480),1,new Rectangle(3840,0,960,600));
         this._pTrigger1.setBlock(false,false,true,true);
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger5.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger4.trigger = this._eTrigger4;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this._pTrigger5.start;
         this._pTrigger5.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function startBattle() : void
      {
         startBossBattle("BGM_BATTLE1");
      }
      
      private function handlerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd3() : void
      {
         this.handlerEnd();
         CitrusLock.instance().lock(new Rectangle(2122,0,1740,600),false,false,true,true);
      }
      
      private function triggerEnd4() : void
      {
         this.handlerEnd();
         this.addBOSS();
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSLingGanWang();
         _boss.x = 4614;
         _boss.y = 250;
         _boss.initData(30231,30231,65);
         _boss.target = _mission.onePlayer;
         (_boss as BOSSLingGanWang).enemyList = this._enemyArgs.slice();
         add(_boss);
      }
      
      override protected function handlerWin() : void
      {
         this.stopTrap();
         TaskProxy.instance().setComplete(11018);
         if(FlagProxy.instance().isComplete(109))
         {
            this.showDrop();
         }
         else
         {
            this._npcStory = new TaskStoryNpc109();
            this._npcStory.start(this.showDrop);
         }
      }
      
      private function showDrop() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      private function stopTrap() : void
      {
         var _loc1_:TrapPosionBeiKe = null;
         for each(_loc1_ in this._traps)
         {
            _loc1_.stopTrap();
         }
      }
      
      override public function destroy() : void
      {
         this._traps = null;
         if(this._npcStory)
         {
            this._npcStory.destroy();
         }
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._eTrigger4)
         {
            this._eTrigger4.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         if(this._pTrigger5)
         {
            this._pTrigger5.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._eTrigger4 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._pTrigger5 = null;
         this._npcStory = null;
         super.destroy();
      }
   }
}

