package mogames.gameData.task.vo
{
   import file.EnemyConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   
   public class TaskEnemyVO
   {
      public var enemyID:Sint;
      
      public var curNum:Sint;
      
      private var _needNum:Sint;
      
      public function TaskEnemyVO(param1:int, param2:int)
      {
         super();
         this.enemyID = new Sint(param1);
         this._needNum = new Sint(param2);
         this.curNum = new Sint();
      }
      
      public function addNum() : void
      {
         this.curNum.v += ConstData.DATA_NUM1.v;
         if(this.curNum.v >= this._needNum.v)
         {
            this.curNum.v = this._needNum.v;
         }
      }
      
      public function get isComplete() : Boolean
      {
         return this.curNum.v >= this._needNum.v;
      }
      
      public function get infor() : String
      {
         var _loc1_:int = Math.min(this._needNum.v,this.curNum.v);
         return EnemyConfig.instance().findRoleAsset(this.enemyID.v).name + "（" + _loc1_ + "/" + this._needNum.v + "）";
      }
      
      public function clone() : TaskEnemyVO
      {
         return new TaskEnemyVO(this.enemyID.v,this._needNum.v);
      }
   }
}

