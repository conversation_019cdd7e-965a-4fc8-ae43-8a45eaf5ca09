package mogames.gameData.base
{
   import mogames.gamePKG.PKGManager;
   import utils.MathUtil;
   import utils.TxtUtil;
   
   public class Sint
   {
      private var real:String;
      
      private var hash:String;
      
      private var salt:String;
      
      private var code:String;
      
      public function Sint(param1:int = 0)
      {
         super();
         this.code = TxtUtil.newCode();
         this.v = param1;
      }
      
      public function set v(param1:int) : void
      {
         this.real = (param1 * 2 + 2).toString();
         this.salt = int(MathUtil.randomNum(10000,99999)).toString();
         this.hash = TxtUtil.hash(this.real.toString() + this.salt,this.code);
      }
      
      public function get v() : int
      {
         if(this.hash != TxtUtil.hash(this.real.toString() + this.salt,this.code))
         {
            PKGManager.handlerCheat(new Sint(1));
            return -1;
         }
         return (int(this.real) - 2) * 0.5;
      }
   }
}

