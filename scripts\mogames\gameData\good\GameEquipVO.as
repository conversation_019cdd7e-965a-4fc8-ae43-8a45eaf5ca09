package mogames.gameData.good
{
   import file.ArgConfig;
   import file.HeroConfig;
   import flash.display.BitmapData;
   import mogames.AssetManager;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.ArgVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.good.constvo.ConstEquipVO;
   import mogames.gameData.good.constvo.ConstFabaoVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameData.role.vo.HeroCountVO;
   import mogames.gameSystem.SysTimer;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.MathUtil;
   
   public class GameEquipVO extends BaseEquipVO
   {
      private static const HOUR:Sint = new Sint(3600);
      
      public var constEquip:ConstEquipVO;
      
      public var level:Sint;
      
      public var owner:HeroGameVO;
      
      public var curTime:Sint;
      
      public var totalTime:Sint;
      
      public var score:Sint;
      
      public var totalHP:Sint;
      
      public var totalMP:Sint;
      
      public var totalATK:Sint;
      
      public var totalPDEF:Sint;
      
      public var totalMDEF:Sint;
      
      public var totalCRIT:Sint;
      
      public var totalMISS:Sint;
      
      public var totalLUCK:Sint;
      
      public var isLock:int;
      
      protected var _holes:Vector.<HoleVO>;
      
      protected var _quality:ArgVO;
      
      protected var _sysTimer:SysTimer;
      
      protected var _hparg:Snum = new Snum(0.01);
      
      protected var _mparg:Snum = new Snum(0.01);
      
      protected var _atkarg:Snum = new Snum(0.03);
      
      protected var _pdefarg:Snum = new Snum(0.02);
      
      protected var _mdefarg:Snum = new Snum(0.02);
      
      protected var _critarg:Snum = new Snum(0.004);
      
      protected var _missarg:Snum = new Snum(0.004);
      
      public function GameEquipVO(param1:ConstEquipVO)
      {
         this.constEquip = param1;
         super(param1 as ConstGoodVO);
         this.level = new Sint();
         this.totalHP = new Sint();
         this.totalMP = new Sint();
         this.totalATK = new Sint();
         this.totalPDEF = new Sint();
         this.totalMDEF = new Sint();
         this.totalCRIT = new Sint();
         this.totalMISS = new Sint();
         this.totalLUCK = new Sint();
         this.curTime = new Sint();
         this.totalTime = new Sint();
         this.score = new Sint();
         this.initHoles();
         this._sysTimer = new SysTimer();
      }
      
      protected function initHoles() : void
      {
         var _loc2_:HoleVO = null;
         this._holes = new Vector.<HoleVO>();
         var _loc1_:int = 0;
         while(_loc1_ < 5)
         {
            _loc2_ = new HoleVO(_loc1_,this.constEquip.caoType.v);
            this._holes[_loc1_] = _loc2_;
            _loc1_++;
         }
      }
      
      public function initNewEquip(param1:int, param2:Boolean = false, param3:Boolean = false) : void
      {
         var _loc8_:int = 0;
         this.quality = param1;
         var _loc4_:int = int(this.constEquip.attList.length);
         var _loc5_:Sint = param2 ? new Sint(_loc4_) : this.countATTNum(param1);
         if(_loc5_.v >= _loc4_)
         {
            _loc5_.v = _loc4_;
         }
         var _loc6_:Array = this.constEquip.attList.slice();
         var _loc7_:int = 0;
         while(_loc7_ < _loc5_.v)
         {
            _loc8_ = Math.random() * _loc6_.length;
            this.setATT(_loc6_[_loc8_],param3);
            _loc6_.splice(_loc8_,1);
            _loc7_++;
         }
         this.setLevel(0);
         this.totalTime.v = this.constEquip.timeLimit.v * HOUR.v;
         this.initTimer();
      }
      
      public function initTimer() : void
      {
         var onFunc:Function = null;
         var endFunc:Function = null;
         onFunc = function():void
         {
            curTime.v += ConstData.DATA_NUM1.v;
         };
         endFunc = function():void
         {
            if(owner)
            {
               owner.updateEquip();
               MiniMsgMediator.instance().showAutoMsg(owner.assetVO.name + "身上的" + "【" + constEquip.name + "】已过期，失去属性加成！");
            }
         };
         if(this.constEquip.timeLimit.v == 0)
         {
            return;
         }
         this._sysTimer.setInterval(1,this.constEquip.timeLimit.v * HOUR.v,onFunc,endFunc);
      }
      
      protected function setATT(param1:int, param2:Boolean) : void
      {
         switch(param1)
         {
            case 0:
               baseHP.v = this.constEquip.baseHP.vo.randomValue(param2);
               break;
            case 1:
               baseMP.v = this.constEquip.baseMP.vo.randomValue(param2);
               break;
            case 2:
               baseATK.v = this.constEquip.baseATK.vo.randomValue(param2);
               break;
            case 3:
               basePDEF.v = this.constEquip.basePDEF.vo.randomValue(param2);
               break;
            case 4:
               baseMDEF.v = this.constEquip.baseMDEF.vo.randomValue(param2);
               break;
            case 5:
               baseCRIT.v = this.constEquip.baseCRIT.vo.randomValue(param2);
               break;
            case 6:
               baseMISS.v = this.constEquip.baseMISS.vo.randomValue(param2);
               break;
            case 7:
               baseLUCK.v = this.constEquip.baseLUCK.vo.randomValue(param2);
         }
         this.totalLUCK.v = baseLUCK.v;
      }
      
      public function upgradeQuality(param1:int) : void
      {
         this.quality = param1;
         this.setLevel(this.level.v);
      }
      
      public function setLevel(param1:int) : void
      {
         this.level.v = param1;
         if(baseHP.v != 0)
         {
            this.totalHP.v = this.level.v != 0 ? int(Math.ceil(baseHP.v * (ConstData.DATA_NUM1.v + Math.pow(this.level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._hparg.v) * this._quality.value.v)) : int(Math.ceil(baseHP.v * this._quality.value.v));
         }
         if(baseMP.v != 0)
         {
            this.totalMP.v = this.level.v != 0 ? int(Math.ceil(baseMP.v * (ConstData.DATA_NUM1.v + Math.pow(this.level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._mparg.v) * this._quality.value.v)) : int(Math.ceil(baseMP.v * this._quality.value.v));
         }
         if(baseATK.v != 0)
         {
            this.totalATK.v = this.level.v != 0 ? int(Math.ceil(baseATK.v * (ConstData.DATA_NUM1.v + Math.pow(this.level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._atkarg.v) * this._quality.value.v)) : int(Math.ceil(baseATK.v * this._quality.value.v));
         }
         if(basePDEF.v != 0)
         {
            this.totalPDEF.v = this.level.v != 0 ? int(Math.ceil(basePDEF.v * (ConstData.DATA_NUM1.v + Math.pow(this.level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._pdefarg.v) * this._quality.value.v)) : int(Math.ceil(basePDEF.v * this._quality.value.v));
         }
         if(baseMDEF.v != 0)
         {
            this.totalMDEF.v = this.level.v != 0 ? int(Math.ceil(baseMDEF.v * (ConstData.DATA_NUM1.v + Math.pow(this.level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._mdefarg.v) * this._quality.value.v)) : int(Math.ceil(baseMDEF.v * this._quality.value.v));
         }
         if(baseCRIT.v != 0)
         {
            this.totalCRIT.v = this.level.v != 0 ? int(Math.ceil((baseCRIT.v * (ConstData.DATA_NUM1.v + Math.pow(this.level.v,ConstData.DATA_NUM2.v) * this._critarg.v) + this.level.v) * this._quality.value.v)) : int(Math.ceil(baseCRIT.v * this._quality.value.v));
         }
         if(baseMISS.v != 0)
         {
            this.totalMISS.v = this.level.v != 0 ? int(Math.ceil((baseMISS.v * (ConstData.DATA_NUM1.v + Math.pow(this.level.v,ConstData.DATA_NUM2.v) * this._missarg.v) + this.level.v) * this._quality.value.v)) : int(Math.ceil(baseMISS.v * this._quality.value.v));
         }
         this.totalLUCK.v = baseLUCK.v;
         this.countScore();
         if(this.owner)
         {
            this.owner.updateEquip();
         }
      }
      
      public function get forgeInfor() : String
      {
         var _loc1_:int = this.level.v + 1;
         var _loc2_:Array = [];
         if(baseHP.v != 0)
         {
            _loc2_.push("生命+" + Math.ceil(baseHP.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._hparg.v) * this._quality.value.v));
         }
         if(baseMP.v != 0)
         {
            _loc2_.push("法力+" + Math.ceil(baseMP.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._mparg.v) * this._quality.value.v));
         }
         if(baseATK.v != 0)
         {
            _loc2_.push("攻击+" + Math.ceil(baseATK.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._atkarg.v) * this._quality.value.v));
         }
         if(basePDEF.v != 0)
         {
            _loc2_.push("物防+" + Math.ceil(basePDEF.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._pdefarg.v) * this._quality.value.v));
         }
         if(baseMDEF.v != 0)
         {
            _loc2_.push("魔防+" + Math.ceil(baseMDEF.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * this._mdefarg.v) * this._quality.value.v));
         }
         if(baseCRIT.v != 0)
         {
            _loc2_.push("暴击+" + Math.ceil((baseCRIT.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_,ConstData.DATA_NUM2.v) * this._critarg.v) + _loc1_) * this._quality.value.v));
         }
         if(baseMISS.v != 0)
         {
            _loc2_.push("闪避+" + Math.ceil((baseMISS.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_,ConstData.DATA_NUM2.v) * this._missarg.v) + _loc1_) * this._quality.value.v));
         }
         return _loc2_.join("<br>");
      }
      
      public function get attInfor() : String
      {
         var _loc1_:Array = [];
         if(this.totalHP.v != 0)
         {
            _loc1_.push("生命+" + this.totalHP.v);
         }
         if(this.totalMP.v != 0)
         {
            _loc1_.push("法力+" + this.totalMP.v);
         }
         if(this.totalATK.v != 0)
         {
            _loc1_.push("攻击+" + this.totalATK.v);
         }
         if(this.totalPDEF.v != 0)
         {
            _loc1_.push("物防+" + this.totalPDEF.v);
         }
         if(this.totalMDEF.v != 0)
         {
            _loc1_.push("魔防+" + this.totalMDEF.v);
         }
         if(this.totalCRIT.v != 0)
         {
            _loc1_.push("暴击+" + this.totalCRIT.v);
         }
         if(this.totalMISS.v != 0)
         {
            _loc1_.push("闪避+" + this.totalMISS.v);
         }
         return _loc1_.join("<br>");
      }
      
      public function addEquipATT() : void
      {
         var _loc1_:HoleVO = null;
         if(this.isOverdue)
         {
            return;
         }
         if(baseHP.v != 0)
         {
            if(this.constEquip.baseHP.isPer)
            {
               this.owner.perHP.v += this.totalHP.v;
            }
            else
            {
               this.owner.equipHP.v += this.totalHP.v;
            }
         }
         if(baseMP.v != 0)
         {
            if(this.constEquip.baseMP.isPer)
            {
               this.owner.perMP.v += this.totalMP.v;
            }
            else
            {
               this.owner.equipMP.v += this.totalMP.v;
            }
         }
         if(baseATK.v != 0)
         {
            if(this.constEquip.baseATK.isPer)
            {
               this.owner.perATK.v += this.totalATK.v;
            }
            else
            {
               this.owner.equipATK.v += this.totalATK.v;
            }
         }
         if(basePDEF.v != 0)
         {
            if(this.constEquip.basePDEF.isPer)
            {
               this.owner.perPDEF.v += this.totalPDEF.v;
            }
            else
            {
               this.owner.equipPDEF.v += this.totalPDEF.v;
            }
         }
         if(baseMDEF.v != 0)
         {
            if(this.constEquip.baseMDEF.isPer)
            {
               this.owner.perMDEF.v += this.totalMDEF.v;
            }
            else
            {
               this.owner.equipMDEF.v += this.totalMDEF.v;
            }
         }
         this.owner.equipMISS.v += this.totalMISS.v;
         this.owner.equipCRIT.v += this.totalCRIT.v;
         this.owner.equipLUCK.v += this.totalLUCK.v;
         for each(_loc1_ in this._holes)
         {
            if(_loc1_.attVO)
            {
               _loc1_.attVO.addEquipATT(this.owner);
            }
         }
      }
      
      public function refreshEquip() : void
      {
         if(!this.owner)
         {
            return;
         }
         this.owner.updateEquip();
         this.countScore();
      }
      
      protected function countScore() : void
      {
         var _loc2_:HoleVO = null;
         this.score.v = 0;
         var _loc1_:HeroCountVO = HeroConfig.instance().findRoleCount(this.constEquip.roleLimit.v);
         if(baseHP.v != 0)
         {
            if(this.constEquip.baseHP.isPer)
            {
               this.score.v += this.totalHP.v * 50;
            }
            else if(_loc1_ != null)
            {
               this.score.v += this.totalHP.v / _loc1_.argHP.v;
            }
            else
            {
               this.score.v += this.totalHP.v * 0.2;
            }
         }
         if(baseMP.v != 0)
         {
            if(this.constEquip.baseMP.isPer)
            {
               this.score.v += this.totalMP.v * 50;
            }
            else if(_loc1_ != null)
            {
               this.score.v += this.totalMP.v / _loc1_.argMP.v;
            }
            else
            {
               this.score.v += this.totalMP.v * 0.2;
            }
         }
         if(baseATK.v != 0)
         {
            if(this.constEquip.baseATK.isPer)
            {
               this.score.v += this.totalATK.v * 50;
            }
            else if(_loc1_ != null)
            {
               this.score.v += this.totalATK.v / _loc1_.argATK.v * 10;
            }
            else
            {
               this.score.v += this.totalATK.v * 2;
            }
         }
         if(basePDEF.v != 0)
         {
            if(this.constEquip.basePDEF.isPer)
            {
               this.score.v += this.totalPDEF.v * 50;
            }
            else if(_loc1_ != null)
            {
               this.score.v += this.totalPDEF.v / _loc1_.argPDEF.v * 10;
            }
            else
            {
               this.score.v += this.totalPDEF.v * 5;
            }
         }
         if(baseMDEF.v != 0)
         {
            if(this.constEquip.baseMDEF.isPer)
            {
               this.score.v += this.totalMDEF.v * 50;
            }
            else if(_loc1_ != null)
            {
               this.score.v += this.totalMDEF.v / _loc1_.argMDEF.v * 10;
            }
            else
            {
               this.score.v += this.totalMDEF.v * 5;
            }
         }
         if(baseCRIT.v != 0)
         {
            if(_loc1_ != null)
            {
               this.score.v += this.totalCRIT.v / _loc1_.baseCRIT.v * 70;
            }
            else
            {
               this.score.v += this.totalCRIT.v * 10;
            }
         }
         if(baseMISS.v != 0)
         {
            if(_loc1_ != null)
            {
               this.score.v += this.totalMISS.v / _loc1_.baseMISS.v * 70;
            }
            else
            {
               this.score.v += this.totalMISS.v * 10;
            }
         }
         if(baseLUCK.v != 0)
         {
            this.score.v += baseLUCK.v;
         }
         for each(_loc2_ in this._holes)
         {
            if(_loc2_.attVO)
            {
               this.score.v += _loc2_.attVO.score;
            }
         }
      }
      
      private function countATTNum(param1:int) : Sint
      {
         if(param1 == 0)
         {
            return new Sint(1);
         }
         if(param1 == 1)
         {
            if(MathUtil.checkOdds(600))
            {
               return new Sint(1);
            }
            return new Sint(2);
         }
         if(param1 == 2)
         {
            if(MathUtil.checkOdds(800))
            {
               return new Sint(2);
            }
            return new Sint(3);
         }
         return new Sint(3);
      }
      
      public function get isOverdue() : Boolean
      {
         if(this.constEquip.timeLimit.v == 0)
         {
            return false;
         }
         return this.curTime.v >= this.totalTime.v;
      }
      
      public function get timeInfor() : String
      {
         if(this.constEquip.timeLimit.v == 0)
         {
            return "";
         }
         if(this.isOverdue)
         {
            return "已过期";
         }
         var _loc1_:int = this.totalTime.v - this.curTime.v;
         var _loc2_:int = _loc1_ / 3600;
         var _loc3_:int = (_loc1_ - _loc2_ * 3600) / 60;
         if(_loc2_ > 0)
         {
            return _loc2_ + "时" + _loc3_ + "分后过期";
         }
         return _loc3_ + "分后过期";
      }
      
      public function get roleLimit() : int
      {
         return this.constEquip.roleLimit.v;
      }
      
      override public function get quality() : int
      {
         return this._quality.id.v;
      }
      
      public function set quality(param1:int) : void
      {
         this._quality = ArgConfig.instance().findQuality(param1,this.isFashion);
      }
      
      public function get isFabao() : Boolean
      {
         return this.constEquip is ConstFabaoVO;
      }
      
      public function get isPetEquip() : Boolean
      {
         return this.constEquip.bodyPart >= 6;
      }
      
      public function get isWeapon() : Boolean
      {
         return this.constEquip.bodyPart == 0 || this.constEquip.bodyPart == 4;
      }
      
      public function get isCloth() : Boolean
      {
         return this.constEquip.bodyPart == 1 || this.constEquip.bodyPart == 5;
      }
      
      public function get isFashion() : Boolean
      {
         return this.constEquip.bodyPart == 4 || this.constEquip.bodyPart == 5;
      }
      
      public function get isAllMax() : Boolean
      {
         if(baseHP.v != 0 && baseHP.v < this.constEquip.baseHP.vo.max.v)
         {
            return false;
         }
         if(baseMP.v != 0 && baseMP.v < this.constEquip.baseMP.vo.max.v)
         {
            return false;
         }
         if(baseATK.v != 0 && baseATK.v < this.constEquip.baseATK.vo.max.v)
         {
            return false;
         }
         if(basePDEF.v != 0 && basePDEF.v < this.constEquip.basePDEF.vo.max.v)
         {
            return false;
         }
         if(baseMDEF.v != 0 && baseMDEF.v < this.constEquip.baseMDEF.vo.max.v)
         {
            return false;
         }
         if(baseCRIT.v != 0 && baseCRIT.v < this.constEquip.baseCRIT.vo.max.v)
         {
            return false;
         }
         if(baseMISS.v != 0 && baseMISS.v < this.constEquip.baseMISS.vo.max.v)
         {
            return false;
         }
         if(baseLUCK.v != 0 && baseLUCK.v < this.constEquip.baseLUCK.vo.max.v)
         {
            return false;
         }
         return true;
      }
      
      override public function get icon() : BitmapData
      {
         return AssetManager.findPicRes(constVO.iname);
      }
      
      public function findHoleVO(param1:int) : HoleVO
      {
         var _loc2_:HoleVO = null;
         for each(_loc2_ in this._holes)
         {
            if(_loc2_.index.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get hasExtra() : Boolean
      {
         var _loc1_:HoleVO = null;
         for each(_loc1_ in this._holes)
         {
            if(_loc1_.attVO != null)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get isCheat() : Boolean
      {
         if(baseHP.v != 0 && baseHP.v > this.constEquip.baseHP.vo.max.v)
         {
            return true;
         }
         if(baseMP.v != 0 && baseMP.v > this.constEquip.baseMP.vo.max.v)
         {
            return true;
         }
         if(baseATK.v != 0 && baseATK.v > this.constEquip.baseATK.vo.max.v)
         {
            return true;
         }
         if(basePDEF.v != 0 && basePDEF.v > this.constEquip.basePDEF.vo.max.v)
         {
            return true;
         }
         if(baseMDEF.v != 0 && baseMDEF.v > this.constEquip.baseMDEF.vo.max.v)
         {
            return true;
         }
         if(baseCRIT.v != 0 && baseCRIT.v > this.constEquip.baseCRIT.vo.max.v)
         {
            return true;
         }
         if(baseMISS.v != 0 && baseMISS.v > this.constEquip.baseMISS.vo.max.v)
         {
            return true;
         }
         if(baseLUCK.v != 0 && baseLUCK.v > this.constEquip.baseLUCK.vo.max.v)
         {
            return true;
         }
         return false;
      }
      
      override public function destroy() : void
      {
         this._sysTimer.destroy();
         this._sysTimer = null;
         this._quality = null;
         this.constEquip = null;
         this.owner = null;
         super.destroy();
      }
      
      override public function parseLoadData(param1:Array) : void
      {
         if(param1[7])
         {
            this.isLock = int(param1[7]);
         }
         var _loc2_:Array = param1[6].split("C");
         this.findHoleVO(0).parseLoadData(_loc2_[0]);
         this.findHoleVO(1).parseLoadData(_loc2_[1]);
         this.findHoleVO(2).parseLoadData(_loc2_[2]);
         this.findHoleVO(3).parseLoadData(_loc2_[3]);
         this.findHoleVO(4).parseLoadData(_loc2_[4]);
         var _loc3_:Array = param1[5].split("A");
         baseHP.v = int(_loc3_[0]);
         baseMP.v = int(_loc3_[1]);
         baseATK.v = int(_loc3_[2]);
         basePDEF.v = int(_loc3_[3]);
         baseMDEF.v = int(_loc3_[4]);
         baseCRIT.v = int(_loc3_[5]);
         baseMISS.v = int(_loc3_[6]);
         baseLUCK.v = int(_loc3_[7]);
         this.totalTime.v = int(param1[4]);
         this.curTime.v = int(param1[3]);
         this.quality = int(param1[2]);
         this.setLevel(int(param1[1]));
         this.initTimer();
      }
      
      override public function collectSaveData() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.constEquip.id.v;
         _loc1_[1] = this.level.v;
         _loc1_[2] = this._quality.id.v;
         _loc1_[3] = this.curTime.v;
         _loc1_[4] = this.totalTime.v;
         _loc1_[5] = [baseHP.v,baseMP.v,baseATK.v,basePDEF.v,baseMDEF.v,baseCRIT.v,baseMISS.v,baseLUCK.v].join("A");
         _loc1_[6] = this.collectHole();
         _loc1_[7] = this.isLock;
         return _loc1_.join("B");
      }
      
      private function collectHole() : String
      {
         var _loc2_:HoleVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._holes)
         {
            _loc1_.push(_loc2_.collectSaveData());
         }
         return _loc1_.join("C");
      }
   }
}

