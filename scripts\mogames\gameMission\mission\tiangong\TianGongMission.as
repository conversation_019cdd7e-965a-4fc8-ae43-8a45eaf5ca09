package mogames.gameMission.mission.tiangong
{
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameUI.battle.BattleHeroTopModule;
   import mogames.gameUI.battle.BattleMenuModule;
   import mogames.gameUI.battle.BossHPManager;
   import mogames.gameUI.battle.HeroHitModule;
   import mogames.gameUI.battle.HeroLeftModule;
   import mogames.gameUI.battle.HeroRightModule;
   import mogames.gameUI.battle.HeroManager;
   
   public class TianGongMission extends BaseMission
   {
      public function TianGongMission()
      {
         super();
      }
      
      override protected function layoutUI() : void
      {
         HeroLeftModule.instance().init();
         if(HeroManager.isDouble)
         {
            HeroRightModule.instance().init();
         }
         BattleMenuModule.instance().init(null);
         BattleHeroTopModule.instance().init();
         HeroHitModule.instance().init();
         BossHPManager.instance().init();
      }
   }
}
