package mogames.gameMission.mission.union.qiongqi
{
   import citrus.objects.CitrusSprite;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   
   public class TrapQQHeiDong extends CitrusSprite implements IRole
   {
      private var _mc:CitrusMC;
      
      private var _hitTargets:Array = [];
      
      private var _hitVO:BaseHitVO;
      
      public function TrapQQHeiDong()
      {
         super("TrapQQHeiDong",{
            "width":100,
            "height":100,
            "group":2
         });
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("TRAP_QIONG_QI_HEI_DONG"),this.onComplete,this.updateFrame);
         this._mc.changeAnimation("attack",false);
         this.view = this._mc;
      }
      
      public function createHitEffect(param1:String, param2:String, param3:int, param4:int) : void
      {
         this._hitVO.hurtSkin = param1;
         this._hitVO.hurtSound = param2;
         this._hitVO.hurtX = param3;
         this._hitVO.hurtY = param4;
      }
      
      public function createHitHurt(param1:int, param2:Number, param3:Boolean, param4:int = 1, param5:int = 0) : void
      {
         this._hitVO.hurt.v = param1;
         this._hitVO.hurtTime.v = param2;
         this._hitVO.atkType.v = param4;
         this._hitVO.WU_XING.v = param5;
      }
      
      private function updateFrame(param1:int) : void
      {
         if(!this._mc.mc.mcAreaATK)
         {
            this.cleanTargets();
            return;
         }
         this._hitVO.range = this._mc.mc.mcAreaATK;
         BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
      }
      
      private function onComplete(param1:String) : void
      {
         if(param1 == "attack")
         {
            kill = true;
         }
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return this._hitTargets.indexOf(param1) != -1;
      }
      
      public function addTargets(param1:IRole) : void
      {
         this._hitTargets[this._hitTargets.length] = param1;
      }
      
      public function cleanTargets() : void
      {
         this._hitTargets.length = 0;
      }
      
      public function hitBack() : void
      {
      }
      
      override public function destroy() : void
      {
         this._mc.destroy();
         this._mc = null;
         this._hitVO = null;
         super.destroy();
      }
   }
}

