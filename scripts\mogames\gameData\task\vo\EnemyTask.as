package mogames.gameData.task.vo
{
   import mogames.gameData.task.constvo.ConstTask;
   
   public class EnemyTask extends BaseTask
   {
      public var needList:Array;
      
      public function EnemyTask(param1:ConstTask)
      {
         super(param1);
         this.initNeed();
      }
      
      private function initNeed() : void
      {
         var _loc1_:TaskEnemyVO = null;
         this.needList = [];
         for each(_loc1_ in constTask.needList)
         {
            this.needList.push(_loc1_.clone());
         }
      }
      
      public function updateEnemy(param1:int) : void
      {
         var _loc2_:TaskEnemyVO = null;
         for each(_loc2_ in this.needList)
         {
            if(_loc2_.enemyID.v == param1)
            {
               _loc2_.addNum();
            }
         }
         this.checkComplete();
      }
      
      override public function checkComplete() : void
      {
         var _loc1_:TaskEnemyVO = null;
         for each(_loc1_ in this.needList)
         {
            if(!_loc1_.isComplete)
            {
               return;
            }
         }
         setComplete();
      }
      
      override public function get targetStr() : String
      {
         var _loc2_:TaskEnemyVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.needList)
         {
            _loc1_.push(_loc2_.infor);
         }
         return "消灭：" + _loc1_.join("，");
      }
      
      private function findEnemyVO(param1:int) : TaskEnemyVO
      {
         var _loc2_:TaskEnemyVO = null;
         for each(_loc2_ in this.needList)
         {
            if(_loc2_.enemyID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      override public function get saveData() : String
      {
         var _loc2_:TaskEnemyVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.needList)
         {
            _loc1_.push(_loc2_.enemyID.v + "B" + _loc2_.curNum.v);
         }
         return [constTask.id.v,_loc1_.join("A"),_flag.v,_isGet.v].join("H");
      }
      
      override public function set loadData(param1:Array) : void
      {
         var _loc4_:TaskEnemyVO = null;
         var _loc5_:* = null;
         var _loc2_:Array = param1[1].split("A");
         var _loc3_:Array = [];
         for each(_loc5_ in _loc2_)
         {
            _loc3_ = _loc5_.split("B");
            _loc4_ = this.findEnemyVO(int(_loc3_[0]));
            if(_loc4_)
            {
               _loc4_.curNum.v = int(_loc3_[1]);
            }
         }
         _flag.v = int(param1[2]);
         if(param1[3])
         {
            _isGet.v = int(param1[3]);
         }
      }
   }
}

