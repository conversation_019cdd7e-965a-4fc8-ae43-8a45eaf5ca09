package citrus.core
{
   import citrus.datastructures.PoolObject;
   import citrus.input.Input;
   import citrus.system.Entity;
   import citrus.view.ACitrusView;
   import citrus.view.spriteview.SpriteView;
   import flash.display.Sprite;
   
   public class State extends Sprite implements IState
   {
      protected var _ce:CitrusEngine;
      
      protected var _realState:MediatorState;
      
      protected var _input:Input;
      
      public function State()
      {
         super();
         this._ce = CitrusEngine.getInstance();
         this._realState = new MediatorState(this);
      }
      
      public function destroy() : void
      {
         this._realState.destroy();
         this._realState = null;
      }
      
      public function get view() : ACitrusView
      {
         if(!this._realState)
         {
            return null;
         }
         return this._realState.view;
      }
      
      public function initialize() : void
      {
         this._realState.view = this.createView();
         this._input = this._ce.input;
      }
      
      public function update(param1:Number) : void
      {
         this._realState.update(param1);
      }
      
      public function add(param1:CitrusObject) : CitrusObject
      {
         return this._realState.add(param1);
      }
      
      public function addEntity(param1:Entity) : Entity
      {
         return this._realState.addEntity(param1);
      }
      
      public function addPoolObject(param1:PoolObject) : PoolObject
      {
         return this._realState.addPoolObject(param1);
      }
      
      public function remove(param1:CitrusObject) : void
      {
         this._realState.remove(param1);
      }
      
      public function removeImmediately(param1:CitrusObject) : void
      {
         this._realState.removeImmediately(param1);
      }
      
      public function getObjectByName(param1:String) : CitrusObject
      {
         return this._realState.getObjectByName(param1);
      }
      
      public function getObjectsByName(param1:String) : Vector.<CitrusObject>
      {
         return this._realState.getObjectsByName(param1);
      }
      
      public function getFirstObjectByType(param1:Class) : CitrusObject
      {
         return this._realState.getFirstObjectByType(param1);
      }
      
      public function getObjectsByType(param1:Class) : Vector.<CitrusObject>
      {
         return this._realState.getObjectsByType(param1);
      }
      
      public function killAllObjects(... rest) : void
      {
         this._realState.killAllObjects(rest);
      }
      
      public function get objects() : Vector.<CitrusObject>
      {
         return this._realState.objects;
      }
      
      protected function createView() : ACitrusView
      {
         return new SpriteView(this);
      }
   }
}

