package mogames.gameData.FJCJ
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.forge.NeedVO;
   
   public class ZBCJVO extends BaseUseVO
   {
      public var id:Sint;
      
      public var stone:NeedVO;
      
      public function ZBCJVO(param1:int, param2:int, param3:NeedVO)
      {
         this.id = new Sint(param1);
         this.stone = param3;
         super(param2,[param3]);
      }
   }
}

