package mogames.gameData.rank
{
   import mogames.gameData.role.HeroGameVO;
   
   public class RankHPHandler extends RankBRHandler
   {
      public function RankHPHandler()
      {
         super();
         rankIDs = [1707,1708,1709,1710,1711];
         _msg = "初始化生命排行榜";
      }
      
      override protected function newScore(param1:HeroGameVO) : int
      {
         return param1.ownHP;
      }
   }
}

