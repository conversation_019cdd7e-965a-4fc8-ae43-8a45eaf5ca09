package mogames.gameMission.co
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.WinEffect;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BaseBOSS;
   import mogames.gameSystem.CitrusTimer;
   
   public class BossScene extends BaseScene
   {
      protected var _boss:BaseBOSS;
      
      protected var _dropRect:Rectangle;
      
      protected var _doorPos:Point;
      
      protected var _isWin:Boolean;
      
      protected var _winTimer:CitrusTimer;
      
      public function BossScene(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         this._isWin = false;
         this._winTimer = new CitrusTimer();
      }
      
      protected function addBOSS() : void
      {
      }
      
      protected function startBossBattle(param1:String = "") : void
      {
         setHeroEnable(true);
         this._boss.activeEnemy(false);
         this._boss.aiEnabled = true;
         this._boss.onRole.add(this.listenBOSS);
         EffectManager.instance().playBGM(param1);
      }
      
      protected function listenBOSS(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         Layers.lockGame = true;
         EffectManager.instance().stopAllSound();
         var _loc2_:WinEffect = new WinEffect();
         _loc2_.start();
         this._winTimer.setTimeOut(3,this.handlerWin);
         this._isWin = true;
      }
      
      protected function handlerWin() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._boss,
            "rect":this._dropRect,
            "hero":this._boss.target
         });
         Layers.lockGame = false;
         addLeaveDoor(this._doorPos.x,this._doorPos.y);
      }
      
      protected function setWinPosition(param1:Rectangle, param2:Point) : void
      {
         this._dropRect = param1;
         this._doorPos = param2;
      }
      
      override public function destroy() : void
      {
         this._winTimer.destroy();
         this._winTimer = null;
         this._boss = null;
         this._dropRect = null;
         super.destroy();
      }
   }
}

