package mogames.gameData.rank
{
   import mogames.gameData.rank.vo.MyRankVO;
   import mogames.gamePKG.GameChecker;
   import mogames.gamePKG.PKGManager;
   import mogames.gameUI.prompt.MsgMediator;
   import unit4399.Open4399Rank;
   
   public class RankHandler
   {
      public var rankIDs:Array;
      
      public var myRanks:Array;
      
      protected var _msg:String;
      
      public function RankHandler(param1:Array)
      {
         super();
         this.rankIDs = param1;
      }
      
      public function parseRank(param1:Array) : Array
      {
         return [];
      }
      
      public function submitRank(param1:Function) : void
      {
         var func:Function = null;
         var okFunc:Function = param1;
         func = function(param1:Array):void
         {
            MsgMediator.clean();
            decodeMyRank(param1);
            if(okFunc != null)
            {
               okFunc();
            }
         };
         if(new GameChecker().isCheat)
         {
            if(okFunc != null)
            {
               okFunc();
            }
            return;
         }
         MsgMediator.instance().show(this._msg);
         Open4399Rank.submitScoreToRankLists(PKGManager.curIndex.v,this.rankData,func);
      }
      
      protected function get rankData() : Array
      {
         return [];
      }
      
      private function decodeMyRank(param1:Array) : void
      {
         var _loc4_:MyRankVO = null;
         if(!param1 || param1.length <= 0)
         {
            return;
         }
         this.myRanks = [];
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         while(_loc2_ < _loc3_)
         {
            _loc4_ = new MyRankVO();
            _loc4_.rid = param1[_loc2_].rId;
            _loc4_.rank.v = param1[_loc2_].curRank;
            this.myRanks[_loc2_] = _loc4_;
            _loc2_++;
         }
      }
      
      public function findMyRank(param1:int) : int
      {
         var _loc2_:MyRankVO = null;
         if(!this.myRanks)
         {
            return 0;
         }
         for each(_loc2_ in this.myRanks)
         {
            if(_loc2_.rid == this.rankIDs[param1])
            {
               return _loc2_.rank.v;
            }
         }
         return 0;
      }
   }
}

