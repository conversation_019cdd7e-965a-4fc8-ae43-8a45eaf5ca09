package mogames.gameFabao.fabao
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameFabao.view.FabaoZhaoYaoJingView;
   import mogames.gameMission.BattleMediator;
   import mogames.gameRole.hero.BaseHero;
   
   public class FabaoZhaoYaoJing extends BaseFabao
   {
      private var _hitVO:BaseHitVO;
      
      public function FabaoZhaoYaoJing(param1:GameFabaoVO)
      {
         super(param1,38,67);
         this.createFabaoData();
         this.createVO();
         createView(new FabaoZhaoYaoJingView());
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(15),
            "hurt":new Sint(200)
         },{
            "cdTime":new Snum(15),
            "hurt":new Sint(270)
         },{
            "cdTime":new Snum(15),
            "hurt":new Sint(336)
         },{
            "cdTime":new Snum(15),
            "hurt":new Sint(453)
         },{
            "cdTime":new Snum(15),
            "hurt":new Sint(583)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      private function createVO() : void
      {
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._hitVO.HURT_X = 0;
         this._hitVO.HURT_Y = 0;
         this._hitVO.HURT_SKIN = "EFFECT_GUN_ATK";
         this._hitVO.HURT_SOUND = "GUNHURT";
         this._hitVO.WU_XING.v = _fabaoVO.constFabao.wuxing.v;
      }
      
      override public function followTarget() : void
      {
         if(_fabaoView.isSkill)
         {
            return;
         }
         super.followTarget();
      }
      
      override public function handlerSkill() : void
      {
         this.y = owner.y;
         this.x = owner.x + owner.width * owner.dirX;
         super.handlerSkill();
      }
      
      public function dispatchSkillOne() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         this._hitVO.init(_fabaoView.mcATK,_fabaoData.hurt.v,1,false);
         if(owner is BaseHero)
         {
            BattleMediator.instance().onHeroATK.dispatch(this._hitVO);
         }
         else
         {
            BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
         }
      }
   }
}

