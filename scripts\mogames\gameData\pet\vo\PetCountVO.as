package mogames.gameData.pet.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   
   public class PetCountVO
   {
      public var petID:Sint;
      
      public var baseHP:Sint;
      
      public var baseATK:Sint;
      
      public var basePDEF:Sint;
      
      public var baseMDEF:Sint;
      
      public var baseCRIT:Sint;
      
      public var baseMISS:Sint;
      
      public var baseMOVE:Snum;
      
      public function PetCountVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:int, param7:int, param8:Number)
      {
         super();
         this.petID = new Sint(param1);
         this.baseHP = new Sint(param2);
         this.baseATK = new Sint(param3);
         this.basePDEF = new Sint(param4);
         this.baseMDEF = new Sint(param5);
         this.baseCRIT = new Sint(param6);
         this.baseMISS = new Sint(param7);
         this.baseMOVE = new Snum(param8);
      }
      
      public function findBase(param1:int) : Sint
      {
         switch(param1)
         {
            case 0:
               return this.baseHP;
            case 1:
               return this.baseATK;
            case 2:
               return this.basePDEF;
            case 3:
               return this.baseMDEF;
            default:
               return null;
         }
      }
   }
}

