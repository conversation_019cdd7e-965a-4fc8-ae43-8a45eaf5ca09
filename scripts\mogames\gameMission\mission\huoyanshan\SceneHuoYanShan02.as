package mogames.gameMission.mission.huoyanshan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneHuoYanShan02 extends BaseScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _moveBG:CitrusMCSprite;
      
      public function SceneHuoYanShan02(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.initEnemy1();
         this._moveBG = new CitrusMCSprite("MC_HUO_YAN_SHAN_BG_CLIP",{
            "width":960,
            "height":600,
            "parallaxX":0,
            "group":0
         });
         this._moveBG.changeAnimation("idle",true);
         add(this._moveBG);
      }
      
      private function initEnemy0() : void
      {
         if(_mission.hasMark("enemy17021"))
         {
            return;
         }
         this._eTrigger0 = new EnemyTrigger(_mission,[[1483,303,150,100],[1730,303,150,100]],17021,this.handlerEnd0);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(1460,447),1,new Rectangle(960,0,960,600));
         this._pTrigger0.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger0.start();
      }
      
      private function initEnemy1() : void
      {
         if(_mission.hasMark("enemy17022"))
         {
            return;
         }
         this._eTrigger1 = new EnemyTrigger(_mission,[[453,303,150,100],[700,303,150,100]],17022,this.handlerEnd1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(900,447),-1,new Rectangle(192,0,1000,600));
         this._pTrigger1.setBlock(false,false,true,true);
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger1.okFunc = this.handlerTrigger1;
         this._pTrigger1.start();
      }
      
      private function handlerTrigger1() : void
      {
         this._moveBG.changeAnimation("move");
      }
      
      private function handlerEnd0() : void
      {
         unlock();
         _mission.setMark("enemy17021");
      }
      
      private function handlerEnd1() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(2);
         _mission.setMark("enemy17022");
         this._moveBG.pauseAnimation(true);
         CitrusLock.instance().lock(new Rectangle(0,0,1200,600),false,false,false,true);
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._moveBG = null;
         super.destroy();
      }
   }
}

