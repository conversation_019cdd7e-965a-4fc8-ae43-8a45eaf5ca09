package mogames.gameData.pet
{
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetHurtSkillVO;
   
   public class PetFengHuangVO extends PetGameVO
   {
      public function PetFengHuangVO()
      {
         allSkills = this.newActiveSkills();
         super(1107);
      }
      
      override public function newActiveSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new PetHurtSkillVO(11071,this));
         _loc1_.push(new PetHurtSkillVO(11072,this));
         _loc1_.push(new PetHurtSkillVO(11073,this));
         _loc1_.push(new PetHurtSkillVO(11074,this));
         _loc1_.push(new PetHurtSkillVO(11075,this));
         return _loc1_;
      }
   }
}

