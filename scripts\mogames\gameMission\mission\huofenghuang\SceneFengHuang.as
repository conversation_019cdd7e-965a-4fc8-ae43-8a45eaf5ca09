package mogames.gameMission.mission.huofenghuang
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.enemy.BOSSHuoFengHuang;
   
   public class SceneFengHuang extends BossScene
   {
      private var _realBOSS:BOSSHuoFengHuang;
      
      public function SceneFengHuang(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(384,212,666,174),new Point(711,426));
         EffectManager.instance().playBGM("BGM_BATTLE1");
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.addBOSS();
         this.startBossBattle();
      }
      
      override protected function addBOSS() : void
      {
         this._realBOSS = new BOSSHuoFengHuang();
         this._realBOSS.x = 771;
         this._realBOSS.y = 200;
         this._realBOSS.initData(40181,40181,162);
         this._realBOSS.target = _mission.curTarget;
         this._realBOSS.setInverted(true);
         add(this._realBOSS);
         this._realBOSS.onRole.add(listenBOSS);
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         setHeroEnable(true);
         this._realBOSS.activeEnemy(false);
      }
      
      override protected function handlerWin() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._realBOSS,
            "rect":_dropRect,
            "hero":this._realBOSS.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         Layers.setKeyEnable(false);
         MissionManager.instance().handlerWin(false);
      }
      
      override public function destroy() : void
      {
         super.destroy();
         this._realBOSS = null;
      }
   }
}

