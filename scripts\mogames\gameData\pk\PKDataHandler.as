package mogames.gameData.pk
{
   import file.GoodConfig;
   import mogames.gameData.good.constvo.ConstEquipVO;
   import mogames.gameData.pk.enemy.PKEnemyBonzeVO;
   import mogames.gameData.pk.enemy.PKEnemyDrakanVO;
   import mogames.gameData.pk.enemy.PKEnemyHorseVO;
   import mogames.gameData.pk.enemy.PKEnemyMonkeyVO;
   import mogames.gameData.pk.enemy.PKEnemyPigVO;
   import mogames.gameData.pk.enemy.PKEnemyVO;
   import mogames.gameData.pk.vo.PKEnemyGameVO;
   import mogames.gamePK.BasePKEnemy;
   import mogames.gamePK.PKBonze;
   import mogames.gamePK.PKDrakan;
   import mogames.gamePK.PKHorse;
   import mogames.gamePK.PKMonkey;
   import mogames.gamePK.PKPig;
   import mogames.gamePKG.PKGManager;
   import utils.MathUtil;
   
   public class PKDataHandler
   {
      private var _data:Object;
      
      public var heros:Object;
      
      public var pkRoles:Array;
      
      public var mcURL:Array;
      
      public function PKDataHandler()
      {
         super();
      }
      
      public function parseData(param1:Object) : void
      {
         if(this.checkHasData(param1))
         {
            this._data = param1;
         }
         else
         {
            this._data = PKGManager.collectSaveData();
         }
         this.heros = this._data.heros;
         this.parseRoleList();
         this.mcURL = [];
         this.parseURL(this.heros.monkey);
         this.parseURL(this.heros.horse);
         this.parseURL(this.heros.pig);
         this.parseURL(this.heros.bonze);
         this.parseURL(this.heros.drakan);
      }
      
      private function checkHasData(param1:Object) : Boolean
      {
         if(!param1)
         {
            return false;
         }
         if(!param1.heros || !param1.server)
         {
            return false;
         }
         var _loc2_:Object = param1.heros;
         if(!_loc2_.monkey || !_loc2_.horse || !_loc2_.pig || !_loc2_.bonze)
         {
            return false;
         }
         var _loc3_:Array = param1.server.split("T");
         if(_loc3_[0] != "0")
         {
            return false;
         }
         return true;
      }
      
      public function get enemyTeams() : Array
      {
         var _loc1_:int = 0;
         if(!this.checkHasData(this._data))
         {
            this.parseData(PKGManager.collectSaveData());
         }
         if(!this.pkRoles || !this.pkRoles[0] || this.pkRoles[0] == 0)
         {
            this.pkRoles = [1001,1002,1003,1004,1005];
         }
         var _loc2_:int = RolePKProxy.instance().activeType + 1;
         var _loc3_:Array = [];
         while(_loc1_ < _loc2_)
         {
            _loc3_[_loc1_] = this.newPKEnemy(this.pkRoles[_loc1_]);
            _loc1_++;
         }
         return _loc3_;
      }
      
      public function newPKEnemy(param1:int) : BasePKEnemy
      {
         switch(param1)
         {
            case 1001:
               return new PKMonkey(this.newPKEnemyVO(1001,this.heros.monkey));
            case 1002:
               return new PKHorse(this.newPKEnemyVO(1002,this.heros.horse));
            case 1003:
               return new PKPig(this.newPKEnemyVO(1003,this.heros.pig));
            case 1004:
               return new PKBonze(this.newPKEnemyVO(1004,this.heros.bonze));
            case 1005:
               return new PKDrakan(this.newPKEnemyVO(1005,this.heros.drakan));
            default:
               return null;
         }
      }
      
      public function get vipNum() : int
      {
         var _loc1_:Array = this._data.master.split("T");
         return int(_loc1_[4]);
      }
      
      private function newPKEnemyVO(param1:int, param2:String) : PKEnemyGameVO
      {
         var _loc3_:PKEnemyVO = null;
         switch(param1)
         {
            case 1001:
               _loc3_ = new PKEnemyMonkeyVO(this.vipNum);
               break;
            case 1002:
               _loc3_ = new PKEnemyHorseVO(this.vipNum);
               break;
            case 1003:
               _loc3_ = new PKEnemyPigVO(this.vipNum);
               break;
            case 1004:
               _loc3_ = new PKEnemyBonzeVO(this.vipNum);
               break;
            case 1005:
               _loc3_ = new PKEnemyDrakanVO(this.vipNum);
         }
         _loc3_.parseLoadData(param2);
         return new PKEnemyGameVO(_loc3_);
      }
      
      private function parseRoleList() : void
      {
         this.pkRoles = [1001,1002,1003,1004,1005];
         var _loc1_:String = this._data.rolePK;
         if(!_loc1_)
         {
            return;
         }
         var _loc2_:Array = _loc1_.split("T");
         if(_loc2_[0] != "none" && _loc2_.length >= 5)
         {
            return;
         }
         this.pkRoles = MathUtil.arrStringToNum(_loc2_[1].split("H"));
         if(this.pkRoles[0] == 0)
         {
            this.pkRoles[0] = 1001;
         }
         if(this.heros.horse != "none" && this.pkRoles[1] == 0)
         {
            this.pkRoles[1] = 1002;
         }
         if(this.heros.pig != "none" && this.pkRoles[2] == 0)
         {
            this.pkRoles[2] = 1003;
         }
         if(this.heros.bonze != "none" && this.pkRoles[3] == 0)
         {
            this.pkRoles[3] = 1004;
         }
         if(this.heros.drakan != "none" && this.pkRoles[4] == 0)
         {
            this.pkRoles[4] = 1005;
         }
      }
      
      private function parseURL(param1:String) : void
      {
         var _loc4_:* = null;
         var _loc5_:Array = null;
         var _loc6_:ConstEquipVO = null;
         if(param1 == "none")
         {
            return;
         }
         var _loc2_:Array = param1.split("H");
         if(!_loc2_[4])
         {
            return;
         }
         var _loc3_:Array = _loc2_[4].split("E");
         for each(_loc4_ in _loc3_)
         {
            _loc5_ = _loc4_.split("B");
            _loc6_ = GoodConfig.instance().findConstGood(int(_loc5_[0])) as ConstEquipVO;
            if(_loc6_.mcURL != "")
            {
               this.addURL(_loc6_.mcURL);
            }
         }
      }
      
      private function addURL(param1:String) : void
      {
         if(!this.mcURL || this.mcURL.indexOf(param1) != -1)
         {
            return;
         }
         this.mcURL.push(param1);
      }
   }
}

