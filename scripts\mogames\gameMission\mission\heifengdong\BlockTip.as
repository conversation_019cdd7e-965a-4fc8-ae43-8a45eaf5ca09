package mogames.gameMission.mission.heifengdong
{
   import Box2D.Dynamics.Contacts.b2Contact;
   import citrus.physics.box2d.Box2DUtils;
   import citrus.physics.box2d.IBox2DPhysicsObject;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import mogames.Layers;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.box2d.BaseSensor;
   import mogames.gameRole.hero.BaseHero;
   
   public class BlockTip extends BaseSensor
   {
      private var _isHurt:Boolean;
      
      private var _bitmap:Bitmap;
      
      private var _active:Boolean;
      
      private var _atkFunc:Function;
      
      private var _touchFunc:Function;
      
      public function BlockTip(param1:Function, param2:Function, param3:String)
      {
         _beginContactCallEnabled = true;
         this._atkFunc = param2;
         this._touchFunc = param1;
         this._bitmap = new Bitmap(GameInLoader.instance().findAsset(param3) as BitmapData);
         super("blockTip",{
            "width":this._bitmap.width,
            "height":this._bitmap.height,
            "group":2
         });
         this.view = this._bitmap;
      }
      
      public function activeATK() : void
      {
         BattleMediator.instance().onHeroATK.add(this.checkATTACK);
      }
      
      private function checkATTACK(param1:BaseHitVO) : void
      {
         if(!this._isHurt && this._bitmap.hitTestObject(param1.range))
         {
            BattleMediator.instance().onHeroATK.remove(this.checkATTACK);
            this._atkFunc();
            EffectManager.instance().addBMCEffect(param1.hurtSkin,Layers.ceEffectLayer,x,y);
            EffectManager.instance().playAudio("GUNHURT");
            this._bitmap.visible = false;
            this.kill = true;
            this._isHurt = true;
         }
      }
      
      override public function handleBeginContact(param1:b2Contact) : void
      {
         var _loc2_:IBox2DPhysicsObject = Box2DUtils.CollisionGetOther(this,param1);
         if(_loc2_ is BaseHero && !Layers.hasEnemy)
         {
            _beginContactCallEnabled = false;
            if(this._touchFunc != null)
            {
               this._touchFunc();
            }
         }
      }
      
      override public function destroy() : void
      {
         this._atkFunc = null;
         this._touchFunc = null;
         BattleMediator.instance().onHeroATK.remove(this.checkATTACK);
         this._bitmap.bitmapData.dispose();
         this._bitmap = null;
         super.destroy();
      }
   }
}

