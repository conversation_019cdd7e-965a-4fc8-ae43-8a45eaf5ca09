package file
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.maze.MazeNeedVO;
   
   public class MazeConfig
   {
      private static var _instance:MazeConfig;
      
      private var _needs:Array;
      
      public var activeIndex:Sint = new Sint();
      
      public function MazeConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : MazeConfig
      {
         if(!_instance)
         {
            _instance = new MazeConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._needs = [];
         this._needs[this._needs.length] = new MazeNeedVO(0,1,10);
         this._needs[this._needs.length] = new MazeNeedVO(1,11,20);
         this._needs[this._needs.length] = new MazeNeedVO(2,21,30);
         this._needs[this._needs.length] = new MazeNeedVO(3,31,40);
         this._needs[this._needs.length] = new MazeNeedVO(4,41,50);
      }
      
      public function get needList() : Array
      {
         return this._needs;
      }
   }
}

