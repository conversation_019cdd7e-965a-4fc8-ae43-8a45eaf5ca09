package mogames.gameMission.mission.heisonglin
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameObj.trap.TrapFallFireBall;
   import mogames.gameRole.enemy.BOSSHuangPaoGuai;
   import mogames.gameSystem.CitrusTimer;
   import utils.MathUtil;
   
   public class SceneHeiSongLin09 extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _startTimer:CitrusTimer;
      
      private var _fireTimer:CitrusTimer;
      
      private var _fangpao:CitrusMCSprite;
      
      public function SceneHeiSongLin09(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(306,148,726,304),new Point(435,396));
         this._startTimer = new CitrusTimer();
         this._fireTimer = new CitrusTimer();
      }
      
      override protected function handlerInit() : void
      {
         this.layoutFangPao();
         _mission.cleanLoadUI();
         this.initTrigger();
      }
      
      private function layoutFangPao() : void
      {
         this._fangpao = new CitrusMCSprite("MC_FANG_PAO_CLIP",{
            "width":10,
            "height":10,
            "parallaxX":0.5,
            "group":0
         });
         this._fangpao.x = 375;
         this._fangpao.y = 94;
         this._fangpao.endFunc = this.onFangpao;
         this._fangpao.changeAnimation("stand",true);
         add(this._fangpao);
      }
      
      private function onFangpao(param1:String) : void
      {
         if(param1 == "fangpao")
         {
            this._fangpao.changeAnimation("stand",true);
         }
      }
      
      private function initTrigger() : void
      {
         this.addBOSS();
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(640,480),1);
         this._pTrigger0.okFunc = this.startBOSS;
         this._pTrigger0.start();
      }
      
      private function startBOSS() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2017,1);
            startBossBattle();
         };
         EffectManager.instance().playBGM("BGM_BATTLE1");
         if(FlagProxy.instance().isComplete(2017))
         {
            this.startBossBattle();
         }
         else
         {
            showDialog("STORY0030",func);
         }
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         this.startFire();
         super.startBossBattle(param1);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHuangPaoGuai();
         _boss.x = 1143;
         _boss.y = 420;
         _boss.initData(30101,30101,26);
         _boss.target = _mission.curTarget;
         add(_boss);
      }
      
      private function handlerFire() : void
      {
         this._fangpao.changeAnimation("fangpao",false);
         var _loc1_:int = MathUtil.randomNum(2,3);
         this._fireTimer.setInterval(0.5,0.5 * _loc1_,this.addFire,this.startFire,false);
         this._fireTimer.startNone();
      }
      
      private function startFire() : void
      {
         this._startTimer.setTimeOut(8,this.handlerFire);
      }
      
      private function addFire() : void
      {
         var _loc1_:TrapFallFireBall = new TrapFallFireBall();
         _loc1_.x = MathUtil.randomNum(200,1240);
         _loc1_.y = 503;
         _loc1_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",5,5);
         _loc1_.createHitHurt(200,0.5,false);
         add(_loc1_);
      }
      
      override protected function handlerWin() : void
      {
         super.handlerWin();
         this._fireTimer.pause();
         this._startTimer.pause();
         TaskProxy.instance().setComplete(11009);
      }
      
      override public function destroy() : void
      {
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         this._pTrigger0 = null;
         this._startTimer.destroy();
         this._fireTimer.destroy();
         this._startTimer = null;
         this._fireTimer = null;
         super.destroy();
      }
   }
}

