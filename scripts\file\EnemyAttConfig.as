package file
{
   import mogames.gameData.role.vo.RoleAttVO;
   
   public class EnemyAttConfig
   {
      private static var _instance:EnemyAttConfig;
      
      private var _attVec:Vector.<RoleAttVO>;
      
      public function EnemyAttConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : EnemyAttConfig
      {
         if(!_instance)
         {
            _instance = new EnemyAttConfig();
         }
         return _instance;
      }
      
      public static function clean() : void
      {
         _instance = null;
      }
      
      public function init() : void
      {
         this._attVec = new Vector.<RoleAttVO>();
         this._attVec.push(new RoleAttVO(20011,800,1,50,10,20,100,0,2,999));
         this._attVec.push(new RoleAttVO(20021,50,1,8,0,0,100,50,2,999));
         this._attVec.push(new RoleAttVO(20031,40,1,14,0,0,100,50,2,999,30));
         this._attVec.push(new RoleAttVO(20041,300,1,20,0,0,100,50,2,999,40));
         this._attVec.push(new RoleAttVO(20032,350,1,24,0,0,100,50,2,999,65));
         this._attVec.push(new RoleAttVO(20022,900,1,36,0,0,100,50,2,999));
         this._attVec.push(new RoleAttVO(20051,800,1,48,0,0,100,50,2,999,30,3));
         this._attVec.push(new RoleAttVO(20033,850,1,63,0,0,100,50,2,999,50));
         this._attVec.push(new RoleAttVO(20026,1300,1,85,50,0,100,50,2,999));
         this._attVec.push(new RoleAttVO(20044,1800,1,70,100,0,100,50,2,999,80));
         this._attVec.push(new RoleAttVO(20071,950,1,78,20,0,100,50,2,999,70,1.5));
         this._attVec.push(new RoleAttVO(20081,1050,1,82,20,0,100,50,2,999,90,1,3));
         this._attVec.push(new RoleAttVO(20091,1150,1,98,20,0,100,50,2,999,2007,20071,20071,2008,20081,20081));
         this._attVec.push(new RoleAttVO(20042,1100,1,86,50,0,100,50,2,999,60));
         this._attVec.push(new RoleAttVO(20023,1200,1,92,50,0,100,50,2,999));
         this._attVec.push(new RoleAttVO(20052,1050,1,88,50,0,100,50,2,999,50,3));
         this._attVec.push(new RoleAttVO(20061,1100,1,92,50,0,100,50,2,999,1,2));
         this._attVec.push(new RoleAttVO(20062,1100,1,103,60,0,100,50,2,999,1,2));
         this._attVec.push(new RoleAttVO(20101,1200,1,107,60,100,100,50,1,999,70,2));
         this._attVec.push(new RoleAttVO(20111,1300,1,105,60,0,100,50,2,999,5));
         this._attVec.push(new RoleAttVO(20121,1400,1,90,60,400,100,50,2,999,0.15));
         this._attVec.push(new RoleAttVO(20053,1300,1,150,70,0,100,50,2,999,80,5));
         this._attVec.push(new RoleAttVO(20082,1400,1,130,70,0,100,50,2,999,120,40,3));
         this._attVec.push(new RoleAttVO(20122,1500,1,120,70,400,100,50,2,999,0.2));
         this._attVec.push(new RoleAttVO(20112,1600,1,115,70,0,100,50,2,999,5));
         this._attVec.push(new RoleAttVO(20131,1800,1,140,80,0,100,50,2,999,65));
         this._attVec.push(new RoleAttVO(20141,1900,1,155,80,0,100,50,2,999,170,3,75));
         this._attVec.push(new RoleAttVO(20123,2000,1,160,80,900,100,50,2,999,0.2));
         this._attVec.push(new RoleAttVO(20102,2100,1,140,90,0,100,50,2,999,300,2));
         this._attVec.push(new RoleAttVO(20092,2200,1,150,90,0,100,50,2,999,2007,20071,20071,2008,20081,20081));
         this._attVec.push(new RoleAttVO(20034,2300,1,160,90,0,100,50,2,999,250));
         this._attVec.push(new RoleAttVO(20054,2400,1,190,90,0,100,50,2,999,110,5));
         this._attVec.push(new RoleAttVO(20151,2500,1,190,100,0,100,50,2,999,270));
         this._attVec.push(new RoleAttVO(20161,2600,1,170,100,0,100,50,2,999,240));
         this._attVec.push(new RoleAttVO(20171,2700,1,210,120,0,100,50,2,999));
         this._attVec.push(new RoleAttVO(20181,2800,1,230,120,0,100,50,2,999));
         this._attVec.push(new RoleAttVO(20191,2900,1,250,120,0,100,50,2,999,600));
         this._attVec.push(new RoleAttVO(20201,3000,1,260,140,0,100,50,2,999,360));
         this._attVec.push(new RoleAttVO(20211,3300,1,290,140,0,100,50,2,999,400));
         this._attVec.push(new RoleAttVO(20221,3400,1,290,160,0,100,50,2,999,450,1));
         this._attVec.push(new RoleAttVO(20231,3600,1,310,160,0,100,50,2,999,10,100));
         this._attVec.push(new RoleAttVO(20072,4200,1,350,180,0,100,50,2,999,450,1));
         this._attVec.push(new RoleAttVO(20083,3800,1,380,180,0,100,50,2,999,400,60,3));
         this._attVec.push(new RoleAttVO(20093,3800,1,330,180,0,100,50,2,999,2007,20072,20072,2008,20083,20083));
         this._attVec.push(new RoleAttVO(20063,3800,1,430,180,0,100,50,2,999,1,2));
         this._attVec.push(new RoleAttVO(20143,4500,1,310,210,0,100,50,2,999,370,3,200));
         this._attVec.push(new RoleAttVO(20125,4000,1,390,210,800,100,50,2,999,0.3));
         this._attVec.push(new RoleAttVO(20233,4900,1,440,210,0,100,50,2,999,10,100));
         this._attVec.push(new RoleAttVO(20057,4900,1,460,210,0,100,50,2,999,250,5));
         this._attVec.push(new RoleAttVO(20126,4700,1,490,250,800,100,50,2,999,0.3));
         this._attVec.push(new RoleAttVO(20222,5200,1,520,250,0,100,50,2,999,600,1));
         this._attVec.push(new RoleAttVO(20192,5200,1,460,250,0,100,50,2,999,850));
         this._attVec.push(new RoleAttVO(20203,6800,1,410,500,0,100,50,2,999,700));
         this._attVec.push(new RoleAttVO(20213,6000,1,560,500,0,100,50,2,999,800));
         this._attVec.push(new RoleAttVO(20241,3800,1,310,200,0,100,50,2,999,200));
         this._attVec.push(new RoleAttVO(20243,6000,1,410,400,0,100,50,2,999,300));
         this._attVec.push(new RoleAttVO(20251,3000,1,360,300,0,100,50,2,999,500,1,300,3,200));
         this._attVec.push(new RoleAttVO(20252,6000,1,460,600,0,100,50,2,999,400,1,400,3,400));
         this._attVec.push(new RoleAttVO(20281,6500,1,480,450,0,100,50,2,999,800,5,350));
         this._attVec.push(new RoleAttVO(20282,8500,1,580,750,0,100,50,2,999,500,5,550));
         this._attVec.push(new RoleAttVO(20291,6000,1,520,450,0,100,50,2,999,650));
         this._attVec.push(new RoleAttVO(20292,8000,1,620,750,0,100,50,2,999,450));
         this._attVec.push(new RoleAttVO(20311,7000,1,620,500,0,100,50,2,999,500,0.5));
         this._attVec.push(new RoleAttVO(20312,9000,1,720,900,0,100,50,2,999,600,0.5));
         this._attVec.push(new RoleAttVO(20063,6000,1,880,500,0,100,50,2,999,1,2));
         this._attVec.push(new RoleAttVO(20037,6500,1,780,600,0,200,50,2,999,700));
         this._attVec.push(new RoleAttVO(20321,7000,1,700,600,0,100,50,2,999,3,500));
         this._attVec.push(new RoleAttVO(20322,7000,1,700,1300,0,100,50,2,999,3,700));
         this._attVec.push(new RoleAttVO(20331,7500,1,800,600,0,100,50,2,999,700,1));
         this._attVec.push(new RoleAttVO(20332,7500,1,800,1400,0,100,50,2,999,800,1));
         this._attVec.push(new RoleAttVO(20361,8000,1,900,750,0,100,50,2,599));
         this._attVec.push(new RoleAttVO(20362,8000,1,900,550,0,100,50,2,499));
         this._attVec.push(new RoleAttVO(20371,7000,1,800,750,0,100,50,2,999,600));
         this._attVec.push(new RoleAttVO(20372,7000,1,800,650,0,100,50,2,999,600));
         this._attVec.push(new RoleAttVO(20381,11000,1,1000,999,0,100,50,3,999,800,500,2,800,500,2));
         this._attVec.push(new RoleAttVO(20391,2500,1,1,10,10,100,50,3,999,100,150,200,250,130,120));
         this._attVec.push(new RoleAttVO(20401,8000,1,260,400,200,100,50,3,999,200,250,300,350,130,220));
         this._attVec.push(new RoleAttVO(20411,8000,1,800,800,0,100,50,2,999,700));
         this._attVec.push(new RoleAttVO(20412,10000,1,800,1200,0,100,50,2,999,1000));
         this._attVec.push(new RoleAttVO(20312,9000,1,900,800,0,100,50,2,999,900,0.5));
         this._attVec.push(new RoleAttVO(20421,9000,1,900,800,0,100,50,2,999,900,5,50));
         this._attVec.push(new RoleAttVO(20431,6000,1,800,800,0,100,50,2,999,20));
         this._attVec.push(new RoleAttVO(20461,8000,1,900,800,700,0,100,4,999,800));
         this._attVec.push(new RoleAttVO(20242,6800,1,1200,900,0,100,50,2,999,600));
         this._attVec.push(new RoleAttVO(20412,8000,1,1000,1000,0,100,50,2,999,900));
         this._attVec.push(new RoleAttVO(20313,9000,1,1000,1000,0,100,50,2,999,1000,0.5));
         this._attVec.push(new RoleAttVO(20471,8000,1,1000,1200,0,0,100,4,999,1000));
         this._attVec.push(new RoleAttVO(20481,9000,1,1100,1300,0,0,100,4,999,1200));
         this._attVec.push(new RoleAttVO(20422,9000,1,1200,900,0,100,50,2,999,1200,5,80));
         this._attVec.push(new RoleAttVO(20462,10000,1,1300,1400,700,0,100,4,999,1000));
         this._attVec.push(new RoleAttVO(20372,12000,1,1200,1200,0,100,50,2,999,800));
         this._attVec.push(new RoleAttVO(20501,11000,1,1300,1500,0,0,100,2,999,800));
         this._attVec.push(new RoleAttVO(20482,13000,1,1400,1600,0,0,100,4,999,1000));
         this._attVec.push(new RoleAttVO(20511,12000,1,1400,1700,0,0,100,2,999,1000));
         this._attVec.push(new RoleAttVO(20521,19000,1,1600,1900,0,0,100,2,999));
         this._attVec.push(new RoleAttVO(20243,15000,1,1200,2000,0,100,50,2,999,1200));
         this._attVec.push(new RoleAttVO(20253,15000,1,1400,1800,0,100,50,2,999,1500,1,2600,3,800));
         this._attVec.push(new RoleAttVO(20340,300,1,18,0,0,100,50,3,999,24,1,20));
         this._attVec.push(new RoleAttVO(20350,500,1,28,0,0,150,50,3,999,34,6,100));
         this._attVec.push(new RoleAttVO(20341,600,1,54,0,0,100,50,3,999,62,1,50));
         this._attVec.push(new RoleAttVO(20351,1000,1,74,0,0,150,50,3,999,92,1,100));
         this._attVec.push(new RoleAttVO(20342,900,1,89,30,0,100,50,3,999,92,1,70));
         this._attVec.push(new RoleAttVO(20352,1500,1,120,30,0,150,50,3,999,102,1,100));
         this._attVec.push(new RoleAttVO(20343,1500,1,133,60,0,100,50,3,999,122,1,100));
         this._attVec.push(new RoleAttVO(20353,2000,1,190,60,0,150,50,3,999,142,1,100));
         this._attVec.push(new RoleAttVO(20344,2500,1,193,120,0,100,50,3,999,172,1,130));
         this._attVec.push(new RoleAttVO(20354,2000,1,250,120,0,150,50,3,999,192,1,100));
         this._attVec.push(new RoleAttVO(20345,3500,1,243,180,0,100,50,3,999,233,1,160));
         this._attVec.push(new RoleAttVO(20355,2500,1,310,180,0,150,50,3,999,253,1,100));
         this._attVec.push(new RoleAttVO(20346,4500,1,303,240,0,100,50,3,999,284,1,190));
         this._attVec.push(new RoleAttVO(20356,3500,1,370,240,0,150,50,3,999,304,1,100));
         this._attVec.push(new RoleAttVO(20347,5500,1,363,300,0,100,50,3,999,335,1,210));
         this._attVec.push(new RoleAttVO(20357,4500,1,420,300,0,150,50,3,999,355,1,100));
         this._attVec.push(new RoleAttVO(20348,6500,1,423,360,0,100,50,3,999,386,1,240));
         this._attVec.push(new RoleAttVO(20358,5000,1,480,360,0,150,50,3,999,406,1,100));
         this._attVec.push(new RoleAttVO(20349,7500,1,483,420,0,100,50,3,999,437,1,270));
         this._attVec.push(new RoleAttVO(20359,6000,1,560,420,0,150,50,3,999,457,1,100));
         this._attVec.push(new RoleAttVO(20024,2600,1,120,100,0,200,50,2,999));
         this._attVec.push(new RoleAttVO(20035,2700,1,140,100,0,200,50,2,999,60));
         this._attVec.push(new RoleAttVO(20043,2800,1,160,140,0,200,50,2,999,80));
         this._attVec.push(new RoleAttVO(20036,2900,1,180,140,0,200,50,2,999,100));
         this._attVec.push(new RoleAttVO(20025,3000,1,200,160,0,200,50,2,999));
         this._attVec.push(new RoleAttVO(20054,3100,1,220,160,0,200,50,2,999,60,5));
         this._attVec.push(new RoleAttVO(20103,3200,1,240,180,0,200,50,2,999,250,1.5));
         this._attVec.push(new RoleAttVO(20113,3300,1,260,180,0,200,50,2,999,3));
         this._attVec.push(new RoleAttVO(20055,3400,1,280,200,0,200,50,2,999,80,5));
         this._attVec.push(new RoleAttVO(20083,3500,1,300,200,0,200,50,2,999,90,40,3));
         this._attVec.push(new RoleAttVO(20152,3000,1,270,200,0,100,50,2,999,200));
         this._attVec.push(new RoleAttVO(20162,3000,1,280,200,0,100,50,2,999,140));
         this._attVec.push(new RoleAttVO(20153,3000,1,290,200,0,100,50,2,999,200));
         this._attVec.push(new RoleAttVO(20056,3000,1,300,200,0,100,50,2,999,120,5));
         this._attVec.push(new RoleAttVO(20163,3000,1,310,200,0,100,50,2,999,240));
         this._attVec.push(new RoleAttVO(20084,3000,1,320,200,0,100,50,2,999,90,40,3));
         this._attVec.push(new RoleAttVO(20132,3000,1,330,200,0,200,50,2,999,260));
         this._attVec.push(new RoleAttVO(20142,3000,1,340,200,0,200,50,2,999,240,3,100));
         this._attVec.push(new RoleAttVO(20154,3000,1,350,200,0,200,50,2,999,240));
         this._attVec.push(new RoleAttVO(20164,3000,1,360,200,0,200,50,2,999,260));
         this._attVec.push(new RoleAttVO(20124,3000,1,370,200,900,100,50,2,999,0.3));
         this._attVec.push(new RoleAttVO(20172,4000,1,320,300,0,100,50,2,999));
         this._attVec.push(new RoleAttVO(20182,5000,1,340,300,0,100,50,2,999));
         this._attVec.push(new RoleAttVO(20192,4000,1,360,300,0,100,50,2,999,950));
         this._attVec.push(new RoleAttVO(20202,5500,1,380,300,0,100,50,2,999,610));
         this._attVec.push(new RoleAttVO(20212,4000,1,400,400,0,100,50,2,999,550));
         this._attVec.push(new RoleAttVO(20222,5000,1,420,400,0,100,50,2,999,800,1));
         this._attVec.push(new RoleAttVO(20232,4500,1,440,400,0,100,50,2,999,10,100));
         this._attVec.push(new RoleAttVO(20224,6000,1,620,800,100,100,50,2,999,900,1));
         this._attVec.push(new RoleAttVO(20234,5900,1,640,900,200,100,50,2,999,10,200));
         this._attVec.push(new RoleAttVO(20193,6200,1,560,700,300,100,50,2,999,1350));
         this._attVec.push(new RoleAttVO(20127,4700,1,690,900,800,100,50,2,999,0.4));
         this._attVec.push(new RoleAttVO(20261,5000,1,30,200,0,100,50,2,999,80,40,100,40,2,70,80,1));
         this._attVec.push(new RoleAttVO(20271,70,1,30,999999999,0,100,50,2,999));
         this._attVec.push(new RoleAttVO(20301,5000,1,300,400,0,100,50,2,999,400,5,300,400,300,400,1,500,600));
         this._attVec.push(new RoleAttVO(20223,4000,1,300,300,0,100,50,2,999,500,1));
         this._attVec.push(new RoleAttVO(20441,10000,1,3688,2200,700,0,100,4,1000,2000,3000,2000));
         this._attVec.push(new RoleAttVO(20451,7000,1,3688,2200,700,0,100,4,1000,3000,3000,4000));
         this._attVec.push(new RoleAttVO(20491,10000,1,888,600,0,0,100,2,999,600,700,800,900,1000));
         this._attVec.push(new RoleAttVO(20155,500,1,50,10,20,200,50,2,999,80));
         this._attVec.push(new RoleAttVO(20165,3000,1,300,30,50,200,50,2,999,60));
         this._attVec.push(new RoleAttVO(20204,6500,1,450,100,100,100,50,2,999,600));
         this._attVec.push(new RoleAttVO(20214,10000,1,600,200,300,100,50,2,999,900));
         this._attVec.push(new RoleAttVO(20156,10000,1,750,300,450,200,50,2,999,1000));
         this._attVec.push(new RoleAttVO(20166,10000,1,900,400,550,200,50,2,999,1200));
         this._attVec.push(new RoleAttVO(20205,10000,1,1050,500,650,100,50,2,999,1400));
         this._attVec.push(new RoleAttVO(20215,10000,1,1200,600,750,100,50,2,999,1600));
         this._attVec.push(new RoleAttVO(30011,8000,1,80,100,0,0,0,2,1000));
         this._attVec.push(new RoleAttVO(30021,750,1,10,0,0,0,0,2,500,15));
         this._attVec.push(new RoleAttVO(30022,6000,1,150,50,50,50,50,3,1000,120));
         this._attVec.push(new RoleAttVO(30023,6000,1,150,50,50,50,50,3,1000,240));
         this._attVec.push(new RoleAttVO(30031,3000,1,60,5,0,0,0,2,2000,95,85,75));
         this._attVec.push(new RoleAttVO(30032,3000,1,100,0,0,50,50,2,500,100,110,90));
         this._attVec.push(new RoleAttVO(30033,5000,1,250,0,0,50,50,2,500,120,160,180));
         this._attVec.push(new RoleAttVO(30034,10000,1,350,0,0,50,50,2,1000,240,320,360));
         this._attVec.push(new RoleAttVO(30041,6000,1,60,20,5,100,50,2,2000,50,70));
         this._attVec.push(new RoleAttVO(30042,3000,1,120,0,0,50,50,2,500,100,120));
         this._attVec.push(new RoleAttVO(30043,5000,1,250,0,0,50,50,2,500,150,180));
         this._attVec.push(new RoleAttVO(30044,12000,1,350,150,50,50,50,2,1000,300,360));
         this._attVec.push(new RoleAttVO(30051,10500,1,100,95,55,100,100,2,700,2,3,150,200,1));
         this._attVec.push(new RoleAttVO(30052,15000,1,150,0,150,100,200,2,1000,2,3,240,300,1));
         this._attVec.push(new RoleAttVO(30061,8400,1,150,110,65,100,100,2,700,5,250,2,1));
         this._attVec.push(new RoleAttVO(30062,12000,1,200,15,250,200,100,2,1000,5,300,2,1));
         this._attVec.push(new RoleAttVO(30071,2800,1,120,100,0,50,10,0.5,500,1));
         this._attVec.push(new RoleAttVO(30072,2600,1,120,100,0,50,10,0.5,500,1));
         this._attVec.push(new RoleAttVO(30073,2300,1,120,100,0,50,10,0.5,500,1));
         this._attVec.push(new RoleAttVO(30074,2000,1,120,100,0,50,10,0.5,500,1));
         this._attVec.push(new RoleAttVO(30081,2800,1,140,100,0,100,50,0.5,1000,3));
         this._attVec.push(new RoleAttVO(30082,2600,1,140,100,0,100,50,0.5,1000,3));
         this._attVec.push(new RoleAttVO(30083,2300,1,140,100,0,100,50,0.5,1000,3));
         this._attVec.push(new RoleAttVO(30084,2000,1,140,100,0,100,50,0.5,1000,3));
         this._attVec.push(new RoleAttVO(30091,2800,1,160,100,0,150,100,2,500,100,50,3,230));
         this._attVec.push(new RoleAttVO(30092,2600,1,160,100,0,150,100,2,500,100,50,3,230));
         this._attVec.push(new RoleAttVO(30093,2300,1,160,100,0,150,100,2,500,100,50,3,230));
         this._attVec.push(new RoleAttVO(30094,2000,1,160,100,0,150,100,2,500,100,50,3,230));
         this._attVec.push(new RoleAttVO(30101,12000,1,40,35,15,100,100,2,800,2,75,100,80,5,90,2));
         this._attVec.push(new RoleAttVO(30102,15000,1,350,0,0,100,100,2,1000,2,110,140,160,5,140,3));
         this._attVec.push(new RoleAttVO(30111,8000,1,250,140,75,200,100,2,1000,150,5,1));
         this._attVec.push(new RoleAttVO(30121,500,1,10,0,0,0,0,2,500,15,15,15,15,5,20,3));
         this._attVec.push(new RoleAttVO(30122,3500,1,100,100,0,10,10,2.5,1000,100,100,100,120,5,150,5));
         this._attVec.push(new RoleAttVO(30123,6500,1,500,200,200,15,15,3,1000,400,400,400,180,5,270,5));
         this._attVec.push(new RoleAttVO(30124,35000,1,700,1300,200,30,20,3.5,1000,500,480,780,460,3,550,5));
         this._attVec.push(new RoleAttVO(30131,10800,1,150,170,85,100,100,2,900,250,1,3,3,220,70,10,70,10));
         this._attVec.push(new RoleAttVO(30132,12000,1,350,300,100,100,100,2,1000,350,1,3,3,290,140,10,140,10));
         this._attVec.push(new RoleAttVO(30141,7200,1,230,250,95,200,200,2,900,10,100,300,150));
         this._attVec.push(new RoleAttVO(30142,8000,1,400,200,150,200,200,2,1000,10,200,400,200));
         this._attVec.push(new RoleAttVO(30151,8000,1,280,150,100,100,100,2,1000,3,100,200,3,100,10,100));
         this._attVec.push(new RoleAttVO(30152,10800,1,360,250,200,200,100,2,900,3,150,200,3,200,10,200));
         this._attVec.push(new RoleAttVO(30153,12000,1,400,300,200,200,100,2,1000,3,150,200,3,200,10,200));
         this._attVec.push(new RoleAttVO(30161,2500,1,20,0,0,0,0,2,2500,60,40,10,60,2,50,2));
         this._attVec.push(new RoleAttVO(30162,7000,1,150,250,0,150,150,2,1000,150,100,20,150,3,150,2));
         this._attVec.push(new RoleAttVO(30163,30000,1,350,1300,300,150,150,2,1000,550,400,200,750,3,550,2));
         this._attVec.push(new RoleAttVO(30164,50000,1,2000,3000,600,150,150,3,1000,2000,1000,200,2500,1,1100,2));
         this._attVec.push(new RoleAttVO(30171,10400,1,250,400,100,50,50,2,800,350,350,2));
         this._attVec.push(new RoleAttVO(30172,15000,1,400,800,200,50,50,2,1000,250,250,2));
         this._attVec.push(new RoleAttVO(30181,13600,1,350,500,200,100,100,2,800,450,250));
         this._attVec.push(new RoleAttVO(30182,15000,1,700,800,200,100,100,2,1000,350,150));
         this._attVec.push(new RoleAttVO(30191,13000,1,400,200,300,50,100,2,1000,300,400));
         this._attVec.push(new RoleAttVO(30192,12000,1,800,600,300,50,100,2,1000,300,400));
         this._attVec.push(new RoleAttVO(30201,17000,1,400,300,400,50,100,2,1000,5,2,3));
         this._attVec.push(new RoleAttVO(30202,20000,1,500,900,400,50,100,2,1000,1,2,2));
         this._attVec.push(new RoleAttVO(30211,15000,1,400,400,400,50,200,2,1000,300));
         this._attVec.push(new RoleAttVO(30212,15000,1,400,700,200,50,200,2,1000,500));
         this._attVec.push(new RoleAttVO(30221,26000,1,650,700,300,100,200,2,1000,400,3,400,5,330,350,10,50,350,10,50,350,10,50,400,200,10));
         this._attVec.push(new RoleAttVO(30231,28000,1,450,1100,300,100,200,2,1000,500,5,50,600,2,10,1000));
         this._attVec.push(new RoleAttVO(30232,16000,1,650,1100,100,100,200,2,1000,600,5,50,700,2,10,1000));
         this._attVec.push(new RoleAttVO(30241,28000,1,500,1100,300,100,200,3,1000,300,2,500,400));
         this._attVec.push(new RoleAttVO(30242,17000,1,750,900,100,100,200,3,1000,700,2,900,600));
         this._attVec.push(new RoleAttVO(30251,32000,1,550,900,250,100,200,4,1000,450,1,300,10,100,100,200));
         this._attVec.push(new RoleAttVO(30252,18000,1,750,900,200,100,200,4,1000,650,1,400,10,100,100,400));
         this._attVec.push(new RoleAttVO(30261,30000,1,700,1000,250,100,100,3,1000,400,3,300,400,2,400,3,300,5));
         this._attVec.push(new RoleAttVO(30262,19000,1,950,1000,200,100,100,3,1000,400,3,300,400,2,500,3,300,5));
         this._attVec.push(new RoleAttVO(30271,34000,1,800,900,250,100,200,3,1000,500,3,15,600,5,150,400,2));
         this._attVec.push(new RoleAttVO(30272,20000,1,1050,900,200,100,200,3,1000,500,3,15,700,5,150,600,2));
         this._attVec.push(new RoleAttVO(30281,35000,1,600,1300,200,100,200,3,1000,400,5,50,700,3,450,1.5));
         this._attVec.push(new RoleAttVO(30282,15000,1,600,1300,600,100,200,3,1000,400,5,50,700,3,450,1.5));
         this._attVec.push(new RoleAttVO(30283,35000,1,1300,1300,600,100,200,3,1000,800,5,50,900,3,650,1.5));
         this._attVec.push(new RoleAttVO(30291,24000,1,850,1200,250,100,200,3,1000,280,800,1.5,800));
         this._attVec.push(new RoleAttVO(30292,34000,1,1400,1200,450,100,200,3,1000,280,800,1.5,800));
         this._attVec.push(new RoleAttVO(30301,34000,1,800,1500,350,100,200,3,1000,400,600,50,5,5,500,500,2,500,300,5));
         this._attVec.push(new RoleAttVO(30311,2000,1,90,50,50,10,10,2,1000,80,120,6,80,90,80,3));
         this._attVec.push(new RoleAttVO(30312,7000,1,150,450,0,15,15,2.5,1000,120,160,7,100,200,100,4));
         this._attVec.push(new RoleAttVO(30313,48000,1,1800,2500,1800,30,20,3,1300,1320,1160,7,1100,800,1300,4));
         this._attVec.push(new RoleAttVO(30314,58000,1,2500,3500,2000,30,20,3,1300,2220,1960,10,2000,3000,2100,4));
         this._attVec.push(new RoleAttVO(30321,28285,1,1000,1400,300,100,100,2,1200,700,1000,30322,30322));
         this._attVec.push(new RoleAttVO(30322,20000,1,1300,1400,300,100,100,2,1200,700,1000,30322,30322));
         this._attVec.push(new RoleAttVO(30322,7000,1,950,1000,300,100,100,2,1200));
         this._attVec.push(new RoleAttVO(30331,38571,1,1100,1400,500,100,100,2,1200,5,500,3,300,700,2,3));
         this._attVec.push(new RoleAttVO(30332,30000,1,1400,1400,500,100,100,2,1200,5,500,3,300,700,2,3));
         this._attVec.push(new RoleAttVO(30341,42857,1,1200,1600,700,100,100,2,1200,5,3,5,900,1200,2));
         this._attVec.push(new RoleAttVO(30342,31000,1,1600,1600,700,100,100,2,1200,5,3,5,900,1200,2));
         this._attVec.push(new RoleAttVO(30351,47142,1,1300,1800,700,100,100,2,1200,8,50,1400,3,900,1600,800,800,1.5));
         this._attVec.push(new RoleAttVO(30352,32000,1,1800,1800,700,100,100,2,1200,8,50,1400,3,900,1600,800,800,1.5));
         this._attVec.push(new RoleAttVO(30361,50000,1,1400,2200,700,100,100,2,1400,1500,1600,1.5,2000,8));
         this._attVec.push(new RoleAttVO(30362,33000,1,2000,2200,700,100,100,2,1400,2500,2000,1.5,3000,8));
         this._attVec.push(new RoleAttVO(30371,55000,1,1500,2400,700,100,100,6,1400,1100,1400,2,1600,1,15,20,1));
         this._attVec.push(new RoleAttVO(30372,25000,1,1600,2300,600,100,100,4,1400,2000,1800,2,2200,1,10,15,1));
         this._attVec.push(new RoleAttVO(30381,45000,1,1600,2600,800,100,100,2,1400,1000,800,2,0,10,20381,20381,1500,800,2));
         this._attVec.push(new RoleAttVO(30382,30000,1,1600,2600,800,100,100,2,1400,1000,800,2,0,10,20381,20381,1500,800,2));
         this._attVec.push(new RoleAttVO(30391,30000,1,1800,2600,800,100,100,2,1400,900,1300,700,1300));
         this._attVec.push(new RoleAttVO(30392,20000,1,1800,2600,800,100,100,2,1400,900,1300,700,1300));
         this._attVec.push(new RoleAttVO(30401,35000,1,2100,2700,800,100,100,2,1400,1500,1800,900));
         this._attVec.push(new RoleAttVO(30402,25000,1,2100,2700,800,100,100,2,1400,1500,1800,900));
         this._attVec.push(new RoleAttVO(30411,40000,1,2200,2800,800,100,100,2,1400,2200,1500,1900,1000));
         this._attVec.push(new RoleAttVO(30421,40000,1,2600,3000,1000,100,100,2,1400,2100,1.5,1100,1,1200,8));
         this._attVec.push(new RoleAttVO(30422,30000,1,2000,2300,1000,100,100,2,1400,2100,1.5,1100,1,1200,8));
         this._attVec.push(new RoleAttVO(30431,54642,1,2800,3200,1100,100,100,4,1700,1500,1500,3,10));
         this._attVec.push(new RoleAttVO(30432,30000,1,2400,2800,1100,100,100,4,1700,2000,2000,3,10));
         this._attVec.push(new RoleAttVO(30441,54642,1,2700,3100,1300,100,100,4,1700,1500,2000,1500,2000));
         this._attVec.push(new RoleAttVO(30442,34642,1,2700,4100,1300,100,100,4,1700,2000,3000,2000,3000));
         this._attVec.push(new RoleAttVO(30451,60526,1,3300,3500,2800,100,100,4,2300,2000,2500,2000,3000));
         this._attVec.push(new RoleAttVO(30452,40526,1,3300,4500,2800,100,100,4,2300,3000,2500,3000,4000));
         this._attVec.push(new RoleAttVO(30461,65586,1,3000,3800,2000,100,100,4,3300,3000,2500,3,3000,3));
         this._attVec.push(new RoleAttVO(30462,35586,1,3000,4800,2000,100,100,4,3300,3000,2500,3,3000,3));
         this._attVec.push(new RoleAttVO(30471,73965,1,4000,4200,2000,100,100,4,3300,2500,3500,2500,2000));
         this._attVec.push(new RoleAttVO(30472,33965,1,4000,5200,2000,100,100,4,3300,3000,4500,3500,3000));
         this._attVec.push(new RoleAttVO(30481,79655,1,4000,4800,1500,100,100,4,3300,3000,2500,2500));
         this._attVec.push(new RoleAttVO(30491,56800,1,3000,5000,2500,100,100,4,3300,2000,3000,2000));
         this._attVec.push(new RoleAttVO(30501,68275,1,3500,5200,2500,100,100,4,3300,2000,2000,2500));
         this._attVec.push(new RoleAttVO(30511,60000,1,3500,4500,2900,100,100,4,3300,3000,2500));
         this._attVec.push(new RoleAttVO(30521,80000,1,3500,6000,3900,100,100,4,3300,2500,2500,3500));
         this._attVec.push(new RoleAttVO(30531,80000,1,3500,5000,3900,100,100,4,3300,2500,3500,2500));
         this._attVec.push(new RoleAttVO(30541,80000,1,3500,6000,3000,100,100,4,3300,2500,3000));
         this._attVec.push(new RoleAttVO(20531,30000,1,3500,9999,300,100,100,4,3300,2000,2000,2000));
         this._attVec.push(new RoleAttVO(30551,200000,1,4000,8000,2000,100,100,4,5000,2500,3000,3000,20531,20531));
         this._attVec.push(new RoleAttVO(30561,200000,1,4500,8500,2500,100,100,4,3300,3000,3000,1500,3000));
         this._attVec.push(new RoleAttVO(30571,200000,1,3500,9000,2000,100,100,4,3300,2500,2500,2500,2500,2500));
         this._attVec.push(new RoleAttVO(30581,200000,1,5000,10000,2000,100,100,4,3300,4000,5500,1000,10,3,6000));
         this._attVec.push(new RoleAttVO(30591,200000,1,5000,15000,3000,100,100,4,3300,3000,1500,2,4000,2500,1000,4,3000));
         this._attVec.push(new RoleAttVO(30601,200000,1,5000,16000,3000,100,100,4,3300,3500,3500,4000,8,3000));
         this._attVec.push(new RoleAttVO(30611,200000,1,5000,19000,3000,100,100,4,3300,8,5000,4000,3500,4000));
         this._attVec.push(new RoleAttVO(30621,200000,1,5000,15000,3000,100,100,4,3300,5000,3000,5000,7000));
         this._attVec.push(new RoleAttVO(30631,200000,1,5000,19000,3500,100,100,4,3300,4000,4000,6000,3000));
         this._attVec.push(new RoleAttVO(30641,200000,1,5000,20000,4000,100,100,4,3300,3000,2500,3000,5000));
         this._attVec.push(new RoleAttVO(30651,200000,1,5000,21000,4200,100,100,4,3300,3000,4500,4000));
         this._attVec.push(new RoleAttVO(30661,200000,1,5000,25000,4800,150,150,4,3300,4000,5500,5000,3000));
         this._attVec.push(new RoleAttVO(40011,14000,1,400,250,200,250,100,2,1000,1.5,300,5,160,400,2));
         this._attVec.push(new RoleAttVO(40012,14000,1,450,400,300,200,100,2,1000,1.5,300,5,160,400,2));
         this._attVec.push(new RoleAttVO(40021,14000,1,450,300,250,200,150,2,1000,250,300));
         this._attVec.push(new RoleAttVO(40022,10000,1,450,450,350,200,100,2,1000,250,300));
         this._attVec.push(new RoleAttVO(40031,18000,1,650,350,300,200,150,2,1000,400,3,120,400,2));
         this._attVec.push(new RoleAttVO(40032,10000,1,450,500,400,200,100,2,1000,400,3,120,400,2));
         this._attVec.push(new RoleAttVO(40041,22000,1,550,400,300,200,150,2,1000,2.5,600,40042,40042));
         this._attVec.push(new RoleAttVO(40043,17000,1,650,600,450,200,100,2,1000,2.5,400,40044,40042));
         this._attVec.push(new RoleAttVO(40042,99999,1,350,0,0,0,0,2,1000,0,900,6));
         this._attVec.push(new RoleAttVO(40044,99999,1,750,0,0,0,0,2,1000,0,900,6));
         this._attVec.push(new RoleAttVO(40051,24000,1,550,850,650,200,150,2,800,200,250,0.5,400,40052,40052));
         this._attVec.push(new RoleAttVO(40052,4000,1,650,600,100,200,100,2,1000,200,250,0.5,400));
         this._attVec.push(new RoleAttVO(40053,16800,1,750,850,800,200,150,2,800,400,550,0.5,600,40052,40052));
         this._attVec.push(new RoleAttVO(40061,35000,1,450,1300,1400,200,100,2,1000,450,550,0.5,200,5,100));
         this._attVec.push(new RoleAttVO(40062,25000,1,450,1600,1200,200,100,2,1000,450,750,0.5,300,5,100));
         this._attVec.push(new RoleAttVO(40071,35000,1,550,1500,1700,200,100,2,1000,450,550,2,600));
         this._attVec.push(new RoleAttVO(40072,25000,1,550,1700,1500,200,100,2,1000,650,750,2,800));
         this._attVec.push(new RoleAttVO(40081,38000,1,1050,2200,2000,200,100,4,1000,800,1000,1200,800));
         this._attVec.push(new RoleAttVO(40082,28000,1,1050,2400,1800,200,100,4,1000,800,400,900,600));
         this._attVec.push(new RoleAttVO(40091,40000,1,1688,2200,700,200,100,4,1000,15,1,1900,3,1500,900));
         this._attVec.push(new RoleAttVO(40101,51000,1,2600,4000,1900,100,50,4,1700,2500,1,3000,2500,3000));
         this._attVec.push(new RoleAttVO(40111,56666,1,3600,3500,2100,100,50,4,1700,3000,2500,1500,3000,3000));
         this._attVec.push(new RoleAttVO(40121,62333,1,3100,3500,1800,100,50,4,1700,3000,2500,2800,2900,20441,20441));
         this._attVec.push(new RoleAttVO(40131,68000,1,4100,3500,2000,100,50,4,1700,3500,3000,2500));
         this._attVec.push(new RoleAttVO(40141,73666,1,3100,4000,2000,100,50,4,1700,3000,2800,2500,20451,20451));
         this._attVec.push(new RoleAttVO(40151,79333,1,4600,4000,1800,100,50,4,1700,3000,2500,4000,2500));
         this._attVec.push(new RoleAttVO(40161,79333,1,4100,5000,1900,100,50,4,1700,1700,1500,3000,3500,1500));
         this._attVec.push(new RoleAttVO(40171,45000,1,2500,5000,1200,100,50,4,1500,2000,1500,2000,2500,1500));
         this._attVec.push(new RoleAttVO(40181,65000,1,3500,7000,2200,100,50,4,1500,3000,2500,3000,3500,1500));
         this._attVec.push(new RoleAttVO(40191,100000,1,3500,6000,2000,100,100,4,3500,2000,3000,4000));
         this._attVec.push(new RoleAttVO(40201,150000,1,4500,9000,2500,100,100,4,3500,3000,3500,4000,3000,4500));
         this._attVec.push(new RoleAttVO(40211,85000,1,6500,10000,4200,100,50,4,1500,4000,5500,4000,5500,5500));
         this._attVec.push(new RoleAttVO(40221,150000,1,2500,13000,3000,100,50,4,3500,4800,5500,4000));
         this._attVec.push(new RoleAttVO(40231,160000,1,3500,13000,3500,100,50,4,3500,5000,5500,4500));
         this._attVec.push(new RoleAttVO(40241,190000,1,3500,15000,3500,100,100,4,3500,5000,4500,3000,2500));
         this._attVec.push(new RoleAttVO(40251,210000,1,4500,17000,4000,100,100,4,3500,5000,4500,6000,3500));
      }
      
      public function findRoleAtt(param1:int) : RoleAttVO
      {
         var _loc2_:RoleAttVO = null;
         for each(_loc2_ in this._attVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

