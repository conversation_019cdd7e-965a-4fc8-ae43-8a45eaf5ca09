package mogames.gameMission.mission.npc.jingjiling
{
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameUI.pet.MiniPetModule;
   import utils.MethodUtil;
   
   public class SceneZhuLin extends BaseScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _btnPet:SimpleButton;
      
      public function SceneZhuLin(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         this.layoutPetBtn();
         this.initEnemies();
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[193,316,150,100],[747,316,150,100]],511,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1185,316,150,100],[1578,316,150,100]],512,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[2058,316,150,100],[2438,316,150,100]],513,this.triggerEnd2);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(672,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1440,450),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2334,450),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd2() : void
      {
         addLeaveDoor(2788,438);
      }
      
      private function triggerEnd0() : void
      {
         this.triggerEnd();
         CitrusLock.instance().lock(new Rectangle(0,0,1920,600),false,false,false,true);
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function layoutPetBtn() : void
      {
         this._btnPet = AssetManager.newButtonRes("BTN_PET_CLIP",887,16);
         Layers.frameLayer.addChild(this._btnPet);
         this._btnPet.addEventListener(MouseEvent.CLICK,this.onPet,false,0,true);
      }
      
      private function removePetBtn() : void
      {
         this._btnPet.removeEventListener(MouseEvent.CLICK,this.onPet);
         MethodUtil.removeMe(this._btnPet);
         this._btnPet = null;
      }
      
      private function onPet(param1:MouseEvent) : void
      {
         MiniPetModule.instance().init();
      }
      
      override protected function listenLeave() : void
      {
         (_mission as JinJiLingMission).onLeaveDoor();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this.removePetBtn();
         super.destroy();
      }
   }
}

