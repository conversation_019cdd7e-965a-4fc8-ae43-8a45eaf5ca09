package mogames.gameData.role.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   
   public class RoleAttVO
   {
      public var maxBar:int;
      
      public var id:Sint;
      
      public var baseHP:Sint;
      
      public var baseMP:Sint;
      
      public var baseATK:Sint;
      
      public var basePDEF:Sint;
      
      public var baseMDEF:Sint;
      
      public var baseCRIT:Sint;
      
      public var baseMISS:Sint;
      
      public var baseMOVE:Snum;
      
      public var argDic:Object;
      
      public function RoleAttVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:int, param7:int, param8:int, param9:Number, param10:int, ... rest)
      {
         super();
         this.id = new Sint(param1);
         this.baseHP = new Sint(param2);
         this.baseMP = new Sint(param3);
         this.baseATK = new Sint(param4);
         this.basePDEF = new Sint(param5);
         this.baseMDEF = new Sint(param6);
         this.baseCRIT = new Sint(param7);
         this.baseMISS = new Sint(param8);
         this.baseMOVE = new Snum(param9);
         this.maxBar = param10;
         this.argDic = new Object();
         var _loc12_:int = 0;
         var _loc13_:int = int(rest.length);
         while(_loc12_ < _loc13_)
         {
            this.argDic["arg" + _loc12_] = new Snum(rest[_loc12_]);
            _loc12_++;
         }
      }
   }
}

