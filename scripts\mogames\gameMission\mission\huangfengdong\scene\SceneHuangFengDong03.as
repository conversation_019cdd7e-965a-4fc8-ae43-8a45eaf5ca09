package mogames.gameMission.mission.huangfengdong.scene
{
   import citrus.objects.CitrusSprite;
   import flash.display.MovieClip;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.setTimeout;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.huangfengdong.EnemyJiGuanXiaoGuai;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BOSSHuangFengGuai;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.MethodUtil;
   import utils.TxtUtil;
   
   public class SceneHuangFengDong03 extends BossScene
   {
      private var _activeEnemy:EnemyJiGuanXiaoGuai;
      
      private var _tangseng:CitrusSprite;
      
      private var _mcTangSeng:MovieClip;
      
      private var _timer:CitrusTimer;
      
      private var _isDead:Boolean;
      
      private var _arg:Object = {
         "interval":10,
         "hp":300,
         "speed":1.2,
         "deadY":-83
      };
      
      public function SceneHuangFengDong03(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(270,342,446,110),new Point(480,414));
         this._timer = new CitrusTimer();
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2007,1);
            startBossBattle("BGM_BATTLE0");
         };
         this.addTangSeng();
         this.addBOSS();
         _mission.cleanLoadUI();
         if(FlagProxy.instance().isComplete(2007))
         {
            this.startBossBattle("BGM_BATTLE0");
         }
         else
         {
            showDialog("STORY0014",func);
         }
      }
      
      private function updateTangSeng() : void
      {
         if(this._isDead)
         {
            return;
         }
         this._tangseng.y += this._arg.speed;
         if(this._tangseng.y >= this._arg.deadY)
         {
            this.handlerTangSengDead();
         }
      }
      
      private function handlerTangSengDead() : void
      {
         var func:Function = null;
         func = function():void
         {
            setHeroEnable(false);
            Layers.lockGame = false;
            MissionManager.instance().handlerLose();
         };
         this._isDead = true;
         (this._tangseng.view as CitrusMC).changeAnimation("dead",true);
         setEnemiesEnable(false);
         Layers.lockGame = true;
         MiniMsgMediator.instance().showAutoMsg("唐僧已经西去......",480,200);
         setTimeout(func,1000);
      }
      
      private function listenXiaoGuai(param1:String) : void
      {
         if(param1 == "onDead")
         {
            this._activeEnemy = null;
            this._timer.setTimeOut(this._arg.interval,this.addXiaoGuai);
         }
         else if(param1 == "onRelease")
         {
            this.updateTangSeng();
         }
      }
      
      override protected function listenBOSS(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         if(this._activeEnemy)
         {
            this._activeEnemy.handlerDead(0);
         }
         super.listenBOSS(param1);
         this._tangseng.visible = false;
         this._mcTangSeng = GameInLoader.instance().findAsset("ROLE_STAND_TANG_SENG") as MovieClip;
         this._mcTangSeng.x = 542;
         this._mcTangSeng.y = 536;
         Layers.effectLayer.addChild(this._mcTangSeng);
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,542,536 - this._mcTangSeng.height * 0.5);
      }
      
      private function addXiaoGuai() : void
      {
         if(_isWin)
         {
            return;
         }
         var _loc1_:EnemyJiGuanXiaoGuai = new EnemyJiGuanXiaoGuai(this._arg.hp);
         _loc1_.x = 717;
         _loc1_.y = 338;
         add(_loc1_);
         _loc1_.onRole.add(this.listenXiaoGuai);
         this._activeEnemy = _loc1_;
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,765,400);
      }
      
      private function addTangSeng() : void
      {
         this._tangseng = new CitrusSprite("tangseng",{
            "width":40,
            "height":412,
            "group":2
         });
         this._tangseng.x = 467;
         this._tangseng.y = -300;
         var _loc1_:CitrusMC = new CitrusMC();
         _loc1_.setupMC(GameInLoader.instance().findAsset("MC_TANG_SENG_DIAO_CLIP"));
         _loc1_.changeAnimation("afraid",true);
         this._tangseng.view = _loc1_;
         add(this._tangseng);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHuangFengGuai();
         _boss.x = 600;
         _boss.y = 420;
         _boss.initData(30041,30041,8);
         _boss.target = _mission.curTarget;
         add(_boss);
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         super.startBossBattle(param1);
         this._timer.setTimeOut(this._arg.interval,this.addXiaoGuai);
         if(!_mission.missionVO.isComplete())
         {
            MiniMsgMediator.instance().showMsg("五行相克：" + TxtUtil.setColor("【木】","ff7f00") + "克" + TxtUtil.setColor("【土】","00ffff"),480,30);
         }
      }
      
      override protected function handlerWin() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(105,1);
            onWin();
            PromptMediator.instance().showPrompt(TxtUtil.setColor("【法宝合成】","99ff00") + "功能已经开放！<br>（花果山找灵吉菩萨）",null,null,true,"NEW_FUNCTION");
            TaskProxy.instance().addTask(11025);
         };
         MiniMsgMediator.clean();
         if(FlagProxy.instance().isComplete(105))
         {
            this.onWin();
         }
         else
         {
            showDialog("STORY0031",func);
         }
         TaskProxy.instance().setComplete(11003);
      }
      
      private function onWin() : void
      {
         MiniMsgMediator.clean();
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         MiniMsgMediator.clean();
         this._timer.destroy();
         this._timer = null;
         this._tangseng.view.destroy();
         this._tangseng = null;
         this._activeEnemy = null;
         if(this._mcTangSeng)
         {
            MethodUtil.removeMe(this._mcTangSeng);
         }
         this._mcTangSeng = null;
         super.destroy();
      }
   }
}

