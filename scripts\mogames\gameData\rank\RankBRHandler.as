package mogames.gameData.rank
{
   import mogames.gameData.rank.vo.RankRoleVO;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameData.role.HeroProxy;
   
   public class RankBRHandler extends RankHandler
   {
      public function RankBRHandler()
      {
         super([1702,1703,1704,1705,1706]);
         _msg = "初始化战力排行榜";
      }
      
      override public function parseRank(param1:Array) : Array
      {
         var _loc5_:RankRoleVO = null;
         if(!param1 || param1.length <= 0)
         {
            return [];
         }
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         var _loc4_:Array = [];
         while(_loc2_ < _loc3_)
         {
            _loc5_ = new RankRoleVO();
            _loc5_.parseData(param1[_loc2_]);
            _loc4_[_loc2_] = _loc5_;
            _loc2_++;
         }
         _loc4_.sortOn("sortIndex",Array.NUMERIC);
         return _loc4_;
      }
      
      override protected function get rankData() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(this.newRankData(HeroProxy.instance().monkeyVO,0));
         if(HeroProxy.instance().horseVO != null)
         {
            _loc1_.push(this.newRankData(HeroProxy.instance().horseVO,1));
         }
         if(HeroProxy.instance().pigVO != null)
         {
            _loc1_.push(this.newRankData(HeroProxy.instance().pigVO,2));
         }
         if(HeroProxy.instance().bonzeVO != null)
         {
            _loc1_.push(this.newRankData(HeroProxy.instance().bonzeVO,3));
         }
         if(HeroProxy.instance().drakanVO != null)
         {
            _loc1_.push(this.newRankData(HeroProxy.instance().drakanVO,4));
         }
         return _loc1_;
      }
      
      protected function newRankData(param1:HeroGameVO, param2:int) : Object
      {
         return {
            "rId":rankIDs[param2],
            "score":this.newScore(param1),
            "extra":[param1.level.v,param1.ownHP,param1.ownATK,param1.ownPDEF,param1.ownMDEF,param1.battleRate,param1.collectSaveData()]
         };
      }
      
      protected function newScore(param1:HeroGameVO) : int
      {
         return param1.battleRate;
      }
   }
}

