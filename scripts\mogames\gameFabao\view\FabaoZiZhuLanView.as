package mogames.gameFabao.view
{
   import mogames.gameData.ConstRole;
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoZiZhuLan;
   
   public class FabaoZiZhuLanView extends FabaoBaseView
   {
      private var _fabao:FabaoZiZhuLan;
      
      public function FabaoZiZhuLanView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         this.addSound(param1);
         super.updateCurrentFrame(param1);
         if(!mcATK)
         {
            return;
         }
         if(curStatus == ConstRole.SKILL_ONE)
         {
            this._fabao.dispatchSkillOne();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as FabaoZiZhuLan;
      }
      
      override protected function addSound(param1:int) : void
      {
         if(param1 == 30)
         {
            EffectManager.instance().playAudio("ATK_FO_SHOU");
         }
      }
   }
}

