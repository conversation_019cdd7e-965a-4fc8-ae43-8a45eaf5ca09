package mogames.gameMission.mission.heisonglin
{
   import flash.geom.Point;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.box2d.Door;
   
   public class SceneHeiSongLin01 extends BaseScene
   {
      private var _eTrigger:EnemyTrigger;
      
      private var _pTrigger:LocalTriggerX;
      
      public function SceneHeiSongLin01(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_SUNSHINE");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2011,1);
            TaskProxy.instance().addTask(11008);
         };
         this.initDoor802();
         _mission.cleanLoadUI();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2011))
         {
            showDialog("STORY0021",func);
         }
      }
      
      private function initEnemies() : void
      {
         if(_mission.hasMark("enemy801"))
         {
            return;
         }
         _mission.setMark("enemy801");
         this._eTrigger = new EnemyTrigger(_mission,[[58,370,150,100],[627,190,150,100],[1060,370,150,100]],8011);
         this._pTrigger = new LocalTriggerX(_mission.onePlayer,new Point(460,470),1);
         this._pTrigger.trigger = this._eTrigger;
         this._pTrigger.start();
      }
      
      private function initDoor802() : void
      {
         (getObjectByName("door802") as Door).active = true;
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger)
         {
            this._eTrigger.destroy();
         }
         if(this._pTrigger)
         {
            this._pTrigger.destroy();
         }
         this._eTrigger = null;
         this._pTrigger = null;
         super.destroy();
      }
   }
}

