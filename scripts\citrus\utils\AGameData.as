package citrus.utils
{
   import flash.utils.Dictionary;
   import flash.utils.Proxy;
   import flash.utils.flash_proxy;
   import org.osflash.signals.Signal;
   
   use namespace flash_proxy;
   
   public dynamic class AGameData extends Proxy
   {
      public var dataChanged:Signal;
      
      public var typeVerification:Boolean = true;
      
      private var __dict:Dictionary;
      
      private var __propNames:Vector.<String>;
      
      private var __numProps:int;
      
      public function AGameData()
      {
         super();
         this.__dict = new Dictionary();
         this.__propNames = new Vector.<String>();
         this.dataChanged = new Signal(String,Object);
      }
      
      override flash_proxy function callProperty(param1:*, ... rest) : *
      {
         if(this.__dict[param1] is Function)
         {
            return this.__dict[param1].apply(this,rest);
         }
         return undefined;
      }
      
      override flash_proxy function getDescendants(param1:*) : *
      {
         return this.__dict[param1];
      }
      
      override flash_proxy function isAttribute(param1:*) : Bo<PERSON>an
      {
         return param1 in this.__dict;
      }
      
      override flash_proxy function nextName(param1:int) : String
      {
         return this.__propNames[param1 - 1];
      }
      
      override flash_proxy function nextNameIndex(param1:int) : int
      {
         var _loc2_:Vector.<String> = null;
         var _loc3_:int = 0;
         var _loc4_:* = undefined;
         if(param1 == 0)
         {
            _loc2_ = this.__propNames;
            _loc2_.length = 0;
            for(_loc4_ in this.__dict)
            {
               var _loc7_:*;
               _loc2_[_loc7_ = _loc3_++] = _loc4_;
            }
            this.__numProps = _loc3_;
         }
         return param1 < this.__numProps ? param1 + 1 : 0;
      }
      
      override flash_proxy function nextValue(param1:int) : *
      {
         return this.__dict[this.__propNames[param1 - 1]];
      }
      
      override flash_proxy function deleteProperty(param1:*) : Boolean
      {
         var _loc2_:* = param1 in this.__dict;
         delete this.__dict[param1];
         return _loc2_;
      }
      
      override flash_proxy function getProperty(param1:*) : *
      {
         if(this.__dict[param1] != undefined)
         {
            return this.__dict[param1];
         }
         throw new ArgumentError("[AGameData] property " + param1 + " doesn\'t exist.");
      }
      
      override flash_proxy function hasProperty(param1:*) : Boolean
      {
         return this.__dict[param1] != undefined;
      }
      
      override flash_proxy function setProperty(param1:*, param2:*) : void
      {
         var _loc3_:Class = null;
         var _loc4_:Class = null;
         if(this.__dict[param1] != undefined)
         {
            if(this.typeVerification)
            {
               _loc3_ = param2.constructor;
               _loc4_ = this.__dict[param1].constructor;
               if(_loc3_ !== _loc4_)
               {
                  throw new ArgumentError("[AGameData] you\'re trying to set \'" + param1 + "\'s value of type " + _loc4_ + " to a new value of type " + _loc3_);
               }
            }
            if(param2 === this.__dict[param1])
            {
               return;
            }
            this.__dict[param1] = param2;
         }
         else
         {
            this.__dict[param1] = param2;
         }
         this.dataChanged.dispatch(String(param1),param2);
      }
      
      public function destroy() : void
      {
         this.__dict = null;
         this.__propNames.length = 0;
         this.dataChanged.removeAll();
      }
   }
}

