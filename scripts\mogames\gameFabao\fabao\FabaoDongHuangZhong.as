package mogames.gameFabao.fabao
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameFabao.view.FabaoDHZView;
   import mogames.gameMission.BattleMediator;
   import mogames.gameRole.hero.BaseHero;
   
   public class Fabao<PERSON>ong<PERSON><PERSON><PERSON>hong extends BaseFabao
   {
      private var _hitVO:BaseHitVO;
      
      public function FabaoDongHuangZhong(param1:GameFabaoVO)
      {
         super(param1,70,88);
         this.createFabaoData();
         this.createVO();
         createView(new FabaoDHZView());
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(19),
            "hurt":new Sint(3333),
            "keepTime":new Snum(5)
         },{
            "cdTime":new Snum(19),
            "hurt":new Sint(3333),
            "keepTime":new Snum(5)
         },{
            "cdTime":new Snum(19),
            "hurt":new Sint(3333),
            "keepTime":new Snum(5)
         },{
            "cdTime":new Snum(19),
            "hurt":new Sint(3333),
            "keepTime":new Snum(5)
         },{
            "cdTime":new Snum(19),
            "hurt":new Sint(3333),
            "keepTime":new Snum(5)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      private function createVO() : void
      {
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._hitVO.HURT_X = 0;
         this._hitVO.HURT_Y = 0;
         this._hitVO.HURT_SKIN = "EFFECT_GUN_ATK";
         this._hitVO.HURT_SOUND = "GUNHURT";
         this._hitVO.WU_XING.v = _fabaoVO.constFabao.wuxing.v;
      }
      
      override public function followTarget() : void
      {
         if(_fabaoView.isSkill)
         {
            return;
         }
         super.followTarget();
      }
      
      override public function handlerSkill() : void
      {
         x = owner.x + owner.dirX * 50;
         super.handlerSkill();
      }
      
      public function dispatchSkillOne() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         this._hitVO.init(_fabaoView.mcATK,_fabaoData.hurt.v,1,false);
         this._hitVO.isMiss = false;
         if(owner is BaseHero)
         {
            BattleMediator.instance().onHeroATK.dispatch(this._hitVO);
         }
         else
         {
            BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
         }
         owner.addBuff(new BuffVO(1010,_fabaoData.keepTime.v,{
            "per":30,
            "skin":"EFFECT_BUFF_MDEF_CLIP0"
         }));
         owner.addBuff(new BuffVO(1009,_fabaoData.keepTime.v,{"per":30}));
      }
   }
}

