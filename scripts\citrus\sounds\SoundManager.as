package citrus.sounds
{
   import aze.motion.eaze;
   import citrus.core.citrus_internal;
   import citrus.events.CitrusEventDispatcher;
   import citrus.events.CitrusSoundEvent;
   import flash.media.SoundMixer;
   import flash.media.SoundTransform;
   import flash.utils.Dictionary;
   
   public class SoundManager extends CitrusEventDispatcher
   {
      internal static var _instance:SoundManager;
      
      protected var soundsDic:Dictionary;
      
      protected var soundGroups:Vector.<CitrusSoundGroup>;
      
      protected var _masterVolume:Number = 1;
      
      protected var _masterMute:Boolean = false;
      
      public function SoundManager()
      {
         super();
         this.soundsDic = new Dictionary();
         this.soundGroups = new Vector.<CitrusSoundGroup>();
         this.createGroup(CitrusSoundGroup.BGM);
         this.createGroup(CitrusSoundGroup.SFX);
         this.createGroup(CitrusSoundGroup.UI);
         addEventListener(CitrusSoundEvent.SOUND_LOADED,this.handleSoundLoaded);
      }
      
      public static function getInstance() : SoundManager
      {
         if(!_instance)
         {
            _instance = new SoundManager();
         }
         return _instance;
      }
      
      public function destroy() : void
      {
         var _loc1_:CitrusSoundGroup = null;
         var _loc2_:CitrusSound = null;
         for each(_loc1_ in this.soundGroups)
         {
            _loc1_.destroy();
         }
         for each(_loc2_ in this.soundsDic)
         {
            _loc2_.destroy();
         }
         this.soundsDic = null;
         _instance = null;
         removeEventListeners();
      }
      
      public function addSound(param1:String, param2:Object = null) : void
      {
         if(!param2.hasOwnProperty("sound"))
         {
            throw new Error("SoundManager addSound() sound:" + param1 + "can\'t be added with no sound definition in the params.");
         }
         if(!(param1 in this.soundsDic))
         {
            this.soundsDic[param1] = new CitrusSound(param1,param2);
         }
      }
      
      public function addGroup(param1:CitrusSoundGroup) : CitrusSoundGroup
      {
         this.soundGroups.push(param1);
         return param1;
      }
      
      public function createGroup(param1:String) : CitrusSoundGroup
      {
         var _loc2_:CitrusSoundGroup = null;
         var _loc3_:CitrusSoundGroup = null;
         for each(_loc3_ in this.soundGroups)
         {
            if(_loc3_.groupID == param1)
            {
               _loc2_ = _loc3_;
            }
         }
         if(_loc2_ != null)
         {
            return _loc2_;
         }
         _loc2_ = new CitrusSoundGroup();
         _loc2_.citrus_internal::setGroupID(param1);
         this.soundGroups.push(_loc2_);
         return _loc2_;
      }
      
      public function removeGroup(param1:String) : void
      {
         var _loc2_:CitrusSoundGroup = this.getGroup(param1);
         var _loc3_:int = int(this.soundGroups.lastIndexOf(_loc2_));
         if(_loc3_ > -1)
         {
            this.soundGroups.splice(_loc3_,1);
            _loc2_.destroy();
         }
      }
      
      public function moveSoundToGroup(param1:String, param2:String = null) : void
      {
         var _loc3_:CitrusSound = null;
         var _loc4_:CitrusSoundGroup = null;
         if(param1 in this.soundsDic)
         {
            _loc3_ = this.soundsDic[param1];
            if(_loc3_.group != null)
            {
               _loc3_.group.removeSound(_loc3_);
            }
            if(param2 != null)
            {
               _loc4_ = this.getGroup(param2);
            }
            if(_loc4_)
            {
               _loc4_.addSound(_loc3_);
            }
         }
      }
      
      public function getGroup(param1:String) : CitrusSoundGroup
      {
         var _loc2_:CitrusSoundGroup = null;
         for each(_loc2_ in this.soundGroups)
         {
            if(_loc2_.groupID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function getSound(param1:String) : CitrusSound
      {
         if(param1 in this.soundsDic)
         {
            return this.soundsDic[param1];
         }
         return null;
      }
      
      public function preloadAllSounds() : void
      {
         var _loc1_:CitrusSound = null;
         for each(_loc1_ in this.soundsDic)
         {
            _loc1_.load();
         }
      }
      
      public function pauseAll(... rest) : void
      {
         var _loc2_:CitrusSound = null;
         var _loc3_:* = null;
         loop0:
         for each(_loc2_ in this.soundsDic)
         {
            for each(_loc3_ in rest)
            {
               if(_loc3_ == _loc2_.name)
               {
                  continue loop0;
               }
            }
            _loc2_.pause();
         }
      }
      
      public function resumeAll(... rest) : void
      {
         var _loc2_:CitrusSound = null;
         var _loc3_:* = null;
         loop0:
         for each(_loc2_ in this.soundsDic)
         {
            for each(_loc3_ in rest)
            {
               if(_loc3_ == _loc2_.name)
               {
                  continue loop0;
               }
            }
            _loc2_.resume();
         }
      }
      
      public function playSound(param1:String) : CitrusSoundInstance
      {
         if(param1 in this.soundsDic)
         {
            return CitrusSound(this.soundsDic[param1]).play();
         }
         return null;
      }
      
      public function pauseSound(param1:String) : void
      {
         if(param1 in this.soundsDic)
         {
            CitrusSound(this.soundsDic[param1]).pause();
         }
      }
      
      public function resumeSound(param1:String) : void
      {
         if(param1 in this.soundsDic)
         {
            CitrusSound(this.soundsDic[param1]).resume();
         }
      }
      
      public function stopSound(param1:String) : void
      {
         if(param1 in this.soundsDic)
         {
            CitrusSound(this.soundsDic[param1]).stop();
         }
      }
      
      public function removeSound(param1:String) : void
      {
         this.stopSound(param1);
         if(param1 in this.soundsDic)
         {
            CitrusSound(this.soundsDic[param1]).destroy();
            this.soundsDic[param1] = null;
            delete this.soundsDic[param1];
         }
      }
      
      public function soundIsPlaying(param1:String) : Boolean
      {
         if(param1 in this.soundsDic)
         {
            return CitrusSound(this.soundsDic[param1]).isPlaying;
         }
         return false;
      }
      
      public function soundIsPaused(param1:String) : Boolean
      {
         if(param1 in this.soundsDic)
         {
            return CitrusSound(this.soundsDic[param1]).isPaused;
         }
         return false;
      }
      
      public function removeAllSounds(... rest) : void
      {
         var _loc2_:CitrusSound = null;
         var _loc3_:* = null;
         loop0:
         for each(_loc2_ in this.soundsDic)
         {
            for each(_loc3_ in rest)
            {
               if(_loc3_ == _loc2_.name)
               {
                  continue loop0;
               }
            }
            this.removeSound(_loc2_.name);
         }
      }
      
      public function get masterVolume() : Number
      {
         return this._masterVolume;
      }
      
      public function get masterMute() : Boolean
      {
         return this._masterMute;
      }
      
      public function set masterVolume(param1:Number) : void
      {
         var _loc3_:String = null;
         var _loc2_:Number = this._masterVolume;
         if(param1 >= 0 && param1 <= 1)
         {
            this._masterVolume = param1;
         }
         else
         {
            this._masterVolume = 1;
         }
         if(_loc2_ != this._masterVolume)
         {
            for(_loc3_ in this.soundsDic)
            {
               this.soundsDic[_loc3_].resetSoundTransform(true);
            }
         }
      }
      
      public function set masterMute(param1:Boolean) : void
      {
         var _loc2_:String = null;
         if(param1 != this._masterMute)
         {
            this._masterMute = param1;
            for(_loc2_ in this.soundsDic)
            {
               this.soundsDic[_loc2_].resetSoundTransform(true);
            }
         }
      }
      
      public function soundIsAdded(param1:String) : Boolean
      {
         return param1 in this.soundsDic;
      }
      
      public function muteFlashSound(param1:Boolean = true) : void
      {
         var _loc2_:SoundTransform = SoundMixer.soundTransform;
         _loc2_.volume = param1 ? 0 : 1;
         SoundMixer.soundTransform = _loc2_;
      }
      
      public function isFlashSoundMuted() : Boolean
      {
         return SoundMixer.soundTransform.volume == 0;
      }
      
      public function setVolume(param1:String, param2:Number) : void
      {
         if(param1 in this.soundsDic)
         {
            this.soundsDic[param1].volume = param2;
         }
      }
      
      public function setPanning(param1:String, param2:Number) : void
      {
         if(param1 in this.soundsDic)
         {
            this.soundsDic[param1].panning = param2;
         }
      }
      
      public function setMute(param1:String, param2:Boolean) : void
      {
         if(param1 in this.soundsDic)
         {
            this.soundsDic[param1].mute = param2;
         }
      }
      
      public function stopAllPlayingSounds(... rest) : void
      {
         var _loc2_:CitrusSound = null;
         var _loc3_:* = null;
         loop0:
         for each(_loc2_ in this.soundsDic)
         {
            for each(_loc3_ in rest)
            {
               if(_loc3_ == _loc2_.name)
               {
                  continue loop0;
               }
            }
            this.stopSound(_loc2_.name);
         }
      }
      
      public function tweenVolume(param1:String, param2:Number = 0, param3:Number = 2, param4:Function = null) : void
      {
         var citrusSound:CitrusSound = null;
         var tweenvolObject:Object = null;
         var id:String = param1;
         var volume:Number = param2;
         var tweenDuration:Number = param3;
         var callback:Function = param4;
         if(this.soundIsPlaying(id))
         {
            citrusSound = CitrusSound(this.soundsDic[id]);
            tweenvolObject = {"volume":citrusSound.volume};
            eaze(tweenvolObject).to(tweenDuration,{"volume":volume}).onUpdate(function():void
            {
               citrusSound.volume = tweenvolObject.volume;
            }).onComplete(function():void
            {
               if(callback != null)
               {
                  if(callback.length == 0)
                  {
                     callback();
                  }
                  else
                  {
                     callback(citrusSound);
                  }
               }
            });
         }
      }
      
      public function crossFade(param1:String, param2:String, param3:Number = 2) : void
      {
         this.tweenVolume(param1,0,param3);
         this.tweenVolume(param2,1,param3);
      }
      
      protected function handleSoundLoaded(param1:CitrusSoundEvent) : void
      {
         var _loc2_:CitrusSound = null;
         for each(_loc2_ in this.soundsDic)
         {
            if(!_loc2_.loaded)
            {
               return;
            }
         }
         dispatchEvent(new CitrusSoundEvent(CitrusSoundEvent.ALL_SOUNDS_LOADED,param1.sound,null));
      }
   }
}

