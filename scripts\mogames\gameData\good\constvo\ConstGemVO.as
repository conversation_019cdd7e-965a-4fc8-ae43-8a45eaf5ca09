package mogames.gameData.good.constvo
{
   import mogames.gameData.base.ValueVO;
   
   public class ConstGemVO extends ConstATTVO
   {
      public function ConstGemVO(param1:int, param2:String, param3:String, param4:ValueVO, param5:ValueVO, param6:ValueVO, param7:ValueVO, param8:ValueVO, param9:int, param10:int, param11:int, param12:int, param13:int, param14:String)
      {
         super(param1,param2,param3,param4,param5,param6,param7,param8,param9,param10,param11,param12,param13,param14);
      }
   }
}

