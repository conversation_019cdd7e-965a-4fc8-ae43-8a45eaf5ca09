package mogames.gameData.rank
{
   import com.hexagonstar.util.debug.Debug;
   import mogames.gameData.rank.vo.RankTotalBRVO;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameData.role.HeroProxy;
   
   public class RankTotalBR extends RankHandler
   {
      public function RankTotalBR()
      {
         super([1782]);
         _msg = "初始化总战力排行榜";
      }
      
      override public function parseRank(param1:Array) : Array
      {
         var _loc5_:RankTotalBRVO = null;
         if(!param1 || param1.length <= 0)
         {
            return [];
         }
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         var _loc4_:Array = [];
         while(_loc2_ < _loc3_)
         {
            _loc5_ = new RankTotalBRVO();
            _loc5_.parseData(param1[_loc2_]);
            _loc4_[_loc2_] = _loc5_;
            _loc2_++;
         }
         _loc4_.sortOn("sortIndex",Array.NUMERIC);
         return _loc4_;
      }
      
      override protected function get rankData() : Array
      {
         return [this.newRankData()];
      }
      
      protected function newRankData() : Object
      {
         return {
            "rId":rankIDs[0],
            "score":this.countTotalBR(),
            "extra":this.collectAllBR()
         };
      }
      
      private function collectAllBR() : Array
      {
         var _loc5_:HeroGameVO = null;
         var _loc1_:Array = [];
         var _loc2_:Array = HeroProxy.instance().collectAllHero();
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = _loc2_[_loc3_];
            _loc1_[_loc3_] = _loc5_ != null ? _loc5_.battleRate : 0;
            _loc3_++;
         }
         Debug.trace("各个角色战力：" + _loc1_);
         return _loc1_;
      }
      
      private function countTotalBR() : int
      {
         var _loc2_:int = 0;
         var _loc3_:HeroGameVO = null;
         var _loc1_:Array = HeroProxy.instance().enableHero;
         for each(_loc3_ in _loc1_)
         {
            _loc2_ += _loc3_.battleRate;
         }
         return _loc2_;
      }
   }
}

