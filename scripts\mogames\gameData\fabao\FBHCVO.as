package mogames.gameData.fabao
{
   import file.GoodConfig;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.UseHandler;
   import mogames.gameData.forge.NeedVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameData.good.constvo.ConstEquipVO;
   
   public class FBHCVO extends BaseUseVO
   {
      public var paperID:Sint;
      
      public var fabaoID:Sint;
      
      public function FBHCVO(param1:int, param2:int, param3:int, param4:Array)
      {
         super(param3,param4);
         this.paperID = new Sint(param1);
         this.fabaoID = new Sint(param2);
      }
      
      override public function useStuff() : void
      {
         if(needGold.v != 0)
         {
            MasterProxy.instance().changeGold(-needGold.v);
         }
         UseHandler.instance().useStuff(needList.concat(new NeedVO(this.paperID.v,1)));
      }
      
      public function handlerCombine() : GameFabaoVO
      {
         this.useStuff();
         var _loc1_:GameFabaoVO = GoodConfig.instance().newGood(this.fabaoID.v) as GameFabaoVO;
         _loc1_.initNewEquip(0,true,false);
         BagProxy.instance().addDirect(_loc1_);
         return _loc1_;
      }
      
      public function get constFabao() : ConstEquipVO
      {
         return GoodConfig.instance().findConstGood(this.fabaoID.v) as ConstEquipVO;
      }
   }
}

