package mogames.gameData.pet
{
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetHurtSkillVO;
   
   public class PetJinLingVO extends PetGameVO
   {
      public function PetJinLingVO()
      {
         allSkills = this.newActiveSkills();
         super(1103);
      }
      
      override public function newActiveSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new PetHurtSkillVO(11031,this,2));
         _loc1_.push(new PetHurtSkillVO(11032,this));
         _loc1_.push(new PetHurtSkillVO(11033,this));
         _loc1_.push(new PetHurtSkillVO(11034,this));
         _loc1_.push(new PetHurtSkillVO(11035,this,3));
         _loc1_.push(new PetHurtSkillVO(11036,this));
         return _loc1_;
      }
   }
}

