package citrus.core
{
   import citrus.system.Entity;
   import citrus.view.ACitrusView;
   
   public interface IState
   {
      function destroy() : void;
      
      function get view() : ACitrusView;
      
      function initialize() : void;
      
      function update(param1:Number) : void;
      
      function add(param1:CitrusObject) : CitrusObject;
      
      function addEntity(param1:Entity) : Entity;
      
      function remove(param1:CitrusObject) : void;
      
      function removeImmediately(param1:CitrusObject) : void;
      
      function getObjectByName(param1:String) : CitrusObject;
      
      function getFirstObjectByType(param1:Class) : CitrusObject;
      
      function getObjectsByType(param1:Class) : Vector.<CitrusObject>;
   }
}

