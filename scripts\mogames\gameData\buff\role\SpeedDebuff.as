package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class SpeedDebuff extends BaseBuff
   {
      private var _move:Number;
      
      private var _run:Number;
      
      public function SpeedDebuff()
      {
         super(1002,true);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         this._move = _roleVO.baseMOVE.v * _buffVO.argDic.arg0.v;
         this._run = _roleVO.baseRUN.v * _buffVO.argDic.arg0.v;
         _roleVO.skillMOVE.v -= this._move;
         _roleVO.skillRUN.v -= this._run;
         _roleVO.updateSPEED();
         if(_role.isReady)
         {
            EffectManager.instance().addHeadWord("移动速度降低！",_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override protected function cleanEffect() : void
      {
         _roleVO.skillMOVE.v += this._move;
         _roleVO.skillRUN.v += this._run;
         _roleVO.updateSPEED();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         if(!_buffVO.argDic.skin)
         {
            return null;
         }
         _skin.setupSequence(AssetManager.findAnimation(_buffVO.argDic.skin),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

