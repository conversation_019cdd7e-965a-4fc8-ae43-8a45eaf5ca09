package file
{
   import flash.utils.Dictionary;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.ValueRandVO;
   import mogames.gameData.base.ValueVO;
   import mogames.gameData.good.GameATTVO;
   import mogames.gameData.good.GameCardVO;
   import mogames.gameData.good.GameEquipVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameData.good.GameGemVO;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.good.PetEquipVO;
   import mogames.gameData.good.constvo.ConstADDGoodVO;
   import mogames.gameData.good.constvo.ConstATTVO;
   import mogames.gameData.good.constvo.ConstCardVO;
   import mogames.gameData.good.constvo.ConstClipVO;
   import mogames.gameData.good.constvo.ConstDanYaoVO;
   import mogames.gameData.good.constvo.ConstEquipVO;
   import mogames.gameData.good.constvo.ConstFabaoVO;
   import mogames.gameData.good.constvo.ConstGemVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   import mogames.gameData.good.constvo.ConstPKGVO;
   import mogames.gameData.good.constvo.ConstPetVO;
   import mogames.gameData.good.constvo.ConstSeedVO;
   import utils.TxtUtil;
   
   public class GoodConfig
   {
      private static var _instance:GoodConfig;
      
      public var goodDic:Dictionary;
      
      public function GoodConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : GoodConfig
      {
         if(!_instance)
         {
            _instance = new GoodConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this.goodDic = new Dictionary();
         this.goodDic[ConstData.GOOD_PROP] = [];
         this.goodDic[ConstData.GOOD_EQUIP] = [];
         this.goodDic[ConstData.GOOD_FASHION] = [];
         this.goodDic[ConstData.GOOD_PET] = [];
         this.goodDic[ConstData.GOOD_CLIP] = [];
         this.goodDic[ConstData.CHENG_HAO] = [];
         this.goodDic[ConstData.GOOD_CARD] = [];
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10000,"铜钱","TONG_QIAN3",1,0,0,"西游世界的流通货币之一，可用于装备强化、法宝合成、购买物品等。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10001,"虎牙","HU_YA",1,0,5,"普通的虎牙。<br>掉落：虎妖、虎法"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10002,"熊肉","XIONG_LOU",1,0,6,"普通的熊肉。<br>掉落：熊妖"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10003,"狼牙","LANG_YA",1,0,50,"锋利的狼牙。<br>掉落：黑松林黄袍怪"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10004,"狐尾","JIUWEIHU_WEIBA",1,0,7,"柔软的狐狸尾巴。<br>掉落：平顶山玉狐"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10005,"彼岸花","BIAN_HUA",1,0,20,"普通的花，生长在山壁中。<br>掉落：黄风洞"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10006,"芫花","YUAN_HUA",1,0,20,"普通的花，生长在山壁中。<br>掉落：黄风洞"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10007,"银锭","YIN_DING",1,4,0,"可在银锭商城中购买一些稀有物品等。完成每日任务获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10008,"鼍角","TUO_JIAO",1,1,70,"鼍龙的角。<br>掉落：黑水河掉落,金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10009,"鱼鳍","YU_QI",1,1,80,"通天河掉落。每日任务道具。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10010,"粽子","ZHONG_ZI",1,3,300,"端午节活动道具。<br>掉落：镇妖塔"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10011,"经验","JING_YAN",1,4,1,"提升角色等级。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10012,"钥匙","YAO_SHI",1,1,1,"一把生锈的钥匙，能打开某处的牢笼。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10013,"止泻草","ZHIXIE_CAO",1,1,1,"非常普通的药草，对腹泻有奇效。<br>掉落：黄风洞"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10014,"野果","YE_GUO",1,1,10,"用来充饥的食物。<br>掉落：白虎岭。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10015,"三清殿钥匙","SANQINGDIAN_YAOSHI",1,1,1,"三清殿大门的钥匙。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10016,"月饼","YUE_BING",1,3,300,"中秋节活动道具。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10017,"国","GUO_GUO",1,3,1,"国庆活动道具，活动界面中兑换奖励。在线奖励、关卡掉落"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10018,"庆","QIN_QIN",1,3,1,"国庆活动道具，活动界面中兑换奖励。在线奖励、宝藏副本、镇妖塔各层掉落"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10019,"快","KUAI_KUAI",1,3,1,"国庆活动道具，活动界面兑换取奖励。在线奖励、宝藏副本、每日任务（紫色）获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10020,"乐","LE_LE",1,3,1,"国庆活动道具，活动界面中兑换奖励。在线奖励、每日任务（橙色）获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10021,"1级原石","YI_YUANSHI",1,0,100,"吐蕃荆棘岭找NPC分解1-19级装备获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10022,"2级原石","ER_YUANSHI",1,1,200,"吐蕃荆棘岭找NPC分解20-29级装备获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10023,"3级原石","SAN_YUANSHI",1,2,300,"吐蕃荆棘岭找NPC分解30-39级装备获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10024,"4级原石","SI_YUANSHI",1,2,400,"吐蕃荆棘岭找NPC分解40-49级装备获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10025,"冰麒麟装备碎片","QILINGZB_SUIPIAN",1,3,1,"随机掉落冰麒麟各部位装备碎片。<br>掉落：冰麒麟副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10026,"冰麒麟装备合成材料","QILINGZB_CAILIAO",1,3,1,"随机掉落蓝墨晶、绿苔岩、水精岩。<br>掉落：冰麒麟副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10027,"周","ZHOU_ZHOU",1,4,1,"国庆活动道具，活动界面中兑换奖励。每日活跃、关卡概率掉落"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10028,"年","NIAN_NIAN",1,4,1,"国庆活动道具，活动界面中兑换奖励。每日活跃、宝藏副本、镇妖塔各层概率掉落"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10029,"快","KUAI1_KUAI",1,4,1,"国庆活动道具，活动界面兑换取奖励。每日活跃、宝藏副本、每日任务（紫色）获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10030,"乐","LE1_LE",1,4,1,"国庆活动道具，活动界面中兑换奖励。每日活跃、每日任务（橙色）获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10031,"火凤凰装备碎片","HUOFH_SUIPIAN",1,3,1,"随机掉落火凤凰各部位装备碎片。<br>掉落：火凤凰副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10032,"火凤凰装备合成材料","HUOFH_CAILIAO",1,3,1,"随机掉落红墨晶、炎苔岩、火精岩。<br>掉落：火凤凰副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10033,"金锭","JIN_DING",1,4,1,"金锭"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(10034,"玄武装备","QILINGZB_CAILIAO",1,3,1,"概率圣石爪、圣石盔、圣石甲、圣石坠的其中1件。<br>掉落：玄武副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPetVO(10090,"冰麒麟","BING_QILING","极品冰麒麟（变异）<br>成长1.5<br>满技能栏<br>西游世界神宠，拥有强大的冰系技能。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPetVO(10091,"冰麒麟","BING_QILING","极品冰麒麟<br>成长1.3、天资10、慧根10、悟性10<br>满技能栏（商城获得）<br>变异满成长冰麒麟Vip6赠送<br>西游世界神宠之一，拥有强大的冰系技能。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPetVO(10092,"火凤凰","HUO_FENGHUANG","极品火凤凰（变异）<br>成长1.5<br>满技能栏<br>西游世界神宠，拥有强大的火系技能。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPetVO(10093,"火凤凰","HUO_FENGHUANG","极品火凤凰<br>成长1.3、天资10、慧根10、悟性10<br>满技能栏（商城获得）<br>变异满成长火凤凰Vip7赠送<br>西游世界神宠之一，拥有强大的火系技能。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPetVO(10094,"玄武","ICON_XUAN_WU","极品玄武（变异）<br>成长1.5<br>满技能栏（幸运宝袋获得）<br>西游世界神宠，拥有强大的土系技能。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPetVO(10095,"玄武","ICON_XUAN_WU","极品玄武<br>成长1.3、天资10、慧根10、悟性10<br>满技能栏（幸运宝袋商城获得）<br>变异满成长幸运宝袋获得<br>西游世界神宠之一，拥有强大的土系技能。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstADDGoodVO(11001,"馒头","MAN_TOU",0,0,150,0,0,1,"水果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstADDGoodVO(11002,"鸡腿","JI_TUI",1,0,200,0,1,1,"水果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstADDGoodVO(11003,"烧鸡","SHAO_JI",1,0,400,0,2,1,"水果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstADDGoodVO(11004,"乳猪","RU_ZHU",1,0,600,0,3,1,"水果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstADDGoodVO(11005,"苹果","PING_GUO",0,1,100,0,0,1,"水果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstADDGoodVO(11006,"香蕉","XIANG_JIAO",1,1,200,0,1,1,"水果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstADDGoodVO(11007,"葡萄","PU_TAO",1,1,400,0,2,1,"水果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstADDGoodVO(11008,"桃子","TAO_ZI",1,1,600,0,3,1,"水果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12001,"缚妖索图谱","FUYAOSUO_TUPU",0,3,20,"缚妖索：对前方妖怪造成伤害并束缚，找灵吉菩萨合成。<br>掉落：白虎岭白骨夫人"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12002,"照妖镜图谱","ZHAOYAOJING_TUPU",0,3,50,"照妖镜：对前方妖怪持续攻击，找灵吉菩萨合成。<br>掉落：双叉岭寅将军"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12003,"净瓶图谱","YUJINGPIN_TUPU",0,3,100,"净瓶：恢复自血量，找灵吉菩萨合成。<br>掉落：黑松林黄袍怪"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12004,"避水珠图谱","BISHUIZHU_TUPU",0,3,130,"避水珠：减少所受伤害并能进入水下场景找灵吉菩萨合成。<br>掉落：乌鸡国狮猁王"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12005,"镇妖塔图谱","ZHENYAOTA_TUPU",0,3,160,"镇妖塔：开启镇妖塔副本重要法宝。<br>掉落：黑水河鼍龙"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12006,"驭兽袋图谱","YUSHOUDAI_TUPU",0,3,180,"驭兽袋：抓捕宠物重要法宝。<br>合成材料查看【图鉴】<br>掉落：黄风洞黄风怪"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12007,"紫金铃图谱","ZIJINLIN_TUPU",0,3,200,"紫金铃：对前方妖怪持续攻击。<br>合成材料查看【图鉴】<br>掉落：太岁副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12008,"玲珑鼎图谱","LLD_TUPU",0,3,200,"玲珑鼎：对前方妖怪攻击。<br>合成材料查看【图鉴】<br>掉落：封魔塔2层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12009,"阴阳瓶图谱","YYP_TUPU",0,3,200,"阴阳瓶：使用后可恢复适当法力。<br>合成材料查看【图鉴】<br>掉落：找观音购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(12010,"金铙图谱","JN_TUPU",0,3,200,"金铙：使用后对全屏怪物造成伤害并晕眩。<br>合成材料查看【图鉴】<br>掉落：找观音购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13101,"甘泉","GAN_QUAN",1,3,50,"神奇的泉水，用处广泛。<br>花果山找观世音购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13102,"虎筋","HU_JIN",1,3,50,"合成缚妖索材料，具体合成查看【图鉴】。<br>掉落：双叉岭寅将军、银锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13103,"熊筋","XIONG_JIN",1,3,50,"合成缚妖索材料，具体合成查看【图鉴】。<br>掉落：黑风山黑熊精、银锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13104,"玄金","XUAN_JIN",1,3,100,"合成照妖镜、宠物装备材料，具体合成查看【图鉴】。<br>掉落：黄风洞黄风怪,金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13105,"水之石","SHUI_ZHI_SHI",1,3,100,"合成净瓶材料，具体合成查看【图鉴】。<br>掉落：挑战小白龙普通难度副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13106,"净瓶碎片","JINGPIN_SUIPIAN",1,3,100,"合成净瓶材料，具体合成查看【图鉴】。<br>掉落：黑松林小头目"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13107,"珍珠","ZHEN_ZHU",1,3,100,"合成镇妖塔、芭蕉扇、宠物装备材料，具体合成查看【图鉴】。<br>掉落：乌鸡国狮猁王,金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13108,"地火石","DIHUO_SHI",1,3,100,"合成镇妖塔、芭蕉扇、宠物装备材料，具体合成查看【图鉴】。<br>掉落：火云洞红孩儿,金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13109,"镇妖塔碎片","ZHENYAOTA_SUIPIAN",1,3,100,"合成镇妖塔材料（灵吉菩萨）<br>合成宠物装备材料（杏仙）<br>掉落：黑水河鼍龙,金锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13110,"芭蕉叶","BAJIAO_YE",1,3,100,"合成芭蕉扇材料、宠物装备材料，具体合成查看【图鉴】。<br>掉落：火焰山牛魔王、金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13111,"紫金铃碎片","ZIJINLIN_SUIPIAN",1,3,100,"合成紫金铃材料，具体合成查看【图鉴】。<br>掉落：太岁副本、金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13112,"太岁毛发","MAOFA_TAISUI",1,3,100,"合成紫金铃材料，具体合成查看【图鉴】。<br>掉落：太岁副本、金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13113,"太岁内丹","NEIDAN_TAISUI",1,3,100,"合成紫金铃材料，具体合成查看【图鉴】。<br>掉落：太岁副本、金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13114,"乌眼石","WUYAN_SHI",1,3,100,"提升紫金铃绿色品质材料，具体合成查看【图鉴】。<br>掉落：太岁副本、金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13115,"天火","TIAN_HUO",1,3,100,"提升紫金铃蓝色品质材料，具体合成查看【图鉴】。<br>掉落：太岁副本、金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13116,"蝎尾","XIE_WEI",1,3,100,"合成宠物装备材料，具体合成查看【图鉴】。<br>掉落：琵琶洞、金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13117,"金石","JING_SHI",1,3,100,"合成宠物装备材料，具体合成查看【图鉴】。<br>掉落：乱石山、金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13118,"黄眼石","HUANGYAN_SHI",1,3,100,"合成宠物装备材料，具体合成查看【图鉴】。<br>掉落：小雷音寺"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13119,"水蓝石","SHUILAN_SHI",1,3,100,"合成宠物装备材料，具体合成查看【图鉴】。<br>掉落：七绝山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13120,"绿苔岩","LVTAI_YAN",1,3,100,"合成宠物装备材料（冰麒麟），具体合成查看【图鉴】。<br>掉落：冰麒麟副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13121,"水精岩","SHUIJIN_YAN",1,3,100,"合成宠物装备材料（冰麒麟），具体合成查看【图鉴】。<br>掉落：冰麒麟副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13122,"蓝墨晶","LANMO_JIN",1,3,100,"合成宠物装备材料（冰麒麟），具体合成查看【图鉴】。<br>掉落：冰麒麟副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13130,"炎苔岩","YANTAI_YAN",1,3,100,"合成宠物装备材料（火凤凰），具体合成查看【图鉴】。<br>掉落：火凤凰副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13131,"火精岩","HUOJIN_YAN",1,3,100,"合成宠物装备材料（火凤凰），具体合成查看【图鉴】。<br>掉落：火凤凰副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13132,"红墨晶","HONGMO_JIN",1,3,100,"合成宠物装备材料（火凤凰），具体合成查看【图鉴】。<br>掉落：火凤凰副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13133,"玲珑鼎碎片","LGD_SP",1,3,100,"合成玲珑鼎材料，具体合成查看【图鉴】。<br>掉落：封魔塔2层、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13134,"玲珑珠","LG_ZHU",1,3,100,"合成玲珑鼎材料，具体合成查看【图鉴】。<br>掉落：封魔塔2层、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13135,"玲珑石","LG_SHI",1,3,100,"合成玲珑鼎材料，具体合成查看【图鉴】。<br>掉落：封魔塔2层、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13136,"精陨石","JYUN_SHI",1,3,100,"提升玲珑鼎绿色品质材料，具体合成查看【图鉴】。<br>掉落：封魔塔3层、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13137,"玲粉石","LFEN_SHI",1,3,100,"提升玲珑鼎绿色品质材料，具体合成查看【图鉴】。<br>掉落：封魔塔3层、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13138,"玲青石","LQIN_SHI",1,3,100,"提升玲珑鼎蓝色品质材料，具体合成查看【图鉴】。<br>掉落：封魔塔3层、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13139,"玲金石","LJIN_SHI",1,3,100,"提升玲珑鼎蓝色品质材料，具体合成查看【图鉴】。<br>掉落：封魔塔3层、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13140,"金铙碎片","JINNAO_SP",1,3,100,"合成金铙或其它法宝材料，具体合成查看【图鉴】。<br>掉落：公会每日福利（飞升以上头衔）"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13141,"翠绿石","CUILV_SHI",1,4,100,"提升阴阳瓶绿色品质材料，具体合成查看【图鉴】。<br>掉落：妖岛-英山、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13142,"橄榄石","GANLAN_SHI",1,4,100,"提升阴阳瓶绿色品质材料，具体合成查看【图鉴】。<br>掉落：妖岛-英山、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13143,"紫皇叶","ZIHUANG_YE",1,4,100,"提升驭兽袋紫色品质材料，具体合成查看【图鉴】。<br>花果山-观音"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13144,"迷彩石","MICAI_SHI",1,4,100,"提升阴阳瓶蓝色品质材料，具体合成查看【图鉴】。<br>掉落：妖岛-杻阳山、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13145,"旋龟角","XUANGUI_SHI",1,4,100,"提升阴阳瓶蓝色品质材料，具体合成查看【图鉴】。<br>掉落：妖岛-杻阳山、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13146,"炼彩石","LIANCAI_SHI",1,4,100,"提升金铙品质材料，具体合成查看【图鉴】。<br>掉落：妖岛-太山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13301,"石玉","SHI_YU",1,1,100,"找灵吉菩萨提升净瓶品质，具体合成查看【图鉴】。掉落：乌鸡国水牢副本变异蟾蜍"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13302,"蚕丝","CAN_SI",1,1,100,"找灵吉菩萨提升避水珠品质，具体合成查看【图鉴】。掉落：乌鸡国水牢副本变异灯鱼"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13303,"绿玄晶","LV_XUANJIN",1,1,100,"提升法宝绿色品质材料，具体合成查看【图鉴】。掉落：挑战小白龙困难难度副本,金锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13304,"灯鱼内丹","DENGYU_NEIDAN",1,1,100,"找灵吉菩萨提升避水珠品质，具体合成查看【图鉴】。掉落：乌鸡国水牢副本变异灯鱼"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13305,"蟾蜍内丹","CHANCHU_NEIDAN",1,1,100,"找灵吉菩萨提升净瓶品质，具体合成查看【图鉴】。掉落：乌鸡国水牢副本变异蟾蜍"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13306,"玉骨","YU_GU",1,1,50,"找灵吉菩萨提升照妖镜品质，具体合成查看【图鉴】。掉落：白虎岭白骨夫人掉落,金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13307,"貂鼠皮","DIAOSHU_PI",1,1,50,"找灵吉菩萨提升照妖镜品质，具体合成查看【图鉴】。掉落：黄风洞黄风怪,金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13308,"狼毛","LANG_MAO",1,1,50,"找灵吉菩萨提升缚妖索品质，具体合成查看【图鉴】。掉落：黑松林黄袍怪,金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13309,"银角","YIN_JIAO",1,1,50,"找灵吉菩萨提升缚妖索品质，具体合成查看【图鉴】。掉落：平顶山银角大王掉落,金锭商城购买"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13310,"千年玄铁","QIANNIAN_XUANTIE",1,1,100,"提升镇妖塔品质的材料，具体合成查看【图鉴】。掉落：金锭商城、降妖塔第一层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13311,"寄居蟹内丹","JIJUXIE_NEIDAN",1,1,100,"找灵吉菩萨提升镇妖塔品质，具体合成查看【图鉴】。掉落：乌鸡国水牢副本变异寄居蟹"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13312,"蚌精内丹","BANGJING_NEIDAN",1,1,100,"掉落：乌鸡国水牢副本变异蚌精掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13313,"蓝玄晶","LAN_XUANJIN",1,2,150,"提升法宝蓝色品质材料，具体合成查看【图鉴】。掉落：金锭商城、降妖塔第二层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13314,"褐玉","HE_YU",1,2,150,"提升照妖镜品质材料，合成宠物装备材料，具体合成查看【图鉴】。掉落：金锭商城、金兜山掉落"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13315,"精霞石","JINGXIA_SHI",1,2,150,"找灵吉菩萨提升照妖镜品质，具体合成查看【图鉴】。掉落：金锭商城、挑战猪八戒困难难度副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13316,"土檀石","TUXUAN_SHI",1,2,150,"提升缚妖索蓝色品质材料，具体合成查看【图鉴】。掉落：金锭商城、琵琶洞木之镇碑副本泥浆怪<br>"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13317,"紫晶","ZI_JIN",1,2,150,"提升法宝蓝色品质材料，具体合成查看【图鉴】。掉落：金锭商城、挑战小白龙副本噩梦难度"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13318,"褐檀石","HEXUAN_SHI",1,2,150,"提升避水珠蓝色品质材料，具体合成查看【图鉴】。掉落：金锭商城、琵琶洞木之镇碑副本岩鼠精"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13319,"绿酝石","LVYUN_SHI",1,1,100,"提升芭蕉扇、紫金铃绿色品质材料，具体合成查看【图鉴】。掉落：金锭商城、降妖塔第1层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13320,"蓝琉石","LANLIU_SHI",1,2,150,"提升芭蕉扇、紫金铃蓝色品质材料，具体合成查看【图鉴】。掉落：金锭商城、降妖塔第三层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13321,"1级洗练石","YI_XILIANSHI",1,0,150,"吐蕃荆棘岭找NPC孤直公洗练1-19级装备。<br>黑松林、平顶山掉落"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13322,"2级洗练石","ER_XILIANSHI",1,1,150,"吐蕃荆棘岭找NPC孤直公洗练20-29级装备。<br>金锭商城、降妖塔第一层掉落（镇妖塔副本）"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13323,"虹檀石","HONGXUAN_SHI",1,2,150,"提升镇妖塔蓝色品质材料，具体合成查看【图鉴】。掉落：金锭商城、琵琶洞木之镇碑副本巨岩精"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13324,"红岩晶","HONGYAN_JIN",1,2,150,"提升净瓶蓝色品质材料，具体合成查看【图鉴】。掉落：金锭商城、琵琶洞木之镇碑副本岩石将军"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13325,"3级洗练石","SAN_XILIANSHI",1,2,150,"吐蕃荆棘岭找NPC孤直公洗练30-39级装备。<br>掉落：金锭商城、降妖塔第二层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13326,"4级洗练石","SI_XILIANSHI",1,2,200,"吐蕃荆棘岭找NPC孤直公洗练40-49级装备。<br>掉落：金锭商城、猪八戒副本噩梦难度"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13327,"橙绚石","CHENGXUAN_SHI",1,4,200,"提升缚妖索、照妖镜、避水珠橙色品质材料。<br>掉落：龙宫-宰相府"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13328,"紫绚石","CHENG_JING",1,4,200,"提升净瓶、镇妖塔、驭兽袋橙色品质材料。<br>掉落：龙宫-珊瑚礁"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13329,"鸿绚石","HONG_JING",1,4,200,"提升芭蕉扇、紫金铃、玲珑鼎橙色品质材料。<br>掉落：龙宫-沉船"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13330,"绿绚石","LV_XUAN_SHI",1,4,200,"提升阴阳瓶橙色品质材料。<br>掉落：龙宫-深渊"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13401,"氢钢岩","QINGANG_YAN",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：金锭商城、降妖塔3层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13402,"青琉石","QINLIU_SHI",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：金锭商城、降妖塔3层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13403,"兰芽石","LANYA_SHI",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：金锭商城、降妖塔3层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13404,"墨裂石","MOLIE_SHI",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：金锭商城、封魔塔1层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13405,"黄硫石","HUANGLIU_SHI",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：金锭商城、封魔塔1层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13406,"幻晶石","HUANJING_SHI",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：金锭商城、封魔塔1层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13407,"冰莲","BING_LIAN",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：冰麒麟副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13408,"冰晶","BING_JIN",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：冰麒麟副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13409,"玄冰","XIAN_BING",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：冰麒麟副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13410,"炎莲","YAN_LIAN",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：火凤凰副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13411,"炎晶","YAN_JIN",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：火凤凰副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13412,"玄炎","XIAN_YAN",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：火凤凰副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13413,"土莲","TU_LIAN",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：玄武副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13414,"土晶","TU_JIN",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：玄武副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13415,"玄土","XIAN_TU",1,4,300,"吐蕃荆棘岭找NPC杏仙熔炼宠物装备。<br>掉落：玄武副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13550,"金红石","JINHONG_SHI",1,4,500,"43级武器升阶材料。<br>掉落：妖岛—凶犁土丘"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13551,"金檀石","JINXUAN_SHI",1,4,500,"43级武器升阶材料。<br>掉落：妖岛—三危山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13552,"绿玛石","LVMA_SHI",1,4,500,"43级武器升阶材料。<br>掉落：妖岛—三危山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13553,"水曲石","SHUIQU_SHI",1,4,500,"43级武器升阶材料。<br>掉落：妖岛—凶犁土丘"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13554,"土裂晶","TULIE_SHI",1,4,500,"43级武器升阶材料。<br>掉落：妖岛—凶犁土丘"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13555,"火络晶","HUOLUO_SHI",1,4,500,"43级武器升阶材料。<br>掉落：妖岛—凶犁土丘"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13556,"橙砾石","CHENLE_SHI",1,4,500,"43级武器升阶材料。<br>掉落：妖岛—凶犁土丘"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13557,"玄紫石","XUANZI_SHI",1,4,500,"45级衣服升阶材料。<br>掉落：妖岛—章尾山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13558,"玄黄石","XUANHUANG_SHI",1,4,500,"45级衣服升阶材料。掉落：妖岛—章尾山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13559,"蓝岚石","LANFEGN_SHI",1,4,500,"45级衣服升阶材料。掉落：妖岛—章尾山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13560,"土青石","TUQING_SHI",1,4,500,"45级衣服升阶材料。掉落：妖岛—章尾山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13561,"厉金石","JINLI_SHI",1,4,500,"45级衣服升阶材料。掉落：妖岛—迷林"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13562,"水吟石","SHUIYIN_SHI",1,4,500,"45级衣服升阶材料。掉落：妖岛—迷林"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(13563,"火流石","HUOLIU_SHI",1,4,500,"45级衣服升阶材料。掉落：妖岛—迷林"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14000,"粉尘","FEN_CHEN",1,4,100,"用于合成五品宝石。<br>可找花果山通天葫芦进行合成<br>掉落：七圣副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14001,"1级强化石","QIANGHUASHI_YI",1,1,100,"用于强化+1至+5装备。找花果山铁匠强化。掉落：黄风洞、白虎岭、竞技场商城、小白龙副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14002,"2级强化石","QIANGHUASHI_ER",1,1,200,"用于强化+6至+8装备。<br>找花果山铁匠进行强化<br>掉落：镇妖塔2层、黑市、竞技场商城、金锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14003,"3级强化石","QIANGHUASHI_SAN",1,2,300,"用于强化+9至+10装备。<br>找花果山铁匠进行强化"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14007,"粉尘心","FEN_CHEN1",1,4,100,"用于合成六品宝石。<br>可找花果山通天葫芦进行合成<br>掉落：七圣副本、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14008,"炼妖丹","LIANYAO_DAN",1,4,300,"使用后宠物炼妖100%成功。<br>掉落：寻宝副本概率、黑市、金锭商城购"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14009,"复活丹","FUHUO_DAN",1,4,300,"使用后角色立即复活，复活后拥有50%血量20%法力。VIP有额外加成。<br>掉落：竞技场商城、黑市、金锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14010,"打孔器","DAKONG_QI",1,1,10,"用于装备打孔，花果山找通天葫处进行打孔。<br>掉落：竞技场商城、银锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14011,"拆解石","CHAIJIE_SHI",1,3,10,"用于已强化过的装备拆解，拆解后根据强化等级返还60%强化石。<br>掉落：降妖塔1层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14020,"黄金锤","HUANGJIN_CHUI",1,3,10,"用于拆卸宝石，花果山找通天葫处进行拆卸。<br>掉落：竞技场商城、银锭商城、黑市。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14021,"1级焚魔石","FENGMOSHI_YI",1,4,300,"用于强化时装，可找花果山铁匠进行强化。<br>掉落：金锭银锭商城、降妖塔1层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14025,"绿焚石","LVFENG_SHI",1,1,300,"用于时装提升品质，背包中点击时装进行提升。<br>掉落：金锭商城、降妖塔2层掉"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14027,"蓝焚石","LANFENG_SHI",1,2,300,"用于时装提升品质，背包中点击时装进行提升。<br>掉落：金锭商城、降妖塔3层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14028,"紫焚石","ZIFEN_SHI",1,3,300,"用于时装提升品质，背包中点击时装进行提升。<br>掉落：金锭商城、挑战沙僧困难难度"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14029,"羽萝石","YULUO_SHI",1,1,100,"用于时装提升品质，背包中点击时装进行提升。<br>掉落：金锭商城、降妖塔2层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14030,"神檀石","SHENXUAN_SHI",1,3,10,"宝石合成成功率+20%。<br>掉落：黑市、金锭商城。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14031,"沙梵石","SHAFENG_SHI",1,1,100,"用于时装提升品质，背包中点击时装进行提升。<br>掉落：金锭商城、降妖塔2层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14035,"晶岩石","JINGYAN_SHI",1,2,150,"用于时装提升品质，背包中点击时装进行提升。<br>掉落：金锭商城、降妖塔3层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14037,"芽愿石","YAYUAN_SHI",1,2,150,"用于时装提升品质，背包中点击时装进行提升。<br>掉落：金锭商城、降妖塔3层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14039,"2级焚魔石","FENGMOSHI_ER",1,4,500,"用于强化时装。<br>可找花果山铁匠进行强化<br>掉落：金锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14045,"3级焚魔石","FENGMOSHI_SAN",1,4,800,"用于强化时装。<br>可找花果山铁匠进行强化<br>掉落：金锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14050,"新手礼包","XINSHOU_LIBAO",1,4,1,"打开后获得玉蝉X2、天人经验丹X10、复活丹X20<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14051,"沙僧新手礼包","SHAS1_LIBAO",1,4,1,"打开后获得时装风行衣X1、天人经验丹X10、复活丹X10、天玄石X5、玉蝉X2<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14052,"沙僧进阶礼包","SHAS2_LIBAO",1,4,1,"打开后获得时装风行铲X1、天人经验丹X20、3级洗练石X10、土系二品丹药各6颗<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14053,"天玄礼包","TIANXUAN_LIBAO",1,4,1,"打开后获得天玄石X20。<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14054,"三品清心丹礼包","SANPINQINGXIN_LIBAO",1,4,1,"打开后获得时金、木、水、火、土系三品清心丹各3颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14055,"银锭礼包","YINDING_LIBAO",1,4,1,"打开后可获得银锭1600。<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14056,"三品大力丹礼包","SANPINDALI_LIBAO",1,4,1,"打开后获得时金、木、水、土系三品大力丹各3颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14057,"二品宝石箱","ERBAOSHI_XIANG",1,4,1,"打开后可获得二品生命石X1、二品物防石X1、二品魔防石X1"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14058,"三品宝石箱","SANBAOSHI_XIANG",1,4,1,"打开后可获得三品生命石X1、三品物防石X1、三品魔防石X1<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14059,"四品宝石箱","SIBAOSHI_XIANG",1,4,1,"打开后可获得四品生命石X1、四品物防石X1、四品魔防石X1"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14060,"焚魔礼包","FENGMOSHI_LIBAO",1,4,1,"打开后获得1级焚魔石X20、2级焚魔石X20、天玄石X5"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14061,"宠物新手礼包","CHONGWU1_LIBAO",1,4,1,"打开后获得宠物经验丹X20、仙桃X5、延寿丹X5<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14062,"宠物进阶礼包","CHONGWU2_LIBAO",1,4,1,"打开后获得洗髓丹X20、云御锁X10、莹月草X5、月光草X5<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14063,"宠物资质礼包","CHONGWU3_LIBAO",1,4,1,"打开后获得资质丹X30"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14064,"五品宝石包","WUPING_BAOSHIBAO",1,4,1,"打开后可获得五品生命石X1、五品物防石X1、五品魔防石X1"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14065,"暗金时装套","ANJING1_TAO",1,3,1,"打开后获得孙悟空暗金时装一套【紫装】<br>时装拥有主动技能<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14066,"秋水时装套","QIUSHUI1_TAO",1,3,1,"打开后获得猪八戒秋水时装一套【紫装】<br>时装拥有主动技能<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14067,"月影时装套","YUEYING1_TAO",1,3,1,"打开后获得小白龙月影时装一套【紫装】<br>时装拥有主动技能<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14068,"日月时装套","RIYUE1_TAO",1,3,1,"打开后获得沙僧日月时装一套【紫装】<br>时装拥有主动技能<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14069,"宠物经验包","CHONGWU_JINGYAN",1,4,1,"打开后获得宠物经验丹X99<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14070,"强化礼包","QIANGHUA_LIBAO",1,4,1,"打开后获得1级强化石X30、2级强化石X20、天玄石X5<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14071,"玉蝉礼包","YUCHAN_LIBAO",1,4,1,"【5折】打开后获得玉蝉X99，玉蝉卖出后共获得495W铜钱<br>获得方式：商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14072,"资质丹礼包","ZIZHIDAN_LIBAO",1,4,1,"打开后获得资质丹X50<br>获得方式：每周限购"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14073,"卡魂礼包","KAHUN_LIBAO",1,4,1,"打开后获得卡魂X10<br>获得方式：每周限购"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14074,"五品宝石包","WUPING_BAOSHIBAO",1,4,1,"打开后可获得五品生命石、五品物防石、五品魔防石其中1颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14075,"二品丹药袋(土)","TUXI_DANYAOXIAO",1,4,1,"打开后获得土系二品丹药各3颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14076,"二品丹药袋(木)","MUXI_DANYAOXIAO",1,4,1,"打开后获得木系二品丹药各3颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14077,"竞技场礼包","JINGJICHANG_LIBAO",1,4,1,"打开后获得挑战符X20、刷新令X10<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14078,"暗金日月礼包","SHIZHUANG2_LIBAO1",1,4,1,"打开后获得孙悟空暗金时装、沙僧日月时装一套【紫装】<br>时装拥有主动技能<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14079,"秋水月影礼包","SHIZHUANG2_LIBAO2",1,4,1,"打开后获得小白龙月影时装、猪八戒秋水时装一套【紫装】<br>时装拥有主动技能<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14080,"四品天心丹礼包","SIPINTIANXIN_LIBAO",1,4,1,"打开后获得时金、木、水、火、土系四品天心丹各5颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14081,"孙悟空新手礼包","WUKONG1_LIBAO",1,4,1,"打开后获得时装祥瑞衣X1、天人经验丹X10、复活丹X10、天玄石X5、玉蝉X2<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14082,"孙悟空进阶礼包","WUKONG2_LIBAO",1,4,1,"打开后获得时装祥瑞棍X1、天人经验丹X20、3级洗练石X10、金系二品丹药各6颗<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14083,"猪八戒新手礼包","BAJIE1_LIBAO",1,4,1,"打开后获得时装晴川衣X1、天人经验丹X10、复活丹X10、天玄石X5、玉蝉X2<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14084,"猪八戒进阶礼包","BAJIE2_LIBAO",1,4,1,"打开后获得时装晴川拳套X1、天人经验丹X20、3级洗练石X10、木系二品丹药各6颗<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14085,"小白龙新手礼包","BAILONG1_LIBAO",1,4,1,"打开后获得时装魅影衣X1、天人经验丹X10、复活丹X10、天玄石X5、玉蝉X2<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14086,"小白龙进阶礼包","BAILONG2_LIBAO",1,4,1,"打开后获得时装魅影剑X1、天人经验丹X20、3级洗练石X10、水系二品丹药各6颗<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14090,"魔炎兽骨套装","SHOUGU_TAO",1,4,1,"打开后获得满属性罕见品质兽骨套装（魔炎装备）<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14091,"蛇灵兽羽套装","SHOUYU_TAO",1,4,1,"打开后获得满属性罕见品质兽羽套装（蛇灵装备）<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14092,"金灵兽俐套装","SHOULI_TAO",1,4,1,"打开后获得满属性罕见品质兽俐套装（金灵装备）<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14093,"水灵兽凝套装","SHOUNING_TAO",1,4,1,"打开后获得满属性罕见品质兽凝套装（水灵装备）<br>获得方式：每周限购"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14095,"麒麟圣冰套装","SHENGBING_TAO",1,4,1,"打开后获得满属性罕见品质圣冰套装（麒麟装备）<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14096,"龙女丹药礼包1","LONGNVDY1_LIBAO",1,4,1,"打开后获得一品大力丹[火]、天心丹[火]、清心丹[火]各1颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14097,"龙女丹药礼包2","LONGNVDY2_LIBAO",1,4,1,"打开后获得二品大力丹[火]、天心丹[火]、清心丹[火]各1颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14098,"龙女丹药礼包3","LONGNVDY3_LIBAO",1,4,1,"打开后获得三品大力丹[火]、天心丹[火]、清心丹[火]各1颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14099,"龙女丹药礼包4","LONGNVDY4_LIBAO",1,4,1,"打开后获得四品大力丹[火]、天心丹[火]、清心丹[火]各1颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14100,"龙女丹药礼包5","LONGNVDY5_LIBAO",1,4,1,"打开后获得五品大力丹[火]、天心丹[火]、清心丹[火]各1颗"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14101,"天玄石","TIANXUAN_SHI",1,4,500,"用于装备强化，可提高30%成功率。<br>掉落：金锭商城、降妖塔1层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14102,"开封石","KAIFENG_SHI",1,3,500,"用于装备开普通卡槽。<br>掉落：竞技场商城、银锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14103,"极品开封石","JPKAIFENG_SHI",1,4,1000,"用于装备开BOSS卡槽。<br>掉落：银锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14104,"封元石","FENGYUAN_SHI",1,3,500,"用于装备普通卡片镶嵌。<br>掉落：银锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14105,"极品封元石","JPFENGYUAN_SHI",1,4,1000,"用于装备BOSS卡片镶嵌。<br>掉落：银锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14106,"拆卡符","CHAIKA_FU",1,4,1000,"用于装备卡片拆除。<br>掉落：银锭商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14107,"卡魂","KA_HUN",1,4,1000,"卡片强化失败后不消失。<br>掉落：商城、黑市、寻宝副本"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14108,"经验丹礼包","JYD_LIBAO",1,4,1000,"打开后可获得天人经验丹99颗<br>掉落：每周限购"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14109,"火凤圣炎套装","SHENGYAN_TAO",1,4,1,"打开后获得满属性罕见品质圣炎套装（火凤装备）<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14110,"混元石","HUNYUAN_SHI",1,4,1,"法宝重塑100%成功<br>获得方式：商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14111,"玄武圣石套装","SHENGSHI_TAO",1,4,1,"打开后获得满属性罕见品质圣石套装（玄武装备）<br>获得方式：春节累充活动"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14112,"东皇钟礼包","DHUANG_ZHONG",1,4,0,"打开后获得法宝东皇钟，使用后额外增加双防30%<br>获得方式：累充活动"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14120,"1级蓝空石","LANKONG_1",1,1,100,"用于强化+1至+5宠物装备。<br>掉落：竞技场商城、黄泉路副本、商城、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14121,"2级蓝空石","LANKONG_2",1,2,200,"用于强化+6至+8宠物装备。<br>掉落：黄泉路副本、商城黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14122,"3级蓝空石","LANKONG_3",1,3,300,"用于强化+9至+10宠物装备。<br>暂无掉落"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14123,"3级强化石","QIANGHUASHI_SAN",1,2,300,"用于强化+9至+10装备。<br>找花果山铁匠进行强化"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14124,"宠物装备强化包","CHONGWU_QIANGHUA",1,4,1000,"打开后获得20个1级蓝空石10个2级蓝空石5个天阴石。<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14125,"天阴石","TIANYIN_SHI",1,4,500,"用于宠物装备强化，可提高20%成功率。<br>掉落：黄泉路副本概率、商城黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14130,"阴阳瓶碎片","YYP_SP",1,4,1,"用于合成法宝阴阳瓶。<br>掉落：公会挑战-饕餮"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14131,"阴阳石","YINYANG_SHI",1,4,1,"用于合成法宝阴阳瓶。<br>掉落：公会挑战-饕餮"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14132,"两仪石","LIANGYI_SHI",1,4,1,"用于合成法宝阴阳瓶。<br>掉落：公会挑战-饕餮"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14150,"天人经验丹","TIANREN_DAN",1,4,10,"服用后经验增加8000。<br>掉落：竞技场商城、金锭商城",true));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14165,"红凤时装套","HONGFENG1_TAO",1,3,1,"打开后获得孙悟空红凤时装一套【紫装】<br>时装拥有主动技能<br>获得方式：商城、荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14166,"红麟时装套","HONGLING1_TAO",1,3,1,"打开后获得猪八戒红麟时装一套【紫装】<br>时装拥有主动技能<br>获得方式：商城、荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14167,"红轩时装套","HONGXUAN1_TAO",1,3,1,"打开后获得小白龙红轩时装一套【紫装】<br>时装拥有主动技能<br>获得方式：商城、荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14168,"红辕时装套","HONGYUAN1_TAO",1,3,1,"打开后获得沙僧红辕时装一套【紫装】<br>时装拥有主动技能<br>获得方式：商城、荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14169,"红霓时装套","HONGNI1_TAO",1,3,1,"打开后获得小龙女红霓时装一套【紫装】<br>时装拥有主动技能<br>获得方式：商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(14170,"荣誉令","RONGYU_LIN",1,4,1,"在竞技场商城兑换道具。竞技场前1000名奖励获得"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14171,"圣·苍羽套","CANGYU_TAO",1,4,1,"打开后获得孙悟空圣·苍羽一套【橙色品质】<br>时装拥有主动技能<br>获得方式：神装合成、商城、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14172,"圣·翠羽套","CUIYU_TAO",1,4,1,"打开后获得猪八戒圣·翠羽一套【橙色品质】<br>时装拥有主动技能<br>获得方式：神装合成、商城、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14173,"圣·浩羽套","HAOYU_TAO",1,4,1,"打开后获得小白龙圣·浩羽一套【橙色品质】<br>时装拥有主动技能<br>获得方式：神装合成、商城、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14174,"圣·尘羽套","CHENYU_TAO",1,4,1,"打开后获得沙僧圣·尘羽一套【橙色品质】<br>时装拥有主动技能<br>获得方式：神装合成、商城、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14175,"圣·炽羽套","CHIYU_TAO",1,4,1,"打开后获得小龙女圣·炽羽一套【橙色品质】<br>时装拥有主动技能<br>获得方式：神装合成、商城"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(14176,"魅蓝装套","MEILAN1_TAO",1,3,1,"打开后获得小龙女魅蓝时装一套【紫装】<br>时装拥有主动技能<br>获得方式：荆棘岭黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14201,"一品生命石","YIPIN_SHENGMING",new ValueVO(20,0),null,null,null,null,0,0,0,0,100,"生命+20。<br>掉落：镇妖塔（降妖塔第2层）<br>通关通天河后，花果山找通天葫芦镶嵌。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14211,"一品物防石","YIPIN_WUFANG",null,null,null,new ValueVO(6,0),null,0,0,0,0,100,"物防+6。<br>掉落：镇妖塔（降妖塔第2层）<br>通关通天河后，花果山找通天葫芦镶嵌。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14221,"一品魔防石","YIPIN_MOFANG",null,null,null,null,new ValueVO(6,0),0,0,0,0,100,"魔防+6。<br>掉落：镇妖塔（降妖塔第2层）<br>通关通天河后，花果山找通天葫芦镶嵌。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14202,"二品生命石","ERPIN_SHENGMING",new ValueVO(40,0),null,null,null,null,0,0,0,1,500,"生命+40。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14212,"二品物防石","ERPIN_WUFANG",null,null,null,new ValueVO(12,0),null,0,0,0,1,500,"物防+12。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14222,"二品魔防石","ERPIN_MOFANG",null,null,null,null,new ValueVO(12,0),0,0,0,1,500,"魔防+12。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14203,"三品生命石","SANPIN_SHENGMING",new ValueVO(70,0),null,null,null,null,0,0,0,1,1000,"生命+70。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14213,"三品物防石","SANPIN_WUFANG",null,null,null,new ValueVO(20,0),null,0,0,0,1,1000,"物防+20。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14223,"三品魔防石","SANPIN_MOFANG",null,null,null,null,new ValueVO(20,0),0,0,0,1,1000,"魔防+20。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14204,"四品生命石","SIPIN_SHENGMING",new ValueVO(140,0),null,null,null,null,0,0,0,2,1500,"生命+140。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14214,"四品物防石","SIPIN_WUFANG",null,null,null,new ValueVO(35,0),null,0,0,0,2,1500,"物防+35。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14224,"四品魔防石","SIPIN_MOFANG",null,null,null,null,new ValueVO(35,0),0,0,0,2,1500,"魔防+35。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成、黑市"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14205,"五品生命石","WUPIN_SHENGMING",new ValueVO(280,0),null,null,null,null,0,0,0,3,2500,"生命+280。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14215,"五品物防石","WUPIN_WUFANG",null,null,null,new ValueVO(60,0),null,0,0,0,3,2500,"物防+60。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14225,"五品魔防石","WUPIN_MOFANG",null,null,null,null,new ValueVO(60,0),0,0,0,3,2500,"魔防+60。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14206,"六品生命石","LIUPIN_SHENGMING",new ValueVO(560,0),null,null,null,null,0,0,0,4,3500,"生命+560。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14216,"六品物防石","LIUPIN_WUFANG",null,null,null,new ValueVO(90,0),null,0,0,0,4,3500,"物防+90。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGemVO(14226,"六品魔防石","LIUPIN_MOFANG",null,null,null,null,new ValueVO(90,0),0,0,0,4,3500,"魔防+90。<br>花果山找通天葫芦镶嵌。<br>通天葫芦合成"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(15001,"柏果","BAI_GUO",1,0,100,"用于丹药合成等。<br>掉落：平顶山右边山洞里"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(15002,"落英花","LUOYING_HUA",1,0,100,"用于丹药合成等。<br>掉落：平顶山右边山洞里"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(15003,"乌舌兰","WUSHE_LAN",1,0,100,"用于丹药合成等。<br>掉落：平顶山右边山洞里"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(15004,"水灵","SHUI_LIN",1,4,1,""));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15100,"玄空时装套（永久）","XIONGMAO_ZHUANG",1,4,100,"获得永久时装玄空套<br>（随机角色）。<br>竞技场周赛奖励第1名获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15101,"玄空时装套（5小时）","XIONGMAO_ZHUANG",1,4,100,"获得时装玄空套<br>（随机角色、下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15102,"玄空时装套（10小时）","XIONGMAO_ZHUANG",1,4,100,"获得时装玄空套<br>（随机角色、下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15103,"玄空时装套（15小时）","XIONGMAO_ZHUANG",1,4,100,"获得时装玄空套<br>（随机角色、下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15104,"玄空时装套（20小时）","XIONGMAO_ZHUANG",1,4,100,"获得时装玄空套<br>（随机角色、下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15105,"玄空时装套（25小时）","XIONGMAO_ZHUANG",1,4,100,"获得时装玄空套<br>（随机角色、下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15106,"玄空时装套（30小时）","XIONGMAO_ZHUANG",1,4,100,"获得时装玄空套<br>（随机角色、下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15201,"孙悟空玄空套装（5小时）","XIONGMAO_WUKONG",1,4,100,"打开后获得孙悟空玄空套装5小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15202,"猪八戒玄空套装（5小时）","XIONGMAO_BAJIE",1,4,100,"打开后获得猪八戒玄空套装5小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15203,"小白龙玄空套装（5小时）","XIONGMAO_BAILONG",1,4,100,"打开后获得小白龙玄空套装5小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15204,"沙僧玄空套装（5小时）","XIONGMAO_SHAS",1,4,100,"打开后获得沙僧玄空套装5小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15205,"玄空套装（5小时）","XIONGMAO_ZHUANG",1,4,100,"打开后获得玄空套装5小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15206,"孙悟空玄空套装（10小时）","XIONGMAO_WUKONG",1,4,100,"打开后获得孙悟空玄空套装10小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15207,"猪八戒玄空套装（10小时）","XIONGMAO_BAJIE",1,4,100,"打开后获得猪八戒玄空装套装10小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15208,"小白龙玄空套装（10小时）","XIONGMAO_BAILONG",1,4,100,"打开后获得小白龙玄空装套装10小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15209,"沙僧玄空套装（10小时）","XIONGMAO_SHAS",1,4,100,"打开后获得沙僧玄空装套装10小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15210,"玄空时装（10小时）","XIONGMAO_ZHUANG",1,4,100,"打开后获得玄空装套装10小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15211,"孙悟空玄空套装（15小时）","XIONGMAO_WUKONG",1,4,100,"打开后获得孙悟空玄空套装15小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15212,"猪八戒玄空套装（15小时）","XIONGMAO_BAJIE",1,4,100,"打开后获得猪八戒玄空套装15小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15213,"小白龙玄空套装（15小时）","XIONGMAO_BAILONG",1,4,100,"打开后获得小白龙玄空套装15小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15214,"沙僧玄空套装（15小时）","XIONGMAO_SHAS",1,4,100,"打开后获得沙僧玄空套装15小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15215,"玄空套装（15小时）","XIONGMAO_ZHUANG",1,4,100,"打开后获得玄空装套装15小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15216,"孙悟空玄空套装（20小时）","XIONGMAO_WUKONG",1,4,100,"打开后获得孙悟空玄空套装20小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15217,"猪八戒玄空套装（20小时）","XIONGMAO_BAJIE",1,4,100,"打开后获得猪八戒玄空套装20小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15218,"小白龙玄空套装（20小时）","XIONGMAO_BAILONG",1,4,100,"打开后获得小白龙玄空套装20小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15219,"沙僧玄空套装（20小时）","XIONGMAO_SHAS",1,4,100,"打开后获得沙僧玄空套装20小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15220,"玄空套装（20小时）","XIONGMAO_ZHUANG",1,4,100,"打开后获得玄空套装20小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15221,"孙悟空玄空套装（25小时）","XIONGMAO_WUKONG",1,4,100,"打开后获得孙悟空玄空套装25小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15222,"猪八戒玄空套装（25小时）","XIONGMAO_BAJIE",1,4,100,"打开后获得猪八戒玄空套装25小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15223,"小白龙玄空套装（25小时）","XIONGMAO_BAILONG",1,4,100,"打开后获得小白龙玄空套装25小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15224,"沙僧玄空套装（25小时）","XIONGMAO_SHAS",1,4,100,"打开后获得沙僧玄空套装25小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15225,"玄空套装（25小时）","XIONGMAO_ZHUANG",1,4,100,"打开后获得玄空套装25小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15226,"孙悟空玄空套装（30小时）","XIONGMAO_WUKONG",1,4,100,"打开后获得孙悟空玄空套装30小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15227,"猪八戒玄空套装（30小时）","XIONGMAO_BAJIE",1,4,100,"打开后获得猪八戒玄空套装30小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15228,"小白龙玄空套装（30小时）","XIONGMAO_BAILONG",1,4,100,"打开后获得小白龙玄空套装30小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15229,"沙僧玄空套装（30小时）","XIONGMAO_SHAS",1,4,100,"打开后获得沙僧玄空套装30小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15230,"玄空套装（30小时）","XIONGMAO_ZHUANG",1,4,100,"打开后获得玄空套装30小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15295,"孙悟空永久玄空套装","XIONGMAO_WUKONG",1,4,100,"打开后获得孙悟空永久玄空套装。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15296,"猪八戒永久玄空套装","XIONGMAO_BAJIE",1,4,100,"打开后获得猪八戒永久玄空套装。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15297,"小白龙永久玄空套装","XIONGMAO_BAILONG",1,4,100,"打开后获得小白龙永久玄空套装。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15298,"沙僧永久玄空套装","XIONGMAO_SHAS",1,4,100,"打开后获得沙僧永久玄空套装。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstPKGVO(15299,"玄空套装（30小时）","XIONGMAO_ZHUANG",1,4,100,"打开后获得玄空套装30小时（下线不计时）。<br>竞技场周赛奖励获得。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16001,16101,"紫草种子","ZI_CAO_Z","MC_ZI_CAO",1,0,10,"可在五庄观种植出紫草用于丹药合成，使用催熟可加快成长速度。<br>花果山百花羞处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16002,16102,"通心草种子","TONGXIN_CAO_Z","MC_TONG_XIN_CAO",1,0,10,"可在五庄观种植出通心草用于丹药合成，使用催熟可加快成长速度。<br>花果山百花羞处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16003,16103,"蓝翎草种子","LANLING_CAO_Z","MC_LAN_LING_CAO",1,0,10,"可在五庄观种植出蓝翎草用于丹药合成，使用催熟可加快成长速度。<br>花果山百花羞处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16004,16104,"绝忧草种子","JUEYOU_CAO_Z","MC_JUE_YOU_CAO",1,1,10,"可在五庄观种植出绝忧草用于丹药合成，使用催熟可加快成长速度。<br>五庄观镇元大仙处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16005,16105,"露凝草种子","LUNING_CAO_Z","MC_LU_NING_CAO",1,1,10,"可在五庄观种植出露凝草用于丹药合成，使用催熟可加快成长速度。<br>五庄观镇元大仙处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16006,16106,"绿天萝种子","LV_LUO_Z","MC_LV_LUO",1,1,10,"可在五庄观种植出绿萝用于丹药合成，使用催熟可加快成长速度。<br>五庄观镇元大仙处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16007,16107,"何首乌种子","HESHOU_WU_Z","MC_HE_SHOU_WU",1,1,10,"可在五庄观种植出何首乌用于丹药合成，使用催熟可加快成长速度。<br>五庄观镇元大仙处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16008,16108,"木心果种子","MUXIN_GUO_Z","MC_MU_XIN_GUO",1,1,10,"可在五庄观种植出木心果用于丹药合成，使用催熟可加快成长速度。<br>五庄观镇元大仙处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16009,16109,"活根草种子","HUO_GEN_CAO_Z","MC_HUO_GEN_CAO",1,2,10,"可在五庄观种植出活根草用于丹药合成。<br>掉落：银锭商城处"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16010,16110,"宁神花种子","NING_SHEN_HUA_Z","MC_NING_SHEN_HUA",1,2,10,"可在五庄观种植出宁神花用于丹药合成。<br>掉落：乌鸡国水牢副本变异蚌精"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16011,16111,"燕凰草种子","YAN_HUANG_CAO_Z","MC_YAN_HUANG_CAO",1,2,10,"可在五庄观种植出燕凰草用于丹药合成。<br>掉落：降妖塔第一层（镇妖塔副本）"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16012,16112,"火焰花种子","HUO_YAN_HUA_Z","MC_HUO_YAN_HUA",1,3,10,"可在五庄观种植出火焰花用于丹药合成。<br>银锭商城处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16013,16113,"幽灵菇种子","YOU_LING_GU_Z","MC_YOU_LING_GU",1,3,10,"可在五庄观种植出幽灵菇用于丹药合成，<br>银锭商城处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16014,16114,"虚空花种子","XU_KONG_HUA_Z","MC_XU_KONG_HUA",1,3,10,"可在五庄观种植出虚空花用于丹药合成，<br>银锭商城处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16016,16116,"邪雾花种子","XIE_WU_CAO_Z","MC_XIE_WU_CAO",1,3,10,"可在五庄观种植出邪雾花用于丹药合成，<br>种子挑战沙僧普通困难副本掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16017,16117,"冬刺草种子","DONG_CI_CAO_Z","MC_DONG_CI_CAO",1,3,10,"可在五庄观种植出冬刺草用于丹药合成，<br>银锭商城处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstSeedVO(16018,16118,"紫莲花种子","ZI_LIAN_HUA_Z","MC_ZI_LIAN_HUA",1,3,10,"可在五庄观种植出紫莲花用于丹药合成，<br>种子挑战沙僧噩梦副本概率掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16101,"紫草","ZI_CAO",1,0,500,"用于丹药合成等。<br>五庄观种植获得，种子花果山百花羞处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16102,"通心草","TONGXIN_CAO",1,0,500,"用于丹药合成等。<br>五庄观种植获得，种子花果山百花羞处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16103,"蓝翎草","LANLING_CAO",1,0,500,"用于丹药合成等。<br>五庄观种植获得，种子花果山百花羞处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16104,"绝忧草","JUEYOU_CAO",1,1,1500,"用于丹药合成等。<br>五庄观种植获得，种子五庄观镇元大仙处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16105,"露凝草","LUNING_CAO",1,1,1500,"用于丹药合成等。<br>五庄观种植获得，种子五庄观镇元大仙处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16106,"绿天萝","LV_LUO",1,1,1500,"用于丹药合成等。<br>五庄观种植获得，种子五庄观镇元大仙处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16107,"何首乌","HESHOU_WU",1,1,1500,"用于丹药合成等。<br>五庄观种植获得，种子五庄观镇元大仙处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16108,"木心果","MUXIN_GUO",1,1,1500,"用于丹药合成等。<br>挑战猪八戒普通难度副本掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16109,"活根草","HUO_GEN_CAO",1,2,2000,"用于丹药合成等。<br>五庄观种植获得。种子银锭商城处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16110,"宁神花","NING_SHEN_HUA",1,2,2000,"用于丹药合成等。<br>五庄观种植获得，种子乌鸡国水牢副本变异蚌精掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16111,"燕凰草","YAN_HUANG_CAO",1,2,2000,"用于丹药合成等。<br>五庄观种植获得，种子降妖塔第一层掉落（镇妖塔副本）。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16112,"火焰花","HUO_YAN_HUA",1,3,2500,"用于丹药合成等。<br>黑市购买、五庄观种植获得。种子银锭商城处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16113,"幽灵菇","YOU_LING_GU",1,3,2500,"用于丹药合成等。<br>黑市购买、五庄观种植获得，种子银锭商城处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16114,"虚空花","XU_KONG_HUA",1,3,2500,"用于丹药合成等。<br>黑市购买、五庄观种植获得，种子银锭商城处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16115,"石楠花","SHI_NAN_HUA",1,2,2000,"用于丹药合成等，挑战沙僧普通难度副本掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16116,"邪雾花","XIE_WU_CAO",1,3,2500,"用于丹药合成等，挑战沙僧普通困难副本掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16117,"冬刺草","DONG_CI_CAO",1,3,2500,"用于丹药合成等<br>种子黑市、银锭商城处购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16118,"紫莲花","ZI_LIAN_HUA",1,3,2500,"用于丹药合成等，挑战沙僧噩梦副本掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16119,"蓝璇花","LAN_XUAN_HUA",1,3,2500,"用于丹药合成等<br>黑市、银锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16120,"梦露花","MENG_LU_HUA",1,3,2500,"用于丹药合成等<br>黑市、银锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16121,"鸿绿果","HONG_LV_GUO",1,4,3000,"用于丹药合成等，挑战七圣副本牛魔王掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16122,"寒齿叶","HAN_CHI_YE",1,4,3000,"用于丹药合成等，挑战七圣副本蛟魔王掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16123,"六色花","LIU_SE_HUA",1,4,3000,"用于丹药合成等，挑战七圣副本鹏魔王掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16124,"魔皇花","MO_HUANG_HUA",1,4,3000,"用于丹药合成等，挑战七圣副本狮驼王掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16125,"圣红果","SHENG_HONG_GUO",1,4,3000,"用于丹药合成等，挑战七圣副本猕猴王掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16126,"泰罗果","TAI_LUO_GUO",1,4,3000,"用于丹药合成等，挑战七圣副本禺狨王掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16127,"千年人参","QIAN_NIAN_REN_SHENG",1,4,3000,"用于丹药合成等，挑战七圣副本美猴王掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16201,"一品天心丹[金]","YIPINTIANXINDAN_JIN",1,5,0,38,1,0,10,"孙悟空服用后，生命上限+38。<br>所需材料：柏果、紫草、绝忧草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16202,"一品天心丹[木]","YIPINTIANXINDAN_MU",2,5,0,48,1,0,10,"猪八戒服用后，生命上限+48。<br>所需材料：柏果、紫草、绿天萝"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16203,"一品天心丹[水]","YIPINTIANXINDAN_SHUI",3,5,0,31,1,0,10,"小白龙服用后，生命上限+31。<br>所需材料：柏果、紫草、露凝草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16204,"一品天心丹[火]","YIPINTIANXINDAN_HUO",4,5,0,30,1,0,10,"小龙女服用后，生命上限+30。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16205,"一品天心丹[土]","YIPINTIANXINDAN_TU",5,5,0,35,1,0,10,"沙僧服用后，生命上限+35。<br>所需材料：柏果、紫草、何首乌"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16211,"一品清心丹[金]","YUIPINQINGXINDAN_JIN",1,5,1,21,1,0,10,"孙悟空服用后，法力上限+21。<br>所需材料：落英花、通心草、绝忧草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16212,"一品清心丹[木]","YUIPINQINGXINDAN_MU",2,5,1,16,1,0,10,"猪八戒服用后，法力上限+16。<br>所需材料：落英花、通心草、何首乌"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16213,"一品清心丹[水]","YUIPINQINGXINDAN_SHUI",3,5,1,19,1,0,10,"小白龙服用后，法力上限+19。<br>所需材料：落英花、通心草、露凝草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16214,"一品清心丹[火]","YUIPINQINGXINDAN_HUO",4,5,1,23,1,0,10,"小龙女服用后，法力上限+23。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16215,"一品清心丹[土]","YUIPINQINGXINDAN_TU",5,5,1,18,1,0,10,"沙僧服用后，法力上限+18。<br>所需材料：落英花、通心草、绿天萝"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16221,"一品大力丹[金]","YIPINDALIDAN_JIN",1,5,2,16,1,0,10,"孙悟空服用后，攻击上限+16。<br>所需材料：乌舌兰、蓝翎草、绝忧草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16222,"一品大力丹[木]","YIPINDALIDAN_MU",2,5,2,12,1,0,10,"猪八戒服用后，攻击上限+12。<br>所需材料：乌舌兰、蓝翎草、木心果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16223,"一品大力丹[水]","YIPINDALIDAN_SHUI",3,5,2,13,1,0,10,"小白龙服用后，攻击上限+13。<br>所需材料：乌舌兰、蓝翎草、露凝草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16224,"一品大力丹[火]","YIPINDALIDAN_HUO",4,5,2,15,1,0,10,"小龙女服用后，攻击上限+15。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16225,"一品大力丹[土]","YIPINDALIDAN_TU",5,5,2,14,1,0,10,"沙僧服用后，攻击上限+14。<br>所需材料：乌舌兰、蓝翎草、绝忧草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16301,"二品天心丹[金]","ERPINTIANXINDAN_JIN",1,6,0,46,1,1,10,"孙悟空服用后，生命上限+46。<br>所需材料：绝忧草、紫草、活根草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16302,"二品天心丹[木]","ERPINTIANXINDAN_MU",2,6,0,58,1,1,10,"猪八戒服用后，生命上限+58。<br>所需材料：木心果、紫草、活根草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16303,"二品天心丹[水]","ERPINTIANXINDAN_SHUI",3,6,0,38,1,1,10,"小白龙服用后，生命上限+38。<br>所需材料：绿天萝、紫草、活根草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16304,"二品天心丹[火]","ERPINTIANXINDAN_HUO",4,6,0,36,1,1,10,"小龙女服用后，生命上限+36。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16305,"二品天心丹[土]","ERPINTIANXINDAN_TU",5,6,0,42,1,1,10,"沙僧服用后，生命上限+42。<br>所需材料：石楠花、紫草、活根草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16311,"二品清心丹[金]","ERIPINQINGXINDAN_JIN",1,6,1,25,1,1,10,"孙悟空服用后，法力上限+25。<br>所需材料：绿天萝、通心草、宁神花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16312,"二品清心丹[木]","ERIPINQINGXINDAN_MU",2,6,1,19,1,1,10,"猪八戒服用后，法力上限+19。<br>所需材料：木心果、通心草、宁神花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16313,"二品清心丹[水]","ERIPINQINGXINDAN_SHUI",3,6,1,22,1,1,10,"小白龙服用后，法力上限+22。<br>所需材料：露凝草、通心草、宁神花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16314,"二品清心丹[火]","ERIPINQINGXINDAN_HUO",4,6,1,27,1,1,10,"小龙女服用后，法力上限+27。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16315,"二品清心丹[土]","ERIPINQINGXINDAN_TU",5,6,1,23,1,1,10,"沙僧龙服用后，法力上限+23。<br>所需材料：石楠花、通心草、宁神花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16321,"二品大力丹[金]","ERPINDALIDAN_JIN",1,6,2,19,1,1,10,"孙悟空服用后，攻击上限+19。<br>所需材料：露凝草、蓝翎草、燕凰草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16322,"二品大力丹[木]","ERPINDALIDAN_MU",2,6,2,14,1,1,10,"猪八戒服用后，攻击上限+14。<br>所需材料：木心果、蓝翎草、燕凰草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16323,"二品大力丹[水]","ERPINDALIDAN_SHUI",3,6,2,15,1,1,10,"小白龙服用后，攻击上限+15。<br>所需材料：绝忧草、蓝翎草、燕凰草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16324,"二品大力丹[火]","ERPINDALIDAN_HUO",4,6,2,18,1,1,10,"小龙女服用后，攻击上限+18。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16325,"二品大力丹[土]","ERPINDALIDAN_TU",5,6,2,17,1,1,10,"沙僧服用后，攻击上限+17。<br>所需材料：石楠花、蓝翎草、燕凰草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16401,"三品天心丹[金]","SANPINTIANXINDAN_JIN",1,7,0,62,1,2,10,"孙悟空服用后，生命上限+62。<br>所需材料：火焰花、活根草、绿天萝"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16402,"三品天心丹[木]","SANPINTIANXINDAN_MU",2,7,0,78,1,2,10,"猪八戒服用后，生命上限+78。<br>所需材料：幽灵菇、宁神花、木心果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16403,"三品天心丹[水]","SANPINTIANXINDAN_SHUI",3,7,0,52,1,2,10,"小白龙服用后，生命上限+52。<br>所需材料：虚空花、燕凰草、绝忧草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16404,"三品天心丹[火]","SANPINTIANXINDAN_HUO",4,7,0,50,1,2,10,"小龙女服用后，生命上限+50。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16405,"三品天心丹[土]","SANPINTIANXINDAN_TU",5,7,0,57,1,2,10,"沙僧服用后，生命上限+57。<br>所需材料：邪雾花、石楠花、活根草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16411,"三品清心丹[金]","SANPINQINGXINDAN_JIN",1,7,1,29,1,2,10,"孙悟空服用后，法力上限+29。<br>所需材料：火焰花、燕凰草、绿天萝"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16412,"三品清心丹[木]","SANPINQINGXINDAN_MU",2,7,1,24,1,2,10,"猪八戒服用后，法力上限+24。<br>所需材料：幽灵菇、活根草、木心果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16413,"三品清心丹[水]","SANPINQINGXINDAN_SHUI",3,7,1,25,1,2,10,"小白龙服用后，法力上限+25。<br>所需材料：虚空花、宁神花、绝忧草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16414,"三品清心丹[火]","SANPINQINGXINDAN_HUO",4,7,1,31,1,2,10,"小龙女服用后，法力上限+31。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16415,"三品清心丹[土]","SANPINQINGXINDAN_TU",5,7,1,27,1,2,10,"沙僧服用后，法力上限+27。<br>所需材料：邪雾花、石楠花、燕凰草\t"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16421,"三品大力丹[金]","SANPINDALIDAN_JIN",1,7,2,22,1,2,10,"孙悟空服用后，攻击上限+22。<br>所需材料：火焰花、燕凰草、绝忧草"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16422,"三品大力丹[木]","SANPINDALIDAN_MU",2,7,2,17,1,2,10,"猪八戒服用后，攻击上限+17。<br>所需材料：幽灵菇、宁神花、木心果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16423,"三品大力丹[水]","SANPINDALIDAN_SHUI",3,7,2,18,1,2,10,"小白龙服用后，攻击上限+18。<br>所需材料：虚空花、活根草、何首乌"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16424,"三品大力丹[火]","SANPINDALIDAN_HUO",4,7,2,21,1,2,10,"小龙女服用后，攻击上限+21。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16425,"三品大力丹[土]","SANPINDALIDAN_TU",5,7,2,19,1,2,10,"沙僧服用后，攻击上限+19。<br>所需材料：邪雾花、石楠花、绿天萝"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16501,"四品天心丹[金]","SIPINTIANXINDAN_JIN",1,8,0,78,1,3,10,"孙悟空服用后，生命上限+78<br>所需材料：火焰花、冬刺草、紫莲花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16502,"四品天心丹[木]","SIPINTIANXINDAN_MU",2,8,0,98,1,3,10,"猪八戒服用后，生命上限+98<br>所需材料：幽灵菇、冬刺草、紫莲花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16503,"四品天心丹[水]","SIPINTIANXINDAN_SHUI",3,8,0,67,1,3,10,"小白龙服用后，生命上限+67<br>所需材料：虚空花、冬刺草、紫莲花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16504,"四品天心丹[火]","SIPINTIANXINDAN_HUO",4,8,0,65,1,3,10,"小龙女服用后，生命上限+65"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16505,"四品天心丹[土]","SIPINTIANXINDAN_TU",5,8,0,72,1,3,10,"沙僧服用后，生命上限+72<br>所需材料：邪雾花、冬刺草、紫莲花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16511,"四品清心丹[金]","SIPINQINGXINDAN_JIN",1,8,1,34,1,3,10,"孙悟空服用后，法力上限+34<br>所需材料：火焰花、冬刺草、蓝璇花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16512,"四品清心丹[木]","SIPINQINGXINDAN_MU",2,8,1,30,1,3,10,"猪八戒服用后，法力上限+30<br>所需材料：幽灵菇、冬刺草、蓝璇花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16513,"四品清心丹[水]","SIPINQINGXINDAN_SHUI",3,8,1,29,1,3,10,"小白龙服用后，法力上限+29<br>所需材料：虚空花、冬刺草、蓝璇花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16514,"四品清心丹[火]","SIPINQINGXINDAN_HUO",4,8,1,36,1,3,10,"小龙女服用后，法力上限+36"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16515,"四品清心丹[土]","SIPINQINGXINDAN_TU",5,8,1,32,1,3,10,"沙僧服用后，法力上限+32<br>所需材料：邪雾花、冬刺草、蓝璇花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16521,"四品大力丹[金]","SIPINDALIDAN_JIN",1,8,2,26,1,3,10,"孙悟空服用后，攻击上限+26<br>所需材料：火焰花、紫莲花、梦露花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16522,"四品大力丹[木]","SIPINDALIDAN_MU",2,8,2,22,1,3,10,"猪八戒服用后，攻击上限+22<br>所需材料：幽灵菇、紫莲花、梦露花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16523,"四品大力丹[水]","SIPINDALIDAN_SHUI",3,8,2,23,1,3,10,"小白龙服用后，攻击上限+23<br>所需材料：虚空花、紫莲花、梦露花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16524,"四品大力丹[火]","SIPINDALIDAN_HUO",4,8,2,25,1,3,10,"小龙女服用后，攻击上限+25"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16525,"四品大力丹[土]","SIPINDALIDAN_TU",5,8,2,24,1,3,10,"沙僧服用后，攻击上限+24<br>所需材料：邪雾花、紫莲花、梦露花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16601,"五品天心丹[金]","WUPINTIANXINDAN_JIN",1,10,0,104,1,4,10,"孙悟空服用后，生命上限+104<br>所需材料：寒齿叶、六色花、魔皇花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16602,"五品天心丹[木]","WUPINTIANXINDAN_MU",2,10,0,128,1,4,10,"猪八戒服用后，生命上限+128<br>所需材料：鸿绿果、寒齿叶、六色花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16603,"五品天心丹[水]","WUPINTIANXINDAN_SHUI",3,10,0,92,1,4,10,"小白龙服用后，生命上限+92<br>所需材料：寒齿叶、六色花、魔皇花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16604,"五品天心丹[火]","WUPINTIANXINDAN_HUO",4,10,0,31,1,4,10,"小龙女服用后，生命上限+90"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16605,"五品天心丹[土]","WUPINTIANXINDAN_TU",5,10,0,97,1,4,10,"沙僧服用后，生命上限+97<br>所需材料：鸿绿果、寒齿叶、六色花"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16611,"五品清心丹[金]","WUPINQINGXINDAN_JIN",1,10,1,49,1,4,10,"孙悟空服用后，法力上限+49<br>所需材料：鸿绿果、圣红果、泰罗果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16612,"五品清心丹[木]","WUPINQINGXINDAN_MU",2,10,1,46,1,4,10,"猪八戒服用后，法力上限+46<br>所需材料：寒齿叶、圣红果、泰罗果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16613,"五品清心丹[水]","WUPINQINGXINDAN_SHUI",3,10,1,43,1,4,10,"小白龙服用后，法力上限+43<br>所需材料：六色花、圣红果、泰罗果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16614,"五品清心丹[火]","WUPINQINGXINDAN_HUO",4,10,1,51,1,4,10,"小龙女服用后，法力上限+51"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16615,"五品清心丹[土]","WUPINQINGXINDAN_TU",5,10,1,47,1,4,10,"沙僧服用后，法力上限+47<br>所需材料：魔皇花、圣红果、泰罗果"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16621,"五品大力丹[金]","WUPINDALIDAN_JIN",1,10,2,40,1,4,10,"孙悟空服用后，攻击上限+40<br>所需材料：鸿绿果、泰罗果、千年人参"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16622,"五品大力丹[木]","WUPINDALIDAN_MU",2,10,2,37,1,4,10,"猪八戒服用后，攻击上限+37<br>所需材料：寒齿叶、泰罗果、千年人参"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16623,"五品大力丹[水]","WUPINDALIDAN_SHUI",3,10,2,38,1,4,10,"小白龙服用后，攻击上限+38<br>所需材料：六色花、泰罗果、千年人参"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16624,"五品大力丹[火]","WUPINDALIDAN_HUO",4,10,2,39,1,4,10,"小龙女服用后，攻击上限+39"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstDanYaoVO(16625,"五品大力丹[土]","WUPINDALIDAN_TU",5,10,2,39,1,4,10,"沙僧服用后，攻击上限+39<br>所需材料：魔皇花、泰罗果、千年人参"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16701,"还魂丹","HUANHUN_DAN",1,0,1000,"用于救活乌鸡国国王。<br>花果山太上老君处合成。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16702,"随机道具","SUIJI_DAOJU",1,0,1000,"宝箱怪死亡后会掉落白色至橙色品质道具（随机）。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16710,"遗忘丹","YIWANG_GUOSHI",1,3,1000,"用于各职业技能点重置。<br>黑市、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16711,"玉蝉","YU_CAN",1,4,50000,"富贵的象征，卖出后能获得大量铜钱。<br>黑市、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16721,"挑战符","TIAOZHAN_FU",1,4,1,"使用后竞技场挑战次数+3。<br>寻宝副本、黑市、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16722,"刷新令","SHUAXIN_LIN",1,4,1,"使用后竞技场房间将随机换一批玩家。<br>黑市、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16723,"银月令","YINYUE_LIN",1,4,1,"公会升级头衔。<br>公会挑战-饕餮"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16750,"宠物改名符","CHONGWU_GAIMINGFU",1,4,1,"用于宠物改名。<br>金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16751,"宠物经验丹","CHONGWU_JINGYANDAN",1,4,1,"服用后增加宠物5000点经验。<br>竞技场商城、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16752,"洗髓丹","XISHUI_DAN",1,4,1,"用于洗宠物成长值。挑战小白龙【噩梦】、七大圣副本美猴王王概率掉落；黑市、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16753,"仙桃","XIAN_TAO",1,3,1,"增加宠物忠诚度3点。七大圣副本美猴王概率掉落；竞技场商城、金锭、银锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16754,"延寿丹","YANSHOU_DAN",1,3,1,"增加宠物寿命3点。<br>竞技场商城、金锭、银锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16755,"莹月草","YINYUE_CAO",1,4,1,"用于宠物主动技能重置。挑战猪八戒【困难】、七大圣副本蛟魔王概率掉落；黑市、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16756,"月光草","YEUGUANG_CAO",1,4,1,"用于宠物被动技能重置。挑战沙僧【困难】、七大圣副本鹏魔王概率掉落；黑市、金锭商城。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16757,"资质丹","ZIZHI_DAN",1,4,1,"用于培养宠物资质。<br>宝藏副本、黑市、七大圣副本牛魔王、竞技场商城、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16758,"天露丹","JINENG_CHONGZHI",1,4,1,"用于宠物技能点重置。<br>黑市、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16759,"宠物技能锁","CHONGWU_JINENGSUO",1,4,1,"用于锁定宠物技能，不会被重置。<br>七大圣副本狮驼王概率掉落；金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16760,"云御锁","YUNYU_SUO",1,4,1,"用于解锁宠物技能。<br>七大圣副本弥猴王概率掉落；黑市、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16761,"彩玄果","CAIXUAN_GUO",1,4,1,"用于宠物进化。<br>竞技场商城、挑战猪八戒【普通】概率掉落。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16762,"资质锁","CHONGWU_ZIZHISUO",1,4,1,"用于锁定宠物资质，不会被重置。<br>掉落：挑战七圣副本禺狨王、宝藏副本。金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16763,"进化丹","JINGHUA_DAN",1,4,1,"用于宠物进化。<br>封魔塔第一层掉落、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16764,"兽冰丹","SHOUBING_DAN",1,4,1,"用于神兽冰麒麟进化。<br>冰麒麟副本掉落、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16765,"兽炎丹","SHOUYAN_DAN",1,4,1,"用于神兽火凤凰进化。<br>火凤凰副本掉落、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16766,"兽石丹","SHOUSHI_DAN",1,4,1,"用于神兽玄武进化。<br>累充活动、金锭商城购买。"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16801,"白莹玉","BAIYING_YU",1,3,1,"用于紫金铃紫色品质熔炼。<br>掉落：妖岛阴山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16802,"软灵脂","RUANLING_ZHI",1,3,1,"用于紫金铃紫色品质熔炼。<br>掉落：妖岛阴山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16803,"仙狐延","XIANHU_YAN",1,3,1,"用于紫金铃紫色品质熔炼。<br>掉落：妖岛阴山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16804,"玉镜花","YUJING_HUA",1,3,1,"用于照妖镜紫色品质熔炼。<br>掉落：金兜山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16805,"赤铜","CHI_TONG",1,3,1,"用于照妖镜紫色、橙色品质熔炼。<br>掉落：琵琶洞"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16806,"地月石","DIYUE_SHI",1,3,1,"用于照妖镜紫色、橙色品质熔炼。<br>掉落：火焰山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16807,"龙须草","LONGXU_CAO",1,3,1,"用于缚妖索紫色品质熔炼。<br>掉落：封魔塔第一层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16808,"碧藤条","BITENG_TIAO",1,3,1,"用于缚妖索紫色、橙色品质熔炼。<br>掉落：封魔塔第一层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16809,"金丝","JIN_SI",1,3,1,"用于缚妖索紫色、橙色品质熔炼。<br>掉落：封魔塔第一层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16810,"蓝皇草","LANHUANG_CAO",1,3,1,"用于净瓶紫色品质熔炼。<br>掉落：封魔塔第二层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16811,"仙丁水","XIANDING_SHUI",1,3,1,"用于净瓶紫色品质熔炼。<br>掉落：封魔塔第二层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16812,"星墨石","XINGMO_SHI",1,3,1,"用于净瓶紫色品质熔炼。<br>掉落：封魔塔第二层"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16813,"魔珊瑚","MOSHAN_HU",1,3,1,"用于避水珠紫色品质熔炼。<br>掉落：乱石山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16814,"绿彩珠","LVCAI_ZHU",1,3,1,"用于避水珠紫色、橙色品质熔炼。<br>掉落：小雷音寺"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16815,"鬼叶草","GUI_YE",1,3,1,"用于避水珠紫色、橙色品质熔炼。<br>掉落：七绝山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16816,"晶沙","JING_SHA3",1,3,1,"用于玲珑鼎紫色品质熔炼。<br>掉落：妖岛章莪山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16817,"火铜石","HUOTONGSHI_TIAO",1,3,1,"用于玲珑鼎紫色品质熔炼。<br>掉落：妖岛章莪山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16818,"黄晶","HUANG_JING4",1,3,1,"用于玲珑鼎紫色品质熔炼。<br>掉落：妖岛章莪山"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16819,"雀石","QUE_SHI",1,3,1,"用于阴阳瓶紫色品质熔炼。<br>掉落：公会挑战-穷奇"));
         this.goodDic[ConstData.GOOD_PROP].push(new ConstGoodVO(16820,"雨花石","YUHUA_SHI",1,3,1,"用于阴阳瓶紫色品质熔炼。<br>掉落：公会挑战-穷奇"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20001,"行者棍","XINGZHE_GUN",1,0,2,1001,0,new ValueRandVO(99,109,0),new ValueRandVO(69,76,0),new ValueRandVO(31,34,0),null,null,null,null,null,[0,1,2],10,"非常普通的木棍。<br>掉落：双叉岭","MC_XING_ZHE_GUN","equip/wqXingZheGun.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20002,"狼牙拳套","LANGYA_QUANTAO",1,0,2,1003,0,new ValueRandVO(132,146,0),new ValueRandVO(58,63,0),new ValueRandVO(27,30,0),null,null,null,null,null,[0,1,2],10,"拳套上有狼牙，一经打中，必受重创。<br>掉落：黄风洞","MC_LANG_YA","equip/wqLangYa.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20003,"青铜剑","QING_TONGJIAN",1,0,2,1002,0,new ValueRandVO(86,95,0),new ValueRandVO(74,82,0),new ValueRandVO(26,28,0),null,null,null,null,null,[0,1,2],10,"青铜制成，上铸有简单的图案纹饰。<br>掉落：黄风洞","MC_QING_TONG_JIAN","equip/wqQingTongJian-110.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20004,"红木杖","HONGMU_ZHANG",1,0,2,1005,0,new ValueRandVO(72,90,0),new ValueRandVO(85,94,0),new ValueRandVO(27,30,0),null,null,null,null,null,[0,1,2],10,"用上好红木刻成的法杖，十分坚硬。<br>掉落：龙宫迷宫1层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20005,"乌金铲","WUJIN_CHAN",1,0,2,1004,0,new ValueRandVO(92,102,0),new ValueRandVO(60,66,0),new ValueRandVO(28,31,0),null,null,null,null,null,[0,1,2],10,"玄铁长枪，头部以乌金打造，气势如虹。<br>掉落：黄风洞","MC_WU_JIN_CHAN","equip/wqWuJinChan.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20006,"盘纹棍","PANWEN_GUN",1,0,13,1001,0,new ValueRandVO(176,195,0),new ValueRandVO(124,137,0),new ValueRandVO(52,57,0),null,null,null,null,null,[0,1,2],20,"周身布满花纹的金刚铁棍。<br>掉落：白虎岭","MC_PAN_WEN_GUN","equip/wqPanWenGun.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20007,"龙牙拳套","LONGYA_QUANTAO",1,0,13,1003,0,new ValueRandVO(234,260,0),new ValueRandVO(115,127,0),new ValueRandVO(46,51,0),null,null,null,null,null,[0,1,2],20,"龙牙制成的拳套，为龙宫秘宝。<br>掉落：平顶山","MC_LONG_YA","equip/wqLongYa.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20008,"青锋剑","QINGFENG_JIAN",1,0,13,1002,0,new ValueRandVO(153,169,0),new ValueRandVO(124,137,0),new ValueRandVO(43,47,0),null,null,null,null,null,[0,1,2],20,"剑长三尺，剑锋冒青光，用起来方便灵活。<br>掉落：白虎岭","MC_QING_FENG_JIAN","equip/wqQingFengJian-110.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20009,"白椴杖","BAIDUAN_ZHANG",1,0,13,1005,0,new ValueRandVO(139,165,0),new ValueRandVO(141,156,0),new ValueRandVO(46,51,0),null,null,null,null,null,[0,1,2],20,"用极佳的椴木精心雕凿的法杖，杖身光滑。<br>掉落：龙宫迷宫2层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20010,"突云铲","TUYUN_CHAN",1,0,13,1004,0,new ValueRandVO(164,182,0),new ValueRandVO(119,132,0),new ValueRandVO(47,52,0),null,null,null,null,null,[0,1,2],20,"气势凌厉，卷风而来，有穿云破月之感。<br>掉落：乌鸡国","MC_TU_YUN_CHAN","equip/wqTuYunChan.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20011,"阴阳棍","YINHANG_GUN",1,0,23,1001,0,new ValueRandVO(246,273,0),new ValueRandVO(160,177,0),new ValueRandVO(72,80,0),null,null,null,null,null,[0,1,2],30,"以磁石两极制作两端，不仅可增加威力，亦可抵挡暗器。<br>掉落：火云洞","MC_YIN_YANG_GUN","equip/wqYinYangGun.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20012,"幻影拳套","HUANYIN_QUANTAO",1,0,23,1003,0,new ValueRandVO(328,364,0),new ValueRandVO(149,165,0),new ValueRandVO(66,72,0),null,null,null,null,null,[0,1,2],30,"念力幻化出的拳套。<br>车迟国掉落。","MC_HUAN_YIN","equip/wqHuanYin.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20013,"游龙剑","YOULONG_JIAN",1,0,23,1002,0,new ValueRandVO(214,237,0),new ValueRandVO(160,177,0),new ValueRandVO(60,66,0),null,null,null,null,null,[0,1,2],30,"剑体九曲如龙形，覆鳞片，手弹其锋刃鸣如龙吟。<br>掉落：黑水河","MC_YOU_LONG_JIAN","equip/wqYouLongJian-110.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20014,"珊莹杖","SHANYIN_ZHANG",1,0,23,1005,0,new ValueRandVO(200,232,0),new ValueRandVO(183,203,0),new ValueRandVO(64,71,0),null,null,null,null,null,[0,1,2],30,"用东海海底的珊瑚锻造而成，通体晶莹。<br>掉落：龙宫迷宫3层","MC_SHAN_YIN_ZHANG","equip/wqShanYinZhang.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20015,"赤炎铲","CHIYAN_CHAN",1,0,23,1004,0,new ValueRandVO(230,255,0),new ValueRandVO(155,171,0),new ValueRandVO(66,73,0),null,null,null,null,null,[0,1,2],30,"刃如霜，枪身赤红如烈焰，威风凛凛气势迫人。<br>掉落：通天河","MC_CHI_YAN_CHAN","equip/wqChiYanChan.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20016,"云龙棍","YUNLONG_GUN",1,0,33,1001,0,new ValueRandVO(325,382,0),new ValueRandVO(212,248,0),new ValueRandVO(89,104,0),null,null,null,null,null,[0,1,2],40,"似云龙腾跃，又似云龙戏珠。<br>掉落：琵琶洞","MC_YUN_LONG_GUN","equip/wqYunLongGun.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20017,"霹雳拳套","QILI_QUANTAO",1,0,33,1003,0,new ValueRandVO(465,546,0),new ValueRandVO(197,231,0),new ValueRandVO(77,90,0),null,null,null,null,null,[0,1,2],40,"据说锻造时加入了天外霹雳陨。<br>掉落：琵琶洞","MC_PI_LI_QUAN_TAO","equip/wqPiLiQuanTao.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20018,"七星剑","QIXING_JIAN",1,0,33,1002,0,new ValueRandVO(282,331,0),new ValueRandVO(212,248,0),new ValueRandVO(81,95,0),null,null,null,null,null,[0,1,2],40,"精炼白金之铁铸造，有北斗七星之纹饰。<br>掉落：琵琶洞","MC_QI_XING_JIAN","equip/wqQiXingJian.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20019,"流萤杖","LIUYIN_ZHANG",1,0,33,1005,0,new ValueRandVO(289,325,0),new ValueRandVO(242,284,0),new ValueRandVO(85,99,0),null,null,null,null,null,[0,1,2],40,"夜明珠带有灿灿光华，如暗夜流萤。<br>掉落：龙宫迷宫4层","MC_LIU_YIN_ZHANG","equip/wqLiuYinZhang.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20020,"梨花铲","LIHUA_CHAN",1,0,33,1004,0,new ValueRandVO(304,357,0),new ValueRandVO(204,240,0),new ValueRandVO(85,100,0),null,null,null,null,null,[0,1,2],40,"隐士高人所制，通体百炼精金，锋刃五裂似梨花绽放。<br>掉落：火焰山","MC_LI_HUA_CHAN","equip/wqLiHuaChan.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20021,"天罡棍","TIANGANG_GUN",1,0,43,1001,0,new ValueRandVO(403,497,0),new ValueRandVO(262,323,0),new ValueRandVO(107,132,0),null,null,null,null,null,[0,1,2],50,"依北斗七星形状所制，有神秘的力量。<br>掉落：黄花观","MC_TIAN_GANG_GUN","equip/wqTianGangGun.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20022,"雷光拳套","LEIGUANG_QUANTAO",1,0,43,1003,0,new ValueRandVO(565,656,0),new ValueRandVO(244,301,0),new ValueRandVO(98,120,0),null,null,null,null,null,[0,1,2],50,"被奇妙紫雷电光芒所缠绕的拳套，拥有令人意想不到的强大破坏力。<br>掉落：盘丝洞","MC_LEI_GUANG_QUAN_TAO","equip/wqLeiGuangQuanTao.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20023,"倚天剑","YITIAN_JIAN",1,0,43,1002,0,new ValueRandVO(350,431,0),new ValueRandVO(262,323,0),new ValueRandVO(101,124,0),null,null,null,null,null,[0,1,2],50,"雾中山神铁所制，朽磨生烟，石击火起，剑光如电。<br>掉落：盘丝洞","MC_YI_TIAN_JIAN","equip/wqYiTianJian.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20024,"繁星杖","FANXING_ZHANG",1,0,43,1005,0,new ValueRandVO(339,428,0),new ValueRandVO(300,370,0),new ValueRandVO(105,129,0),null,null,null,null,null,[0,1,2],50,"法杖上刻录着繁星阵法，破坏力极大。<br>掉落：龙宫迷宫5层","MC_FAN_XIN_ZHANG","equip/wqFanXinZhang.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20025,"飞鸿铲","FEIHONG_CHAN",1,0,43,1004,0,new ValueRandVO(377,465,0),new ValueRandVO(253,312,0),new ValueRandVO(106,130,0),null,null,null,null,null,[0,1,2],50,"远看似天外飞鸿，飒沓如流星。<br>掉落：黄花观","MC_FEI_HONG_CHAN","equip/wqFeiHongChan.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20121,"天罡棍","TIANGANG_GUN",1,0,53,1001,0,new ValueRandVO(403,497,0),new ValueRandVO(262,323,0),new ValueRandVO(107,132,0),null,null,null,null,null,[0,1,2],50,"依北斗七星形状所制，有神秘的力量。<br>天竺-大雄宝殿（文殊菩萨）","MC_TIAN_GANG_GUN","equip/wqTianGangGun.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20122,"雷光拳套","LEIGUANG_QUANTAO",1,0,53,1003,0,new ValueRandVO(565,656,0),new ValueRandVO(244,301,0),new ValueRandVO(98,120,0),null,null,null,null,null,[0,1,2],50,"被奇妙紫雷电光芒所缠绕的拳套，拥有令人意想不到的强大破坏力。<br>天竺-大雄宝殿（文殊菩萨）","MC_LEI_GUANG_QUAN_TAO","equip/wqLeiGuangQuanTao.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20123,"倚天剑","YITIAN_JIAN",1,0,53,1002,0,new ValueRandVO(350,431,0),new ValueRandVO(262,323,0),new ValueRandVO(101,124,0),null,null,null,null,null,[0,1,2],50,"雾中山神铁所制，朽磨生烟，石击火起，剑光如电。<br>天竺-大雄宝殿（文殊菩萨）","MC_YI_TIAN_JIAN","equip/wqYiTianJian.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20124,"繁星杖","FANXING_ZHANG",1,0,53,1005,0,new ValueRandVO(339,428,0),new ValueRandVO(300,370,0),new ValueRandVO(105,129,0),null,null,null,null,null,[0,1,2],50,"法杖上刻录着繁星阵法，破坏力极大。<br>天竺-大雄宝殿（文殊菩萨）","MC_FAN_XIN_ZHANG","equip/wqFanXinZhang.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(20125,"飞鸿铲","FEIHONG_CHAN",1,0,53,1004,0,new ValueRandVO(377,465,0),new ValueRandVO(253,312,0),new ValueRandVO(106,130,0),null,null,null,null,null,[0,1,2],50,"远看似天外飞鸿，飒沓如流星。<br>天竺-大雄宝殿（文殊菩萨）","MC_FEI_HONG_CHAN","equip/wqFeiHongChan.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21001,"棉衫","MIAN_SHAN",1,0,5,1001,0,new ValueRandVO(99,109,0),null,null,new ValueRandVO(18,20,0),new ValueRandVO(18,20,0),null,null,null,[0,3,4],10,"用绞线交织而成的薄衫，防御力亦略优于一般布衣。<br>掉落：黑风洞","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21002,"布衫","BU_SHAN",1,0,5,1003,0,new ValueRandVO(132,146,0),null,null,new ValueRandVO(14,17,0),new ValueRandVO(14,17,0),null,null,null,[0,3,4],10,"寻常的布衫，足以遮身蔽体，抵御严寒，抵挡冰箭稍显不足。<br>掉落：黄风洞","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21003,"麻布衣","MABU-YI",1,0,5,1002,0,new ValueRandVO(86,95,0),null,null,new ValueRandVO(14,15,0),new ValueRandVO(13,15,0),null,null,null,[0,3,4],10,"麻布缝制的衣服，可以起到增加防御的作用。<br>掉落：黄风洞","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21004,"丝绸袍","SICHOU_PAO",1,0,5,1005,0,new ValueRandVO(72,90,0),null,null,new ValueRandVO(14,15,0),new ValueRandVO(27,30,0),null,null,null,[0,3,4],10,"用名贵丝绸制成的长裙，摇曳有姿。<br>掉落：龙宫迷宫1层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21005,"皮衣","PI_YI",1,0,5,1004,0,new ValueRandVO(92,102,0),null,null,new ValueRandVO(32,35,0),new ValueRandVO(13,15,0),null,null,null,[0,3,4],10,"看上去轻薄，但有一定防护力。<br>掉落：白虎岭","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21006,"兽纹衣","SHOU_WEN",1,0,15,1001,0,new ValueRandVO(176,195,0),null,null,new ValueRandVO(34,37,0),new ValueRandVO(33,37,0),null,null,null,[0,3,4],20,"双肩和前胸都有雕有兽纹的绝世护心衣。<br>掉落：黑松林","MC_SHOU_WEN","equip/yfShouWen.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21007,"宵烛衣","XIAOZHU_YI",1,0,15,1003,0,new ValueRandVO(234,260,0),null,null,new ValueRandVO(18,20,0),new ValueRandVO(18,20,0),null,null,null,[0,3,4],20,"以云霓为裳，织成的衣服。<br>掉落：乌鸡国","MC_XIAO_ZHU_YI","equip/yfXiaoZhuYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21008,"貂皮衣","DIAOPI_YI",1,0,15,1002,0,new ValueRandVO(153,169,0),null,null,new ValueRandVO(24,26,0),new ValueRandVO(23,26,0),null,null,null,[0,3,4],20,"选用素有“裘中之王”美称的西域珍贵紫貂皮缝制而成。<br>掉落：黑松林","MC_DIAO_PI_YI","equip/yfDiaoPiYi-110.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21009,"霓裳袍","NICHANG_PAO",1,0,15,1005,0,new ValueRandVO(139,165,0),null,null,new ValueRandVO(24,26,0),new ValueRandVO(48,54,0),null,null,null,[0,3,4],20,"非常美丽的羽衣。<br>掉落：龙宫迷宫2层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21010,"琥珀衣","HUBO_YI",1,0,15,1004,0,new ValueRandVO(164,182,0),null,null,new ValueRandVO(49,54,0),new ValueRandVO(23,26,0),null,null,null,[0,3,4],20,"上面镶嵌着琥珀的衣服。<br>掉落：黑水河","MC_HU_PO_YI","equip/yfHuPoYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21011,"软猬衣","RUAN-WEI",1,0,25,1001,0,new ValueRandVO(246,273,0),null,null,new ValueRandVO(50,55,0),new ValueRandVO(49,55,0),null,null,null,[0,3,4],30,"软猬衣是用金丝和千年滕枝混合编织而成的，不但可以刀枪不入而且可以保暖。<br>掉落：火云洞","MC_RUAN_WEI","equip/yfRuanWei.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21012,"紧身衣","JINGSHEN_YI",1,0,25,1003,0,new ValueRandVO(328,364,0),null,null,new ValueRandVO(27,30,0),new ValueRandVO(27,30,0),null,null,null,[0,3,4],30,"紧身的皮衣，非常轻便，有较强的防御力。<br>掉落：车迟国","MC_JIN_SHEN_YI","equip/yfJinShenYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21013,"银环衣","YINHUAN_YI",1,0,25,1002,0,new ValueRandVO(214,237,0),null,null,new ValueRandVO(36,39,0),new ValueRandVO(35,39,0),null,null,null,[0,3,4],30," 银环穿成的软甲，轻巧贴身，适合各种场合。<br>掉落：黑水河","MC_YIN_HUAN_YI","equip/yfYinHuanYi-110.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21014,"金缕袍","JINROU_PAO",1,0,25,1005,0,new ValueRandVO(200,232,0),null,null,new ValueRandVO(36,39,0),new ValueRandVO(72,81,0),null,null,null,[0,3,4],30,"用金丝玉片缝制的衣裳，看起来华贵异常。<br>掉落：龙宫迷宫3层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21015,"乌蚕衣","WUCHAN_YI",1,0,25,1004,0,new ValueRandVO(229,255,0),null,null,new ValueRandVO(64,70,0),new ValueRandVO(35,39,0),null,null,null,[0,3,4],30,"雪山上的乌蚕蚕丝所制成。<br>掉落：金兜山","MC_WU_CAN_YI","equip/yfWuCanYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21016,"战魂衣","ZHAN_HUN",1,0,35,1001,0,new ValueRandVO(325,382,0),null,null,new ValueRandVO(63,74,0),new ValueRandVO(63,74,0),null,null,null,[0,3,4],40,"七魄附于生前所穿的战铠上，穿上的人可以拥有前人的部分能力。<br>掉落：乱石山","MC_ZHAN_HUN_YI","equip/yfZhanHunYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21017,"玄风衣","XUAN_FENG",1,0,35,1003,0,new ValueRandVO(465,546,0),null,null,new ValueRandVO(44,51,0),new ValueRandVO(43,51,0),null,null,null,[0,3,4],40,"三秋夜，九夏天。<br>掉落：乱石山","MC_XUAN_FENG_YI","equip/yfXuanFengYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21018,"青莲衣","QING_LIAN",1,0,35,1002,0,new ValueRandVO(282,331,0),null,null,new ValueRandVO(50,59,0),new ValueRandVO(50,59,0),null,null,null,[0,3,4],40,"以佛心青莲凝化而成，看似软而无力，实可闭日破月。<br>掉落：小雷音寺","MC_QING_LIAN_YI","equip/yfQingLianYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21019,"七宝袍","QI_BAO",1,0,35,1005,0,new ValueRandVO(289,325,0),null,null,new ValueRandVO(50,59,0),new ValueRandVO(78,91,0),null,null,null,[0,3,4],40,"所谓天衣无缝，上有七宝华文，是可遇不可求的极品。<br>掉落：龙宫迷宫4层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21020,"梵天衣","FENG_TIAN",1,0,35,1004,0,new ValueRandVO(304,357,0),null,null,new ValueRandVO(78,91,0),new ValueRandVO(50,59,0),null,null,null,[0,3,4],40,"装饰了梵天经文图案的衣服，能驱除妖魅<br>掉落：小雷音寺","MC_FAN_TIAN_YI","equip/yfFanTianYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21021,"青龙衣","QIN_LONG",1,0,45,1001,0,new ValueRandVO(403,497,0),null,null,new ValueRandVO(77,92,0),new ValueRandVO(77,92,0),null,null,null,[0,3,4],50,"龙鳞制成的战甲，让佩戴者拥有如龙族般强悍的防御力。<br>掉落：隐雾山","MC_QING_LONG_YI","equip/yfQingLongYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21022,"夜魔衣","YE_MO",1,0,45,1003,0,new ValueRandVO(565,656,0),null,null,new ValueRandVO(72,87,0),new ValueRandVO(72,87,0),null,null,null,[0,3,4],50,"来自阴间的衣服，吸取阴间幽冥气息。<br>掉落：陷空山","MC_YE_MO_YI","equip/yfYeMoYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21023,"龙神衣","LONG_SHEN",1,0,45,1002,0,new ValueRandVO(350,431,0),null,null,new ValueRandVO(73,88,0),new ValueRandVO(73,88,0),null,null,null,[0,3,4],50,"远古巨龙死后，灵魂不肯安息，固执的停留在战甲上。<br>掉落：狮驼岭","MC_LONG_SHEN_YI","equip/yfLongShenYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21024,"流云袍","LIU_YUN",1,0,45,1005,0,new ValueRandVO(339,428,0),null,null,new ValueRandVO(50,88,0),new ValueRandVO(78,119,0),null,null,null,[0,3,4],50,"相传是织女用天上的流云缝制而成，有不可思议的防御力。<br>掉落：龙宫迷宫5层","MC_LIU_YUN_PAO","equip/yfLiuYunPao.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21025,"佛谕衣","FO_YU",1,0,45,1004,0,new ValueRandVO(377,465,0),null,null,new ValueRandVO(98,119,0),new ValueRandVO(73,88,0),null,null,null,[0,3,4],50,"受到佛谕祝福的银色披风，带有灵动之气。<br>掉落：清华洞","MC_FO_YU_YI","equip/yfFoYuYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21121,"青龙衣","QIN_LONG",1,0,55,1001,0,new ValueRandVO(403,497,0),null,null,new ValueRandVO(77,92,0),new ValueRandVO(77,92,0),null,null,null,[0,3,4],50,"龙鳞制成的战甲，让佩戴者拥有如龙族般强悍的防御力。<br>天竺-大雄宝殿（文殊菩萨）","MC_QING_LONG_YI","equip/yfQingLongYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21122,"夜魔衣","YE_MO",1,0,55,1003,0,new ValueRandVO(565,656,0),null,null,new ValueRandVO(72,87,0),new ValueRandVO(72,87,0),null,null,null,[0,3,4],50,"来自阴间的衣服，吸取阴间幽冥气息。<br>天竺-大雄宝殿（文殊菩萨）","MC_YE_MO_YI","equip/yfYeMoYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21123,"龙神衣","LONG_SHEN",1,0,55,1002,0,new ValueRandVO(350,431,0),null,null,new ValueRandVO(73,88,0),new ValueRandVO(73,88,0),null,null,null,[0,3,4],50,"远古巨龙死后，灵魂不肯安息，固执的停留在战甲上。<br>天竺-大雄宝殿（文殊菩萨）","MC_LONG_SHEN_YI","equip/yfLongShenYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21124,"流云袍","LIU_YUN",1,0,55,1005,0,new ValueRandVO(339,428,0),null,null,new ValueRandVO(50,88,0),new ValueRandVO(78,119,0),null,null,null,[0,3,4],50,"相传是织女用天上的流云缝制而成，有不可思议的防御力。<br>天竺-大雄宝殿（文殊菩萨）","MC_LIU_YUN_PAO","equip/yfLiuYunPao.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(21125,"佛谕衣","FO_YU",1,0,55,1004,0,new ValueRandVO(377,465,0),null,null,new ValueRandVO(98,119,0),new ValueRandVO(73,88,0),null,null,null,[0,3,4],50,"受到佛谕祝福的银色披风，带有灵动之气。<br>天竺-大雄宝殿（文殊菩萨）","MC_FO_YU_YI","equip/yfFoYuYi.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22001,"荧光(金)","YINGGUANG_JIN",0,0,7,1001,0,null,null,null,null,null,new ValueRandVO(3,4,0),new ValueRandVO(4,5,0),new ValueRandVO(1,2,0),[5,6,7],10,"坠子上镶嵌了带有荧光的宝石，佩戴它可以在黑暗中视物。<br>掉落：黑风洞","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22002,"荧光(木)","YINGGUANG_MU",0,0,7,1003,0,null,null,null,null,null,new ValueRandVO(3,4,0),new ValueRandVO(3,4,0),new ValueRandVO(1,2,0),[5,6,7],10,"坠子上镶嵌了带有荧光的宝石，佩戴它可以在黑暗中视物。<br>掉落：黄风洞","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22003,"荧光(水)","YINGGUANG_SHUI",0,0,7,1002,0,null,null,null,null,null,new ValueRandVO(8,9,0),new ValueRandVO(7,8,0),new ValueRandVO(1,2,0),[5,6,7],10,"坠子上镶嵌了带有荧光的宝石，佩戴它可以在黑暗中视物。<br>掉落：黄风洞","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22004,"荧光(火)","YINGGUANG_HUO",0,0,7,1005,0,null,null,null,null,null,new ValueRandVO(4,5,0),new ValueRandVO(2,3,0),new ValueRandVO(1,2,0),[5,6,7],10,"坠子上镶嵌了带有荧光的宝石，佩戴它可以在黑暗中视物。<br>掉落：龙宫迷宫1层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22005,"荧光(土)","YINGGUANG_TU",0,0,7,1004,0,null,null,null,null,null,new ValueRandVO(5,6,0),new ValueRandVO(3,4,0),new ValueRandVO(1,2,0),[5,6,7],10,"坠子上镶嵌了带有荧光的宝石，佩戴它可以在黑暗中视物。<br>掉落：黑松林","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22006,"风月(金)","FENGYUE_JIN",0,0,17,1001,0,null,null,null,null,null,new ValueRandVO(7,8,0),new ValueRandVO(8,9,0),new ValueRandVO(4,5,0),[5,6,7],20,"精致饰品，据说此链成于光风霁月之时。<br>掉落：平顶山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22007,"风月(木)","FENGYUE_MU",0,0,17,1003,0,null,null,null,null,null,new ValueRandVO(7,8,0),new ValueRandVO(7,8,0),new ValueRandVO(4,5,0),[5,6,7],20,"精致饰品，据说此链成于光风霁月之时。<br>掉落：火云洞","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22008,"风月(水)","FENGYUE_SHUI",0,0,17,1002,0,null,null,null,null,null,new ValueRandVO(14,16,0),new ValueRandVO(12,14,0),new ValueRandVO(4,5,0),[5,6,7],20,"精致饰品，据说此链成于光风霁月之时。<br>掉落：平顶山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22009,"风月(火)","FENGYUE_HUO",0,0,17,1005,0,null,null,null,null,null,new ValueRandVO(8,9,0),new ValueRandVO(5,6,0),new ValueRandVO(4,5,0),[5,6,7],20,"精致饰品，据说此链成于光风霁月之时。<br>掉落：龙宫迷宫2层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22010,"风月(土)","FENGYUE_TU",0,0,17,1004,0,null,null,null,null,null,new ValueRandVO(10,11,0),new ValueRandVO(6,7,0),new ValueRandVO(4,5,0),[5,6,7],20,"精致饰品，据说此链成于光风霁月之时。<br>掉落：车迟国","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22011,"碧水(金)","BISHUI_JIN",0,0,27,1001,0,null,null,null,null,null,new ValueRandVO(11,13,0),new ValueRandVO(12,14,0),new ValueRandVO(7,8,0),[5,6,7],30,"龙形环状，戴在颈部的华贵装饰。<br>掉落：火云洞","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22012,"碧水(木)","BISHUI_MU",0,0,27,1003,0,null,null,null,null,null,new ValueRandVO(11,13,0),new ValueRandVO(11,13,0),new ValueRandVO(7,8,0),[5,6,7],30,"龙形环状，戴在颈部的华贵装饰。<br>掉落：车迟国","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22013,"碧水(水)","BISHUI_SHUI",0,0,27,1002,0,null,null,null,null,null,new ValueRandVO(22,25,0),new ValueRandVO(19,22,0),new ValueRandVO(7,8,0),[5,6,7],30,"龙形环状，戴在颈部的华贵装饰。<br>掉落：黑水河","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22014,"碧水(火)","BISHUI_HUO",0,0,27,1005,0,null,null,null,null,null,new ValueRandVO(12,14,0),new ValueRandVO(8,9,0),new ValueRandVO(7,8,0),[5,6,7],30,"龙形环状，戴在颈部的华贵装饰。<br>掉落：龙宫迷宫3层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22015,"碧水(土)","BISHUI_TU",0,0,27,1004,0,null,null,null,null,null,new ValueRandVO(16,18,0),new ValueRandVO(10,11,0),new ValueRandVO(7,8,0),[5,6,7],30,"龙形环状，戴在颈部的华贵装饰。<br>掉落：金兜山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22016,"卷云(金)","JUANYUN_JIN",0,0,37,1001,0,null,null,null,null,null,new ValueRandVO(17,19,0),new ValueRandVO(18,20,0),new ValueRandVO(10,11,0),[5,6,7],40,"据说是大禹留下的宝物，可退九州之阴雨。<br>掉落：麒麟山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22017,"卷云(木)","JUANYUN_MU",0,0,37,1003,0,null,null,null,null,null,new ValueRandVO(17,19,0),new ValueRandVO(17,19,0),new ValueRandVO(10,11,0),[5,6,7],40,"据说是大禹留下的宝物，可退九州之阴雨。<br>掉落：麒麟山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22018,"卷云(水)","JUANYUN_SHUI",0,0,37,1002,0,null,null,null,null,null,new ValueRandVO(27,30,0),new ValueRandVO(24,27,0),new ValueRandVO(10,11,0),[5,6,7],40,"据说是大禹留下的宝物，可退九州之阴雨。<br>掉落：七绝山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22019,"卷云(火)","JUANYUN_HUO",0,0,37,1005,0,null,null,null,null,null,new ValueRandVO(15,18,0),new ValueRandVO(13,15,0),new ValueRandVO(10,11,0),[5,6,7],40,"据说是大禹留下的宝物，可退九州之阴雨。<br>掉落：龙宫迷宫4层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22020,"卷云(土)","JUANYUN_TU",0,0,37,1004,0,null,null,null,null,null,new ValueRandVO(21,23,0),new ValueRandVO(15,17,0),new ValueRandVO(10,11,0),[5,6,7],40,"据说是大禹留下的宝物，可退九州之阴雨。<br>掉落：七绝山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22021,"七彩(金)","QICAI_JING",0,0,47,1001,0,null,null,null,null,null,new ValueRandVO(22,27,0),new ValueRandVO(23,28,0),new ValueRandVO(12,14,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：布金禅寺","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22022,"七彩(木)","QICAI_MU",0,0,47,1003,0,null,null,null,null,null,new ValueRandVO(22,27,0),new ValueRandVO(22,27,0),new ValueRandVO(12,14,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：青龙山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22023,"七彩(水)","QICAI_SHUI",0,0,47,1002,0,null,null,null,null,null,new ValueRandVO(31,34,0),new ValueRandVO(28,31,0),new ValueRandVO(12,14,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：豹头山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22024,"七彩(火)","QICAI_HUO",0,0,47,1005,0,null,null,null,null,null,new ValueRandVO(20,26,0),new ValueRandVO(19,25,0),new ValueRandVO(12,14,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：龙宫迷宫5层","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22025,"七彩(土)","QICAI_TU",0,0,47,1004,0,null,null,null,null,null,new ValueRandVO(24,29,0),new ValueRandVO(18,23,0),new ValueRandVO(12,14,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：竹节山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22121,"七彩(金)","QICAI_JING",0,0,57,1001,0,null,null,null,null,null,new ValueRandVO(22,27,0),new ValueRandVO(23,28,0),new ValueRandVO(14,19,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：妖岛-北岳山","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22122,"七彩(木)","QICAI_MU",0,0,57,1003,0,null,null,null,null,null,new ValueRandVO(22,27,0),new ValueRandVO(22,27,0),new ValueRandVO(14,19,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：妖岛-厌火林","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22123,"七彩(水)","QICAI_SHUI",0,0,57,1002,0,null,null,null,null,null,new ValueRandVO(31,34,0),new ValueRandVO(28,31,0),new ValueRandVO(14,19,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：妖岛-厌火林","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22124,"七彩(火)","QICAI_HUO",0,0,57,1005,0,null,null,null,null,null,new ValueRandVO(20,26,0),new ValueRandVO(19,25,0),new ValueRandVO(14,19,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：妖岛-昆仑之丘","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstEquipVO(22125,"七彩(土)","QICAI_TU",0,0,57,1004,0,null,null,null,null,null,new ValueRandVO(24,29,0),new ValueRandVO(18,23,0),new ValueRandVO(14,19,0),[5,6,7],50,"串有七彩玲珑的巨大珍珠，集海之灵气，给予佩戴者祥瑞的祝福。<br>掉落：妖岛-昆仑之丘","",""));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23001,"[木]缚妖索","BOYAO_SUO",2,10,null,null,new ValueRandVO(72,80,0),new ValueRandVO(27,30,0),null,null,null,null,[2,3],200,"普通法宝，战斗中能将目标捆缚，不能移动","MC_FU_YAO_SUO","equip/mcFuYaoSuo.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23002,"[金]照妖镜","ZHAOYAO_JIN",1,5,null,null,new ValueRandVO(45,50,0),null,new ValueRandVO(27,30,0),null,null,null,[2,4],100,"天地间一种宝镜，在有妖气的地方，能照出妖魔的原形","MC_ZHAO_YAO_JING","equip/mcZhaoYaoJing.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23003,"[水]净瓶","YUJIN_PIN",3,15,new ValueRandVO(225,250,0),new ValueRandVO(90,100,0),null,null,null,null,null,null,[0,1],300,"普通的法器，使用后可恢复适当血量","MC_YU_JIN_PING","equip/mcYuJinPing.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23004,"[水]避水珠","BISHUI_ZHU",3,15,null,null,null,new ValueRandVO(36,40,0),new ValueRandVO(36,40,0),null,null,null,[3,4],400,"使用后能降低所受伤害，装备避水珠后才能进入水下场景","MC_BI_SHUI_ZHU","equip/mcBiShuiZhu.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23005,"[土]镇妖塔","ZHENYAO_TA",5,20,null,null,null,new ValueRandVO(40,45,0),new ValueRandVO(40,45,0),null,null,new ValueRandVO(1,5,0),[3,4,7],500,"使用后能吸收大妖元神并开启镇妖塔副本，成功率与大妖血量和法宝品质有关","MC_ZHEN_YAO_TA","equip/mcZhenYaoTa-170.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23006,"[火]伪芭蕉扇","WEI_BAJIAOSHAN",4,1,new ValueRandVO(250,250,0),null,new ValueRandVO(30,30,0),null,new ValueRandVO(25,25,0),null,null,null,[0,2,4],500,"绝版法宝，使用后召唤出一道旋风，合成真芭蕉扇必备法宝","MC_WEI_BA_JIAO_SHAN","equip/mcWeiBaJiaoShan-170.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23007,"[火]芭蕉扇","BAJIAO_SHAN",4,28,new ValueRandVO(350,350,0),null,new ValueRandVO(90,90,0),null,new ValueRandVO(35,35,0),null,null,null,[0,2,4],5000,"绝版法宝，使用后面前召唤出一道强大旋风持续3秒，对敌人造成持续伤害","MC_ZHEN_BA_JIAO_SHAN","equip/mcZhenBaJiaoShan.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23008,"[水]驭兽袋","YUSHOU_DAI",3,1,new ValueRandVO(240,300,0),null,null,null,null,null,null,null,[0],500,"按\'N\'抓捕五庄观中的宠物。品质越高捕宠成功率也越高","MC_YU_SHOU_DAI","equip/mcYuShouDai.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23009,"[土]紫金铃","ZIJIN_LIN",5,30,new ValueRandVO(265,300,0),null,new ValueRandVO(76,86,0),new ValueRandVO(31,35,0),null,null,null,null,[0,2,3],600,"使用后，对前方妖怪造成多次伤害","MC_ZI_JIN_LING","equip/mcZiJinLing-540.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23010,"[火]玲珑鼎","LINGLONG_DING",4,30,new ValueRandVO(285,320,0),null,new ValueRandVO(78,88,0),null,new ValueRandVO(31,35,0),null,null,null,[0,2,4],600,"使用后，对前方妖怪造成多次伤害","MC_LING_LONG_DING","equip/mcLingLongDing.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23011,"[木]紫竹篮","ZIZHU_LAN",2,30,new ValueRandVO(350,350,0),null,new ValueRandVO(105,105,0),new ValueRandVO(40,40,0),null,null,null,null,[0,2,3],0,"公会会长专属法宝，使用对全屏妖怪造成伤害并麻痹2秒","MC_ZI_ZHU_LAN","equip/mcZiZhuLan.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23012,"[水]阴阳瓶","YINYANG_PIN",3,30,new ValueRandVO(550,550,0),null,new ValueRandVO(155,155,0),null,new ValueRandVO(60,60,0),null,null,null,[0,2,4],0,"使用后可恢复适当法力","MC_ER_QI_PING","equip/mcErQiPing.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23013,"[金]金铙","JIN_NAO",1,30,null,null,new ValueRandVO(180,180,0),new ValueRandVO(55,55,0),new ValueRandVO(55,55,0),null,null,null,[2,3,4],0,"使用后对全屏妖怪造成伤害并眩晕2秒","MC_JIN_NAO","equip/mcJinNao.swf"));
         this.goodDic[ConstData.GOOD_EQUIP].push(new ConstFabaoVO(23014,"[火]东皇钟","DHUANG_ZHONG",4,40,new ValueRandVO(1000,1000,0),new ValueRandVO(170,170,0),new ValueRandVO(200,200,0),null,new ValueRandVO(80,80,0),null,null,null,[0,1,2,4],0,"使用后对全屏妖怪造成伤害并提升自身30%双防，持续5秒","MC_DONG_HUANG_ZHONG_CLIP","equip/mcDongHuangZhong.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34101,"祥瑞棍 ","YANGRUI_GUN",0,0,1,1001,0,new ValueRandVO(109,109,0),null,new ValueRandVO(34,34,0),null,null,null,null,new ValueRandVO(2,2,0),[0,2,7],1,"风调雨顺，禾生双穗。<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_XIANG_RUI_GUN","fashion/sbXiangRuiGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34102,"晴川拳套","QINGCHUAN_QUANTAO",0,0,1,1003,0,new ValueRandVO(146,146,0),null,new ValueRandVO(26,26,0),null,null,null,null,new ValueRandVO(2,2,0),[0,2,7],1,"晴川历历汉阳树，芳草萋萋鹦鹉洲。<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_QING_CHUAN_QUAN_TAO","fashion/sbQingChuanQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34103,"魅影剑","MEIYING_JIAN",0,0,1,1002,0,new ValueRandVO(95,95,0),null,new ValueRandVO(28,28,0),null,null,null,null,new ValueRandVO(2,2,0),[0,2,7],1,"魅影庭庭影魅，流萤层层盈流。<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_MEI_YING_JIAN","fashion/sbMeiYingJian-350.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34104,"风行铲","FENGXING_CHAN",0,0,1,1004,0,new ValueRandVO(102,102,0),null,new ValueRandVO(31,31,0),null,null,null,null,new ValueRandVO(2,2,0),[0,2,7],1,"黑云崔嵬行风中，凛如鬼神塞虚空。<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_FENG_XING_CHAN","fashion/sbFengXingChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34105,"紫彩杖","ZICAIZHANG_ZHANG",0,0,1,1005,0,new ValueRandVO(90,90,0),null,new ValueRandVO(30,30,0),null,null,null,null,new ValueRandVO(2,2,0),[0,2,7],1,"<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_ZI_CAI_ZHANG","fashion/sbZiCaiZhang.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35001,"祥瑞衣 ","YANGRUI_YI",0,0,1,1001,0,new ValueRandVO(109,109,0),null,null,new ValueRandVO(20,20,0),new ValueRandVO(20,20,0),null,null,null,[0,3,4],1,"风调雨顺，禾生双穗。<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_XIANG_RUI_YI","fashion/shenjiaXiangRuiYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35002,"晴川衣","QINGCHUAN_YI",0,0,1,1003,0,new ValueRandVO(146,146,0),null,null,new ValueRandVO(14,14,0),new ValueRandVO(14,14,0),null,null,null,[0,3,4],1,"晴川历历汉阳树，芳草萋萋鹦鹉洲。<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_QING_CHUAN_YI","fashion/shenjiaQingChuanYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35003,"魅影衣","MEIYING_YI",0,0,1,1002,0,new ValueRandVO(95,95,0),null,null,new ValueRandVO(15,15,0),new ValueRandVO(15,15,0),null,null,null,[0,3,4],1,"魅影庭庭影魅，流萤层层盈流。<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_MEI_YING_YI","fashion/shenjiaMeiYingYi-350.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35004,"风行衣","FENGXING_YI",0,0,1,1004,0,new ValueRandVO(102,102,0),null,null,new ValueRandVO(35,35,0),new ValueRandVO(15,15,0),null,null,null,[0,3,4],1,"黑云崔嵬行风中，凛如鬼神塞虚空。<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_FENG_XING_YI","fashion/shenjiaFengXingYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35005,"紫彩衣","ZICAI_YI",0,0,1,1005,0,new ValueRandVO(90,90,0),null,null,new ValueRandVO(15,15,0),new ValueRandVO(35,35,0),null,null,null,[0,3,4],1,"<br>获得方式：金锭商城<br>熔炼后提升品质并获得更高属性","MC_ZI_CAI_YI","fashion/shenjiaZiCaiYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34111,"暗金棍 ","HUOFENG_GUN",0,0,20,1001,0,new ValueRandVO(195,195,0),null,new ValueRandVO(65,65,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：敲山震虎<br>攻击时15%概率幻化出巨棒砸向地面对妖怪造成伤害","99ff00"),"MC_HUO_FEN_GUN","fashion/sbHuoFenGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34112,"秋水拳套","QIUSHUI_QUANTAO",0,0,20,1003,0,new ValueRandVO(260,260,0),null,new ValueRandVO(54,54,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：嗜血荆棘<br>攻击时15%概率释放荆棘缠绕怪物并吸取360点生命","99ff00"),"MC_QIU_SHUI_QUAN_TAO","fashion/sbQiuShuiQuanTao-390.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34113,"月影剑","YUEYIN_JIAN",0,0,20,1002,0,new ValueRandVO(169,169,0),null,new ValueRandVO(57,57,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：水龙卷<br>攻击时15%概率对前面释放一道水龙卷对妖怪造成伤害","99ff00"),"MC_YUE_YIN_JIAN","fashion/sbYueYinJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34114,"日月铲","RIYUE_CHAN",0,0,20,1004,0,new ValueRandVO(182,182,0),null,new ValueRandVO(60,60,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：沙暴<br>攻击时15%概率对前方释放沙暴，妖怪受到伤害并迟缓2秒","99ff00"),"MC_RI_YUE_CHAN ","fashion/sbRiYueChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34115,"魅蓝杖","MEILAN_ZHANG",0,0,20,1005,0,new ValueRandVO(165,165,0),null,new ValueRandVO(62,62,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城<br>" + TxtUtil.setColor("套装技能：地焰<br>攻击时15%概率对前方地面释放火焰对妖怪造成伤害","99ff00"),"MC_MEI_LAN_ZHANG","fashion/sbMeiLanZhang.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35011,"暗金衣","HUOFENG_YI",0,0,20,1001,0,new ValueRandVO(195,195,0),null,null,new ValueRandVO(37,37,0),new ValueRandVO(37,37,0),null,null,null,[0,3,4],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：敲山震虎<br>攻击时15%概率幻化出巨棒砸向地面对妖怪造成伤害","99ff00"),"MC_HUO_FEN_YI","fashion/shenjiaHuoFenYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35012,"秋水衣","QIUSHUI_YI",0,0,20,1003,0,new ValueRandVO(260,260,0),null,null,new ValueRandVO(25,25,0),new ValueRandVO(25,25,0),null,null,null,[0,3,4],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：嗜血荆棘<br>攻击时15%概率释放荆棘缠绕怪物并吸取360点生命","99ff00"),"MC_QIU_SHUI_YI","fashion/shenjiaQiuShuiYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35013,"月影衣","YUEYING_YI",0,0,20,1002,0,new ValueRandVO(169,169,0),null,null,new ValueRandVO(29,29,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：水龙卷<br>攻击时15%概率对前面释放一道水龙卷对妖怪造成伤害","99ff00"),"MC_YUE_YIN_YI","fashion/shenjiaYueYinYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35014,"日月衣","RIYUE_YI",0,0,20,1004,0,new ValueRandVO(182,182,0),null,null,new ValueRandVO(54,54,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：沙暴<br>攻击时15%概率对前方释放沙暴，妖怪受到伤害并迟缓3秒","99ff00"),"MC_RI_YUE_YI ","fashion/shenjiaRiYueYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35015,"魅蓝衣","MEILAN_YI",0,0,20,1005,0,new ValueRandVO(165,165,0),null,null,new ValueRandVO(29,29,0),new ValueRandVO(54,54,0),null,null,null,[0,3,4],1,"熔炼后提升品质并获得更高属性。<br>获得方式：金锭商城<br>" + TxtUtil.setColor("套装技能：地焰<br>攻击时15%概率对前方地面释放火焰对妖怪造成伤害","99ff00"),"MC_MEI_LAN_YI","fashion/shenjiaMeiLanYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34121,"红凤棍","HONGFENG_GUN",0,0,1,1001,0,new ValueRandVO(240,240,0),null,new ValueRandVO(63,63,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"红凤套装。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_FENG_GUN","fashion/sbHongFengGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34122,"红麟拳套","HONGLING_QUANTAO",0,0,1,1003,0,new ValueRandVO(299,299,0),null,new ValueRandVO(58,58,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"红麟套装。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_LING_QUAN_TAO","fashion/sbHongLingQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34123,"红轩剑","HONGXUAN_JIAN",0,0,1,1002,0,new ValueRandVO(278,278,0),null,new ValueRandVO(60,60,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"红轩套装。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_XUAN_JIAN","fashion/sbHongXuanJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34124,"红辕铲","HONGXUAN_CHAN",0,0,1,1004,0,new ValueRandVO(244,244,0),null,new ValueRandVO(61,61,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"红辕套装。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_YUAN_CHAN","fashion/sbHongYuanChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34125,"红霓杖","HONGNI_ZHANG",0,0,1,1005,0,new ValueRandVO(276,276,0),null,new ValueRandVO(62,62,0),null,null,null,null,new ValueRandVO(5,5,0),[0,2,7],1,"红霓套装。<br>获得方式：金锭商城<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_NI_ZHANG","fashion/sbHongNiZhang.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35021,"红凤衣","HONGFENG_YI",0,0,1,1001,0,new ValueRandVO(240,240,0),null,null,new ValueRandVO(42,42,0),new ValueRandVO(42,42,0),null,null,null,[0,3,4],1,"红凤套装。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_FENG_YI","fashion/shenjiaHongFengYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35022,"红麟衣","HONGLING_YI",0,0,1,1003,0,new ValueRandVO(299,299,0),null,null,new ValueRandVO(32,32,0),new ValueRandVO(32,32,0),null,null,null,[0,3,4],1,"红麟套装。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_LING_YI","fashion/shenjiaHongLingYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35023,"红轩衣","HONGXUAN_YI",0,0,1,1002,0,new ValueRandVO(278,278,0),null,null,new ValueRandVO(34,34,0),new ValueRandVO(34,34,0),null,null,null,[0,3,4],1,"红轩套装。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_XUAN_YI","fashion/shenjiaHongXuanYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35024,"红辕衣","HONGYUAN_YI",0,0,1,1004,0,new ValueRandVO(244,244,0),null,null,new ValueRandVO(57,57,0),new ValueRandVO(34,34,0),null,null,null,[0,3,4],1,"红辕套装。<br>获得方式：金锭商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_YUAN_YI","fashion/shenjiaHongYuanYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35025,"红霓衣","HONGNI_YI",0,0,1,1005,0,new ValueRandVO(276,276,0),null,null,new ValueRandVO(34,34,0),new ValueRandVO(57,57,0),null,null,null,[0,3,4],1,"红霓套装。<br>获得方式：金锭商城<br>" + TxtUtil.setColor("套装技能：爆竹震天<br>攻击时17%概率掉落爆竹对妖怪造成爆炸伤害","99ff00"),"MC_HONG_NI_YI","fashion/shenjiaHongNiYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34131,"圣·苍羽棍","CHANGYU_GUN",0,0,25,1001,0,new ValueRandVO(495,495,0),null,new ValueRandVO(116,116,0),null,null,null,new ValueRandVO(3,3,0),null,[0,2,6],1,"圣·苍羽套装<br>获得方式：神装合成、商城、黑市（橙装）<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_CANG_YU_GUN","fashion/sbCangYuGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34132,"圣·翠羽拳套","CUIYU_QUANTAO",0,0,25,1003,0,new ValueRandVO(675,675,0),null,new ValueRandVO(111,111,0),null,null,null,new ValueRandVO(4,4,0),null,[0,2,6],1,"圣·翠羽套装<br>获得方式：神装合成、商城、黑市（橙装）<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_CUI_YU_QUAN_TAO","fashion/sbCuiYuQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34133,"圣·浩羽剑","HAOYU_JIAN",0,0,25,1002,0,new ValueRandVO(575,575,0),null,new ValueRandVO(110,110,0),null,null,new ValueRandVO(2,2,0),null,null,[0,2,5],1,"圣·浩羽套装<br>获得方式：神装合成、商城、黑市（橙装）<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_HAO_YU_JIAN","fashion/sbHaoYuJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34134,"圣·尘羽铲","CHENYU_CHAN",0,0,25,1004,0,new ValueRandVO(480,480,0),null,new ValueRandVO(112,112,0),null,null,new ValueRandVO(3,3,0),null,null,[0,2,5],1,"圣·尘羽套装<br>获得方式：神装合成、商城、黑市（橙装）<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_CHEN_YU_CHAN","fashion/sbChenYuChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34135,"圣·炽羽杖","CHIYU_ZHANG",0,0,25,1005,0,new ValueRandVO(588,588,0),null,new ValueRandVO(115,115,0),null,null,new ValueRandVO(3,3,0),null,null,[0,2,5],1,"圣·炽羽套装<br>获得方式：神装合成、商城、宝袋<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_CHI_YU_ZHANG","fashion/sbChiYuZhang.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35031,"圣·苍羽衣","CHANGYU_YI",0,0,25,1001,0,new ValueRandVO(495,495,0),null,null,new ValueRandVO(52,52,0),new ValueRandVO(52,52,0),null,null,null,[0,3,4],1,"圣·苍羽套装<br>获得方式：神装合成、商城、黑市（橙装）<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_CANG_YU_YI","fashion/shenjiaCangYuYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35032,"圣·翠羽衣","CUIYU_YI",0,0,25,1003,0,new ValueRandVO(675,675,0),null,null,new ValueRandVO(50,50,0),new ValueRandVO(50,50,0),null,null,null,[0,3,4],1,"圣·翠羽套装<br>获得方式：神装合成、商城、黑市（橙装）<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_CUI_YU_YI","fashion/shenjiaCuiYuYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35033,"圣·浩羽衣","HAOYU_YI",0,0,25,1002,0,new ValueRandVO(575,575,0),null,null,new ValueRandVO(51,51,0),new ValueRandVO(51,51,0),null,null,null,[0,3,4],1,"圣·浩羽套装<br>获得方式：神装合成、商城、黑市（紫装）<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_HAO_YU_YI","fashion/shenjiaHaoYuYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35034,"圣·尘羽衣","CHENYU_YI",0,0,25,1004,0,new ValueRandVO(480,480,0),null,null,new ValueRandVO(61,61,0),new ValueRandVO(48,48,0),null,null,null,[0,3,4],1,"圣·尘羽套装<br>获得方式：神装合成、商城、黑市（橙装）<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_CHEN_YU_YI","fashion/shenjiaChenYuYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35035,"圣·炽羽衣","CHIYU_YI",0,0,25,1005,0,new ValueRandVO(588,588,0),null,null,new ValueRandVO(48,48,0),new ValueRandVO(61,61,0),null,null,null,[0,3,4],1,"圣·炽羽套装<br>获得方式：神装合成、商城、宝袋<br>" + TxtUtil.setColor("套装技能：黑洞<br>攻击时17%概率召唤出黑洞对周围妖怪造成伤害","99ff00"),"MC_CHI_YU_YI","fashion/shenjiaChiYuYi.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34801,"玄空棍","XUANKONG_GUN",0,0,25,1001,5,new ValueRandVO(300,300,0),null,new ValueRandVO(78,78,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间5小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_GUN","fashion/sbXuanZuGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34802,"玄空拳套","XUANKONG_QUANTAO",0,0,25,1003,5,new ValueRandVO(373,373,0),null,new ValueRandVO(72,72,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间5小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_QUAN_TAO","fashion/sbXuanZuQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34803,"玄空剑","XUANKONG_JIAN",0,0,25,1002,5,new ValueRandVO(255,255,0),null,new ValueRandVO(75,75,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间5小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_JIAN","fashion/sbXuanZuJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34804,"玄空铲","XUANKONG_CHAN",0,0,25,1004,5,new ValueRandVO(277,277,0),null,new ValueRandVO(77,77,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间5小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_CHAN","fashion/sbXuanZuChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34806,"玄空棍","XUANKONG_GUN",0,0,25,1001,10,new ValueRandVO(300,300,0),null,new ValueRandVO(78,78,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间10小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_GUN","fashion/sbXuanZuGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34807,"玄空拳套","XUANKONG_QUANTAO",0,0,25,1003,10,new ValueRandVO(373,373,0),null,new ValueRandVO(72,72,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间10小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_QUAN_TAO","fashion/sbXuanZuQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34808,"玄空剑","XUANKONG_JIAN",0,0,25,1002,10,new ValueRandVO(255,255,0),null,new ValueRandVO(75,75,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间10小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_JIAN","fashion/sbXuanZuJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34809,"玄空铲","XUANKONG_CHAN",0,0,25,1004,10,new ValueRandVO(277,277,0),null,new ValueRandVO(77,77,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间10小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_CHAN","fashion/sbXuanZuChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34811,"玄空棍","XUANKONG_GUN",0,0,25,1001,15,new ValueRandVO(300,300,0),null,new ValueRandVO(78,78,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间15小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_GUN","fashion/sbXuanZuGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34812,"玄空拳套","XUANKONG_QUANTAO",0,0,25,1003,15,new ValueRandVO(373,373,0),null,new ValueRandVO(72,72,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间15小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_QUAN_TAO","fashion/sbXuanZuQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34813,"玄空剑","XUANKONG_JIAN",0,0,25,1002,15,new ValueRandVO(255,255,0),null,new ValueRandVO(75,75,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间15小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_JIAN","fashion/sbXuanZuJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34814,"玄空铲","XUANKONG_CHAN",0,0,25,1004,15,new ValueRandVO(277,277,0),null,new ValueRandVO(77,77,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间15小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_CHAN","fashion/sbXuanZuChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34816,"玄空棍","XUANKONG_GUN",0,0,25,1001,20,new ValueRandVO(300,300,0),null,new ValueRandVO(78,78,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间20小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_GUN","fashion/sbXuanZuGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34817,"玄空拳套","XUANKONG_QUANTAO",0,0,25,1003,20,new ValueRandVO(373,373,0),null,new ValueRandVO(72,72,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间20小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_QUAN_TAO","fashion/sbXuanZuQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34818,"玄空剑","XUANKONG_JIAN",0,0,25,1002,20,new ValueRandVO(255,255,0),null,new ValueRandVO(75,75,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间20小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_JIAN","fashion/sbXuanZuJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34819,"玄空铲","XUANKONG_CHAN",0,0,25,1004,20,new ValueRandVO(277,277,0),null,new ValueRandVO(77,77,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间20小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_CHAN","fashion/sbXuanZuChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34821,"玄空棍","XUANKONG_GUN",0,0,25,1001,25,new ValueRandVO(300,300,0),null,new ValueRandVO(78,78,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间25小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_GUN","fashion/sbXuanZuGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34822,"玄空拳套","XUANKONG_QUANTAO",0,0,25,1003,25,new ValueRandVO(373,373,0),null,new ValueRandVO(72,72,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间25小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_QUAN_TAO","fashion/sbXuanZuQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34823,"玄空剑","XUANKONG_JIAN",0,0,25,1002,25,new ValueRandVO(255,255,0),null,new ValueRandVO(75,75,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间25小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_JIAN","fashion/sbXuanZuJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34824,"玄空铲","XUANKONG_CHAN",0,0,25,1004,25,new ValueRandVO(277,277,0),null,new ValueRandVO(77,77,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间25小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_CHAN","fashion/sbXuanZuChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34826,"玄空棍","XUANKONG_GUN",0,0,25,1001,30,new ValueRandVO(300,300,0),null,new ValueRandVO(78,78,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间30小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_GUN","fashion/sbXuanZuGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34827,"玄空拳套","XUANKONG_QUANTAO",0,0,25,1003,30,new ValueRandVO(373,373,0),null,new ValueRandVO(72,72,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间30小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_QUAN_TAO","fashion/sbXuanZuQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34828,"玄空剑","XUANKONG_JIAN",0,0,25,1002,30,new ValueRandVO(255,255,0),null,new ValueRandVO(75,75,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间30小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_JIAN","fashion/sbXuanZuJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34829,"玄空铲","XUANKONG_CHAN",0,0,25,1004,30,new ValueRandVO(277,277,0),null,new ValueRandVO(77,77,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"有效时间30小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_CHAN","fashion/sbXuanZuChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34831,"玄空棍","XUANKONG_GUN",0,0,1,1001,0,new ValueRandVO(300,300,0),null,new ValueRandVO(78,78,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_GUN","fashion/sbXuanZuGun.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34832,"玄空拳套","XUANKONG_QUANTAO",0,0,1,1003,0,new ValueRandVO(373,373,0),null,new ValueRandVO(72,72,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_QUAN_TAO","fashion/sbXuanZuQuanTao.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34833,"玄空剑","XUANKONG_JIAN",0,0,1,1002,0,new ValueRandVO(255,255,0),null,new ValueRandVO(75,75,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_JIAN","fashion/sbXuanZuJian.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(34834,"玄空铲","XUANKONG_CHAN",0,0,1,1004,0,new ValueRandVO(277,277,0),null,new ValueRandVO(77,77,0),null,null,null,null,new ValueRandVO(10,10,0),[0,2,7],1,"获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_CHAN","fashion/sbXuanZuChan.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35901,"玄空衣(金)","XUANKONG_JIN",0,0,25,1001,5,new ValueRandVO(300,300,0),null,null,new ValueRandVO(38,38,0),new ValueRandVO(38,38,0),null,null,null,[0,3,4],1,"有效时间5小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI1","fashion/shenjiaXuanZuYi1.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35902,"玄空衣(木)","XUANKONG_MU",0,0,25,1003,5,new ValueRandVO(373,373,0),null,null,new ValueRandVO(26,26,0),new ValueRandVO(26,26,0),null,null,null,[0,3,4],1,"有效时间5小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI2","fashion/shenjiaXuanZuYi2.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35903,"玄空衣(水)","XUANKONG_SHUI",0,0,25,1002,5,new ValueRandVO(255,255,0),null,null,new ValueRandVO(29,29,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间5小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI3","fashion/shenjiaXuanZuYi3.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35904,"玄空衣(土)","XUANKONG_TU",0,0,25,1004,5,new ValueRandVO(277,277,0),null,null,new ValueRandVO(54,54,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间5小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI4","fashion/shenjiaXuanZuYi4.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35906,"玄空衣(金)","XUANKONG_JIN",0,0,25,1001,10,new ValueRandVO(300,300,0),null,null,new ValueRandVO(38,38,0),new ValueRandVO(38,38,0),null,null,null,[0,3,4],1,"有效时间10小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI1","fashion/shenjiaXuanZuYi1.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35907,"玄空衣(木)","XUANKONG_MU",0,0,25,1003,10,new ValueRandVO(373,373,0),null,null,new ValueRandVO(26,26,0),new ValueRandVO(26,26,0),null,null,null,[0,3,4],1,"有效时间10小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI2","fashion/shenjiaXuanZuYi2.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35908,"玄空衣(水)","XUANKONG_SHUI",0,0,25,1002,10,new ValueRandVO(255,255,0),null,null,new ValueRandVO(29,29,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间10小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI3","fashion/shenjiaXuanZuYi3.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35909,"玄空衣(土)","XUANKONG_TU",0,0,25,1004,10,new ValueRandVO(277,277,0),null,null,new ValueRandVO(54,54,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间10小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI4","fashion/shenjiaXuanZuYi4.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35911,"玄空衣(金)","XUANKONG_JIN",0,0,25,1001,15,new ValueRandVO(300,300,0),null,null,new ValueRandVO(38,38,0),new ValueRandVO(38,38,0),null,null,null,[0,3,4],1,"有效时间15小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI1","fashion/shenjiaXuanZuYi1.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35912,"玄空衣(木)","XUANKONG_MU",0,0,25,1003,15,new ValueRandVO(373,373,0),null,null,new ValueRandVO(26,26,0),new ValueRandVO(26,26,0),null,null,null,[0,3,4],1,"有效时间15小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI2","fashion/shenjiaXuanZuYi2.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35913,"玄空衣(水)","XUANKONG_SHUI",0,0,25,1002,15,new ValueRandVO(255,255,0),null,null,new ValueRandVO(29,29,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间15小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI3","fashion/shenjiaXuanZuYi3.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35914,"玄空衣(土)","XUANKONG_TU",0,0,25,1004,15,new ValueRandVO(277,277,0),null,null,new ValueRandVO(54,54,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间15小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI4","fashion/shenjiaXuanZuYi4.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35916,"玄空衣(金)","XUANKONG_JIN",0,0,25,1001,20,new ValueRandVO(300,300,0),null,null,new ValueRandVO(38,38,0),new ValueRandVO(38,38,0),null,null,null,[0,3,4],1,"有效时间20小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI1","fashion/shenjiaXuanZuYi1.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35917,"玄空衣(木)","XUANKONG_MU",0,0,25,1003,20,new ValueRandVO(373,373,0),null,null,new ValueRandVO(26,26,0),new ValueRandVO(26,26,0),null,null,null,[0,3,4],1,"有效时间20小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI2","fashion/shenjiaXuanZuYi2.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35918,"玄空衣(水)","XUANKONG_SHUI",0,0,25,1002,20,new ValueRandVO(255,255,0),null,null,new ValueRandVO(29,29,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间20小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI3","fashion/shenjiaXuanZuYi3.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35919,"玄空衣(土)","XUANKONG_TU",0,0,25,1004,20,new ValueRandVO(277,277,0),null,null,new ValueRandVO(54,54,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间20小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI4","fashion/shenjiaXuanZuYi4.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35921,"玄空衣(金)","XUANKONG_JIN",0,0,25,1001,25,new ValueRandVO(300,300,0),null,null,new ValueRandVO(38,38,0),new ValueRandVO(38,38,0),null,null,null,[0,3,4],1,"有效时间25小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI1","fashion/shenjiaXuanZuYi1.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35922,"玄空衣(木)","XUANKONG_MU",0,0,25,1003,25,new ValueRandVO(373,373,0),null,null,new ValueRandVO(26,26,0),new ValueRandVO(26,26,0),null,null,null,[0,3,4],1,"有效时间25小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI2","fashion/shenjiaXuanZuYi2.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35923,"玄空衣(水)","XUANKONG_SHUI",0,0,25,1002,25,new ValueRandVO(255,255,0),null,null,new ValueRandVO(29,29,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间25小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI3","fashion/shenjiaXuanZuYi3.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35924,"玄空衣(土)","XUANKONG_TU",0,0,25,1004,25,new ValueRandVO(277,277,0),null,null,new ValueRandVO(54,54,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间25小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI4","fashion/shenjiaXuanZuYi4.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35926,"玄空衣(金)","XUANKONG_JIN",0,0,25,1001,30,new ValueRandVO(300,300,0),null,null,new ValueRandVO(38,38,0),new ValueRandVO(38,38,0),null,null,null,[0,3,4],1,"有效时间30小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI1","fashion/shenjiaXuanZuYi1.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35927,"玄空衣(木)","XUANKONG_MU",0,0,25,1003,30,new ValueRandVO(373,373,0),null,null,new ValueRandVO(26,26,0),new ValueRandVO(26,26,0),null,null,null,[0,3,4],1,"有效时间30小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI2","fashion/shenjiaXuanZuYi2.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35928,"玄空衣(水)","XUANKONG_SHUI",0,0,25,1002,30,new ValueRandVO(255,255,0),null,null,new ValueRandVO(29,29,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间30小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI3","fashion/shenjiaXuanZuYi3.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35929,"玄空衣(土)","XUANKONG_TU",0,0,25,1004,30,new ValueRandVO(277,277,0),null,null,new ValueRandVO(54,54,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"有效时间30小时（下线不计时）<br>获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI4","fashion/shenjiaXuanZuYi4.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35931,"玄空衣(金)","XUANKONG_JIN",0,0,1,1001,0,new ValueRandVO(300,300,0),null,null,new ValueRandVO(38,38,0),new ValueRandVO(38,38,0),null,null,null,[0,3,4],1,"获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI1","fashion/shenjiaXuanZuYi1.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35932,"玄空衣(木)","XUANKONG_MU",0,0,1,1003,0,new ValueRandVO(373,373,0),null,null,new ValueRandVO(26,26,0),new ValueRandVO(26,26,0),null,null,null,[0,3,4],1,"获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI2","fashion/shenjiaXuanZuYi2.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35933,"玄空衣(水)","XUANKONG_SHUI",0,0,1,1002,0,new ValueRandVO(255,255,0),null,null,new ValueRandVO(29,29,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI3","fashion/shenjiaXuanZuYi3.swf"));
         this.goodDic[ConstData.GOOD_FASHION].push(new ConstEquipVO(35934,"玄空衣(土)","XUANKONG_TU",0,0,1,1004,0,new ValueRandVO(277,277,0),null,null,new ValueRandVO(54,54,0),new ValueRandVO(29,29,0),null,null,null,[0,3,4],1,"获得方式：竞技场周赛奖励<br>" + TxtUtil.setColor("套装技能：玄空竹影<br>攻击时15%概率掉落玄竹造成3次伤害并晕眩1.5秒","99ff00"),"MC_XUAN_ZU_YI4","fashion/shenjiaXuanZuYi4.swf"));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(46001,"兽骨爪","HUOYAN_ZHUA",0,0,13,1101,0,null,null,new ValueRandVO(64,80,0),null,null,null,null,null,[2],1,"普通的火系爪。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(46002,"兽羽爪","SHELING_ZHUA",0,0,13,1102,0,null,null,new ValueRandVO(56,70,0),null,null,null,null,null,[2],1,"普通的木系爪。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(46003,"兽俐爪","SHELI_ZHUA",0,0,13,1103,0,null,null,new ValueRandVO(75,90,0),null,null,null,null,null,[2],1,"普通的金系爪。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(46004,"兽凝爪","HUONING_ZHUA",0,0,13,1104,0,null,null,new ValueRandVO(84,105,0),null,null,null,null,null,[2],1,"普通的水系爪。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(46051,"圣冰爪","SHENGBING_ZHUA",0,0,13,1106,0,null,null,new ValueRandVO(196,280,0),null,null,null,null,null,[2],1,"神兽冰麒麟专属武器。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(46052,"圣炎爪","SHENGYAN_ZHUA",0,0,13,1107,0,null,null,new ValueRandVO(250,365,0),null,null,null,null,null,[2],1,"神兽火凤凰专属武器。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(46053,"圣石爪","SHENGSHI_ZHUA",0,0,13,1108,0,null,null,new ValueRandVO(230,345,0),null,null,null,null,null,[2],1,"神兽玄武专属武器。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(47001,"兽骨盔","HUOYAN_KUI",1,0,15,1101,0,null,null,null,new ValueRandVO(44,55,0),null,null,null,null,[3],1,"普通的火系盔。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(47002,"兽羽盔","SHELING_KUI",1,0,15,1102,0,null,null,null,new ValueRandVO(41,52,0),null,null,null,null,[3],1,"普通的木系盔。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(47003,"兽俐盔","SHELI_KUI",1,0,15,1103,0,null,null,null,new ValueRandVO(43,54,0),null,null,null,null,[3],1,"普通的金系盔。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(47004,"兽凝盔","HUONING_KUI",1,0,15,1104,0,null,null,null,new ValueRandVO(42,53,0),null,null,null,null,[3],1,"普通的水系盔。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(47051,"圣冰盔","SHENGBING_KUI",1,0,15,1106,0,null,null,null,new ValueRandVO(77,110,0),null,null,null,null,[3],1,"神兽冰麒麟专属头盔。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(47052,"圣炎盔","SHENGYAN_KUI",1,0,15,1107,0,null,null,null,new ValueRandVO(99,130,0),null,null,null,null,[3],1,"神兽火凤凰专属头盔。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(47053,"圣石盔","SHENGSHI_KUI",1,0,15,1108,0,null,null,null,new ValueRandVO(110,150,0),null,null,null,null,[3],1,"神兽玄武专属头盔。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(48001,"兽骨甲","HUOYAN_JIA",1,0,17,1101,0,new ValueRandVO(224,280,0),null,null,null,null,null,null,null,[0],1,"普通的火系甲。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(48002,"兽羽甲","SHELING_JIA",1,0,17,1102,0,new ValueRandVO(272,340,0),null,null,null,null,null,null,null,[0],1,"普通的木系甲。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(48003,"兽俐甲","SHELI_JIA",1,0,17,1103,0,new ValueRandVO(292,360,0),null,null,null,null,null,null,null,[0],1,"普通的金系甲。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(48004,"兽凝甲","HUONING_JIA",1,0,17,1104,0,new ValueRandVO(282,350,0),null,null,null,null,null,null,null,[0],1,"普通的水系甲。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(48051,"圣冰甲","SHENGBING_JIA",1,0,17,1106,0,new ValueRandVO(642,918,0),null,null,null,null,null,null,null,[0],1,"神兽冰麒麟专属护甲。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(48052,"圣炎甲","SHENGYAN_JIA",1,0,17,1107,0,new ValueRandVO(842,1218,0),null,null,null,null,null,null,null,[0],1,"神兽火凤凰专属护甲。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(48053,"圣石甲","SHENGSHI_JIA",1,0,17,1108,0,new ValueRandVO(999,1566,0),null,null,null,null,null,null,null,[0],1,"神兽玄武专属护甲。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(49001,"兽骨坠","HUOYAN_ZUI",0,0,19,1101,0,null,null,null,null,new ValueRandVO(40,50,0),null,null,null,[4],1,"普通的火系坠。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(49002,"兽羽坠","SHELING_ZUI",0,0,19,1102,0,null,null,null,null,new ValueRandVO(41,52,0),null,null,null,[4],1,"普通的木系坠。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(49003,"兽俐坠","SHELI_ZUI",0,0,19,1103,0,null,null,null,null,new ValueRandVO(43,54,0),null,null,null,[4],1,"普通的金系坠。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(49004,"兽凝坠","HUONING_ZUI",0,0,19,1104,0,null,null,null,null,new ValueRandVO(54,68,0),null,null,null,[4],1,"普通的水系坠。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(49051,"圣冰坠","SHENGBING_ZUI",0,0,19,1106,0,null,null,null,null,new ValueRandVO(70,100,0),null,null,null,[4],1,"神兽冰麒麟专属吊坠。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(49052,"圣炎坠","SHENGYAN_ZUI",0,0,19,1107,0,null,null,null,null,new ValueRandVO(90,120,0),null,null,null,[4],1,"神兽火凤凰专属吊坠。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_PET].push(new ConstEquipVO(49053,"圣石坠","SHENGSHI_ZUI",0,0,19,1108,0,null,null,null,null,new ValueRandVO(110,150,0),null,null,null,[4],1,"神兽玄武专属吊坠。找荆棘岭杏仙合成。","",""));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50000,"卡片碎片I","KAPIAN_SUIPIAN1",1,3,10,"用于小妖卡片合成，商城、小妖卡片分解获得（荆棘岭凌空子）。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50005,"卡片精华I","KAPIAN_JINGHUA1",1,4,10,"用于大妖卡片合成，商城、大妖卡片分解获得（荆棘岭凌空子）。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50008,"高级卡片精华","KAPIAN_JINGHUA2",1,4,10,"用于七圣卡合成，商城、七圣卡分解获得（荆棘岭凌空子）。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50101,"兽骨爪碎片","SHOUGUZHA_SP",1,3,10,"合成兽骨爪材料。<br>找荆棘岭杏仙合成兽骨爪。<br>掉落：黑松林。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50102,"兽骨盔碎片","SHOUGUKUI_SP",1,3,10,"合成兽骨盔材料。<br>找荆棘岭杏仙合成兽骨盔。<br>掉落：平顶山。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50103,"兽骨甲碎片","SHOUGUJIA_SP",1,3,10,"合成兽骨甲材料。<br>找荆棘岭杏仙合成兽骨甲。<br>掉落：乌鸡国。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50104,"兽骨坠碎片","SHOUGUZUI_SP",1,3,10,"合成兽骨坠材料。<br>找荆棘岭杏仙合成兽骨坠。<br>掉落：火云洞。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50105,"兽羽爪碎片","SHOUYUZHA_SP",1,3,10,"合成兽羽爪材料。<br>找荆棘岭杏仙合成兽羽爪。<br>掉落：黑水河。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50106,"兽羽盔碎片","SHOUYUKUI_SP",1,3,10,"合成兽羽盔材料。<br>找荆棘岭杏仙合成兽羽盔。<br>掉落：车迟国。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50107,"兽羽甲碎片","SHOUYUJIA_SP",1,3,10,"合成兽羽甲材料。<br>找荆棘岭杏仙合成兽羽甲。<br>掉落：通天河。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50108,"兽羽坠碎片","SHOUYUZUI_SP",1,3,10,"合成兽羽坠材料。<br>找荆棘岭杏仙合成兽羽坠。<br>掉落：金兜山。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50109,"兽俐爪碎片","SHOULIZHA_SP",1,3,10,"合成兽俐爪材料。<br>找荆棘岭杏仙合成兽俐爪。<br>掉落：金兜山。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50110,"兽俐盔碎片","SHOULIKUI_SP",1,3,10,"合成兽俐盔材料。<br>找荆棘岭杏仙合成兽俐盔。<br>掉落：琵琶洞。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50111,"兽俐甲碎片","SHOULIJIA_SP",1,3,10,"合成兽俐甲材料。<br>找荆棘岭杏仙合成兽俐甲。<br>掉落：火焰山。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50112,"兽俐坠碎片","SHOULIZUI_SP",1,3,10,"合成兽俐坠材料。<br>找荆棘岭杏仙合成兽俐坠。<br>掉落：乱石山。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50113,"兽凝爪碎片","SHOUNINGZHA_SP",1,3,10,"合成兽俐爪材料。<br>找荆棘岭杏仙合成兽俐爪。<br>掉落：小雷音寺。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50114,"兽凝盔碎片","SHOUNINGKUI_SP",1,3,10,"合成兽俐盔材料。<br>找荆棘岭杏仙合成兽俐盔。<br>掉落：七绝山。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50115,"兽凝甲碎片","SHOUNINGJIA_SP",1,3,10,"合成兽俐甲材料。<br>找荆棘岭杏仙合成兽俐甲。<br>掉落：麒麟山。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50116,"兽凝坠碎片","SHOUNINGZUI_SP",1,3,10,"合成兽俐坠材料。<br>找荆棘岭杏仙合成兽俐坠。<br>掉落：盘丝洞。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50201,"圣冰爪碎片","SHENGBINGZHA_SP",1,3,10,"合成圣冰爪材料。<br>找荆棘岭杏仙合成圣冰爪。<br>掉落：冰麒麟副本。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50202,"圣冰盔碎片","SHENGBINGKUI_SP",1,3,10,"合成圣冰盔材料。<br>找荆棘岭杏仙合成圣冰盔。<br>掉落：冰麒麟副本。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50203,"圣冰甲碎片","SHENGBINGJIA_SP",1,3,10,"合成圣冰甲材料。<br>找荆棘岭杏仙合成圣冰甲。<br>掉落：冰麒麟副本。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50204,"圣冰坠碎片","SHENGBINGZUI_SP",1,3,10,"合成圣冰坠材料。<br>找荆棘岭杏仙合成圣冰坠。<br>掉落：冰麒麟副本。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50205,"圣炎爪碎片","SHENGYANZHA_SP",1,3,10,"合成圣炎爪材料。<br>找荆棘岭杏仙合成圣炎爪。<br>掉落：火凤凰副本。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50206,"圣炎盔碎片","SHENGYANKUI_SP",1,3,10,"合成圣炎盔材料。<br>找荆棘岭杏仙合成圣炎盔。<br>掉落：火凤凰副本。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50207,"圣炎甲碎片","SHENGYANJIA_SP",1,3,10,"合成圣炎甲材料。<br>找荆棘岭杏仙合成圣炎甲。<br>掉落：火凤凰副本。"));
         this.goodDic[ConstData.GOOD_CLIP].push(new ConstClipVO(50208,"圣炎坠碎片","SHENGYANZUI_SP",1,3,10,"合成圣炎坠材料。<br>找荆棘岭杏仙合成圣炎坠。<br>掉落：火凤凰副本。"));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65001,"灭妖新人","MC_MIE_YAO_XIN_REN",new ValueVO(200,0),null,new ValueVO(20,0),null,null,0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65002,"一路向西","MC_YI_LU_XIANG_XI",new ValueVO(300,0),null,null,new ValueVO(10,0),new ValueVO(10,0),0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65003,"东土勇士","MC_DONG_TU_YONG_SHI",new ValueVO(200,0),new ValueVO(50,0),null,null,null,0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65004,"吐蕃勇士","MC_TU_BO_YONG_SHI",null,new ValueVO(100,0),null,new ValueVO(15,0),new ValueVO(15,0),0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65005,"腰缠万贯","MC_YAO_CHAN_WAN_GUAN",new ValueVO(350,0),null,new ValueVO(10,0),null,null,0,0,10,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65006,"任务达人","MC_REN_WU_DA_REN",new ValueVO(500,0),null,null,null,null,20,20,0,2,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65007,"暑假超级爽","MC_CHAO_JI_SHUANG",new ValueVO(800,0),null,null,null,null,0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65008,"土豪","MC_TU_HAO",new ValueVO(50,1),new ValueVO(50,1),new ValueVO(50,1),new ValueVO(50,1),new ValueVO(50,1),0,10,0,4,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65009,"中秋快乐","MC_ZHONG_QIU_KUAI_LE",null,new ValueVO(80,0),null,new ValueVO(30,0),new ValueVO(30,0),0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65010,"西域勇士","MC_XI_YU_YONG_SHI",null,null,new ValueVO(45,0),new ValueVO(20,0),new ValueVO(20,0),0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65011,"灭妖尊者","MC_MIE_YAO_ZUN_ZHE",new ValueVO(1000,0),new ValueVO(200,0),new ValueVO(55,0),null,null,0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65015,"国庆节快乐","MC_GUO_QIN_KUAI_LE",new ValueVO(500,0),new ValueVO(100,0),null,null,null,0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65016,"喜庆闹元宵","MC_XI_QIN_YUAN_XIAO",null,null,null,new ValueVO(35,0),new ValueVO(35,0),25,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65017,"灭妖周年庆","MC_MIE_YAO_YI_ZHOU_NIAN",new ValueVO(20,1),new ValueVO(20,1),new ValueVO(20,1),new ValueVO(20,1),new ValueVO(20,1),0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65100,"唯我独尊","MC_WEI_WO_DU_ZUN",new ValueVO(100,1),new ValueVO(100,1),new ValueVO(100,1),new ValueVO(100,1),new ValueVO(100,1),0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65101,"威震寰宇","MC_WEI_ZHEN_HUAN_YU",new ValueVO(80,1),new ValueVO(80,1),new ValueVO(80,1),new ValueVO(80,1),new ValueVO(80,1),0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65102,"无与伦比","MC_WU_YU_LUN_BI",new ValueVO(60,1),new ValueVO(60,1),new ValueVO(60,1),new ValueVO(60,1),new ValueVO(60,1),0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65103,"罕见敌手","MC_HAN_JIAN_DI_SHOU",new ValueVO(600,0),null,new ValueVO(100,0),null,null,0,0,0,1,1,""));
         this.goodDic[ConstData.CHENG_HAO].push(new ConstATTVO(65104,"高手寂寞","MC_GAO_SHOU_JI_MO",null,null,new ValueVO(70,0),new ValueVO(40,0),new ValueVO(40,0),0,0,0,1,1,""));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71001,"虎怪卡","HUGUAI_KA",null,null,null,null,null,0,0,1,0,5000,"幸运+1。掉落：双叉岭黄风洞黑松林<br>下一级:幸运+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71002,"虎怪卡","HUGUAI_KA",null,null,null,null,null,0,0,2,1,6000,"幸运+2<br>荆棘岭找凌空子合成获得。<br>下一级:幸运+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71003,"虎怪卡","HUGUAI_KA",null,null,null,null,null,0,0,3,2,7000,"幸运+3<br>荆棘岭找凌空子合成获得。<br>下一级:幸运+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71004,"虎怪卡","HUGUAI_KA",null,null,null,null,null,0,0,4,3,8000,"幸运+4<br>荆棘岭找凌空子合成获得。<br>下一级:幸运+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71005,"虎怪卡","HUGUAI_KA",null,null,null,null,null,0,0,5,4,10000,"幸运+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71006,"蝙蝠怪卡","BIANFUGUAI_KA",null,null,null,null,null,0,2,0,0,5000,"闪避+2。掉落：双叉岭黑风洞黄风洞火云洞麒麟山<br>下一级:闪避+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71007,"蝙蝠怪卡","BIANFUGUAI_KA",null,null,null,null,null,0,4,0,1,6000,"闪避+4<br>荆棘岭找凌空子合成获得。<br>下一级:闪避+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71008,"蝙蝠怪卡","BIANFUGUAI_KA",null,null,null,null,null,0,6,0,2,7000,"闪避+6<br>荆棘岭找凌空子合成获得。<br>下一级:闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71009,"蝙蝠怪卡","BIANFUGUAI_KA",null,null,null,null,null,0,8,0,3,8000,"闪避+8<br>荆棘岭找凌空子合成获得。<br>下一级:闪避+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71010,"蝙蝠怪卡","BIANFUGUAI_KA",null,null,null,null,null,0,10,0,4,10000,"闪避+10<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71011,"熊怪卡","XIONGGUAI_KA",new ValueVO(10,0),null,null,null,null,0,0,0,0,5000,"生命+10<br>掉落：黑风洞黑松林<br>下一级:生命+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71012,"熊怪卡","XIONGGUAI_KA",new ValueVO(20,0),null,null,null,null,0,0,0,1,6000,"生命+20<br>荆棘岭找凌空子合成获得。<br>下一级:生命+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71013,"熊怪卡","XIONGGUAI_KA",new ValueVO(30,0),null,null,null,null,0,0,0,2,7000,"生命+30<br>荆棘岭找凌空子合成获得。<br>下一级:生命+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71014,"熊怪卡","XIONGGUAI_KA",new ValueVO(40,0),null,null,null,null,0,0,0,3,8000,"生命+40<br>荆棘岭找凌空子合成获得。<br>下一级:生命+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71015,"熊怪卡","XIONGGUAI_KA",new ValueVO(50,0),null,null,null,null,0,0,0,4,10000,"生命+50<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71016,"虎法师","HUFA_KA",null,new ValueVO(5,0),null,null,null,0,0,0,0,5000,"法力+5。掉落：黄风洞黑松林乌鸡国火云洞火焰山 <br>下一级:法力+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71017,"虎法师","HUFA_KA",null,new ValueVO(10,0),null,null,null,0,0,0,1,6000,"法力+10<br>荆棘岭找凌空子合成获得。<br>下一级:法力+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71018,"虎法师","HUFA_KA",null,new ValueVO(15,0),null,null,null,0,0,0,2,7000,"法力+15<br>荆棘岭找凌空子合成获得。<br>下一级:法力+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71019,"虎法师","HUFA_KA",null,new ValueVO(20,0),null,null,null,0,0,0,3,8000,"法力+20<br>荆棘岭找凌空子合成获得。<br>下一级:法力+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71020,"虎法师","HUFA_KA",null,new ValueVO(25,0),null,null,null,0,0,0,4,10000,"法力+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71021,"蝙蝠弓卡","BIANFUGONG_KA",null,null,new ValueVO(3,0),null,null,0,0,0,0,5000,"攻击+3。掉落：黑松林平顶山琵琶洞麒麟山<br>下一级:攻击+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71022,"蝙蝠弓卡","BIANFUGONG_KA",null,null,new ValueVO(6,0),null,null,0,0,0,1,6000,"攻击+6<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71023,"蝙蝠弓卡","BIANFUGONG_KA",null,null,new ValueVO(9,0),null,null,0,0,0,2,7000,"攻击+9<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71024,"蝙蝠弓卡","BIANFUGONG_KA",null,null,new ValueVO(12,0),null,null,0,0,0,3,8000,"攻击+12<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71025,"蝙蝠弓卡","BIANFUGONG_KA",null,null,new ValueVO(15,0),null,null,0,0,0,4,10000,"攻击+15<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71026,"骷髅卡","KULOUBU_KA",null,null,null,null,null,2,0,0,0,5000,"暴击+2<br>掉落：白虎岭琵琶洞<br>下一级:暴击+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71027,"骷髅卡","KULOUBU_KA",null,null,null,null,null,4,0,0,1,6000,"暴击+4<br>荆棘岭找凌空子合成获得。<br>下一级:暴击+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71028,"骷髅卡","KULOUBU_KA",null,null,null,null,null,6,0,0,2,7000,"暴击+6<br>荆棘岭找凌空子合成获得。<br>下一级:暴击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71029,"骷髅卡","KULOUBU_KA",null,null,null,null,null,8,0,0,3,8000,"暴击+8<br>荆棘岭找凌空子合成获得。<br>下一级:暴击+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71030,"骷髅卡","KULOUBU_KA",null,null,null,null,null,10,0,0,4,10000,"暴击+10<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71031,"骷髅弓卡","KULOUGONG_KA",new ValueVO(10,0),null,null,null,null,1,0,0,0,5000,"生命+10暴击+1<br>掉落：白虎岭乌鸡国琵琶洞<br>下一级:生命+20暴击+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71032,"骷髅弓卡","KULOUGONG_KA",new ValueVO(20,0),null,null,null,null,2,0,0,1,6000,"生命+20暴击+2<br>荆棘岭找凌空子合成获得。<br>下一级:生命+30暴击+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71033,"骷髅弓卡","KULOUGONG_KA",new ValueVO(30,0),null,null,null,null,3,0,0,2,7000,"生命+30暴击+3<br>荆棘岭找凌空子合成获得。<br>下一级:生命+40暴击+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71034,"骷髅弓卡","KULOUGONG_KA",new ValueVO(40,0),null,null,null,null,4,0,0,3,8000,"生命+40暴击+4<br>荆棘岭找凌空子合成获得。<br>下一级:生命+50暴击+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71035,"骷髅弓卡","KULOUGONG_KA",new ValueVO(50,0),null,null,null,null,5,0,0,4,10000,"生命+50暴击+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71036,"骷髅法师卡","KULOUFA_KA",null,new ValueVO(4,0),null,null,new ValueVO(2,0),0,0,0,0,5000,"法力+4魔防+2<br>掉落：白虎岭火云洞琵琶洞<br>下一级:法力+4魔防+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71037,"骷髅法师卡","KULOUFA_KA",null,new ValueVO(8,0),null,null,new ValueVO(4,0),0,0,0,1,6000,"法力+8魔防+4<br>荆棘岭找凌空子合成获得。<br>下一级:法力+12魔防+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71038,"骷髅法师卡","KULOUFA_KA",null,new ValueVO(12,0),null,null,new ValueVO(6,0),0,0,0,2,7000,"法力+12魔防+6<br>荆棘岭找凌空子合成获得。<br>下一级:法力+16魔防+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71039,"骷髅法师卡","KULOUFA_KA",null,new ValueVO(16,0),null,null,new ValueVO(8,0),0,0,0,3,8000,"法力+16魔防+8<br>荆棘岭找凌空子合成获得。<br>下一级:法力+20魔防+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71040,"骷髅法师卡","KULOUFA_KA",null,new ValueVO(20,0),null,null,new ValueVO(10,0),0,0,0,4,10000,"法力+20魔防+10<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71041,"金角怪卡","JINJIAO_KA",null,null,new ValueVO(2,0),new ValueVO(2,0),null,0,0,0,0,5000,"攻击+2物防+2。<br>掉落：平顶山火云洞 <br>下一级:攻击+4物防+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71042,"金角怪卡","JINJIAO_KA",null,null,new ValueVO(4,0),new ValueVO(4,0),null,0,0,0,1,6000,"攻击+4物防+4<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+6物防+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71043,"金角怪卡","JINJIAO_KA",null,null,new ValueVO(6,0),new ValueVO(6,0),null,0,0,0,2,7000,"攻击+6物防+6<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+8物防+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71044,"金角怪卡","JINJIAO_KA",null,null,new ValueVO(8,0),new ValueVO(8,0),null,0,0,0,3,8000,"攻击+8物防+8<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+10物防+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71045,"金角怪卡","JINJIAO_KA",null,null,new ValueVO(10,0),new ValueVO(10,0),null,0,0,0,4,10000,"攻击+10物防+10<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71046,"银角怪卡","YINJIAO_KA",null,null,null,new ValueVO(4,0),null,0,0,0,0,5000,"物防+4。<br>掉落：平顶山乌鸡国<br>下一级:物防+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71047,"银角怪卡","YINJIAO_KA",null,null,null,new ValueVO(8,0),null,0,0,0,1,6000,"物防+8<br>荆棘岭找凌空子合成获得。<br>下一级:物防+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71048,"银角怪卡","YINJIAO_KA",null,null,null,new ValueVO(12,0),null,0,0,0,2,7000,"物防+12<br>荆棘岭找凌空子合成获得。<br>下一级:物防+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71049,"银角怪卡","YINJIAO_KA",null,null,null,new ValueVO(16,0),null,0,0,0,3,8000,"物防+16<br>荆棘岭找凌空子合成获得。<br>下一级:物防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71050,"银角怪卡","YINJIAO_KA",null,null,null,new ValueVO(20,0),null,0,0,0,4,10000,"物防+20<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71051,"玉狐卡","YUHU_KA",new ValueVO(10,0),null,null,null,new ValueVO(2,0),0,0,0,0,5000,"生命+10魔防+2。掉落：平顶山乌鸡国火云洞火焰山乱石山<br>下一级:生命+20魔防+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71052,"玉狐卡","YUHU_KA",new ValueVO(20,0),null,null,null,new ValueVO(4,0),0,0,0,1,6000,"生命+20魔防+4<br>荆棘岭找凌空子合成获得。<br>下一级:生命+10魔防+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71053,"玉狐卡","YUHU_KA",new ValueVO(30,0),null,null,null,new ValueVO(6,0),0,0,0,2,7000,"生命+30魔防+6<br>荆棘岭找凌空子合成获得。<br>下一级:生命+10魔防+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71054,"玉狐卡","YUHU_KA",new ValueVO(40,0),null,null,null,new ValueVO(8,0),0,0,0,3,8000,"生命+40魔防+8<br>荆棘岭找凌空子合成获得。<br>下一级:生命+10魔防+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71055,"玉狐卡","YUHU_KA",new ValueVO(50,0),null,null,null,new ValueVO(10,0),0,0,0,4,10000,"生命+50魔防10<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71056,"持枪小妖卡","CHIQIANGYAO_KA",null,null,new ValueVO(2,0),null,null,0,1,0,0,5000,"攻击+2闪避+1<br>掉落：火云洞 <br>下一级:攻击+4闪避+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71057,"持枪小妖卡","CHIQIANGYAO_KA",null,null,new ValueVO(4,0),null,null,0,2,0,1,6000,"攻击+4闪避+2<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+6闪避+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71058,"持枪小妖卡","CHIQIANGYAO_KA",null,null,new ValueVO(6,0),null,null,0,3,0,2,7000,"攻击+6闪避+3<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+8闪避+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71059,"持枪小妖卡","CHIQIANGYAO_KA",null,null,new ValueVO(8,0),null,null,0,4,0,3,8000,"攻击+8闪避+4<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+10闪避+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71060,"持枪小妖卡","CHIQIANGYAO_KA",null,null,new ValueVO(10,0),null,null,0,5,0,4,10000,"攻击+10闪避+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71061,"持刀小妖卡","CHIDAOYAO_KA",null,null,null,null,null,1,1,0,0,5000,"暴击+1闪避+1<br>掉落：火云洞 火焰山<br>下一级:暴击+2闪避+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71062,"持刀小妖卡","CHIDAOYAO_KA",null,null,null,null,null,2,2,0,1,6000,"暴击+2闪避+2<br>荆棘岭找凌空子合成获得。<br>下一级:暴击+3闪避+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71063,"持刀小妖卡","CHIDAOYAO_KA",null,null,null,null,null,3,3,0,2,7000,"暴击+3闪避+3<br>荆棘岭找凌空子合成获得。<br>下一级:暴击+4闪避+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71064,"持刀小妖卡","CHIDAOYAO_KA",null,null,null,null,null,4,4,0,3,8000,"暴击+4闪避+4<br>荆棘岭找凌空子合成获得。<br>下一级:暴击+5闪避+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71065,"持刀小妖卡","CHIDAOYAO_KA",null,null,null,null,null,5,5,0,4,10000,"暴击+5闪避+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71066,"虾兵甲卡","XIABINGJIA_KA",null,null,null,null,new ValueVO(5,0),0,0,0,0,5000,"魔防+5<br>掉落：黑水河<br>下一级:魔防+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71067,"虾兵甲卡","XIABINGJIA_KA",null,null,null,null,new ValueVO(10,0),0,0,0,1,6000,"魔防+10<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71068,"虾兵甲卡","XIABINGJIA_KA",null,null,null,null,new ValueVO(15,0),0,0,0,2,7000,"魔防+15<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71069,"虾兵甲卡","XIABINGJIA_KA",null,null,null,null,new ValueVO(20,0),0,0,0,3,8000,"魔防+20<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71070,"虾兵甲卡","XIABINGJIA_KA",null,null,null,null,new ValueVO(25,0),0,0,0,4,10000,"魔防+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71071,"蟹将甲卡","XIEJIANGJIA_KA",null,null,null,new ValueVO(5,0),null,0,0,0,0,5000,"物防+5<br>掉落：黑水河<br>下一级:物防+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71072,"蟹将甲卡","XIEJIANGJIA_KA",null,null,null,new ValueVO(10,0),null,0,0,0,1,6000,"物防+10<br>荆棘岭找凌空子合成获得。<br>下一级:物防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71073,"蟹将甲卡","XIEJIANGJIA_KA",null,null,null,new ValueVO(15,0),null,0,0,0,2,7000,"物防+15<br>荆棘岭找凌空子合成获得。<br>下一级:物防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71074,"蟹将甲卡","XIEJIANGJIA_KA",null,null,null,new ValueVO(20,0),null,0,0,0,3,8000,"物防+20<br>荆棘岭找凌空子合成获得。<br>下一级:物防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71075,"蟹将甲卡","XIEJIANGJIA_KA",null,null,null,new ValueVO(25,0),null,0,0,0,4,10000,"物防+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71076,"虎妖卡","HUYAO_KA",null,new ValueVO(4,0),null,new ValueVO(4,0),null,0,0,0,0,5000,"法力+4物防+4<br>掉落：车迟国<br>下一级:法力+8物防+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71077,"虎妖卡","HUYAO_KA",null,new ValueVO(8,0),null,new ValueVO(8,0),null,0,0,0,1,6000,"法力+8物防+8<br>荆棘岭找凌空子合成获得。<br>下一级:法力+12物防+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71078,"虎妖卡","HUYAO_KA",null,new ValueVO(12,0),null,new ValueVO(12,0),null,0,0,0,2,7000,"法力+12物防+12<br>荆棘岭找凌空子合成获得。<br>下一级:法力+16物防+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71079,"虎妖卡","HUYAO_KA",null,new ValueVO(16,0),null,new ValueVO(16,0),null,0,0,0,3,8000,"法力+16物防+16<br>荆棘岭找凌空子合成获得。<br>下一级:法力+20物防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71080,"虎妖卡","HUYAO_KA",null,new ValueVO(20,0),null,new ValueVO(20,0),null,0,0,0,4,10000,"法力+20物防+20<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71081,"鹿妖卡","LVYAO_KA",new ValueVO(20,0),null,null,null,null,0,1,0,0,5000,"生命+20闪避+1<br>掉落：车迟国<br>下一级:生命+30闪避+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71082,"鹿妖卡","LVYAO_KA",new ValueVO(30,0),null,null,null,null,0,2,0,1,6000,"生命+30闪避+2<br>荆棘岭找凌空子合成获得。<br>下一级:生命+40闪避+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71083,"鹿妖卡","LVYAO_KA",new ValueVO(40,0),null,null,null,null,0,3,0,2,7000,"生命+40闪避+3<br>荆棘岭找凌空子合成获得。<br>下一级:生命+50闪避+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71084,"鹿妖卡","LVYAO_KA",new ValueVO(50,0),null,null,null,null,0,4,0,3,8000,"生命+50闪避+4<br>荆棘岭找凌空子合成获得。<br>下一级:生命+60闪避+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71085,"鹿妖卡","LVYAO_KA",new ValueVO(60,0),null,null,null,null,0,5,0,4,10000,"生命+60闪避+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71086,"羊妖卡","YANGYAO_KA",null,null,new ValueVO(3,0),null,null,1,0,0,0,5000,"攻击+3暴击+1<br>掉落：车迟国乱石山<br>下一级:攻击+6暴击+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71087,"羊妖卡","YANGYAO_KA",null,null,new ValueVO(6,0),null,null,2,0,0,1,6000,"攻击+6暴击+2<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+9暴击+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71088,"羊妖卡","YANGYAO_KA",null,null,new ValueVO(9,0),null,null,3,0,0,2,7000,"攻击+9暴击+3<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+12暴击+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71089,"羊妖卡","YANGYAO_KA",null,null,new ValueVO(12,0),null,null,4,0,0,3,8000,"攻击+12暴击+4<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+15暴击+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71090,"羊妖卡","YANGYAO_KA",null,null,new ValueVO(15,0),null,null,5,0,0,4,10000,"攻击+15暴击+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71091,"虾兵乙卡","XIABINGYI_KA",null,null,null,null,new ValueVO(5,0),0,1,0,0,5000,"魔防+5闪避+1<br>掉落：通天河<br>下一级:魔防+10闪避+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71092,"虾兵乙卡","XIABINGYI_KA",null,null,null,null,new ValueVO(10,0),0,2,0,1,6000,"魔防+10闪避+2<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+10闪避+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71093,"虾兵乙卡","XIABINGYI_KA",null,null,null,null,new ValueVO(15,0),0,3,0,2,7000,"魔防+15闪避+3<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+15闪避+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71094,"虾兵乙卡","XIABINGYI_KA",null,null,null,null,new ValueVO(20,0),0,4,0,3,8000,"魔防+20闪避+4<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+20闪避+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71095,"虾兵乙卡","XIABINGYI_KA",null,null,null,null,new ValueVO(25,0),0,5,0,4,10000,"魔防+25闪避+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71096,"蟹将乙卡","XIEJIANGYI_KA",null,null,null,new ValueVO(5,0),null,1,0,0,0,5000,"物防+5暴击+1<br>掉落：通天河<br>下一级:物防+10暴击+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71097,"蟹将乙卡","XIEJIANGYI_KA",null,null,null,new ValueVO(10,0),null,2,0,0,1,6000,"物防+10暴击+2<br>荆棘岭找凌空子合成获得。<br>下一级:物防+15暴击+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71098,"蟹将乙卡","XIEJIANGYI_KA",null,null,null,new ValueVO(15,0),null,3,0,0,2,7000,"物防+15暴击+3<br>荆棘岭找凌空子合成获得。<br>下一级:物防+20暴击+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71099,"蟹将乙卡","XIEJIANGYI_KA",null,null,null,new ValueVO(20,0),null,4,0,0,3,8000,"物防+20暴击+4<br>荆棘岭找凌空子合成获得。<br>下一级:物防+25暴击+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71100,"蟹将乙卡","XIEJIANGYI_KA",null,null,null,new ValueVO(25,0),null,5,0,0,4,10000,"物防+25暴击+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71101,"牛头小妖卡","NIUTOUXIAOYAO_KA",new ValueVO(20,0),new ValueVO(4,0),null,null,null,0,0,0,0,5000,"生命+20法力+4<br>掉落：金兜山乱石山<br>下一级:生命+30法力+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71102,"牛头小妖卡","NIUTOUXIAOYAO_KA",new ValueVO(30,0),new ValueVO(8,0),null,null,null,0,0,0,1,6000,"生命+30法力+8<br>荆棘岭找凌空子合成获得。<br>下一级:生命+40法力+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71103,"牛头小妖卡","NIUTOUXIAOYAO_KA",new ValueVO(40,0),new ValueVO(12,0),null,null,null,0,0,0,2,7000,"生命+40法力+12<br>荆棘岭找凌空子合成获得。<br>下一级:生命+50法力+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71104,"牛头小妖卡","NIUTOUXIAOYAO_KA",new ValueVO(50,0),new ValueVO(16,0),null,null,null,0,0,0,3,8000,"生命+50法力+16<br>荆棘岭找凌空子合成获得。<br>下一级:生命+60法力+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71105,"牛头小妖卡","NIUTOUXIAOYAO_KA",new ValueVO(60,0),new ValueVO(20,0),null,null,null,0,0,0,4,10000,"生命+60法力+20<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71106,"牛头老妖卡","NIUTOULAOYAO_KA",null,new ValueVO(4,0),null,null,null,0,0,1,0,5000,"法力+4幸运+1<br>掉落：金兜山火焰山<br>下一级:法力+8幸运+2"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71107,"牛头老妖卡","NIUTOULAOYAO_KA",null,new ValueVO(8,0),null,null,null,0,0,2,1,6000,"法力+8幸运+2<br>荆棘岭找凌空子合成获得。<br>下一级:法力+12幸运+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71108,"牛头老妖卡","NIUTOULAOYAO_KA",null,new ValueVO(12,0),null,null,null,0,0,3,2,7000,"法力+12幸运+3<br>荆棘岭找凌空子合成获得。<br>下一级:法力+16幸运+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71109,"牛头老妖卡","NIUTOULAOYAO_KA",null,new ValueVO(16,0),null,null,null,0,0,4,3,8000,"法力+16幸运+4<br>荆棘岭找凌空子合成获得。<br>下一级:法力+20幸运+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71110,"牛头老妖卡","NIUTOULAOYAO_KA",null,new ValueVO(20,0),null,null,null,0,0,5,4,10000,"法力+20幸运+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71111,"邪武僧卡","XIEWUS_KA",null,null,new ValueVO(3,0),null,null,0,1,0,0,5000,"攻击+3闪避+1<br>掉落：小雷音寺<br>下一级:攻击+6闪避+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71112,"邪武僧卡","XIEWUS_KA",null,null,new ValueVO(6,0),null,null,0,2,0,1,6000,"攻击+6闪避+2<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+9闪避+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71113,"邪武僧卡","XIEWUS_KA",null,null,new ValueVO(9,0),null,null,0,3,0,2,7000,"攻击+9闪避+3<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+12闪避+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71114,"邪武僧卡","XIEWUS_KA",null,null,new ValueVO(12,0),null,null,0,4,0,3,8000,"攻击+12闪避+4<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+15闪避+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71115,"邪武僧卡","XIEWUS_KA",null,null,new ValueVO(15,0),null,null,0,5,0,4,10000,"攻击+15闪避+5<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71116,"邪法僧卡","XIEFAS_KA",null,new ValueVO(4,0),null,null,new ValueVO(3,0),0,0,0,0,5000,"法力+4魔防+3<br>掉落：小雷音寺<br>下一级:法力+8魔防+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71117,"邪法僧卡","XIEFAS_KA",null,new ValueVO(8,0),null,null,new ValueVO(6,0),0,0,0,1,6000,"法力+8魔防+6<br>荆棘岭找凌空子合成获得。<br>下一级:法力+12魔防+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71118,"邪法僧卡","XIEFAS_KA",null,new ValueVO(12,0),null,null,new ValueVO(9,0),0,0,0,2,7000,"法力+12魔防+9<br>荆棘岭找凌空子合成获得。<br>下一级:法力+16魔防+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71119,"邪法僧卡","XIEFAS_KA",null,new ValueVO(16,0),null,null,new ValueVO(12,0),0,0,0,3,8000,"法力+16魔防+12<br>荆棘岭找凌空子合成获得。<br>下一级:法力+20魔防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71120,"邪法僧卡","XIEFAS_KA",null,new ValueVO(20,0),null,null,new ValueVO(15,0),0,0,0,4,10000,"法力+20魔防+15<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71121,"绿蛇卡","LVS_KA",new ValueVO(20,0),null,null,new ValueVO(5,0),null,0,0,0,0,5000,"生命+20物防+5<br>掉落：七绝山<br>下一级:生命+30物防+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71122,"绿蛇卡","universal",new ValueVO(30,0),null,null,new ValueVO(10,0),null,0,0,0,1,6000,"生命+30物防+10<br>荆棘岭找凌空子合成获得。<br>下一级:生命+40物防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71123,"绿蛇卡","LVS_KA",new ValueVO(40,0),null,null,new ValueVO(15,0),null,0,0,0,2,7000,"生命+40物防+15<br>荆棘岭找凌空子合成获得。<br>下一级:生命+50物防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71124,"绿蛇卡","LVS_KA",new ValueVO(50,0),null,null,new ValueVO(20,0),null,0,0,0,3,8000,"生命+50物防+20<br>荆棘岭找凌空子合成获得。<br>下一级:生命+60物防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71125,"绿蛇卡","LVS_KA",new ValueVO(60,0),null,null,new ValueVO(25,0),null,0,0,0,4,10000,"生命+60物防+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71126,"红蛇卡","HONGSHE_KA",new ValueVO(20,0),null,null,null,new ValueVO(5,0),0,0,0,0,5000,"生命+20魔防+5<br>掉落：七绝山<br>下一级:生命+30魔防+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71127,"红蛇卡","HONGSHE_KA",new ValueVO(30,0),null,null,null,new ValueVO(10,0),0,0,0,1,6000,"生命+30魔防+10<br>荆棘岭找凌空子合成获得。<br>下一级:生命+40魔防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71128,"红蛇卡","HONGSHE_KA",new ValueVO(40,0),null,null,null,new ValueVO(15,0),0,0,0,2,7000,"生命+40魔防+15<br>荆棘岭找凌空子合成获得。<br>下一级:生命+50魔防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71129,"红蛇卡","HONGSHE_KA",new ValueVO(50,0),null,null,null,new ValueVO(20,0),0,0,0,3,8000,"生命+50魔防+20<br>荆棘岭找凌空子合成获得。<br>下一级:生命+60魔防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71130,"红蛇卡","HONGSHE_KA",new ValueVO(60,0),null,null,null,new ValueVO(25,0),0,0,0,4,10000,"生命+60魔防+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71131,"假面怪卡","JIAMIANGUAI_KA",new ValueVO(20,0),null,new ValueVO(2,0),null,null,0,0,0,0,5000,"生命+20攻击+2<br>掉落：麒麟山<br>下一级:生命+30攻击+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71132,"假面怪卡","JIAMIANGUAI_KA",new ValueVO(30,0),null,new ValueVO(4,0),null,null,0,0,0,1,6000,"生命+30攻击+4<br>荆棘岭找凌空子合成获得。<br>下一级:生命+40攻击+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71133,"假面怪卡","JIAMIANGUAI_KA",new ValueVO(40,0),null,new ValueVO(6,0),null,null,0,0,0,2,7000,"生命+40攻击+6<br>荆棘岭找凌空子合成获得。<br>下一级:生命+50攻击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71134,"假面怪卡","JIAMIANGUAI_KA",new ValueVO(50,0),null,new ValueVO(8,0),null,null,0,0,0,3,8000,"生命+50攻击+8<br>荆棘岭找凌空子合成获得。<br>下一级:生命+60攻击+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71135,"假面怪卡","JIAMIANGUAI_KA",new ValueVO(60,0),null,new ValueVO(10,0),null,null,0,0,0,4,10000,"生命+60攻击+10<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71136,"绿蛛卡","LVZHU_KA",null,null,null,new ValueVO(5,0),null,2,0,0,0,5000,"物防+5暴击+2<br>掉落：盘丝洞<br>下一级:物防+10暴击+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71137,"绿蛛卡","LVZHU_KA",null,null,null,new ValueVO(10,0),null,3,0,0,1,6000,"物防+10暴击+3<br>荆棘岭找凌空子合成获得。<br>下一级:物防+15暴击+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71138,"绿蛛卡","LVZHU_KA",null,null,null,new ValueVO(15,0),null,4,0,0,2,7000,"物防+15暴击+4<br>荆棘岭找凌空子合成获得。<br>下一级:物防+20暴击+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71139,"绿蛛卡","LVZHU_KA",null,null,null,new ValueVO(20,0),null,5,0,0,3,8000,"物防+20暴击+5<br>荆棘岭找凌空子合成获得。<br>下一级:物防+25暴击+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71140,"绿蛛卡","LVZHU_KA",null,null,null,new ValueVO(25,0),null,6,0,0,4,10000,"物防+25暴击+6<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71141,"紫蛛卡","ZIZHU_KA",null,null,null,null,new ValueVO(5,0),0,2,0,0,5000,"魔防+5闪避+2<br>掉落：盘丝洞<br>下一级:魔防+10闪避+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71142,"紫蛛卡","ZIZHU_KA",null,null,null,null,new ValueVO(10,0),0,3,0,1,6000,"魔防+10闪避+3<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+15闪避+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71143,"紫蛛卡","ZIZHU_KA",null,null,null,null,new ValueVO(15,0),0,4,0,2,7000,"魔防+15闪避+4<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+20闪避+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71144,"紫蛛卡","ZIZHU_KA",null,null,null,null,new ValueVO(20,0),0,5,0,3,8000,"魔防+20闪避+5<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+25闪避+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71145,"紫蛛卡","ZIZHU_KA",null,null,null,null,new ValueVO(25,0),0,6,0,4,10000,"魔防+25闪避+6<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71146,"黄蜈蚣卡","HUANGWUGONG_KA",null,new ValueVO(5,0),null,new ValueVO(5,0),null,0,0,0,0,5000,"法力+5物防+5<br>掉落：黄花观<br>下一级:法力+10物防+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71147,"黄蜈蚣卡","HUANGWUGONG_KA",null,new ValueVO(10,0),null,new ValueVO(10,0),null,0,0,0,1,6000,"法力+10物防+10<br>荆棘岭找凌空子合成获得。<br>下一级:法力+15物防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71148,"黄蜈蚣卡","HUANGWUGONG_KA",null,new ValueVO(15,0),null,new ValueVO(15,0),null,0,0,0,2,7000,"法力+15物防+15<br>荆棘岭找凌空子合成获得。<br>下一级:法力+20物防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71149,"黄蜈蚣卡","HUANGWUGONG_KA",null,new ValueVO(20,0),null,new ValueVO(20,0),null,0,0,0,3,8000,"法力+20物防+20<br>荆棘岭找凌空子合成获得。<br>下一级:法力+25物防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71150,"黄蜈蚣卡","HUANGWUGONG_KA",null,new ValueVO(25,0),null,new ValueVO(25,0),null,0,0,0,4,10000,"法力+25物防+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71151,"蓝蜈蚣卡","LVWUGONG_KA",null,new ValueVO(5,0),null,null,new ValueVO(5,0),0,0,0,0,5000,"法力+5魔防+5<br>掉落：黄花观<br>下一级:法力+10魔防+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71152,"蓝蜈蚣卡","LVWUGONG_KA",null,new ValueVO(10,0),null,null,new ValueVO(10,0),0,0,0,1,6000,"法力+10魔防+10<br>荆棘岭找凌空子合成获得。<br>下一级:法力+15魔防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71153,"蓝蜈蚣卡","LVWUGONG_KA",null,new ValueVO(15,0),null,null,new ValueVO(15,0),0,0,0,2,7000,"法力+15魔防+15<br>荆棘岭找凌空子合成获得。<br>下一级:法力+20魔防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71154,"蓝蜈蚣卡","LVWUGONG_KA",null,new ValueVO(20,0),null,null,new ValueVO(20,0),0,0,0,3,8000,"法力+20魔防+20<br>荆棘岭找凌空子合成获得。<br>下一级:法力+25魔防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71155,"蓝蜈蚣卡","LVWUGONG_KA",null,new ValueVO(25,0),null,null,new ValueVO(25,0),0,0,0,4,10000,"法力+25魔防+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71156,"白象卡","BAIXIANG_KA",null,null,new ValueVO(4,0),null,null,2,0,0,0,5000,"攻击+4暴击+2<br>掉落：狮驼岭<br>下一级:攻击+8暴击+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71157,"白象卡","BAIXIANG_KA",null,null,new ValueVO(8,0),null,null,3,0,0,1,6000,"攻击+8暴击+3<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+12暴击+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71158,"白象卡","BAIXIANG_KA",null,null,new ValueVO(12,0),null,null,4,0,0,2,7000,"攻击+12暴击+4<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+16暴击+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71159,"白象卡","BAIXIANG_KA",null,null,new ValueVO(16,0),null,null,5,0,0,3,8000,"攻击+16暴击+5<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+20暴击+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71160,"白象卡","BAIXIANG_KA",null,null,new ValueVO(20,0),null,null,6,0,0,4,10000,"攻击+20暴击+6<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71161,"黄狮卡","SHIZI_KA",new ValueVO(40,0),null,null,new ValueVO(10,0),null,0,0,0,0,5000,"生命+40物防+10<br>掉落：清华洞<br>下一级:生命+50物防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71162,"黄狮卡","SHIZI_KA",new ValueVO(50,0),null,null,new ValueVO(15,0),null,0,0,0,1,6000,"生命+50物防+15<br>荆棘岭找凌空子合成获得。<br>下一级:生命+60物防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71163,"黄狮卡","SHIZI_KA",new ValueVO(60,0),null,null,new ValueVO(20,0),null,0,0,0,2,7000,"生命+60物防+20<br>荆棘岭找凌空子合成获得。<br>下一级:生命+70物防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71164,"黄狮卡","SHIZI_KA",new ValueVO(70,0),null,null,new ValueVO(25,0),null,0,0,0,3,8000,"生命+70物防+25<br>荆棘岭找凌空子合成获得。<br>下一级:生命+80物防+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71165,"黄狮卡","SHIZI_KA",new ValueVO(80,0),null,null,new ValueVO(30,0),null,0,0,0,4,10000,"生命+80物防+30<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71166,"白狐卡","BAIHULI_KA",new ValueVO(40,0),new ValueVO(5,0),null,null,null,0,0,0,0,10000,"生命+40法力+5<br>掉落：清华洞<br>下一级:生命+50法力+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71167,"白狐卡","BAIHULI_KA",new ValueVO(50,0),new ValueVO(10,0),null,null,null,0,0,0,1,15000,"生命+50法力+10<br>荆棘岭找凌空子合成获得。<br>下一级:生命+60法力+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71168,"白狐卡","BAIHULI_KA",new ValueVO(60,0),new ValueVO(15,0),null,null,null,0,0,0,2,20000,"生命+60法力+15<br>荆棘岭找凌空子合成获得。<br>下一级:生命+70法力+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71169,"白狐卡","BAIHULI_KA",new ValueVO(70,0),new ValueVO(20,0),null,null,null,0,0,0,3,25000,"生命+70法力+20<br>荆棘岭找凌空子合成获得。<br>下一级:生命+80法力+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71170,"白狐卡","BAIHULI_KA",new ValueVO(80,0),new ValueVO(25,0),null,null,null,0,0,0,4,30000,"生命+80法力+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71171,"小鹏弓手卡","XIAOPENGGONG_KA",null,null,new ValueVO(5,0),null,null,0,2,0,0,10000,"攻击+5闪避+2<br>掉落：陷空山<br>下一级:攻击+10闪避+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71172,"小鹏弓手卡","XIAOPENGGONG_KA",null,null,new ValueVO(10,0),null,null,0,3,0,1,15000,"攻击+10闪避+3<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+15闪避+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71173,"小鹏弓手卡","XIAOPENGGONG_KA",null,null,new ValueVO(15,0),null,null,0,4,0,2,20000,"攻击+15闪避+4<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+20闪避+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71174,"小鹏弓手卡","XIAOPENGGONG_KA",null,null,new ValueVO(20,0),null,null,0,5,0,3,25000,"攻击+20闪避+5<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+25闪避+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71175,"小鹏弓手卡","XIAOPENGGONG_KA",null,null,new ValueVO(25,0),null,null,0,6,0,4,30000,"攻击+25闪避+6<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71176,"苍狼小妖卡","CANGLANG_KA",null,null,null,new ValueVO(10,0),new ValueVO(5,0),0,0,0,0,10000,"物防+10魔防+5<br>掉落：隐雾山<br>下一级:物防+15魔防+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71177,"苍狼小妖卡","CANGLANG_KA",null,null,null,new ValueVO(15,0),new ValueVO(10,0),0,0,0,1,15000,"物防+15魔防+10<br>荆棘岭找凌空子合成获得。<br>下一级:物防+20魔防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71178,"苍狼小妖卡","CANGLANG_KA",null,null,null,new ValueVO(20,0),new ValueVO(15,0),0,0,0,2,20000,"物防+20魔防+15<br>荆棘岭找凌空子合成获得。<br>下一级:物防+25魔防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71179,"苍狼小妖卡","CANGLANG_KA",null,null,null,new ValueVO(25,0),new ValueVO(20,0),0,0,0,3,25000,"物防+25魔防+20<br>荆棘岭找凌空子合成获得。<br>下一级:物防+30魔防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71180,"苍狼小妖卡","CANGLANG_KA",null,null,null,new ValueVO(30,0),new ValueVO(25,0),0,0,0,4,30000,"物防+30魔防+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71181,"豹精小妖卡","BAOJING_KA",new ValueVO(60,0),null,null,null,new ValueVO(10,0),0,0,0,0,10000,"生命+60魔防+10<br>掉落：豹头山<br>下一级:生命+70魔防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71182,"豹精小妖卡","BAOJING_KA",new ValueVO(70,0),null,null,null,new ValueVO(15,0),0,0,0,1,15000,"生命+70魔防+15<br>荆棘岭找凌空子合成获得。<br>下一级:生命+80魔防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71183,"豹精小妖卡","BAOJING_KA",new ValueVO(80,0),null,null,null,new ValueVO(20,0),0,0,0,2,20000,"生命+80魔防+20<br>荆棘岭找凌空子合成获得。<br>下一级:生命+90魔防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71184,"豹精小妖卡","BAOJING_KA",new ValueVO(90,0),null,null,null,new ValueVO(25,0),0,0,0,3,25000,"生命+90魔防+25<br>荆棘岭找凌空子合成获得。<br>下一级:生命+100魔防+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71185,"豹精小妖卡","BAOJING_KA",new ValueVO(100,0),null,null,null,new ValueVO(30,0),0,0,0,4,30000,"生命+100魔防+30<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71186,"犀牛小妖卡","XINIU_KA",null,null,new ValueVO(5,0),new ValueVO(10,0),null,0,0,0,0,10000,"攻击+5物防+10<br>掉落：青龙山 <br>下一级:攻击+10物防+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71187,"犀牛小妖卡","XINIU_KA",null,null,new ValueVO(10,0),new ValueVO(15,0),null,0,0,0,1,15000,"攻击+10物防+15<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+15物防+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71188,"犀牛小妖卡","XINIU_KA",null,null,new ValueVO(15,0),new ValueVO(20,0),null,0,0,0,2,20000,"攻击+15物防+20<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+20物防+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71189,"犀牛小妖卡","XINIU_KA",null,null,new ValueVO(20,0),new ValueVO(25,0),null,0,0,0,3,25000,"攻击+20物防+25<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+25物防+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71190,"犀牛小妖卡","XINIU_KA",null,null,new ValueVO(25,0),new ValueVO(30,0),null,0,0,0,4,30000,"攻击+25物防+30<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71191,"兔妖卡","TUYAO_KA",null,null,null,null,new ValueVO(10,0),2,0,0,0,10000,"魔防+10暴击+2<br>掉落：布金禅寺<br>下一级:魔防+15暴击+3"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71192,"兔妖卡","TUYAO_KA",null,null,null,null,new ValueVO(15,0),3,0,0,1,15000,"魔防+15暴击+3<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+20暴击+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71193,"兔妖卡","TUYAO_KA",null,null,null,null,new ValueVO(20,0),4,0,0,2,20000,"魔防+20暴击+4<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+25暴击+5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71194,"兔妖卡","TUYAO_KA",null,null,null,null,new ValueVO(25,0),5,0,0,3,25000,"魔防+25暴击+5<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+30暴击+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71195,"兔妖卡","TUYAO_KA",null,null,null,null,new ValueVO(30,0),6,0,0,4,30000,"魔防+30暴击+6<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71196,"武僧卡","WUS_KA",new ValueVO(60,0),null,new ValueVO(5,0),null,null,0,0,0,0,5000,"生命60攻击+5<br>掉落：大雷音寺<br>下一级:生命+70攻击+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71197,"武僧卡","WUS_KA",new ValueVO(70,0),null,new ValueVO(10,0),null,null,0,0,0,1,6000,"生命+70攻击+10<br>荆棘岭找凌空子合成获得。<br>下一级:生命+80攻击+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71198,"武僧卡","WUS_KA",new ValueVO(80,0),null,new ValueVO(15,0),null,null,0,0,0,2,7000,"生命+80攻击+15<br>荆棘岭找凌空子合成获得。<br>下一级:生命+90攻击+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71199,"武僧卡","WUS_KA",new ValueVO(90,0),null,new ValueVO(20,0),null,null,0,0,0,3,8000,"生命+90攻击+20<br>荆棘岭找凌空子合成获得。<br>下一级:生命+100攻击+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71200,"武僧卡","WUS_KA",new ValueVO(100,0),null,new ValueVO(25,0),null,null,0,0,0,4,10000,"生命+100攻击+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71501,"+1虎怪卡","HUGUAI_KA",null,null,null,null,null,0,0,6,4,10000,"幸运6<br>荆棘岭找凌空子强化获得。<br>下一级:幸运+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71502,"+2虎怪卡","HUGUAI_KA",null,null,null,null,null,0,0,7,4,10000,"幸运+7<br>荆棘岭找凌空子强化获得。<br>下一级:幸运+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71503,"+3虎怪卡","HUGUAI_KA",null,null,null,null,null,0,0,8,4,10000,"幸运+8<br>荆棘岭找凌空子强化获得。<br>下一级:幸运+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71504,"+4虎怪卡","HUGUAI_KA",null,null,null,null,null,0,0,9,4,10000,"幸运+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71506,"+1蝙蝠怪卡","BIANFUGUAI_KA",null,null,null,null,null,0,12,0,4,10000,"闪避+12<br>荆棘岭找凌空子强化获得。<br>下一级:闪避+14"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71507,"+2蝙蝠怪卡","BIANFUGUAI_KA",null,null,null,null,null,0,14,0,4,10000,"闪避+14<br>荆棘岭找凌空子强化获得。<br>下一级:闪避+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71508,"+3蝙蝠怪卡","BIANFUGUAI_KA",null,null,null,null,null,0,16,0,4,10000,"闪避+16<br>荆棘岭找凌空子强化获得。<br>下一级:闪避+18"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71509,"+4蝙蝠怪卡","BIANFUGUAI_KA",null,null,null,null,null,0,18,0,4,10000,"闪避+18<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71511,"+1熊怪卡","XIONGGUAI_KA",new ValueVO(70,0),null,null,null,null,0,0,0,4,10000,"生命+70<br>荆棘岭找凌空子强化获得。<br>下一级:生命+90"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71512,"+2熊怪卡","XIONGGUAI_KA",new ValueVO(90,0),null,null,null,null,0,0,0,4,10000,"生命+90<br>荆棘岭找凌空子强化获得。<br>下一级:生命+110"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71513,"+3熊怪卡","XIONGGUAI_KA",new ValueVO(110,0),null,null,null,null,0,0,0,4,10000,"生命+110<br>荆棘岭找凌空子强化获得。<br>下一级:生命+130"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71514,"+4熊怪卡","XIONGGUAI_KA",new ValueVO(130,0),null,null,null,null,0,0,0,4,10000,"生命+130<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71516,"+1虎法师","HUFA_KA",null,new ValueVO(35,0),null,null,null,0,0,0,4,10000,"法力+35<br>荆棘岭找凌空子强化获得。<br>下一级:法力+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71517,"+2虎法师","HUFA_KA",null,new ValueVO(45,0),null,null,null,0,0,0,4,10000,"法力+45<br>荆棘岭找凌空子强化获得。<br>下一级:法力+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71518,"+3虎法师","HUFA_KA",null,new ValueVO(55,0),null,null,null,0,0,0,4,10000,"法力+55<br>荆棘岭找凌空子强化获得。<br>下一级:法力+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71519,"+4虎法师","HUFA_KA",null,new ValueVO(65,0),null,null,null,0,0,0,4,10000,"法力+65<br>荆棘岭找凌空子强化获得。5"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71521,"+1蝙蝠弓卡","BIANFUGONG_KA",null,null,new ValueVO(21,0),null,null,0,0,0,4,10000,"攻击+21<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71522,"+2蝙蝠弓卡","BIANFUGONG_KA",null,null,new ValueVO(27,0),null,null,0,0,0,4,10000,"攻击+27<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+33"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71523,"+3蝙蝠弓卡","BIANFUGONG_KA",null,null,new ValueVO(33,0),null,null,0,0,0,4,10000,"攻击+33<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+39"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71524,"+4蝙蝠弓卡","BIANFUGONG_KA",null,null,new ValueVO(39,0),null,null,0,0,0,4,10000,"攻击+39<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71526,"+1骷髅卡","KULOUBU_KA",null,null,null,null,null,12,0,0,4,10000,"暴击+12<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+14"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71527,"+2骷髅卡","KULOUBU_KA",null,null,null,null,null,14,0,0,4,10000,"暴击+14<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71528,"+3骷髅卡","KULOUBU_KA",null,null,null,null,null,16,0,0,4,10000,"暴击+16<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+18"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71529,"+4骷髅卡","KULOUBU_KA",null,null,null,null,null,18,0,0,4,10000,"暴击+18<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71531,"+1骷髅弓卡","KULOUGONG_KA",new ValueVO(70,0),null,null,null,null,6,0,0,4,10000,"生命+70暴击+6<br>荆棘岭找凌空子强化获得。<br>下一级:生命+90暴击+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71532,"+2骷髅弓卡","KULOUGONG_KA",new ValueVO(90,0),null,null,null,null,7,0,0,4,10000,"生命+90暴击+7<br>荆棘岭找凌空子强化获得。<br>下一级:生命+110暴击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71533,"+3骷髅弓卡","KULOUGONG_KA",new ValueVO(110,0),null,null,null,null,8,0,0,4,10000,"生命+110暴击+8<br>荆棘岭找凌空子强化获得。<br>下一级:生命+130暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71534,"+4骷髅弓卡","KULOUGONG_KA",new ValueVO(130,0),null,null,null,null,9,0,0,4,10000,"生命+130暴击+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71536,"+1骷髅法师卡","KULOUFA_KA",null,new ValueVO(28,0),null,null,new ValueVO(14,0),0,0,0,4,10000,"法力+28魔防+14<br>荆棘岭找凌空子强化获得。<br>下一级:法力+36魔防+18"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71537,"+2骷髅法师卡","KULOUFA_KA",null,new ValueVO(36,0),null,null,new ValueVO(18,0),0,0,0,4,10000,"法力+36魔防+18<br>荆棘岭找凌空子强化获得。<br>下一级:法力+44魔防+22"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71538,"+3骷髅法师卡","KULOUFA_KA",null,new ValueVO(44,0),null,null,new ValueVO(22,0),0,0,0,4,10000,"法力+44魔防+22<br>荆棘岭找凌空子强化获得。<br>下一级:法力+52魔防+26"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71539,"+4骷髅法师卡","KULOUFA_KA",null,new ValueVO(52,0),null,null,new ValueVO(26,0),0,0,0,4,10000,"法力+52魔防+26<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71541,"+1金角怪卡","JINJIAO_KA",null,null,new ValueVO(14,0),new ValueVO(14,0),null,0,0,0,4,10000,"攻击+14物防+14<br>荆棘岭找凌空子强化获得。 <br>下一级:攻击+18物防+18"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71542,"+2金角怪卡","JINJIAO_KA",null,null,new ValueVO(18,0),new ValueVO(18,0),null,0,0,0,4,10000,"攻击+18物防+18<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+22物防+22"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71543,"+3金角怪卡","JINJIAO_KA",null,null,new ValueVO(22,0),new ValueVO(22,0),null,0,0,0,4,10000,"攻击+22物防+22<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+26物防+26"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71544,"+4金角怪卡","JINJIAO_KA",null,null,new ValueVO(26,0),new ValueVO(26,0),null,0,0,0,4,10000,"攻击+26物防+26<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71546,"+1银角怪卡","YINJIAO_KA",null,null,null,new ValueVO(28,0),null,0,0,0,4,10000,"物防+28<br>荆棘岭找凌空子强化获得。<br>下一级:物防+36"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71547,"+2银角怪卡","YINJIAO_KA",null,null,null,new ValueVO(36,0),null,0,0,0,4,10000,"物防+36<br>荆棘岭找凌空子强化获得。<br>下一级:物防+44"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71548,"+3银角怪卡","YINJIAO_KA",null,null,null,new ValueVO(44,0),null,0,0,0,4,10000,"物防+44<br>荆棘岭找凌空子强化获得。<br>下一级:物防+52"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71549,"+4银角怪卡","YINJIAO_KA",null,null,null,new ValueVO(52,0),null,0,0,0,4,10000,"物防+52<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71551,"+1玉狐卡","YUHU_KA",new ValueVO(70,0),null,null,null,new ValueVO(14,0),0,0,0,4,10000,"生命+70魔防+14<br>荆棘岭找凌空子强化获得。<br>下一级:生命+90魔防+18"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71552,"+2玉狐卡","YUHU_KA",new ValueVO(90,0),null,null,null,new ValueVO(18,0),0,0,0,4,10000,"生命+90魔防+18<br>荆棘岭找凌空子强化获得。<br>下一级:生命+110魔防+22"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71553,"+3玉狐卡","YUHU_KA",new ValueVO(110,0),null,null,null,new ValueVO(22,0),0,0,0,4,10000,"生命+110魔防+22<br>荆棘岭找凌空子强化获得。<br>下一级:生命+130魔防+26"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71554,"+4玉狐卡","YUHU_KA",new ValueVO(130,0),null,null,null,new ValueVO(26,0),0,0,0,4,10000,"生命+130魔防+26<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71556,"+1持枪小妖卡","CHIQIANGYAO_KA",null,null,new ValueVO(14,0),null,null,0,6,0,4,10000,"攻击+14闪避+6<br>荆棘岭找凌空子强化获得。 <br>下一级:攻击+18闪避+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71557,"+2持枪小妖卡","CHIQIANGYAO_KA",null,null,new ValueVO(18,0),null,null,0,7,0,4,10000,"攻击+18闪避+7<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+22闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71558,"+3持枪小妖卡","CHIQIANGYAO_KA",null,null,new ValueVO(22,0),null,null,0,8,0,4,10000,"攻击+22闪避+8<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+26闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71559,"+4持枪小妖卡","CHIQIANGYAO_KA",null,null,new ValueVO(26,0),null,null,0,9,0,4,10000,"攻击+26闪避+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71561,"+1持刀小妖卡","CHIDAOYAO_KA",null,null,null,null,null,6,6,0,4,10000,"暴击+6闪避+6<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+7闪避+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71562,"+2持刀小妖卡","CHIDAOYAO_KA",null,null,null,null,null,7,7,0,4,10000,"暴击+7闪避+7<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+8闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71563,"+3持刀小妖卡","CHIDAOYAO_KA",null,null,null,null,null,8,8,0,4,10000,"暴击+8闪避+8<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+9闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71564,"+4持刀小妖卡","CHIDAOYAO_KA",null,null,null,null,null,9,9,0,4,10000,"暴击+9闪避+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71566,"+1虾兵甲卡","XIABINGJIA_KA",null,null,null,null,new ValueVO(35,0),0,0,0,4,10000,"魔防+35<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71567,"+2虾兵甲卡","XIABINGJIA_KA",null,null,null,null,new ValueVO(45,0),0,0,0,4,10000,"魔防+45<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71568,"+3虾兵甲卡","XIABINGJIA_KA",null,null,null,null,new ValueVO(55,0),0,0,0,4,10000,"魔防+55<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71569,"+4虾兵甲卡","XIABINGJIA_KA",null,null,null,null,new ValueVO(65,0),0,0,0,4,10000,"魔防+65<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71571,"+1蟹将甲卡","XIEJIANGJIA_KA",null,null,null,new ValueVO(35,0),null,0,0,0,4,10000,"物防+35<br>荆棘岭找凌空子强化获得。<br>下一级:物防+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71572,"+2蟹将甲卡","XIEJIANGJIA_KA",null,null,null,new ValueVO(45,0),null,0,0,0,4,10000,"物防+45<br>荆棘岭找凌空子强化获得。<br>下一级:物防+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71573,"+3蟹将甲卡","XIEJIANGJIA_KA",null,null,null,new ValueVO(55,0),null,0,0,0,4,10000,"物防+55<br>荆棘岭找凌空子强化获得。<br>下一级:物防+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71574,"+4蟹将甲卡","XIEJIANGJIA_KA",null,null,null,new ValueVO(65,0),null,0,0,0,4,10000,"物防+65<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71576,"+1虎妖卡","HUYAO_KA",null,new ValueVO(28,0),null,new ValueVO(28,0),null,0,0,0,4,10000,"法力+28物防+28<br>荆棘岭找凌空子强化获得。<br>下一级:法力+36物防+36"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71577,"+2虎妖卡","HUYAO_KA",null,new ValueVO(36,0),null,new ValueVO(36,0),null,0,0,0,4,10000,"法力+36物防+36<br>荆棘岭找凌空子强化获得。<br>下一级:法力+44物防+44"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71578,"+3虎妖卡","HUYAO_KA",null,new ValueVO(44,0),null,new ValueVO(44,0),null,0,0,0,4,10000,"法力+44物防+44<br>荆棘岭找凌空子强化获得。<br>下一级:法力+52物防+52"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71579,"+4虎妖卡","HUYAO_KA",null,new ValueVO(52,0),null,new ValueVO(52,0),null,0,0,0,4,10000,"法力+52物防+52<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71581,"+1鹿妖卡","LVYAO_KA",new ValueVO(80,0),null,null,null,null,0,6,0,4,10000,"生命+80闪避+6<br>荆棘岭找凌空子强化获得。<br>下一级:生命+100闪避+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71582,"+2鹿妖卡","LVYAO_KA",new ValueVO(100,0),null,null,null,null,0,7,0,4,10000,"生命+100闪避+7<br>荆棘岭找凌空子强化获得。<br>下一级:生命+120闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71583,"+3鹿妖卡","LVYAO_KA",new ValueVO(120,0),null,null,null,null,0,8,0,4,10000,"生命+120闪避+8<br>荆棘岭找凌空子强化获得。<br>下一级:生命+140闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71584,"+4鹿妖卡","LVYAO_KA",new ValueVO(140,0),null,null,null,null,0,9,0,4,10000,"生命+140闪避+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71586,"+1羊妖卡","YANGYAO_KA",null,null,new ValueVO(21,0),null,null,6,0,0,4,10000,"攻击+21暴击+6<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+27暴击+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71587,"+2羊妖卡","YANGYAO_KA",null,null,new ValueVO(27,0),null,null,7,0,0,4,10000,"攻击+27暴击+7<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+33暴击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71588,"+3羊妖卡","YANGYAO_KA",null,null,new ValueVO(33,0),null,null,8,0,0,4,10000,"攻击+33暴击+8<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+39暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71589,"+4羊妖卡","YANGYAO_KA",null,null,new ValueVO(39,0),null,null,9,0,0,4,10000,"攻击+39暴击+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71591,"+1虾兵乙卡","XIABINGYI_KA",null,null,null,null,new ValueVO(35,0),0,6,0,4,10000,"魔防+35闪避+6<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+45闪避+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71592,"+2虾兵乙卡","XIABINGYI_KA",null,null,null,null,new ValueVO(45,0),0,7,0,4,10000,"魔防+45闪避+7<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+55闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71593,"+3虾兵乙卡","XIABINGYI_KA",null,null,null,null,new ValueVO(55,0),0,8,0,4,10000,"魔防+55闪避+8<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+65闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71594,"+4虾兵乙卡","XIABINGYI_KA",null,null,null,null,new ValueVO(65,0),0,9,0,4,10000,"魔防+65闪避+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71596,"+1蟹将乙卡","XIEJIANGYI_KA",null,null,null,new ValueVO(35,0),null,6,0,0,4,10000,"物防+35暴击+6<br>荆棘岭找凌空子强化获得。<br>下一级:物防+45暴击+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71597,"+2蟹将乙卡","XIEJIANGYI_KA",null,null,null,new ValueVO(45,0),null,7,0,0,4,10000,"物防+45暴击+7<br>荆棘岭找凌空子强化获得。<br>下一级:物防+55暴击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71598,"+3蟹将乙卡","XIEJIANGYI_KA",null,null,null,new ValueVO(55,0),null,8,0,0,4,10000,"物防+55暴击+8<br>荆棘岭找凌空子强化获得。<br>下一级:物防+65暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71599,"+4蟹将乙卡","XIEJIANGYI_KA",null,null,null,new ValueVO(65,0),null,9,0,0,4,10000,"物防+65暴击+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71601,"+1牛头小妖卡","NIUTOUXIAOYAO_KA",new ValueVO(80,0),new ValueVO(28,0),null,null,null,0,0,0,4,10000,"生命+80法力+28<br>荆棘岭找凌空子强化获得。<br>下一级:生命+100法力+36"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71602,"+2牛头小妖卡","NIUTOUXIAOYAO_KA",new ValueVO(100,0),new ValueVO(36,0),null,null,null,0,0,0,4,10000,"生命+100法力+36<br>荆棘岭找凌空子强化获得。<br>下一级:生命+120法力+44"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71603,"+3牛头小妖卡","NIUTOUXIAOYAO_KA",new ValueVO(120,0),new ValueVO(44,0),null,null,null,0,0,0,4,10000,"生命+120法力+44<br>荆棘岭找凌空子强化获得。<br>下一级:生命+140法力+52"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71604,"+4牛头小妖卡","NIUTOUXIAOYAO_KA",new ValueVO(140,0),new ValueVO(52,0),null,null,null,0,0,0,4,10000,"生命+140法力+52<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71606,"+1牛头老妖卡","NIUTOULAOYAO_KA",null,new ValueVO(28,0),null,null,null,0,0,6,4,10000,"法力+28幸运+6<br>荆棘岭找凌空子强化获得。<br>下一级:法力+36幸运+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71607,"+2牛头老妖卡","NIUTOULAOYAO_KA",null,new ValueVO(36,0),null,null,null,0,0,7,4,10000,"法力+36幸运+7<br>荆棘岭找凌空子强化获得。<br>下一级:法力+44幸运+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71608,"+3牛头老妖卡","NIUTOULAOYAO_KA",null,new ValueVO(44,0),null,null,null,0,0,8,4,10000,"法力+44幸运+8<br>荆棘岭找凌空子强化获得。<br>下一级:法力+42幸运+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71609,"+4牛头老妖卡","NIUTOULAOYAO_KA",null,new ValueVO(52,0),null,null,null,0,0,9,4,10000,"法力+52幸运+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71611,"+1邪武僧卡","XIEWUS_KA",null,null,new ValueVO(21,0),null,null,0,6,0,4,10000,"攻击+21闪避+6<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+27闪避+7"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71612,"+2邪武僧卡","XIEWUS_KA",null,null,new ValueVO(27,0),null,null,0,7,0,4,10000,"攻击+27闪避+7<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+33闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71613,"+3邪武僧卡","XIEWUS_KA",null,null,new ValueVO(33,0),null,null,0,8,0,4,10000,"攻击+33闪避+8<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+39闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71614,"+4邪武僧卡","XIEWUS_KA",null,null,new ValueVO(39,0),null,null,0,9,0,4,10000,"攻击+39闪避+9<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71616,"+1邪法僧卡","XIEFAS_KA",null,new ValueVO(28,0),null,null,new ValueVO(21,0),0,0,0,4,10000,"法力+28魔防+21<br>荆棘岭找凌空子强化获得。<br>下一级:法力+36魔防+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71617,"+2邪法僧卡","XIEFAS_KA",null,new ValueVO(36,0),null,null,new ValueVO(27,0),0,0,0,4,10000,"法力+36魔防+27<br>荆棘岭找凌空子强化获得。<br>下一级:法力+44魔防+33"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71618,"+3邪法僧卡","XIEFAS_KA",null,new ValueVO(44,0),null,null,new ValueVO(33,0),0,0,0,4,10000,"法力+44魔防+33<br>荆棘岭找凌空子强化获得。<br>下一级:法力+52魔防+39"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71619,"+4邪法僧卡","XIEFAS_KA",null,new ValueVO(52,0),null,null,new ValueVO(39,0),0,0,0,4,10000,"法力+52魔防+39<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71621,"+1绿蛇卡","LVS_KA",new ValueVO(80,0),null,null,new ValueVO(35,0),null,0,0,0,4,10000,"生命+80物防+35<br>荆棘岭找凌空子强化获得。<br>下一级:生命+100物防+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71622,"+2绿蛇卡","LVS_KA",new ValueVO(100,0),null,null,new ValueVO(45,0),null,0,0,0,4,10000,"生命+100物防+45<br>荆棘岭找凌空子强化获得。<br>下一级:生命+120物防+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71623,"+3绿蛇卡","LVS_KA",new ValueVO(120,0),null,null,new ValueVO(55,0),null,0,0,0,4,10000,"生命+120物防+55<br>荆棘岭找凌空子强化获得。<br>下一级:生命+140物防+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71624,"+4绿蛇卡","LVS_KA",new ValueVO(140,0),null,null,new ValueVO(65,0),null,0,0,0,4,10000,"生命+140物防+60<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71626,"+1红蛇卡","HONGSHE_KA",new ValueVO(80,0),null,null,null,new ValueVO(35,0),0,0,0,4,10000,"生命+80魔防+35<br>荆棘岭找凌空子强化获得。<br>下一级:生命+100魔防+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71627,"+2红蛇卡","HONGSHE_KA",new ValueVO(100,0),null,null,null,new ValueVO(45,0),0,0,0,4,10000,"生命+100魔防+45<br>荆棘岭找凌空子强化获得。<br>下一级:生命+120魔防+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71628,"+3红蛇卡","HONGSHE_KA",new ValueVO(120,0),null,null,null,new ValueVO(55,0),0,0,0,4,10000,"生命+120魔防+55<br>荆棘岭找凌空子强化获得。<br>下一级:生命+140魔防+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71629,"+4红蛇卡","HONGSHE_KA",new ValueVO(140,0),null,null,null,new ValueVO(65,0),0,0,0,4,10000,"生命+140魔防+60<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71631,"+1假面怪卡","JIAMIANGUAI_KA",new ValueVO(80,0),null,new ValueVO(14,0),null,null,0,0,0,4,10000,"生命+80攻击+14<br>荆棘岭找凌空子强化获得。<br>下一级:生命+100攻击+18"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71632,"+2假面怪卡","JIAMIANGUAI_KA",new ValueVO(100,0),null,new ValueVO(18,0),null,null,0,0,0,4,10000,"生命+100攻击+18<br>荆棘岭找凌空子强化获得。<br>下一级:生命+120攻击+22"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71633,"+3假面怪卡","JIAMIANGUAI_KA",new ValueVO(120,0),null,new ValueVO(22,0),null,null,0,0,0,4,10000,"生命+120攻击+22<br>荆棘岭找凌空子强化获得。<br>下一级:生命+140攻击+26"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71634,"+4假面怪卡","JIAMIANGUAI_KA",new ValueVO(140,0),null,new ValueVO(26,0),null,null,0,0,0,4,10000,"生命+140攻击+26<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71636,"+1绿蛛卡","LVZHU_KA",null,null,null,new ValueVO(35,0),null,7,0,0,4,10000,"物防+35暴击+7<br>荆棘岭找凌空子强化获得。<br>下一级:物防+45暴击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71637,"+2绿蛛卡","LVZHU_KA",null,null,null,new ValueVO(45,0),null,8,0,0,4,10000,"物防+45暴击+8<br>荆棘岭找凌空子强化获得。<br>下一级:物防+55暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71638,"+3绿蛛卡","LVZHU_KA",null,null,null,new ValueVO(55,0),null,9,0,0,4,10000,"物防+55暴击+9<br>荆棘岭找凌空子强化获得。<br>下一级:物防+65暴击+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71639,"+4绿蛛卡","LVZHU_KA",null,null,null,new ValueVO(65,0),null,10,0,0,4,10000,"物防+65暴击+10<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71641,"+1紫蛛卡","ZIZHU_KA",null,null,null,null,new ValueVO(35,0),0,7,0,4,10000,"魔防+35闪避+7<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+45闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71642,"+2紫蛛卡","ZIZHU_KA",null,null,null,null,new ValueVO(45,0),0,8,0,4,10000,"魔防+45闪避+8<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+55闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71643,"+3紫蛛卡","ZIZHU_KA",null,null,null,null,new ValueVO(55,0),0,9,0,4,10000,"魔防+55闪避+9<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+65闪避+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71644,"+4紫蛛卡","ZIZHU_KA",null,null,null,null,new ValueVO(65,0),0,10,0,4,10000,"魔防+65闪避+10<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71646,"+1黄蜈蚣卡","HUANGWUGONG_KA",null,new ValueVO(35,0),null,new ValueVO(35,0),null,0,0,0,4,10000,"法力+35物防+35<br>荆棘岭找凌空子强化获得。<br>下一级:法力+45物防+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71647,"+2黄蜈蚣卡","HUANGWUGONG_KA",null,new ValueVO(45,0),null,new ValueVO(45,0),null,0,0,0,4,10000,"法力+45物防+45<br>荆棘岭找凌空子强化获得。<br>下一级:法力+55物防+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71648,"+3黄蜈蚣卡","HUANGWUGONG_KA",null,new ValueVO(55,0),null,new ValueVO(55,0),null,0,0,0,4,10000,"法力+55物防+55<br>荆棘岭找凌空子强化获得。<br>下一级:法力+65物防+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71649,"+4黄蜈蚣卡","HUANGWUGONG_KA",null,new ValueVO(65,0),null,new ValueVO(65,0),null,0,0,0,4,10000,"法力+65物防+65<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71651,"+1蓝蜈蚣卡","LVWUGONG_KA",null,new ValueVO(35,0),null,null,new ValueVO(35,0),0,0,0,4,10000,"法力+35魔防+35<br>荆棘岭找凌空子强化获得。<br>下一级:法力+45魔防+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71652,"+2蓝蜈蚣卡","LVWUGONG_KA",null,new ValueVO(45,0),null,null,new ValueVO(45,0),0,0,0,4,10000,"法力+45魔防+45<br>荆棘岭找凌空子强化获得。<br>下一级:法力+55魔防+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71653,"+3蓝蜈蚣卡","LVWUGONG_KA",null,new ValueVO(55,0),null,null,new ValueVO(55,0),0,0,0,4,10000,"法力+55魔防+55<br>荆棘岭找凌空子强化获得。<br>下一级:法力+65魔防+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71654,"+4蓝蜈蚣卡","LVWUGONG_KA",null,new ValueVO(65,0),null,null,new ValueVO(65,0),0,0,0,4,10000,"法力+65魔防+65<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71656,"+1白象卡","BAIXIANG_KA",null,null,new ValueVO(28,0),null,null,7,0,0,4,10000,"攻击+28暴击+7<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+36暴击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71657,"+2白象卡","BAIXIANG_KA",null,null,new ValueVO(36,0),null,null,8,0,0,4,10000,"攻击+36暴击+8<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+44暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71658,"+3白象卡","BAIXIANG_KA",null,null,new ValueVO(44,0),null,null,9,0,0,4,10000,"攻击+44暴击+9<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+52暴击+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71659,"+4白象卡","BAIXIANG_KA",null,null,new ValueVO(52,0),null,null,10,0,0,4,10000,"攻击+52暴击+10<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71661,"+1狮子卡","SHIZI_KA",new ValueVO(100,0),null,null,new ValueVO(40,0),null,0,0,0,4,10000,"生命+100物防+40<br>荆棘岭找凌空子强化获得。<br>下一级:生命+120物防+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71662,"+2狮子卡","SHIZI_KA",new ValueVO(120,0),null,null,new ValueVO(50,0),null,0,0,0,4,10000,"生命+120物防+50<br>荆棘岭找凌空子强化获得。<br>下一级:生命+140物防+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71663,"+3狮子卡","SHIZI_KA",new ValueVO(140,0),null,null,new ValueVO(60,0),null,0,0,0,4,10000,"生命+140物防+60<br>荆棘岭找凌空子强化获得。<br>下一级:生命+160物防+70"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71664,"+4狮子卡","SHIZI_KA",new ValueVO(160,0),null,null,new ValueVO(70,0),null,0,0,0,4,10000,"生命+160物防+70<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71666,"+1白狐卡","BAIHULI_KA",new ValueVO(100,0),new ValueVO(35,0),null,null,null,0,0,0,4,10000,"生命+100法力+35<br>荆棘岭找凌空子强化获得。<br>下一级:生命+120法力+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71667,"+2白狐卡","BAIHULI_KA",new ValueVO(120,0),new ValueVO(45,0),null,null,null,0,0,0,4,10000,"生命+120法力+45<br>荆棘岭找凌空子强化获得。<br>下一级:生命+140法力+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71668,"+3白狐卡","BAIHULI_KA",new ValueVO(140,0),new ValueVO(55,0),null,null,null,0,0,0,4,10000,"生命+140法力+55<br>荆棘岭找凌空子强化获得。<br>下一级:生命+160法力+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71669,"+4白狐卡","BAIHULI_KA",new ValueVO(160,0),new ValueVO(65,0),null,null,null,0,0,0,4,10000,"生命+160法力+65<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71671,"+1小鹏弓手卡","XIAOPENGGONG_KA",null,null,new ValueVO(35,0),null,null,0,7,0,4,10000,"攻击+35闪避+7<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+45闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71672,"+2小鹏弓手卡","XIAOPENGGONG_KA",null,null,new ValueVO(45,0),null,null,0,8,0,4,10000,"攻击+45闪避+8<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+55闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71673,"+3小鹏弓手卡","XIAOPENGGONG_KA",null,null,new ValueVO(55,0),null,null,0,9,0,4,10000,"攻击+55闪避+9<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+65闪避+0"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71674,"+4小鹏弓手卡","XIAOPENGGONG_KA",null,null,new ValueVO(65,0),null,null,0,10,0,4,10000,"攻击+65闪避+10<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71676,"+1苍狼小妖卡","CANGLANG_KA",null,null,null,new ValueVO(40,0),new ValueVO(35,0),0,0,0,4,10000,"物防+40魔防+35<br>荆棘岭找凌空子强化获得。<br>下一级:物防+50魔防+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71677,"+2苍狼小妖卡","CANGLANG_KA",null,null,null,new ValueVO(50,0),new ValueVO(45,0),0,0,0,4,10000,"物防+50魔防+45<br>荆棘岭找凌空子强化获得。<br>下一级:物防+60魔防+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71678,"+3苍狼小妖卡","CANGLANG_KA",null,null,null,new ValueVO(60,0),new ValueVO(55,0),0,0,0,4,10000,"物防+60魔防+55<br>荆棘岭找凌空子强化获得。<br>下一级:物防+70魔防+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71679,"+4苍狼小妖卡","CANGLANG_KA",null,null,null,new ValueVO(70,0),new ValueVO(65,0),0,0,0,4,10000,"物防+70魔防+65<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71681,"+1豹精小妖卡","BAOJING_KA",new ValueVO(120,0),null,null,null,new ValueVO(35,0),0,0,0,4,10000,"生命+120魔防+35<br>荆棘岭找凌空子强化获得。<br>下一级:生命+120魔防+35"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71682,"+2豹精小妖卡","BAOJING_KA",new ValueVO(140,0),null,null,null,new ValueVO(45,0),0,0,0,4,10000,"生命+140魔防+45<br>荆棘岭找凌空子强化获得。<br>下一级:生命+160魔防+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71683,"+3豹精小妖卡","BAOJING_KA",new ValueVO(160,0),null,null,null,new ValueVO(55,0),0,0,0,4,10000,"生命+160魔防+55<br>荆棘岭找凌空子强化获得。<br>下一级:生命+180魔防+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71684,"+4豹精小妖卡","BAOJING_KA",new ValueVO(180,0),null,null,null,new ValueVO(65,0),0,0,0,4,10000,"生命+180魔防+65<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71686,"+1犀牛小妖卡","XINIU_KA",null,null,new ValueVO(35,0),new ValueVO(40,0),null,0,0,0,4,10000,"攻击+35物防+40<br>荆棘岭找凌空子强化获得。 <br>下一级:攻击+45物防+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71687,"+2犀牛小妖卡","XINIU_KA",null,null,new ValueVO(45,0),new ValueVO(50,0),null,0,0,0,4,10000,"攻击+45物防+50<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+55物防+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71688,"+3犀牛小妖卡","XINIU_KA",null,null,new ValueVO(55,0),new ValueVO(60,0),null,0,0,0,4,10000,"攻击+55物防+60<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+65物防+70"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71689,"+4犀牛小妖卡","XINIU_KA",null,null,new ValueVO(65,0),new ValueVO(70,0),null,0,0,0,4,10000,"攻击+65物防+70<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71691,"+1兔妖卡","TUYAO_KA",null,null,null,null,new ValueVO(40,0),7,0,0,4,10000,"魔防+40暴击+7<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+50暴击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71692,"+2兔妖卡","TUYAO_KA",null,null,null,null,new ValueVO(50,0),8,0,0,4,10000,"魔防+50暴击+8<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+60暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71693,"+3兔妖卡","TUYAO_KA",null,null,null,null,new ValueVO(60,0),9,0,0,4,10000,"魔防+60暴击+9<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+70暴击+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71694,"+4兔妖卡","TUYAO_KA",null,null,null,null,new ValueVO(70,0),10,0,0,4,10000,"魔防+70暴击+10<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71696,"+1武僧卡","WUS_KA",new ValueVO(120,0),null,new ValueVO(35,0),null,null,0,0,0,4,10000,"生命+120攻击+35<br>荆棘岭找凌空子强化获得。<br>下一级:生命+120攻击+35"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71697,"+2武僧卡","WUS_KA",new ValueVO(140,0),null,new ValueVO(45,0),null,null,0,0,0,4,10000,"生命+140攻击+45<br>荆棘岭找凌空子强化获得。<br>下一级:生命+160攻击+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71698,"+3武僧卡","WUS_KA",new ValueVO(160,0),null,new ValueVO(55,0),null,null,0,0,0,4,10000,"生命+160攻击+55<br>荆棘岭找凌空子强化获得。<br>下一级:生命+180攻击+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(71699,"+4武僧卡","WUS_KA",new ValueVO(180,0),null,new ValueVO(65,0),null,null,0,0,0,4,10000,"生命+180攻击+65<br>荆棘岭找凌空子强化获得。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70001,"寅将军卡","YANJIANGJUN_KA",new ValueVO(50,0),null,null,null,null,0,0,0,0,10000,"生命+50<br>掉落：双叉岭<br>下一级:生命+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70002,"寅将军卡","YANJIANGJUN_KA",new ValueVO(100,0),null,null,null,null,0,0,0,1,15000,"生命+100<br>荆棘岭找凌空子合成获得。<br>下一级:生命+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70003,"寅将军卡","YANJIANGJUN_KA",new ValueVO(150,0),null,null,null,null,0,0,0,2,20000,"生命+150<br>荆棘岭找凌空子合成获得。<br>下一级:生命+200"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70004,"寅将军卡","YANJIANGJUN_KA",new ValueVO(200,0),null,null,null,null,0,0,0,3,25000,"生命+200<br>荆棘岭找凌空子合成获得。<br>下一级:生命+250"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70005,"寅将军卡","YANJIANGJUN_KA",new ValueVO(250,0),null,null,null,null,0,0,0,4,30000,"生命+250<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70006,"黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(25,0),null,null,null,0,0,0,0,10000,"法力+25<br>掉落：黑风洞<br>下一级:法力+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70007,"黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(50,0),null,null,null,0,0,0,1,15000,"法力+50<br>荆棘岭找凌空子合成获得。<br>下一级:法力+75"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70008,"黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(75,0),null,null,null,0,0,0,2,20000,"法力+75<br>荆棘岭找凌空子合成获得。<br>下一级:法力+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70009,"黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(100,0),null,null,null,0,0,0,3,25000,"法力+100<br>荆棘岭找凌空子合成获得。<br>下一级:法力+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70010,"黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(125,0),null,null,null,0,0,0,4,30000,"法力+125<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70011,"黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(20,0),null,0,0,0,0,10000,"物防+20<br>掉落：黄风洞<br>下一级:物防+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70012,"黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(40,0),null,0,0,0,1,15000,"物防+40<br>荆棘岭找凌空子合成获得。<br>下一级:物防+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70013,"黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(60,0),null,0,0,0,2,20000,"物防+60<br>荆棘岭找凌空子合成获得。<br>下一级:物防+80"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70014,"黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(80,0),null,0,0,0,3,25000,"物防+80<br>荆棘岭找凌空子合成获得。<br>下一级:物防+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70015,"黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(100,0),null,0,0,0,4,30000,"物防+100<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70016,"白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(20,0),0,0,0,0,10000,"魔防+20<br>掉落：白虎岭<br>下一级:魔防+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70017,"白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(40,0),0,0,0,1,15000,"魔防+40<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70018,"白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(60,0),0,0,0,2,20000,"魔防+60<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+80"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70019,"白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(80,0),0,0,0,3,25000,"魔防+80<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70020,"白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(100,0),0,0,0,4,30000,"魔防+100<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70021,"黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(50,0),null,null,null,null,0,0,2,0,10000,"生命+50幸运+2<br>掉落：黑松林<br>下一级:生命+100幸运+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70022,"黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(100,0),null,null,null,null,0,0,4,1,15000,"生命+100幸运+4<br>荆棘岭找凌空子合成获得。<br>下一级:生命+150幸运+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70023,"黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(150,0),null,null,null,null,0,0,6,2,20000,"生命+150幸运+6<br>荆棘岭找凌空子合成获得。<br>下一级:生命+200幸运+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70024,"黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(200,0),null,null,null,null,0,0,8,3,25000,"生命+200幸运+8<br>荆棘岭找凌空子合成获得。<br>下一级:生命+250幸运+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70025,"黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(250,0),null,null,null,null,0,0,10,4,30000,"生命+250幸运+10<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70026,"银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(20,0),null,3,0,0,0,10000,"物防+20暴击+3<br>掉落：平顶山<br>下一级:物防+40暴击+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70027,"银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(40,0),null,6,0,0,1,15000,"物防+40暴击+6<br>荆棘岭找凌空子合成获得。<br>下一级:物防+60暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70028,"银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(60,0),null,9,0,0,2,20000,"物防+60暴击+9<br>荆棘岭找凌空子合成获得。<br>下一级:物防+80暴击+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70029,"银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(80,0),null,12,0,0,3,25000,"物防+80暴击+12<br>荆棘岭找凌空子合成获得。<br>下一级:物防+100暴击+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70030,"银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(100,0),null,15,0,0,4,30000,"物防+100暴击+15<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70031,"金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(20,0),0,3,0,0,10000,"魔防+20闪避+3<br>掉落：平顶山<br>下一级:魔防+40闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70032,"金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(40,0),0,6,0,1,15000,"魔防+40闪避+6<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+60闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70033,"金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(60,0),0,9,0,2,20000,"魔防+60闪避+9<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+80闪避+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70034,"金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(80,0),0,12,0,3,25000,"魔防+80闪避+12<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+100闪避+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70035,"金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(100,0),0,15,0,4,30000,"魔防+100闪避+15<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70036,"九尾狐卡","JIUWEIHU_KA",new ValueVO(50,0),new ValueVO(20,0),null,null,null,0,0,0,0,10000,"生命+50法力+20<br>掉落：平顶山<br>下一级:生命+100法力+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70037,"九尾狐卡","JIUWEIHU_KA",new ValueVO(100,0),new ValueVO(40,0),null,null,null,0,0,0,1,15000,"生命+100法力+40<br>荆棘岭找凌空子合成获得。<br>下一级:生命+150法力+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70038,"九尾狐卡","JIUWEIHU_KA",new ValueVO(150,0),new ValueVO(60,0),null,null,null,0,0,0,2,20000,"生命+150法力+60<br>荆棘岭找凌空子合成获得。<br>下一级:生命+200法力+80"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70039,"九尾狐卡","JIUWEIHU_KA",new ValueVO(200,0),new ValueVO(80,0),null,null,null,0,0,0,3,25000,"生命+200法力+80<br>荆棘岭找凌空子合成获得。<br>下一级:生命+250法力+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70040,"九尾狐卡","JIUWEIHU_KA",new ValueVO(250,0),new ValueVO(100,0),null,null,null,0,0,0,4,30000,"生命+250法力+100<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70041,"狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(25,0),null,null,0,3,0,0,10000,"攻击+25闪避+3<br>掉落：乌鸡国<br>下一级:攻击+50闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70042,"狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(50,0),null,null,0,6,0,1,15000,"攻击+50闪避+6<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+75闪避+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70043,"狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(75,0),null,null,0,9,0,2,20000,"攻击+75闪避+9<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+100闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70044,"狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(100,0),null,null,0,12,0,3,25000,"攻击+100闪避+12<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+125闪避+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70045,"狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(125,0),null,null,0,15,0,4,30000,"攻击+125闪避+15<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70046,"红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(25,0),null,null,3,0,0,0,10000,"攻击+25暴击+3<br>掉落：火云洞<br>下一级: 攻击+50暴击+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70047,"红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(50,0),null,null,6,0,0,1,15000,"攻击+50暴击+6<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+75暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70048,"红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(75,0),null,null,9,0,0,2,20000,"攻击+75暴击+9<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+100暴击+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70049,"红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(100,0),null,null,12,0,0,3,25000,"攻击+100暴击+12<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+125暴击+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70050,"红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(125,0),null,null,15,0,0,4,30000,"攻击+125暴击+15<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70051,"鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(20,0),new ValueVO(20,0),0,0,0,0,10000,"物防+20魔防+20<br>掉落：黑水河 <br>下一级:物防+40魔防+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70052,"鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(40,0),new ValueVO(40,0),0,0,0,1,15000,"物防+40魔防+40<br>荆棘岭找凌空子合成获得。<br>下一级:物防+60魔防+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70053,"鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(60,0),new ValueVO(60,0),0,0,0,2,20000,"物防+60魔防+60<br>荆棘岭找凌空子合成获得。<br>下一级:物防+80魔防+80"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70054,"鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(80,0),new ValueVO(80,0),0,0,0,3,25000,"物防+80魔防+80<br>荆棘岭找凌空子合成获得。<br>下一级:物防+100魔防+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70055,"鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(100,0),new ValueVO(100,0),0,0,0,4,30000,"物防+100魔防+100<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70056,"虎力大仙卡","HULIDAXIAN_KA",new ValueVO(60,0),new ValueVO(25,0),null,null,null,0,0,0,0,10000,"生命+60法力+25<br>掉落：车迟国<br>下一级:生命+120法力+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70057,"虎力大仙卡","HULIDAXIAN_KA",new ValueVO(120,0),new ValueVO(50,0),null,null,null,0,0,0,1,15000,"生命+120法力+50<br>荆棘岭找凌空子合成获得。<br>下一级:生命+180法力+75"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70058,"虎力大仙卡","HULIDAXIAN_KA",new ValueVO(180,0),new ValueVO(75,0),null,null,null,0,0,0,2,20000,"生命+180法力+75<br>荆棘岭找凌空子合成获得。<br>下一级:生命+240法力+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70059,"虎力大仙卡","HULIDAXIAN_KA",new ValueVO(240,0),new ValueVO(100,0),null,null,null,0,0,0,3,25000,"生命+240法力+100<br>荆棘岭找凌空子合成获得。<br>下一级:生命+300法力+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70060,"虎力大仙卡","HULIDAXIAN_KA",new ValueVO(300,0),new ValueVO(125,0),null,null,null,0,0,0,4,30000,"生命+300法力+125<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70061,"鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(60,0),null,null,new ValueVO(25,0),null,0,0,0,0,10000,"生命+60物防+25<br>掉落：车迟国<br>下一级:生命+120物防+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70062,"鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(120,0),null,null,new ValueVO(50,0),null,0,0,0,1,15000,"生命+120物防+50<br>荆棘岭找凌空子合成获得。<br>下一级:生命+180物防+75"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70063,"鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(180,0),null,null,new ValueVO(75,0),null,0,0,0,2,20000,"生命+180物防+75<br>荆棘岭找凌空子合成获得。<br>下一级:生命+240物防+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70064,"鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(240,0),null,null,new ValueVO(100,0),null,0,0,0,3,25000,"生命+240物防+100<br>荆棘岭找凌空子合成获得。<br>下一级:生命+300物防+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70065,"鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(300,0),null,null,new ValueVO(125,0),null,0,0,0,4,30000,"生命+300物防+125<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70066,"羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(60,0),null,null,null,new ValueVO(25,0),0,0,0,0,10000,"生命+60魔防+25<br>掉落：车迟国<br>下一级:生命+120魔防+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70067,"羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(120,0),null,null,null,new ValueVO(50,0),0,0,0,1,15000,"生命+120魔防+50<br>荆棘岭找凌空子合成获得。<br>下一级:生命+180魔防+75"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70068,"羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(180,0),null,null,null,new ValueVO(75,0),0,0,0,2,20000,"生命+180魔防+75<br>荆棘岭找凌空子合成获得。<br>下一级:生命+240魔防+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70069,"羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(240,0),null,null,null,new ValueVO(100,0),0,0,0,3,25000,"生命+240魔防+100<br>荆棘岭找凌空子合成获得。<br>下一级:生命+300魔防+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70070,"羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(300,0),null,null,null,new ValueVO(125,0),0,0,0,4,30000,"生命+300魔防+125<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70071,"妖魂王卡","YAOHUNWANG_KA",new ValueVO(60,0),null,new ValueVO(25,0),null,null,0,0,0,0,10000,"生命+60攻击+25<br>掉落：车迟国<br>下一级:生命+120攻击+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70072,"妖魂王卡","YAOHUNWANG_KA",new ValueVO(120,0),null,new ValueVO(50,0),null,null,0,0,0,1,15000,"生命+120攻击+50<br>荆棘岭找凌空子合成获得。<br>下一级:生命+180攻击+75"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70073,"妖魂王卡","YAOHUNWANG_KA",new ValueVO(180,0),null,new ValueVO(75,0),null,null,0,0,0,2,20000,"生命+180攻击+75<br>荆棘岭找凌空子合成获得。<br>下一级:生命+240攻击+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70074,"妖魂王卡","YAOHUNWANG_KA",new ValueVO(240,0),null,new ValueVO(100,0),null,null,0,0,0,3,25000,"生命+240攻击+100<br>荆棘岭找凌空子合成获得。<br>下一级:生命+300攻击+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70075,"妖魂王卡","YAOHUNWANG_KA",new ValueVO(300,0),null,new ValueVO(125,0),null,null,0,0,0,4,30000,"生命+300攻击+125<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70076,"灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(30,0),null,null,null,3,0,0,0,10000,"法力+30暴击+3<br>掉落：通天河<br>下一级:法力+60暴击+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70077,"灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(60,0),null,null,null,6,0,0,1,15000,"法力+60暴击+6<br>荆棘岭找凌空子合成获得。<br>下一级:法力+90暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70078,"灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(90,0),null,null,null,9,0,0,2,20000,"法力+90暴击+9<br>荆棘岭找凌空子合成获得。<br>下一级:法力+120暴击+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70079,"灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(120,0),null,null,null,12,0,0,3,25000,"法力+120暴击+12<br>荆棘岭找凌空子合成获得。<br>下一级:法力+150暴击+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70080,"灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(150,0),null,null,null,15,0,0,4,30000,"法力+150暴击+15<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70081,"独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(30,0),null,null,0,0,2,0,10000,"攻击+30幸运+2<br>掉落：金兜山<br>下一级:攻击+60幸运+4"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70082,"独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(60,0),null,null,0,0,4,1,15000,"攻击+60幸运+4<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+90幸运+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70083,"独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(90,0),null,null,0,0,6,2,20000,"攻击+90幸运+6<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+120幸运+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70084,"独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(120,0),null,null,0,0,8,3,25000,"攻击+120幸运+8<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+150幸运+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70085,"独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(150,0),null,null,0,0,10,4,30000,"攻击+150幸运+10<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70086,"蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(30,0),null,0,3,0,0,10000,"物防+30闪避+3<br>掉落：琵琶洞<br>下一级:物防+60闪避+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70087,"蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(60,0),null,0,6,0,1,15000,"物防+60闪避+6<br>荆棘岭找凌空子合成获得。<br>下一级:物防+90闪避+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70088,"蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(90,0),null,0,9,0,2,20000,"物防+90闪避+9<br>荆棘岭找凌空子合成获得。<br>下一级:物防+120闪避+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70089,"蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(120,0),null,0,12,0,3,25000,"物防+120闪避+12<br>荆棘岭找凌空子合成获得。<br>下一级:物防+150闪避+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70090,"蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(150,0),null,0,15,0,4,30000,"物防+150闪避+15<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70091,"铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(80,0),null,null,null,new ValueVO(30,0),0,0,0,0,10000,"生命+80魔防+30<br>掉落：火焰山<br>下一级:生命+160魔防+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70092,"铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(160,0),null,null,null,new ValueVO(60,0),0,0,0,1,15000,"生命+160魔防+60<br>荆棘岭找凌空子合成获得。<br>下一级:生命+240魔防+90"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70093,"铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(240,0),null,null,null,new ValueVO(90,0),0,0,0,2,20000,"生命+240魔防+90<br>荆棘岭找凌空子合成获得。<br>下一级:生命+320魔防+120"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70094,"铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(320,0),null,null,null,new ValueVO(120,0),0,0,0,3,25000,"生命+320魔防+120<br>荆棘岭找凌空子合成获得。<br>下一级:生命+400魔防+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70095,"铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(400,0),null,null,null,new ValueVO(150,0),0,0,0,4,30000,"生命+400魔防+150<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70096,"牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(40,0),null,null,3,0,0,0,10000,"攻击+40暴击+3<br>掉落：火焰山<br>下一级:攻击+80暴击+6"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70097,"牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(80,0),null,null,6,0,0,1,15000,"攻击+80暴击+6<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+120暴击+9"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70098,"牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(120,0),null,null,9,0,0,2,20000,"攻击+120暴击+9<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+160暴击+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70099,"牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(160,0),null,null,12,0,0,3,25000,"攻击+160暴击+12<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+200暴击+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70100,"牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(200,0),null,null,15,0,0,4,30000,"攻击+200暴击+15<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70101,"九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(25,0),new ValueVO(25,0),0,0,0,0,10000,"物防+25魔防+25<br>掉落：乱石山<br>下一级:物防+50魔防+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70102,"九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(50,0),new ValueVO(50,0),0,0,0,1,15000,"物防+50魔防+50<br>荆棘岭找凌空子合成获得。<br>下一级:物防+75魔防+75"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70103,"九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(75,0),new ValueVO(75,0),0,0,0,2,20000,"物防+75魔防+75<br>荆棘岭找凌空子合成获得。<br>下一级:物防+100魔防+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70104,"九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(100,0),new ValueVO(100,0),0,0,0,3,25000,"物防+100魔防+100<br>荆棘岭找凌空子合成获得。<br>下一级:物防+125魔防+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70105,"九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(125,0),new ValueVO(125,0),0,0,0,4,30000,"物防+125魔防+125<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70106,"黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,4,4,0,0,10000,"暴击+4闪避+4<br>掉落：小雷音寺<br>下一级:暴击+8闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70107,"黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,8,8,0,1,15000,"暴击+8闪避+8<br>荆棘岭找凌空子合成获得。<br>下一级:暴击+12闪避+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70108,"黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,12,12,0,2,20000,"暴击+12闪避+12<br>荆棘岭找凌空子合成获得。<br>下一级:暴击+16闪避+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70109,"黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,16,16,0,3,25000,"暴击+16闪避+16<br>荆棘岭找凌空子合成获得。<br>下一级:暴击+20闪避+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70110,"黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,20,20,0,4,30000,"暴击+20闪避+20<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70111,"蟒蛇精卡","MENGSHEJ_KA",new ValueVO(80,0),null,null,new ValueVO(30,0),null,0,0,0,0,10000,"生命+80物防+30<br>掉落：七绝山<br>下一级:生命+160物防+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70112,"蟒蛇精卡","MENGSHEJ_KA",new ValueVO(160,0),null,null,new ValueVO(60,0),null,0,0,0,1,15000,"生命+160物防+60<br>荆棘岭找凌空子合成获得。<br>下一级:生命+240物防+90"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70113,"蟒蛇精卡","MENGSHEJ_KA",new ValueVO(240,0),null,null,new ValueVO(90,0),null,0,0,0,2,20000,"生命+240物防+90<br>荆棘岭找凌空子合成获得。<br>下一级:生命+320物防+120"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70114,"蟒蛇精卡","MENGSHEJ_KA",new ValueVO(320,0),null,null,new ValueVO(120,0),null,0,0,0,3,25000,"生命+320物防+120<br>荆棘岭找凌空子合成获得。<br>下一级:生命+400物防+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70115,"蟒蛇精卡","MENGSHEJ_KA",new ValueVO(400,0),null,null,new ValueVO(150,0),null,0,0,0,4,30000,"生命+400物防+150<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70116,"赛太岁卡","SAITAISUI_KA",null,new ValueVO(25,0),new ValueVO(35,0),null,null,0,0,0,0,10000,"攻击+35法力+25<br>掉落：麒麟山<br>下一级:攻击+70法力+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70117,"赛太岁卡","SAITAISUI_KA",null,new ValueVO(50,0),new ValueVO(70,0),null,null,0,0,0,1,15000,"攻击+70法力+50<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+105法力+75"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70118,"赛太岁卡","SAITAISUI_KA",null,new ValueVO(75,0),new ValueVO(105,0),null,null,0,0,0,2,20000,"攻击+105法力+75<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+140法力+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70119,"赛太岁卡","SAITAISUI_KA",null,new ValueVO(100,0),new ValueVO(140,0),null,null,0,0,0,3,25000,"攻击+140法力+100<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+175法力+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70120,"赛太岁卡","SAITAISUI_KA",null,new ValueVO(125,0),new ValueVO(175,0),null,null,0,0,0,4,30000,"攻击+175法力+125<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70121,"蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(25,0),null,null,new ValueVO(30,0),0,0,0,0,10000,"法力+25魔防+30<br>掉落：麒麟山<br>下一级:法力+50魔防+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70122,"蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(50,0),null,null,new ValueVO(60,0),0,0,0,1,15000,"法力+50魔防+60<br>荆棘岭找凌空子合成获得。<br>下一级:法力+75魔防+90"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70123,"蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(75,0),null,null,new ValueVO(90,0),0,0,0,2,20000,"法力+75魔防+90<br>荆棘岭找凌空子合成获得。<br>下一级:法力+100魔防+120"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70124,"蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(100,0),null,null,new ValueVO(120,0),0,0,0,3,25000,"法力+100魔防+120<br>荆棘岭找凌空子合成获得。<br>下一级:法力+125魔防+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70125,"蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(125,0),null,null,new ValueVO(150,0),0,0,0,4,30000,"法力+125魔防+150<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70126,"蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(40,0),new ValueVO(25,0),null,0,0,0,0,10000,"攻击+40物防+25<br>掉落：黄花观<br>下一级:攻击+80物防+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70127,"蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(80,0),new ValueVO(50,0),null,0,0,0,1,15000,"攻击+80物防+80<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+120物防+75"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70128,"蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(120,0),new ValueVO(75,0),null,0,0,0,2,20000,"攻击+120物防+75<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+160物防+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70129,"蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(160,0),new ValueVO(100,0),null,0,0,0,3,25000,"攻击+160物防+100<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+200物防+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70130,"蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(200,0),new ValueVO(125,0),null,0,0,0,4,30000,"攻击+200物防+125<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70131,"狮子精卡","SHIZIJING_KA",new ValueVO(80,0),null,null,null,null,4,0,0,0,10000,"生命+80暴击+4<br>掉落：狮驼岭<br>下一级:生命+160暴击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70132,"狮子精卡","SHIZIJING_KA",new ValueVO(160,0),null,null,null,null,8,0,0,1,15000,"生命+160暴击+8<br>荆棘岭找凌空子合成获得。<br>下一级:生命+240暴击+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70133,"狮子精卡","SHIZIJING_KA",new ValueVO(240,0),null,null,null,null,12,0,0,2,20000,"生命+240暴击+12<br>荆棘岭找凌空子合成获得。<br>下一级:生命+320暴击+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70134,"狮子精卡","SHIZIJING_KA",new ValueVO(320,0),null,null,null,null,16,0,0,3,25000,"生命+320暴击+16<br>荆棘岭找凌空子合成获得。<br>下一级:生命+400暴击+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70135,"狮子精卡","SHIZIJING_KA",new ValueVO(400,0),null,null,null,null,20,0,0,4,30000,"生命+400暴击+20<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70136,"白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(30,0),new ValueVO(30,0),0,0,0,0,10000,"物防+30魔防+30<br>掉落：狮驼岭<br>下一级:物防+60魔防+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70137,"白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(60,0),new ValueVO(60,0),0,0,0,1,15000,"物防+60魔防+60<br>荆棘岭找凌空子合成获得。<br>下一级:物防+90魔防+90"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70138,"白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(90,0),new ValueVO(90,0),0,0,0,2,20000,"物防+90魔防+90<br>荆棘岭找凌空子合成获得。<br>下一级:物防+120魔防+120"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70139,"白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(120,0),new ValueVO(120,0),0,0,0,3,25000,"物防+120魔防+120<br>荆棘岭找凌空子合成获得。<br>下一级:物防+150魔防+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70140,"白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(150,0),new ValueVO(150,0),0,0,0,4,30000,"物防+150魔防+150<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70141,"大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(40,0),null,new ValueVO(25,0),0,0,0,0,10000,"攻击+40魔防+25<br>掉落：狮驼岭<br>下一级:攻击+80魔防+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70142,"大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(80,0),null,new ValueVO(50,0),0,0,0,1,15000,"攻击+80魔防+80<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+120魔防+75"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70143,"大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(120,0),null,new ValueVO(75,0),0,0,0,2,20000,"攻击+120魔防+75<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+160魔防+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70144,"大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(160,0),null,new ValueVO(100,0),0,0,0,3,25000,"攻击+160魔防+100<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+200魔防+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70145,"大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(200,0),null,new ValueVO(125,0),0,0,0,4,30000,"攻击+200魔防+125<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70146,"白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(60,0),4,0,0,0,10000,"魔防+60暴击+4<br>掉落：清华洞<br>下一级:魔防+60暴击+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70147,"白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(90,0),8,0,0,1,15000,"魔防+90暴击+8<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+120暴击+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70148,"白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(120,0),12,0,0,2,20000,"魔防+120暴击+12<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+150暴击+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70149,"白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(150,0),16,0,0,3,25000,"魔防+150暴击+16<br>荆棘岭找凌空子合成获得。<br>下一级:魔防+180暴击+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70150,"白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(180,0),20,0,0,4,30000,"魔防+180暴击+20<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70151,"地涌夫人卡","DIYONG_KA",new ValueVO(120,0),new ValueVO(75,0),null,null,null,0,0,0,0,10000,"生命+120法力+75<br>掉落：陷空山<br>下一级:生命+240法力+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70152,"地涌夫人卡","DIYONG_KA",new ValueVO(240,0),new ValueVO(100,0),null,null,null,0,0,0,1,15000,"生命+240法力+100<br>荆棘岭找凌空子合成获得。<br>下一级:生命+360法力+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70153,"地涌夫人卡","DIYONG_KA",new ValueVO(360,0),new ValueVO(125,0),null,null,null,0,0,0,2,20000,"生命+360法力+125<br>荆棘岭找凌空子合成获得。<br>下一级:生命+480法力+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70154,"地涌夫人卡","DIYONG_KA",new ValueVO(480,0),new ValueVO(150,0),null,null,null,0,0,0,3,25000,"生命+480法力+150<br>荆棘岭找凌空子合成获得。<br>下一级:生命+600法力+175"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70155,"地涌夫人卡","DIYONG_KA",new ValueVO(600,0),new ValueVO(175,0),null,null,null,0,0,0,4,30000,"生命+600法力+175<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70156,"豹子精卡","BAOZIJING_KA",null,null,new ValueVO(40,0),new ValueVO(60,0),null,0,0,0,0,10000,"攻击+40物防+60<br>掉落：黄花观<br>下一级:攻击+80物防+90"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70157,"豹子精卡","BAOZIJING_KA",null,null,new ValueVO(80,0),new ValueVO(90,0),null,0,0,0,1,15000,"攻击+80物防+90<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+120物防+120"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70158,"豹子精卡","BAOZIJING_KA",null,null,new ValueVO(120,0),new ValueVO(120,0),null,0,0,0,2,20000,"攻击+120物防+120<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+160物防+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70159,"豹子精卡","BAOZIJING_KA",null,null,new ValueVO(160,0),new ValueVO(150,0),null,0,0,0,3,25000,"攻击+160物防+150<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+200物防+180"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70160,"豹子精卡","BAOZIJING_KA",null,null,new ValueVO(200,0),new ValueVO(180,0),null,0,0,0,4,30000,"攻击+200物防+180<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70161,"黄狮精卡","HUANGSHIJING_KA",new ValueVO(120,0),null,new ValueVO(40,0),null,null,0,0,0,0,10000,"生命+120攻击+40<br>掉落：豹头山<br>下一级:生命+240攻击+80"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70162,"黄狮精卡","HUANGSHIJING_KA",new ValueVO(240,0),null,new ValueVO(80,0),null,null,0,0,0,1,15000,"生命+240攻击+80<br>荆棘岭找凌空子合成获得。<br>下一级:生命+360攻击+120"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70163,"黄狮精卡","HUANGSHIJING_KA",new ValueVO(360,0),null,new ValueVO(120,0),null,null,0,0,0,2,20000,"生命+360攻击+120<br>荆棘岭找凌空子合成获得。<br>下一级:生命+480攻击+160"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70164,"黄狮精卡","HUANGSHIJING_KA",new ValueVO(480,0),null,new ValueVO(160,0),null,null,0,0,0,3,25000,"生命+480攻击+160<br>荆棘岭找凌空子合成获得。<br>下一级:生命+600攻击+200"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70165,"黄狮精卡","HUANGSHIJING_KA",new ValueVO(600,0),null,new ValueVO(200,0),null,null,0,0,0,4,30000,"生命+600攻击+200<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70166,"九灵元圣卡","JIULING_KA",null,new ValueVO(75,0),new ValueVO(40,0),null,null,0,0,0,0,10000,"攻击+40法力+75<br>掉落：竹节山<br>下一级:攻击+80法力+100"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70167,"九灵元圣卡","JIULING_KA",null,new ValueVO(100,0),new ValueVO(80,0),null,null,0,0,0,1,15000,"攻击+80法力+100<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+120法力+125"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70168,"九灵元圣卡","JIULING_KA",null,new ValueVO(125,0),new ValueVO(120,0),null,null,0,0,0,2,20000,"攻击+120法力+125<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+160法力+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70169,"九灵元圣卡","JIULING_KA",null,new ValueVO(150,0),new ValueVO(160,0),null,null,0,0,0,3,25000,"攻击+160法力+150<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+200法力+175"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70170,"九灵元圣卡","JIULING_KA",null,new ValueVO(175,0),new ValueVO(200,0),null,null,0,0,0,4,30000,"攻击+200法力+175<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70171,"辟暑王卡","PISHUDAWANG_KA",new ValueVO(360,0),null,null,null,new ValueVO(90,0),0,0,0,0,10000,"生命+360魔防+90<br>掉落：青龙山<br>下一级:生命+480魔防+120"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70172,"辟暑王卡","PISHUDAWANG_KA",new ValueVO(480,0),null,null,null,new ValueVO(120,0),0,0,0,1,15000,"生命+480魔防+120<br>荆棘岭找凌空子合成获得。<br>下一级:生命+600魔防+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70173,"辟暑王卡","PISHUDAWANG_KA",new ValueVO(600,0),null,null,null,new ValueVO(150,0),0,0,0,2,20000,"生命+600魔防+150<br>荆棘岭找凌空子合成获得。<br>下一级:生命+720魔防+180"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70174,"辟暑王卡","PISHUDAWANG_KA",new ValueVO(720,0),null,null,null,new ValueVO(180,0),0,0,0,3,25000,"生命+720魔防+180<br>荆棘岭找凌空子合成获得。<br>下一级:生命+840魔防+210"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70175,"辟暑王卡","PISHUDAWANG_KA",new ValueVO(840,0),null,null,null,new ValueVO(210,0),0,0,0,4,30000,"生命+840魔防+210<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70176,"辟寒王卡","PIHANDAWANG_KA",new ValueVO(360,0),null,null,null,null,5,0,0,0,10000,"生命+360暴击+5<br>掉落：青龙山<br>下一级:生命+480暴击+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70177,"辟寒王卡","PIHANDAWANG_KA",new ValueVO(480,0),null,null,null,null,10,0,0,1,15000,"生命+480暴击+10<br>荆棘岭找凌空子合成获得。<br>下一级:生命+600暴击+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70178,"辟寒王卡","PIHANDAWANG_KA",new ValueVO(600,0),null,null,null,null,15,0,0,2,20000,"生命+600暴击+15<br>荆棘岭找凌空子合成获得。<br>下一级:生命+720暴击+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70179,"辟寒王卡","PIHANDAWANG_KA",new ValueVO(720,0),null,null,null,null,20,0,0,3,25000,"生命+720暴击+20<br>荆棘岭找凌空子合成获得。<br>下一级:生命+840暴击+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70180,"辟寒王卡","PIHANDAWANG_KA",new ValueVO(840,0),null,null,null,null,25,0,0,4,30000,"生命+840暴击+25<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70181,"辟尘王卡","PICHENDAWANG_KA",new ValueVO(360,0),null,null,new ValueVO(90,0),null,0,0,0,0,10000,"生命+360物防+90<br>掉落：青龙山<br>下一级:生命+480物防+120"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70182,"辟尘王卡","PICHENDAWANG_KA",new ValueVO(480,0),null,null,new ValueVO(120,0),null,0,0,0,1,15000,"生命+480物防+120<br>荆棘岭找凌空子合成获得。<br>下一级:生命+600物防+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70183,"辟尘王卡","PICHENDAWANG_KA",new ValueVO(600,0),null,null,new ValueVO(150,0),null,0,0,0,2,20000,"生命+600物防+150<br>荆棘岭找凌空子合成获得。<br>下一级:生命+720物防+180"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70184,"辟尘王卡","PICHENDAWANG_KA",new ValueVO(720,0),null,null,new ValueVO(180,0),null,0,0,0,3,25000,"生命+720物防+180<br>荆棘岭找凌空子合成获得。<br>下一级:生命+840物防+210"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70185,"辟尘王卡","PICHENDAWANG_KA",new ValueVO(840,0),null,null,new ValueVO(210,0),null,0,0,0,4,30000,"生命+840物防+210<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70186,"玉兔精卡","YUTUJING_KA",null,new ValueVO(75,0),null,null,new ValueVO(90,0),0,0,0,0,10000,"法力+75魔防+90<br>掉落：布金禅寺<br>下一级:法力+100魔防+120"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70187,"玉兔精卡","YUTUJING_KA",null,new ValueVO(100,0),null,null,new ValueVO(120,0),0,0,0,1,15000,"法力+100魔防+120<br>荆棘岭找凌空子合成获得。<br>下一级:法力+125魔防+150"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70188,"玉兔精卡","YUTUJING_KA",null,new ValueVO(125,0),null,null,new ValueVO(150,0),0,0,0,2,20000,"法力+125魔防+150<br>荆棘岭找凌空子合成获得。<br>下一级:法力+150魔防+180"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70189,"玉兔精卡","YUTUJING_KA",null,new ValueVO(150,0),null,null,new ValueVO(180,0),0,0,0,3,25000,"法力+150魔防+180<br>荆棘岭找凌空子合成获得。<br>下一级:法力+175魔防+210"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70190,"玉兔精卡","YUTUJING_KA",null,new ValueVO(175,0),null,null,new ValueVO(210,0),0,0,0,4,30000,"法力+175魔防+210<br>可去荆棘岭找凌空子强化卡片"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70191,"阿傩卡","ANAN_KA",null,null,new ValueVO(40,0),null,null,0,4,0,0,10000,"攻击+40闪避+4<br>掉落：大雷音寺<br>下一级:攻击+80闪避+8"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70192,"阿傩卡","ANAN_KA",null,null,new ValueVO(80,0),null,null,0,8,0,1,15000,"攻击+80闪避+8<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+120闪避+12"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70193,"阿傩卡","ANAN_KA",null,null,new ValueVO(120,0),null,null,0,12,0,2,20000,"攻击+120闪避+12<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+160闪避+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70194,"阿傩卡","ANAN_KA",null,null,new ValueVO(160,0),null,null,0,16,0,3,25000,"攻击+160闪避+16<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+200闪避+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70195,"阿傩卡","ANAN_KA",null,null,new ValueVO(200,0),null,null,0,20,0,4,30000,"攻击+200闪避+20<br>可去荆棘岭找凌空子强化卡片"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70196,"伽叶卡","JIAYE_KA",null,null,new ValueVO(40,0),null,null,5,0,0,0,10000,"攻击+40暴击+5<br>掉落：大雷音寺<br>下一级: 攻击+80暴击+10"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70197,"伽叶卡","JIAYE_KA",null,null,new ValueVO(80,0),null,null,10,0,0,1,15000,"攻击+80暴击+10<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+120暴击+15"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70198,"伽叶卡","JIAYE_KA",null,null,new ValueVO(120,0),null,null,15,0,0,2,20000,"攻击+120暴击+15<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+160暴击+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70199,"伽叶卡","JIAYE_KA",null,null,new ValueVO(160,0),null,null,20,0,0,3,25000,"攻击+160暴击+20<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+200暴击+25"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70200,"伽叶卡","JIAYE_KA",null,null,new ValueVO(200,0),null,null,25,0,0,4,30000,"攻击+200暴击+25<br>可去荆棘岭找凌空子强化卡片"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70501,"+1寅将军卡","YANJIANGJUN_KA",new ValueVO(350,0),null,null,null,null,0,0,0,4,30000,"生命+350<br>荆棘岭找凌空子强化获得。<br>下一级:生命+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70502,"+2寅将军卡","YANJIANGJUN_KA",new ValueVO(450,0),null,null,null,null,0,0,0,4,30000,"生命+450<br>荆棘岭找凌空子强化获得。<br>下一级:生命+550"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70503,"+3寅将军卡","YANJIANGJUN_KA",new ValueVO(550,0),null,null,null,null,0,0,0,4,30000,"生命+550<br>荆棘岭找凌空子强化获得。<br>下一级:生命+650"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70504,"+4寅将军卡","YANJIANGJUN_KA",new ValueVO(650,0),null,null,null,null,0,0,0,4,30000,"生命+650<br>荆棘岭找凌空子强化获得。<br>下一级:生命+750"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70505,"+5寅将军卡","YANJIANGJUN_KA",new ValueVO(750,0),null,null,null,null,0,0,0,4,30000,"生命+750"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70506,"+1黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(175,0),null,null,null,0,0,0,4,30000,"法力+175<br>荆棘岭找凌空子强化获得。<br>下一级:法力+225"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70507,"+2黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(225,0),null,null,null,0,0,0,4,30000,"法力+225<br>荆棘岭找凌空子强化获得。<br>下一级:法力+275"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70508,"+3黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(275,0),null,null,null,0,0,0,4,30000,"法力+275<br>荆棘岭找凌空子强化获得。<br>下一级:法力+325"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70509,"+4黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(325,0),null,null,null,0,0,0,4,30000,"法力+325<br>荆棘岭找凌空子强化获得。<br>下一级:法力+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70510,"+5黑熊精卡","HEIXIONGJ_KA",null,new ValueVO(375,0),null,null,null,0,0,0,4,30000,"法力+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70511,"+1黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(140,0),null,0,0,0,4,30000,"物防+140<br>荆棘岭找凌空子强化获得。<br>下一级:物防+180"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70512,"+2黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(180,0),null,0,0,0,4,30000,"物防+180<br>荆棘岭找凌空子强化获得。<br>下一级:物防+220"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70513,"+3黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(220,0),null,0,0,0,4,30000,"物防+220<br>荆棘岭找凌空子强化获得。<br>下一级:物防+260"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70514,"+4黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(260,0),null,0,0,0,4,30000,"物防+260<br>荆棘岭找凌空子强化获得。<br>下一级:物防+300"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70515,"+5黄风怪卡","HUANGFENGGUAI_KA",null,null,null,new ValueVO(300,0),null,0,0,0,4,30000,"物防+300"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70516,"+1白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(140,0),0,0,0,4,30000,"魔防+140<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+180"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70517,"+2白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(180,0),0,0,0,4,30000,"魔防+180<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+220"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70518,"+3白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(220,0),0,0,0,4,30000,"魔防+220<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+260"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70519,"+4白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(260,0),0,0,0,4,30000,"魔防+260<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+300"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70520,"+5白骨精卡","BAIGUJ3_KA",null,null,null,null,new ValueVO(300,0),0,0,0,4,30000,"魔防+300"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70521,"+1黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(350,0),null,null,null,null,0,0,18,4,30000,"生命+350幸运+18<br>荆棘岭找凌空子强化获得。<br>下一级:生命+450幸运+21"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70522,"+2黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(450,0),null,null,null,null,0,0,21,4,30000,"生命+450幸运+21<br>荆棘岭找凌空子强化获得。<br>下一级:生命+550幸运+24"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70523,"+3黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(550,0),null,null,null,null,0,0,24,4,30000,"生命+550幸运+24<br>荆棘岭找凌空子强化获得。<br>下一级:生命+650幸运+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70524,"+4黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(650,0),null,null,null,null,0,0,27,4,30000,"生命+650幸运+27<br>荆棘岭找凌空子强化获得。<br>下一级:生命+750幸运+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70525,"+5黄袍怪卡","HUANGPAOGUAI_KA",new ValueVO(750,0),null,null,null,null,0,0,30,4,30000,"生命+750幸运+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70526,"+1银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(140,0),null,18,0,0,4,30000,"物防+140暴击+18<br>荆棘岭找凌空子强化获得。<br>下一级:物防+180暴击+21"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70527,"+2银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(180,0),null,21,0,0,4,30000,"物防+180暴击+21<br>荆棘岭找凌空子强化获得。<br>下一级:物防+220暴击+24"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70528,"+3银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(220,0),null,24,0,0,4,30000,"物防+220暴击+24<br>荆棘岭找凌空子强化获得。<br>下一级:物防+260暴击+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70529,"+4银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(260,0),null,27,0,0,4,30000,"物防+260暴击+27<br>荆棘岭找凌空子强化获得。<br>下一级:物防+300暴击+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70530,"+5银角大王卡","YINJIAODAWANG_KA",null,null,null,new ValueVO(300,0),null,30,0,0,4,30000,"物防+300暴击+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70531,"+1金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(140,0),0,18,0,4,30000,"魔防+140闪避+18<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+180闪避+21"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70532,"+2金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(180,0),0,21,0,4,30000,"魔防+180闪避+21<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+220闪避+24"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70533,"+3金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(220,0),0,24,0,4,30000,"魔防+220闪避+24<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+260闪避+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70534,"+4金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(260,0),0,27,0,4,30000,"魔防+260闪避+27<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+300闪避+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70535,"+5金角大王卡","JINJIAODAWANG_KA",null,null,null,null,new ValueVO(300,0),0,30,0,4,30000,"魔防+300闪避+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70536,"+1九尾狐卡","JIUWEIHU_KA",new ValueVO(350,0),new ValueVO(140,0),null,null,null,0,0,0,4,30000,"生命+350法力+140<br>荆棘岭找凌空子强化获得。<br>下一级:生命+450法力+180"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70537,"+2九尾狐卡","JIUWEIHU_KA",new ValueVO(450,0),new ValueVO(180,0),null,null,null,0,0,0,4,30000,"生命+450法力+180<br>荆棘岭找凌空子强化获得。<br>下一级:生命+550法力+220"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70538,"+3九尾狐卡","JIUWEIHU_KA",new ValueVO(550,0),new ValueVO(220,0),null,null,null,0,0,0,4,30000,"生命+550法力+220<br>荆棘岭找凌空子强化获得。<br>下一级:生命+650法力+260"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70539,"+4九尾狐卡","JIUWEIHU_KA",new ValueVO(650,0),new ValueVO(260,0),null,null,null,0,0,0,4,30000,"生命+650法力+260<br>荆棘岭找凌空子强化获得。<br>下一级:生命+750法力+300"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70540,"+5九尾狐卡","JIUWEIHU_KA",new ValueVO(750,0),new ValueVO(300,0),null,null,null,0,0,0,4,30000,"生命+750法力+300"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70541,"+1狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(175,0),null,null,0,18,0,4,30000,"攻击+175闪避+18<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+235闪避+21"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70542,"+2狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(235,0),null,null,0,21,0,4,30000,"攻击+235闪避+21<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+305闪避+24"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70543,"+3狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(305,0),null,null,0,24,0,4,30000,"攻击+305闪避+24<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+385闪避+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70544,"+4狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(385,0),null,null,0,27,0,4,30000,"攻击+385闪避+27<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+465闪避+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70545,"+5狮俐王卡","SHILIWANG_KA",null,null,new ValueVO(465,0),null,null,0,30,0,4,30000,"攻击+465闪避+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70546,"+1红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(175,0),null,null,18,0,0,4,30000,"攻击+175暴击+18<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+235暴击+21"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70547,"+2红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(235,0),null,null,21,0,0,4,30000,"攻击+235暴击+21<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+305暴击+24"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70548,"+3红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(305,0),null,null,24,0,0,4,30000,"攻击+305暴击+24<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+385暴击+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70549,"+4红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(385,0),null,null,27,0,0,4,30000,"攻击+385暴击+27<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+465暴击+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70550,"+5红孩儿卡","HONGHAIER_KA",null,null,new ValueVO(465,0),null,null,30,0,0,4,30000,"攻击+465暴击+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70551,"+1鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(140,0),new ValueVO(140,0),0,0,0,4,30000,"物防+140魔防+140<br>荆棘岭找凌空子强化获得。<br>下一级:物防+190魔防+190"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70552,"+2鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(190,0),new ValueVO(190,0),0,0,0,4,30000,"物防+190魔防+190<br>荆棘岭找凌空子强化获得。<br>下一级:物防+250魔防+250"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70553,"+3鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(250,0),new ValueVO(250,0),0,0,0,4,30000,"物防+250魔防+250<br>荆棘岭找凌空子强化获得。<br>下一级:物防+320魔防+320"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70554,"+4鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(320,0),new ValueVO(320,0),0,0,0,4,30000,"物防+320魔防+320<br>荆棘岭找凌空子强化获得。<br>下一级:物防+400魔防+400"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70555,"+5鼍龙卡","TUOLONG_KA",null,null,null,new ValueVO(400,0),new ValueVO(400,0),0,0,0,4,30000,"物防+400魔防+400"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70556,"+1虎力大仙卡","HULIDAXIAN_KA",new ValueVO(420,0),new ValueVO(175,0),null,null,null,0,0,0,4,30000,"生命+420法力+175<br>荆棘岭找凌空子强化获得。<br>下一级:生命+540法力+225"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70557,"+2虎力大仙卡","HULIDAXIAN_KA",new ValueVO(540,0),new ValueVO(225,0),null,null,null,0,0,0,4,30000,"生命+540法力+225<br>荆棘岭找凌空子强化获得。<br>下一级:生命+660法力+275"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70558,"+3虎力大仙卡","HULIDAXIAN_KA",new ValueVO(660,0),new ValueVO(275,0),null,null,null,0,0,0,4,30000,"生命+660法力+275<br>荆棘岭找凌空子强化获得。<br>下一级:生命+780法力+325"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70559,"+4虎力大仙卡","HULIDAXIAN_KA",new ValueVO(780,0),new ValueVO(325,0),null,null,null,0,0,0,4,30000,"生命+780法力+325<br>荆棘岭找凌空子强化获得。<br>下一级:生命+900法力+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70560,"+5虎力大仙卡","HULIDAXIAN_KA",new ValueVO(900,0),new ValueVO(375,0),null,null,null,0,0,0,4,30000,"生命+900法力+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70561,"+1鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(420,0),null,null,new ValueVO(175,0),null,0,0,0,4,30000,"生命+420物防+175<br>荆棘岭找凌空子强化获得。<br>下一级:生命+540物防+225"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70562,"+2鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(540,0),null,null,new ValueVO(225,0),null,0,0,0,4,30000,"生命+540物防+225<br>荆棘岭找凌空子强化获得。<br>下一级:生命+660物防+275"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70563,"+3鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(660,0),null,null,new ValueVO(275,0),null,0,0,0,4,30000,"生命+660物防+275<br>荆棘岭找凌空子强化获得。<br>下一级:生命+780物防+325"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70564,"+4鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(780,0),null,null,new ValueVO(325,0),null,0,0,0,4,30000,"生命+780物防+325<br>荆棘岭找凌空子强化获得。<br>下一级:生命+900物防+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70565,"+5鹿力大仙卡","LULIDAXIAN_KA",new ValueVO(900,0),null,null,new ValueVO(375,0),null,0,0,0,4,30000,"生命+900物防+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70566,"+1羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(420,0),null,null,null,new ValueVO(175,0),0,0,0,4,30000,"生命+420魔防+175<br>荆棘岭找凌空子强化获得。<br>下一级:生命+540魔防+225"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70567,"+2羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(540,0),null,null,null,new ValueVO(225,0),0,0,0,4,30000,"生命+540魔防+225<br>荆棘岭找凌空子强化获得。<br>下一级:生命+660魔防+275"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70568,"+3羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(660,0),null,null,null,new ValueVO(275,0),0,0,0,4,30000,"生命+660魔防+275<br>荆棘岭找凌空子强化获得。<br>下一级:生命+780魔防+325"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70569,"+4羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(780,0),null,null,null,new ValueVO(325,0),0,0,0,4,30000,"生命+780魔防+325<br>荆棘岭找凌空子强化获得。<br>下一级:生命+900魔防+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70570,"+5羊力大仙卡","YANGLIDAXIAN_KA",new ValueVO(900,0),null,null,null,new ValueVO(375,0),0,0,0,4,30000,"生命+900魔防+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70571,"+1妖魂王卡","YAOHUNWANG_KA",new ValueVO(420,0),null,new ValueVO(175,0),null,null,0,0,0,4,30000,"生命+420攻击+175<br>荆棘岭找凌空子强化获得。<br>下一级:生命+540攻击+225"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70572,"+2妖魂王卡","YAOHUNWANG_KA",new ValueVO(540,0),null,new ValueVO(225,0),null,null,0,0,0,4,30000,"生命+540攻击+225<br>荆棘岭找凌空子强化获得。<br>下一级:生命+660攻击+275"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70573,"+3妖魂王卡","YAOHUNWANG_KA",new ValueVO(660,0),null,new ValueVO(275,0),null,null,0,0,0,4,30000,"生命+660攻击+275<br>荆棘岭找凌空子强化获得。<br>下一级:生命+780攻击+325"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70574,"+4妖魂王卡","YAOHUNWANG_KA",new ValueVO(780,0),null,new ValueVO(325,0),null,null,0,0,0,4,30000,"生命+780攻击+325<br>荆棘岭找凌空子强化获得。<br>下一级:生命+900攻击+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70575,"+5妖魂王卡","YAOHUNWANG_KA",new ValueVO(900,0),null,new ValueVO(375,0),null,null,0,0,0,4,30000,"生命+900攻击+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70576,"+1灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(210,0),null,null,null,18,0,0,4,30000,"法力+210暴击+18<br>荆棘岭找凌空子强化获得。<br>下一级:法力+270暴击+21"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70577,"+2灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(270,0),null,null,null,21,0,0,4,30000,"法力+270暴击+21<br>荆棘岭找凌空子强化获得。<br>下一级:法力+330暴击+24"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70578,"+3灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(330,0),null,null,null,24,0,0,4,30000,"法力+330暴击+24<br>荆棘岭找凌空子强化获得。<br>下一级:法力+390暴击+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70579,"+4灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(390,0),null,null,null,27,0,0,4,30000,"法力+390暴击+27<br>荆棘岭找凌空子强化获得。<br>下一级:法力+450暴击+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70580,"+5灵感大王卡","LINGGANDAXIAN_KA",null,new ValueVO(450,0),null,null,null,30,0,0,4,30000,"法力+450暴击+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70581,"+1独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(210,0),null,null,0,0,12,4,30000,"攻击+210幸运+12<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+270幸运+14"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70582,"+2独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(270,0),null,null,0,0,14,4,30000,"攻击+270幸运+14<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+330幸运+16"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70583,"+3独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(330,0),null,null,0,0,16,4,30000,"攻击+330幸运+16<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+390幸运+18"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70584,"+4独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(390,0),null,null,0,0,18,4,30000,"攻击+390幸运+18<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+450幸运+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70585,"+5独角兕卡","DUJIAODOU_KA",null,null,new ValueVO(450,0),null,null,0,0,20,4,30000,"攻击+450幸运+20"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70586,"+1蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(210,0),null,0,18,0,4,30000,"物防+210闪避+18<br>荆棘岭找凌空子强化获得。<br>下一级:物防+270闪避+21"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70587,"+2蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(270,0),null,0,21,0,4,30000,"物防+270闪避+21<br>荆棘岭找凌空子强化获得。<br>下一级:物防+330闪避+24"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70588,"+3蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(330,0),null,0,24,0,4,30000,"物防+330闪避+24<br>荆棘岭找凌空子强化获得。<br>下一级:物防+390闪避+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70589,"+4蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(390,0),null,0,27,0,4,30000,"物防+390闪避+27<br>荆棘岭找凌空子强化获得。<br>下一级:物防+450闪避+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70590,"+5蝎子精卡","XIEZIJING_KA",null,null,null,new ValueVO(450,0),null,0,30,0,4,30000,"物防+450闪避+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70591,"+1铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(560,0),null,null,null,new ValueVO(210,0),0,0,0,4,30000,"生命+560魔防+210<br>荆棘岭找凌空子强化获得。<br>下一级:生命+720魔防+270"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70592,"+2铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(720,0),null,null,null,new ValueVO(270,0),0,0,0,4,30000,"生命+720魔防+270<br>荆棘岭找凌空子强化获得。<br>下一级:生命+880魔防+330"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70593,"+3铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(880,0),null,null,null,new ValueVO(330,0),0,0,0,4,30000,"生命+880魔防+330<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1040魔防+390"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70594,"+4铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(1040,0),null,null,null,new ValueVO(390,0),0,0,0,4,30000,"生命+1040魔防+390<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1200魔防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70595,"+5铁扇公主卡","TIESHANGONGZHU_KA",new ValueVO(1200,0),null,null,null,new ValueVO(450,0),0,0,0,4,30000,"生命+1200魔防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70596,"+1牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(280,0),null,null,18,0,0,4,30000,"攻击+280暴击+18<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+360暴击+21"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70597,"+2牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(360,0),null,null,21,0,0,4,30000,"攻击+360暴击+21<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+440暴击+24"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70598,"+3牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(440,0),null,null,24,0,0,4,30000,"攻击+440暴击+24<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+520暴击+27"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70599,"+4牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(520,0),null,null,27,0,0,4,30000,"攻击+520暴击+27<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+600暴击+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70600,"+5牛魔王卡","NIUMOWANG_KA",null,null,new ValueVO(600,0),null,null,30,0,0,4,30000,"攻击+600暴击+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70601,"+1九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(175,0),new ValueVO(175,0),0,0,0,4,30000,"物防+175魔防+175<br>荆棘岭找凌空子强化获得。<br>下一级:物防+225魔防+225"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70602,"+2九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(225,0),new ValueVO(225,0),0,0,0,4,30000,"物防+225魔防+225<br>荆棘岭找凌空子强化获得。<br>下一级:物防+275魔防+275"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70603,"+3九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(275,0),new ValueVO(275,0),0,0,0,4,30000,"物防+275魔防+275<br>荆棘岭找凌空子强化获得。<br>下一级:物防+325魔防+325"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70604,"+4九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(325,0),new ValueVO(325,0),0,0,0,4,30000,"物防+325魔防+325<br>荆棘岭找凌空子强化获得。<br>下一级:物防+375魔防+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70605,"+5九头虫卡","JIUTOUCHONG_KA",null,null,null,new ValueVO(375,0),new ValueVO(375,0),0,0,0,4,30000,"物防+375魔防+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70606,"+1黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,24,24,0,4,30000,"暴击+24闪避+24<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+28闪避+28"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70607,"+2黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,28,28,0,4,30000,"暴击+28闪避+28<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+32闪避+32"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70608,"+3黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,32,32,0,4,30000,"暴击+32闪避+32<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+36闪避+36"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70609,"+4黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,36,36,0,4,30000,"暴击+36闪避+36<br>荆棘岭找凌空子强化获得。<br>下一级:暴击+40闪避+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70610,"+5黄眉怪卡","HUANGMEIGUAI_KA",null,null,null,null,null,40,40,0,4,30000,"暴击+40闪避+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70611,"+1蟒蛇精卡","MENGSHEJ_KA",new ValueVO(560,0),null,null,new ValueVO(210,0),null,0,0,0,4,30000,"生命+560物防+210<br>荆棘岭找凌空子强化获得。<br>下一级:生命+720物防+270"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70612,"+2蟒蛇精卡","MENGSHEJ_KA",new ValueVO(720,0),null,null,new ValueVO(270,0),null,0,0,0,4,30000,"生命+720物防+270<br>荆棘岭找凌空子强化获得。<br>下一级:生命+880物防+330"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70613,"+3蟒蛇精卡","MENGSHEJ_KA",new ValueVO(880,0),null,null,new ValueVO(330,0),null,0,0,0,4,30000,"生命+880物防+330<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1040物防+390"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70614,"+4蟒蛇精卡","MENGSHEJ_KA",new ValueVO(1040,0),null,null,new ValueVO(390,0),null,0,0,0,4,30000,"生命+1040物防+390<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1200物防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70615,"+5蟒蛇精卡","MENGSHEJ_KA",new ValueVO(1200,0),null,null,new ValueVO(450,0),null,0,0,0,4,30000,"生命+1200物防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70616,"+1赛太岁卡","SAITAISUI_KA",null,new ValueVO(175,0),new ValueVO(245,0),null,null,0,0,0,4,30000,"攻击+245法力+175<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+315法力+225"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70617,"+2赛太岁卡","SAITAISUI_KA",null,new ValueVO(225,0),new ValueVO(315,0),null,null,0,0,0,4,30000,"攻击+315法力+225<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+385法力+275"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70618,"+3赛太岁卡","SAITAISUI_KA",null,new ValueVO(275,0),new ValueVO(385,0),null,null,0,0,0,4,30000,"攻击+385法力+275<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+455法力+325"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70619,"+4赛太岁卡","SAITAISUI_KA",null,new ValueVO(325,0),new ValueVO(455,0),null,null,0,0,0,4,30000,"攻击+455法力+325<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+525法力+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70620,"+5赛太岁卡","SAITAISUI_KA",null,new ValueVO(375,0),new ValueVO(525,0),null,null,0,0,0,4,30000,"攻击+525法力+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70621,"+1蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(175,0),null,null,new ValueVO(210,0),0,0,0,4,30000,"法力+175魔防+210<br>荆棘岭找凌空子强化获得。<br>下一级:法力+225魔防+270"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70622,"+2蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(225,0),null,null,new ValueVO(270,0),0,0,0,4,30000,"法力+225魔防+270<br>荆棘岭找凌空子强化获得。<br>下一级:法力+275魔防+330"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70623,"+3蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(275,0),null,null,new ValueVO(330,0),0,0,0,4,30000,"法力+275魔防+330<br>荆棘岭找凌空子强化获得。<br>下一级:法力+325魔防+390"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70624,"+4蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(325,0),null,null,new ValueVO(390,0),0,0,0,4,30000,"法力+325魔防+390<br>荆棘岭找凌空子强化获得。<br>下一级:法力+375魔防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70625,"+5蜘蛛精卡","ZHIZHUJIN_KA",null,new ValueVO(375,0),null,null,new ValueVO(450,0),0,0,0,4,30000,"法力+375魔防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70626,"+1蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(280,0),new ValueVO(175,0),null,0,0,0,4,30000,"攻击+280物防+175<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+370物防+235"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70627,"+2蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(370,0),new ValueVO(235,0),null,0,0,0,4,30000,"攻击+370物防+235<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+470物防+305"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70628,"+3蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(470,0),new ValueVO(305,0),null,0,0,0,4,30000,"攻击+470物防+305<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+580物防+385"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70629,"+4蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(580,0),new ValueVO(385,0),null,0,0,0,4,30000,"攻击+580物防+385<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+700物防+475"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70630,"+5蜈蚣精卡","WUGONGJING_KA",null,null,new ValueVO(700,0),new ValueVO(475,0),null,0,0,0,4,30000,"攻击+700物防+475"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70631,"+1狮子精卡","SHIZIJING_KA",new ValueVO(560,0),null,null,null,null,24,0,0,4,30000,"生命+560暴击+24<br>荆棘岭找凌空子强化获得。<br>下一级:生命+720暴击+28"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70632,"+2狮子精卡","SHIZIJING_KA",new ValueVO(720,0),null,null,null,null,28,0,0,4,30000,"生命+720暴击+28<br>荆棘岭找凌空子强化获得。<br>下一级:生命+880暴击+32"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70633,"+3狮子精卡","SHIZIJING_KA",new ValueVO(880,0),null,null,null,null,32,0,0,4,30000,"生命+880暴击+32<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1040暴击+36"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70634,"+4狮子精卡","SHIZIJING_KA",new ValueVO(1040,0),null,null,null,null,36,0,0,4,30000,"生命+1040暴击+36<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1200暴击+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70635,"+5狮子精卡","SHIZIJING_KA",new ValueVO(1200,0),null,null,null,null,40,0,0,4,30000,"生命+1200暴击+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70636,"+1白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(210,0),new ValueVO(210,0),0,0,0,4,30000,"物防+210魔防+210<br>荆棘岭找凌空子强化获得。<br>下一级:物防+280魔防+280"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70637,"+2白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(280,0),new ValueVO(280,0),0,0,0,4,30000,"物防+280魔防+280<br>荆棘岭找凌空子强化获得。<br>下一级:物防+360魔防+360"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70638,"+3白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(360,0),new ValueVO(360,0),0,0,0,4,30000,"物防+360魔防+360<br>荆棘岭找凌空子强化获得。<br>下一级:物防+450魔防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70639,"+4白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(450,0),new ValueVO(450,0),0,0,0,4,30000,"物防+450魔防+450<br>荆棘岭找凌空子强化获得。<br>下一级:物防+550魔防+550"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70640,"+5白象精卡","BAIXIANGJING_KA",null,null,null,new ValueVO(550,0),new ValueVO(550,0),0,0,0,4,30000,"物防+550魔防+550"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70641,"+1大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(280,0),null,new ValueVO(175,0),0,0,0,4,30000,"攻击+280魔防+175<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+360魔防+225"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70642,"+2大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(360,0),null,new ValueVO(225,0),0,0,0,4,30000,"攻击+360魔防+225<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+440魔防+275"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70643,"+3大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(440,0),null,new ValueVO(275,0),0,0,0,4,30000,"攻击+440魔防+275<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+520魔防+325"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70644,"+4大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(520,0),null,new ValueVO(325,0),0,0,0,4,30000,"攻击+520魔防+325<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+600魔防+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70645,"+5大鹏精卡","DAPENGJING_KA",null,null,new ValueVO(600,0),null,new ValueVO(375,0),0,0,0,4,30000,"攻击+600魔防+375"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70646,"+1白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(240,0),24,0,0,4,30000,"魔防+240暴击+24<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+310暴击+28"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70647,"+2白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(310,0),28,0,0,4,30000,"魔防+310暴击+28<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+390暴击+32"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70648,"+3白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(390,0),32,0,0,4,30000,"魔防+390暴击+32<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+480暴击+36"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70649,"+4白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(480,0),36,0,0,4,30000,"魔防+480暴击+36<br>荆棘岭找凌空子强化获得。<br>下一级:魔防+580暴击+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70650,"+5白鹿精卡","BAILUJING_KA",null,null,null,null,new ValueVO(580,0),40,0,0,4,30000,"魔防+580暴击+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70651,"+1地涌夫人卡","DIYONG_KA",new ValueVO(840,0),new ValueVO(225,0),null,null,null,0,0,0,4,30000,"生命+840法力+225<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1080法力+285"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70652,"+2地涌夫人卡","DIYONG_KA",new ValueVO(1080,0),new ValueVO(285,0),null,null,null,0,0,0,4,30000,"生命+1080法力+285<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1320法力+335"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70653,"+3地涌夫人卡","DIYONG_KA",new ValueVO(1320,0),new ValueVO(355,0),null,null,null,0,0,0,4,30000,"生命+1320法力+355<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1560法力+435"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70654,"+4地涌夫人卡","DIYONG_KA",new ValueVO(1560,0),new ValueVO(435,0),null,null,null,0,0,0,4,30000,"生命+1560法力+435<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1800法力+525"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70655,"+5地涌夫人卡","DIYONG_KA",new ValueVO(1800,0),new ValueVO(525,0),null,null,null,0,0,0,4,30000,"生命+1800法力+525"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70656,"+1豹子精卡","BAOZIJING_KA",null,null,new ValueVO(280,0),new ValueVO(240,0),null,0,0,0,4,30000,"攻击+280物防+240<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+370物防+310"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70657,"+2豹子精卡","BAOZIJING_KA",null,null,new ValueVO(370,0),new ValueVO(310,0),null,0,0,0,4,30000,"攻击+370物防+310<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+470物防+390"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70658,"+3豹子精卡","BAOZIJING_KA",null,null,new ValueVO(470,0),new ValueVO(390,0),null,0,0,0,4,30000,"攻击+470物防+390<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+580物防+480"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70659,"+4豹子精卡","BAOZIJING_KA",null,null,new ValueVO(580,0),new ValueVO(480,0),null,0,0,0,4,30000,"攻击+580物防+480<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+700物防+580"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70660,"+5豹子精卡","BAOZIJING_KA",null,null,new ValueVO(700,0),new ValueVO(580,0),null,0,0,0,4,30000,"攻击+700物防+580"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70661,"+1黄狮精卡","HUANGSHIJING_KA",new ValueVO(840,0),null,new ValueVO(280,0),null,null,0,0,0,4,30000,"生命+840攻击+280<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1080攻击+370"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70662,"+2黄狮精卡","HUANGSHIJING_KA",new ValueVO(1080,0),null,new ValueVO(370,0),null,null,0,0,0,4,30000,"生命+1080攻击+370<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1320攻击+470"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70663,"+3黄狮精卡","HUANGSHIJING_KA",new ValueVO(1320,0),null,new ValueVO(470,0),null,null,0,0,0,4,30000,"生命+1320攻击+470<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1560攻击+580"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70664,"+4黄狮精卡","HUANGSHIJING_KA",new ValueVO(1560,0),null,new ValueVO(580,0),null,null,0,0,0,4,30000,"生命+1560攻击+580<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1800攻击+700"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70665,"+5黄狮精卡","HUANGSHIJING_KA",new ValueVO(1800,0),null,new ValueVO(700,0),null,null,0,0,0,4,30000,"生命+1800攻击+700"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70666,"+1九灵元圣卡","JIULING_KA",null,new ValueVO(225,0),new ValueVO(280,0),null,null,0,0,0,4,30000,"攻击+280法力+225<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+370法力+285"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70667,"+2九灵元圣卡","JIULING_KA",null,new ValueVO(285,0),new ValueVO(370,0),null,null,0,0,0,4,30000,"攻击+370法力+285<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+470法力+355"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70668,"+3九灵元圣卡","JIULING_KA",null,new ValueVO(355,0),new ValueVO(470,0),null,null,0,0,0,4,30000,"攻击+470法力+355<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+580法力+435"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70669,"+4九灵元圣卡","JIULING_KA",null,new ValueVO(435,0),new ValueVO(580,0),null,null,0,0,0,4,30000,"攻击+580法力+435<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+700法力+525"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70670,"+5九灵元圣卡","JIULING_KA",null,new ValueVO(525,0),new ValueVO(700,0),null,null,0,0,0,4,30000,"攻击+700法力+525"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70671,"+1辟暑王卡","PISHUDAWANG_KA",new ValueVO(1080,0),null,null,null,new ValueVO(270,0),0,0,0,4,30000,"生命+1080魔防+270<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1320魔防+330"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70672,"+2辟暑王卡","PISHUDAWANG_KA",new ValueVO(1320,0),null,null,null,new ValueVO(330,0),0,0,0,4,30000,"生命+1320魔防+330<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1560魔防+390"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70673,"+3辟暑王卡","PISHUDAWANG_KA",new ValueVO(1560,0),null,null,null,new ValueVO(390,0),0,0,0,4,30000,"生命+1560魔防+390<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1800魔防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70674,"+4辟暑王卡","PISHUDAWANG_KA",new ValueVO(1800,0),null,null,null,new ValueVO(450,0),0,0,0,4,30000,"生命+1800魔防+450<br>荆棘岭找凌空子强化获得。<br>下一级:生命+2040魔防+510"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70675,"+5辟暑王卡","PISHUDAWANG_KA",new ValueVO(2040,0),null,null,null,new ValueVO(510,0),0,0,0,4,30000,"生命+2040魔防+510"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70676,"+1辟寒王卡","PIHANDAWANG_KA",new ValueVO(1080,0),null,null,null,null,30,0,0,4,30000,"生命+1080暴击+30<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1320暴击+35"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70677,"+2辟寒王卡","PIHANDAWANG_KA",new ValueVO(1320,0),null,null,null,null,35,0,0,4,30000,"生命+1320暴击+35<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1560暴击+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70678,"+3辟寒王卡","PIHANDAWANG_KA",new ValueVO(1560,0),null,null,null,null,40,0,0,4,30000,"生命+1560暴击+40<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1800暴击+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70679,"+4辟寒王卡","PIHANDAWANG_KA",new ValueVO(1800,0),null,null,null,null,45,0,0,4,30000,"生命+1800暴击+45<br>荆棘岭找凌空子强化获得。<br>下一级:生命+2040暴击+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70680,"+5辟寒王卡","PIHANDAWANG_KA",new ValueVO(2040,0),null,null,null,null,50,0,0,4,30000,"生命+2040暴击+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70681,"+1辟尘王卡","PICHENDAWANG_KA",new ValueVO(1080,0),null,null,new ValueVO(270,0),null,0,0,0,4,30000,"生命+1080物防+270<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1320物防+330"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70682,"+2辟尘王卡","PICHENDAWANG_KA",new ValueVO(1320,0),null,null,new ValueVO(330,0),null,0,0,0,4,30000,"生命+1320物防+330<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1560物防+390"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70683,"+3辟尘王卡","PICHENDAWANG_KA",new ValueVO(1560,0),null,null,new ValueVO(390,0),null,0,0,0,4,30000,"生命+1560物防+390<br>荆棘岭找凌空子强化获得。<br>下一级:生命+1800物防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70684,"+4辟尘王卡","PICHENDAWANG_KA",new ValueVO(1800,0),null,null,new ValueVO(450,0),null,0,0,0,4,30000,"生命+1800物防+450<br>荆棘岭找凌空子强化获得。<br>下一级:生命+2040物防+510"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70685,"+5辟尘王卡","PICHENDAWANG_KA",new ValueVO(2040,0),null,null,new ValueVO(510,0),null,0,0,0,4,30000,"生命+2040物防+510"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70686,"+1玉兔精卡","YUTUJING_KA",null,new ValueVO(225,0),null,null,new ValueVO(270,0),0,0,0,4,30000,"法力+225魔防+270<br>荆棘岭找凌空子强化获得。<br>下一级:法力+285魔防+330"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70687,"+2玉兔精卡","YUTUJING_KA",null,new ValueVO(285,0),null,null,new ValueVO(330,0),0,0,0,4,30000,"法力+285魔防+330<br>荆棘岭找凌空子强化获得。<br>下一级:法力+355魔防+390"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70688,"+3玉兔精卡","YUTUJING_KA",null,new ValueVO(355,0),null,null,new ValueVO(390,0),0,0,0,4,30000,"法力+355魔防+390<br>荆棘岭找凌空子强化获得。<br>下一级:法力+435魔防+450"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70689,"+4玉兔精卡","YUTUJING_KA",null,new ValueVO(435,0),null,null,new ValueVO(450,0),0,0,0,4,30000,"法力+435魔防+450<br>荆棘岭找凌空子强化获得。<br>下一级:法力+525魔防+510"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70690,"+5玉兔精卡","YUTUJING_KA",null,new ValueVO(525,0),null,null,new ValueVO(510,0),0,0,0,4,30000,"法力+525魔防+510"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70691,"+1阿傩卡","ANAN_KA",null,null,new ValueVO(280,0),null,null,0,24,0,4,30000,"攻击+280闪避+24<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+370闪避+28"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70692,"+2阿傩卡","ANAN_KA",null,null,new ValueVO(370,0),null,null,0,28,0,4,30000,"攻击+370闪避+28<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+470闪避+32"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70693,"+3阿傩卡","ANAN_KA",null,null,new ValueVO(470,0),null,null,0,32,0,4,30000,"攻击+470闪避+32<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+580闪避+36"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70694,"+4阿傩卡","ANAN_KA",null,null,new ValueVO(580,0),null,null,0,36,0,4,30000,"攻击+580闪避+36<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+700闪避+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70695,"+5阿傩卡","ANAN_KA",null,null,new ValueVO(700,0),null,null,0,40,0,4,30000,"攻击+700闪避+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70696,"+1伽叶卡","JIAYE_KA",null,null,new ValueVO(280,0),null,null,30,0,0,4,30000,"攻击+280暴击+30<br>荆棘岭找凌空子合成获得。<br>下一级: 攻击+370暴击+35"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70697,"+2伽叶卡","JIAYE_KA",null,null,new ValueVO(370,0),null,null,35,0,0,4,30000,"攻击+370暴击+35<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+470暴击+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70698,"+3伽叶卡","JIAYE_KA",null,null,new ValueVO(470,0),null,null,40,0,0,4,30000,"攻击+470暴击+40<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+580暴击+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70699,"+4伽叶卡","JIAYE_KA",null,null,new ValueVO(580,0),null,null,45,0,0,4,30000,"攻击+580暴击+45<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+700暴击+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70700,"+5伽叶卡","JIAYE_KA",null,null,new ValueVO(700,0),null,null,50,0,0,4,30000,"攻击+700暴击+50"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70901,"+1圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(250,1),null,null,new ValueVO(270,1),null,0,0,0,4,80000,"生命+25%物防+27%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+31%物防+33%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70902,"+2圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(310,1),null,null,new ValueVO(330,1),null,0,0,0,4,85000,"生命+31%物防+33%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+38%物防+40%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70903,"+3圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(380,1),null,null,new ValueVO(400,1),null,0,0,0,4,90000,"生命+38%物防+40%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+46%物防+48%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70904,"+4圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(460,1),null,null,new ValueVO(480,1),null,0,0,0,4,95000,"生命+46%物防+48%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+55%物防+57%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70905,"+5圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(550,1),null,null,new ValueVO(570,1),null,0,0,0,4,100000,"生命+55%物防+57%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70906,"+1圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(250,1),null,null,null,new ValueVO(270,1),0,0,0,4,80000,"生命+25%魔防+27%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+31%魔防+33%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70907,"+2圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(310,1),null,null,null,new ValueVO(330,1),0,0,0,4,85000,"生命+31%魔防+33%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+38%魔防+40%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70908,"+3圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(380,1),null,null,null,new ValueVO(400,1),0,0,0,4,90000,"生命+38%魔防+40%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+46%魔防+48%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70909,"+4圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(460,1),null,null,null,new ValueVO(480,1),0,0,0,4,95000,"生命+46%魔防+48%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+55%魔防+57%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70910,"+5圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(550,1),null,null,null,new ValueVO(570,1),0,0,0,4,100000,"生命+55%魔防+57%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70911,"+1圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(270,1),new ValueVO(270,1),0,0,0,4,80000,"物防+27%魔防+27%<br>荆棘岭找凌空子强化获得。<br>下一级:物防+33%魔防+33%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70912,"+2圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(330,1),new ValueVO(330,1),0,0,0,4,85000,"物防+33%魔防+33%<br>荆棘岭找凌空子强化获得。<br>下一级:物防+40%魔防+40%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70913,"+3圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(400,1),new ValueVO(400,1),0,0,0,4,90000,"物防+40%魔防+40%<br>荆棘岭找凌空子强化获得。<br>下一级:物防+48%魔防+48%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70914,"+4圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(480,1),new ValueVO(480,1),0,0,0,4,95000,"物防+48%魔防+48%<br>荆棘岭找凌空子强化获得。<br>下一级:物防+57%魔防+57%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70915,"+5圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(570,1),new ValueVO(570,1),0,0,0,4,100000,"物防+57%魔防+57%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70916,"+1圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(250,1),new ValueVO(190,1),null,null,null,0,0,0,4,80000,"生命+25%法力+19%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+31%法力+24%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70917,"+2圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(310,1),new ValueVO(240,1),null,null,null,0,0,0,4,85000,"生命+31%法力+24%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+38%法力+30%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70918,"+3圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(380,1),new ValueVO(300,1),null,null,null,0,0,0,4,90000,"生命+38%法力+30%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+46%法力+37%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70919,"+4圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(460,1),new ValueVO(370,1),null,null,null,0,0,0,4,95000,"生命+46%法力+37%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+55%法力+45%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70920,"+5圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(550,1),new ValueVO(450,1),null,null,null,0,0,0,4,100000,"生命+55%法力+45%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70921,"+1圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(190,1),new ValueVO(190,1),null,null,0,0,0,4,80000,"攻击+19%法力+19%<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+24%法力+24%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70922,"+2圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(240,1),new ValueVO(240,1),null,null,0,0,0,4,85000,"攻击+24%法力+24%<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+30%法力+30%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70923,"+3圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(300,1),new ValueVO(300,1),null,null,0,0,0,4,90000,"攻击+30%法力+30%<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+37%法力+37%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70924,"+4圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(370,1),new ValueVO(370,1),null,null,0,0,0,4,95000,"攻击+37%法力+37%<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+45%法力+45%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70925,"+5圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(450,1),new ValueVO(450,1),null,null,0,0,0,4,100000,"攻击+45%法力+45%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70926,"+1圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(190,1),null,null,50,0,0,4,80000,"攻击+19%暴击+50<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+24%暴击+55"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70927,"+2圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(240,1),null,null,55,0,0,4,85000,"攻击+24%暴击+55<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+30%暴击+60"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70928,"+3圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(300,1),null,null,60,0,0,4,90000,"攻击+30%暴击+60<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+37%暴击+65"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70929,"+4圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(370,1),null,null,65,0,0,4,95000,"攻击+37%暴击+65<br>荆棘岭找凌空子强化获得。<br>下一级:攻击+45%暴击+70"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70930,"+5圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(450,1),null,null,70,0,0,4,100000,"攻击+45%暴击+70"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70931,"+1圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(250,1),null,new ValueVO(190,1),null,null,0,0,0,4,80000,"生命+25%攻击+19%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+31%攻击+24%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70932,"+2圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(310,1),null,new ValueVO(240,1),null,null,0,0,0,4,85000,"生命+31%攻击+24%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+38%攻击+30%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70933,"+3圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(380,1),null,new ValueVO(300,1),null,null,0,0,0,4,90000,"生命+38%攻击+30%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+46%攻击+37%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70934,"+4圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(460,1),null,new ValueVO(370,1),null,null,0,0,0,4,95000,"生命+46%攻击+37%<br>荆棘岭找凌空子强化获得。<br>下一级:生命+55%攻击+45%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70935,"+5圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(550,1),null,new ValueVO(450,1),null,null,0,0,0,4,100000,"生命+55%攻击+45%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70951,"圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(40,1),null,null,new ValueVO(60,1),null,0,0,0,0,50000,"生命+4%物防+6%<br>掉落：圣·牛魔王（七圣副本）<br>下一级:生命+8%物防+10%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70952,"圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(80,1),null,null,new ValueVO(100,1),null,0,0,0,1,55000,"生命+8%物防+10%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+12%物防+14%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70953,"圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(120,1),null,null,new ValueVO(140,1),null,0,0,0,2,60000,"生命+12%物防+14%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+16%物防+18%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70954,"圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(160,1),null,null,new ValueVO(180,1),null,0,0,0,3,65000,"生命+16%物防+18%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+20%物防+22%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70955,"圣·牛魔王卡","SHENGNIUMOWANG_KA",new ValueVO(200,1),null,null,new ValueVO(220,1),null,0,0,0,4,70000,"生命+20%物防+22%<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70956,"圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(40,1),null,null,null,new ValueVO(60,1),0,0,0,0,50000,"生命+4%魔防+6%<br>掉落：圣·蛟魔王（七圣副本）<br>下一级:生命+8%魔防+10%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70957,"圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(80,1),null,null,null,new ValueVO(100,1),0,0,0,1,55000,"生命+8%魔防+10%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+12%魔防+14%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70958,"圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(120,1),null,null,null,new ValueVO(140,1),0,0,0,2,60000,"生命+12%魔防+14%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+16%魔防+18%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70959,"圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(160,1),null,null,null,new ValueVO(180,1),0,0,0,3,65000,"生命+16%魔防+18%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+20%魔防+22%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70960,"圣·蛟魔王卡","SHENGJIAOMOWANG_KA",new ValueVO(200,1),null,null,null,new ValueVO(220,1),0,0,0,4,70000,"生命+20%魔防+22%<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70961,"圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(60,1),new ValueVO(60,1),0,0,0,0,50000,"物防+6%魔防+6%<br>掉落：圣·鹏魔王（七圣副本）<br>下一级:物防+10%魔防+10%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70962,"圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(100,1),new ValueVO(100,1),0,0,0,1,55000,"物防+10%魔防+10%<br>荆棘岭找凌空子合成获得。<br>下一级:物防+14%魔防+14%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70963,"圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(140,1),new ValueVO(140,1),0,0,0,2,60000,"物防+14%魔防+14%<br>荆棘岭找凌空子合成获得。<br>下一级:物防+18%魔防+18%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70964,"圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(180,1),new ValueVO(180,1),0,0,0,3,65000,"物防+18%魔防+18%<br>荆棘岭找凌空子合成获得。<br>下一级:物防+22%魔防+22%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70965,"圣·鹏魔王卡","SHENGPENGMOWANG_KA",null,null,null,new ValueVO(220,1),new ValueVO(220,1),0,0,0,4,70000,"物防+22%魔防+22%<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70966,"圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(40,1),new ValueVO(30,1),null,null,null,0,0,0,0,50000,"生命+4%法力+3%<br>掉落：圣·狮驼王（七圣副本）<br>下一级:生命+8%法力+6%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70967,"圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(80,1),new ValueVO(60,1),null,null,null,0,0,0,1,55000,"生命+8%法力+6%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+12%法力+9%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70968,"圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(120,1),new ValueVO(90,1),null,null,null,0,0,0,2,60000,"生命+12%法力+9%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+16%法力+12%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70969,"圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(160,1),new ValueVO(120,1),null,null,null,0,0,0,3,65000,"生命+16%法力+12%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+20%法力+15%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70970,"圣·狮驼王卡","SHENGSHITUOWANG_KA",new ValueVO(200,1),new ValueVO(150,1),null,null,null,0,0,0,4,70000,"生命+20%法力+15%<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70971,"圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(30,1),new ValueVO(30,1),null,null,0,0,0,0,50000,"攻击+3%法力+3%<br>掉落：圣·猕猴王（七圣副本）<br>下一级:攻击+6%法力+6%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70972,"圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(60,1),new ValueVO(60,1),null,null,0,0,0,1,55000,"攻击+6%法力+6%<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+9%法力+9%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70973,"圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(90,1),new ValueVO(90,1),null,null,0,0,0,2,60000,"攻击+9%法力+9%<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+12%法力+12%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70974,"圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(120,1),new ValueVO(120,1),null,null,0,0,0,3,65000,"攻击+12%法力+12%<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+15%法力+15%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70975,"圣·猕猴王卡","SHENGMIHOUWANG_KA",null,new ValueVO(150,1),new ValueVO(150,1),null,null,0,0,0,4,70000,"攻击+15%法力+15%<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70976,"圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(30,1),null,null,25,0,0,0,50000,"攻击+3%暴击+25<br>掉落：圣·禺狨王（七圣副本）<br>下一级:攻击+6%暴击+30"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70977,"圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(60,1),null,null,30,0,0,1,55000,"攻击+6%暴击+30<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+9%暴击+35"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70978,"圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(90,1),null,null,35,0,0,2,60000,"攻击+9%暴击+35<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+12%暴击+40"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70979,"圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(120,1),null,null,40,0,0,3,65000,"攻击+12%暴击+40<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+15%暴击+45"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70980,"圣·禺狨王卡","SHENGYURONGWANG_KA",null,null,new ValueVO(150,1),null,null,45,0,0,4,70000,"攻击+15%暴击+45<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70981,"圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(40,1),null,new ValueVO(30,1),null,null,0,0,0,0,50000,"生命+4%攻击+3%<br>掉落：圣·美猴王（七圣副本）<br>下一级:生命+8%攻击+6%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70982,"圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(80,1),null,new ValueVO(60,1),null,null,0,0,0,1,55000,"生命+8%攻击+6%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+12%攻击+9%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70983,"圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(120,1),null,new ValueVO(90,1),null,null,0,0,0,2,60000,"生命+12%攻击+9%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+16%攻击+12%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70984,"圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(160,1),null,new ValueVO(120,1),null,null,0,0,0,3,65000,"生命+16%攻击+12%<br>荆棘岭找凌空子合成获得。<br>下一级:生命+20%攻击+15%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70985,"圣·美猴王卡","SHENGMEIHOUWANG_KA",new ValueVO(200,1),null,new ValueVO(150,1),null,null,0,0,0,4,70000,"生命+20%攻击+15%<br>可去荆棘岭找凌空子强化卡片。"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70991,"圣·牛魔王卡","SHENGNIUMOWANG_KA1",new ValueVO(4,1),null,null,new ValueVO(6,1),null,0,0,0,0,50000,"生命+4%物防+6%<br>掉落：圣·牛魔王（七圣副本）<br>下一级:生命+8%物防+10%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70992,"圣·蛟魔王卡","SHENGJIAOMOWANG_KA1",new ValueVO(4,1),null,null,null,new ValueVO(6,1),0,0,0,0,50000,"生命+4%魔防+6%<br>掉落：圣·蛟魔王（七圣副本）<br>下一级:生命+8%魔防+10%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70993,"圣·鹏魔王卡","SHENGPENGMOWANG_KA1",null,null,null,new ValueVO(6,1),new ValueVO(6,1),0,0,0,0,50000,"物防+6%魔防+6%<br>掉落：圣·鹏魔王（七圣副本）<br>下一级:物防+10%魔防+10%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70994,"圣·狮驼王卡","SHENGSHITUOWANG_KA1",new ValueVO(4,1),new ValueVO(3,1),null,null,null,0,0,0,0,50000,"生命+4%法力+3%<br>掉落：圣·狮驼王（七圣副本）<br>下一级:生命+8%法力+6%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70995,"圣·猕猴王卡","SHENGMIHOUWANG_KA1",null,new ValueVO(3,1),new ValueVO(3,1),null,null,0,0,0,0,50000,"攻击+3%法力+3%<br>掉落：圣·猕猴王（七圣副本）<br>下一级:攻击+6%法力+6%"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70996,"圣·禺狨王卡","SHENGYURONGWANG_KA1",null,null,new ValueVO(6,1),null,null,30,0,0,0,55000,"攻击+6%暴击+30<br>荆棘岭找凌空子合成获得。<br>下一级:攻击+9%暴击+35"));
         this.goodDic[ConstData.GOOD_CARD].push(new ConstCardVO(70997,"圣·美猴王卡","SHENGMEIHOUWANG_KA1",new ValueVO(4,1),null,new ValueVO(3,1),null,null,0,0,0,0,50000,"生命+4%攻击+3%<br>掉落：圣·美猴王（七圣副本）<br>下一级:生命+8%攻击+6%"));
      }
      
      public function newGood(param1:int) : GameGoodVO
      {
         var _loc2_:String = this.findGoodType(param1);
         var _loc3_:ConstGoodVO = this.findConstGood(param1);
         if(!_loc3_)
         {
            return null;
         }
         switch(_loc2_)
         {
            case ConstData.GOOD_PROP:
            case ConstData.GOOD_CLIP:
            case ConstData.GOOD_CARD:
               return this.newProp(_loc3_);
            case ConstData.GOOD_EQUIP:
            case ConstData.GOOD_FASHION:
               if(this.findEquipType(param1) == ConstData.EQUIP_FABAO)
               {
                  return new GameFabaoVO(_loc3_ as ConstFabaoVO);
               }
               return new GameEquipVO(_loc3_ as ConstEquipVO);
               break;
            case ConstData.GOOD_PET:
               return new PetEquipVO(_loc3_ as ConstEquipVO);
            case ConstData.CHENG_HAO:
               return new GameATTVO(_loc3_);
            default:
               return null;
         }
      }
      
      private function newProp(param1:ConstGoodVO) : GameGoodVO
      {
         if(param1 is ConstGemVO)
         {
            return new GameGemVO(param1);
         }
         if(param1 is ConstCardVO)
         {
            return new GameCardVO(param1);
         }
         return new GameGoodVO(param1);
      }
      
      public function findConstGood(param1:int) : ConstGoodVO
      {
         var _loc2_:String = this.findGoodType(param1);
         var _loc3_:Array = this.goodDic[_loc2_];
         var _loc4_:int = int(_loc3_.length);
         var _loc5_:int = 0;
         while(_loc5_ < _loc4_)
         {
            if(_loc3_[_loc5_].id.v == param1)
            {
               return _loc3_[_loc5_];
            }
            _loc5_++;
         }
         return null;
      }
      
      public function getGoodName(param1:int) : String
      {
         var _loc2_:ConstGoodVO = this.findConstGood(param1);
         if(!_loc2_)
         {
            return "查无此物";
         }
         return _loc2_.name;
      }
      
      public function findGoodType(param1:int) : String
      {
         return String(param1).charAt(0);
      }
      
      public function findEquipType(param1:int) : String
      {
         return String(param1).charAt(1);
      }
   }
}

