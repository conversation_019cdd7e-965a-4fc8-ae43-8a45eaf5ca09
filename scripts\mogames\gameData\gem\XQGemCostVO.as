package mogames.gameData.gem
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.shop.vo.GameCostVO;
   
   public class XQGemCostVO
   {
      public var index:Sint;
      
      public var dakongCost:GameCostVO;
      
      public var chaichuCost:GameCostVO;
      
      public var xiangqianCost:GameCostVO;
      
      public function XQGemCostVO(param1:int, param2:GameCostVO, param3:GameCostVO, param4:GameCostVO)
      {
         super();
         this.index = new Sint(param1);
         this.dakongCost = param2;
         this.chaichuCost = param3;
         this.xiangqianCost = param4;
      }
      
      public function get dakongNeed() : String
      {
         return this.dakongCost.needStr;
      }
      
      public function get chaichuNeed() : String
      {
         return this.chaichuCost.needStr;
      }
      
      public function get xiangqianNeed() : String
      {
         return this.xiangqianCost.needStr;
      }
   }
}

