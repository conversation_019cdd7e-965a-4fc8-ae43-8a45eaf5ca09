package file
{
   import flash.utils.Dictionary;
   import mogames.gameData.base.Sint;
   import mogames.gameData.mission.BossWaveVO;
   import mogames.gameData.mission.EnemyWaveVO;
   
   public class TowerWaveConfig
   {
      private static var _instance:TowerWaveConfig;
      
      public var waveDic:Dictionary;
      
      public function TowerWaveConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : TowerWaveConfig
      {
         if(!_instance)
         {
            _instance = new TowerWaveConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.waveDic = new Dictionary();
         this.waveDic["TOWER001"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2002),
            "attID":new Sint(20024),
            "aiID":new Sint(20021),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20024),
            "aiID":new Sint(20021),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20024),
            "aiID":new Sint(20021),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20024),
            "aiID":new Sint(20021),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20024),
            "aiID":new Sint(20021),
            "dropID":new Sint(55)
         }]);
         this.waveDic["TOWER002"] = new BossWaveVO([{
            "id":new Sint(3002),
            "attID":new Sint(30023),
            "aiID":new Sint(30023),
            "dropID":new Sint(78)
         }]);
         this.waveDic["TOWER003"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2004),
            "attID":new Sint(20043),
            "aiID":new Sint(20041),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20043),
            "aiID":new Sint(20041),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20043),
            "aiID":new Sint(20041),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20043),
            "aiID":new Sint(20041),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20043),
            "aiID":new Sint(20041),
            "dropID":new Sint(55)
         }]);
         this.waveDic["TOWER004"] = new BossWaveVO([{
            "id":new Sint(3003),
            "attID":new Sint(30034),
            "aiID":new Sint(30034),
            "dropID":new Sint(78)
         }]);
         this.waveDic["TOWER005"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2002),
            "attID":new Sint(20025),
            "aiID":new Sint(20022),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20025),
            "aiID":new Sint(20022),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20025),
            "aiID":new Sint(20022),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20025),
            "aiID":new Sint(20022),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20025),
            "aiID":new Sint(20022),
            "dropID":new Sint(55)
         }]);
         this.waveDic["TOWER006"] = new BossWaveVO([{
            "id":new Sint(3004),
            "attID":new Sint(30044),
            "aiID":new Sint(30044),
            "dropID":new Sint(55)
         },{
            "id":new Sint(3010),
            "attID":new Sint(30102),
            "aiID":new Sint(30102),
            "dropID":new Sint(78)
         }]);
         this.waveDic["TOWER007"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(55)
         }]);
         this.waveDic["TOWER008"] = new BossWaveVO([{
            "id":new Sint(3005),
            "attID":new Sint(30052),
            "aiID":new Sint(30052),
            "dropID":new Sint(55)
         },{
            "id":new Sint(3006),
            "attID":new Sint(30062),
            "aiID":new Sint(30062),
            "dropID":new Sint(78)
         }]);
         this.waveDic["TOWER009"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2005),
            "attID":new Sint(20055),
            "aiID":new Sint(20053),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20055),
            "aiID":new Sint(20053),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20055),
            "aiID":new Sint(20053),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20123),
            "aiID":new Sint(20122),
            "dropID":new Sint(55)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20055),
            "aiID":new Sint(20053),
            "dropID":new Sint(55)
         }]);
         this.waveDic["TOWER0010"] = new BossWaveVO([{
            "id":new Sint(3013),
            "attID":new Sint(30132),
            "aiID":new Sint(30132),
            "dropID":new Sint(55)
         },{
            "id":new Sint(3014),
            "attID":new Sint(30142),
            "aiID":new Sint(30142),
            "dropID":new Sint(56)
         }]);
         this.waveDic["TOWER011"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2015),
            "attID":new Sint(20152),
            "aiID":new Sint(20151),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20152),
            "aiID":new Sint(20151),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20152),
            "aiID":new Sint(20151),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20152),
            "aiID":new Sint(20151),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20152),
            "aiID":new Sint(20151),
            "dropID":new Sint(57)
         }]);
         this.waveDic["TOWER012"] = new BossWaveVO([{
            "id":new Sint(4001),
            "attID":new Sint(40012),
            "aiID":new Sint(40011),
            "dropID":new Sint(79)
         }]);
         this.waveDic["TOWER013"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2005),
            "attID":new Sint(20056),
            "aiID":new Sint(20053),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20056),
            "aiID":new Sint(20053),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20056),
            "aiID":new Sint(20053),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20124),
            "aiID":new Sint(20122),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20056),
            "aiID":new Sint(20053),
            "dropID":new Sint(57)
         }]);
         this.waveDic["TOWER014"] = new BossWaveVO([{
            "id":new Sint(4002),
            "attID":new Sint(40022),
            "aiID":new Sint(40021),
            "dropID":new Sint(79)
         },{
            "id":new Sint(4003),
            "attID":new Sint(40032),
            "aiID":new Sint(40031),
            "dropID":new Sint(57)
         }]);
         this.waveDic["TOWER015"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2008),
            "attID":new Sint(20084),
            "aiID":new Sint(20082),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20084),
            "aiID":new Sint(20082),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20084),
            "aiID":new Sint(20082),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20084),
            "aiID":new Sint(20082),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20084),
            "aiID":new Sint(20082),
            "dropID":new Sint(57)
         }]);
         this.waveDic["TOWER016"] = new BossWaveVO([{
            "id":new Sint(4004),
            "attID":new Sint(40043),
            "aiID":new Sint(40041),
            "dropID":new Sint(79)
         }]);
         this.waveDic["TOWER017"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2013),
            "attID":new Sint(20132),
            "aiID":new Sint(20131),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20132),
            "aiID":new Sint(20131),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20132),
            "aiID":new Sint(20131),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20142),
            "aiID":new Sint(20141),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20132),
            "aiID":new Sint(20131),
            "dropID":new Sint(57)
         }]);
         this.waveDic["TOWER018"] = new BossWaveVO([{
            "id":new Sint(3015),
            "attID":new Sint(30153),
            "aiID":new Sint(30152),
            "dropID":new Sint(79)
         },{
            "id":new Sint(3019),
            "attID":new Sint(30192),
            "aiID":new Sint(30191),
            "dropID":new Sint(57)
         }]);
         this.waveDic["TOWER019"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2016),
            "attID":new Sint(20164),
            "aiID":new Sint(20161),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20164),
            "aiID":new Sint(20161),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20164),
            "aiID":new Sint(20161),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20124),
            "aiID":new Sint(20122),
            "dropID":new Sint(57)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20164),
            "aiID":new Sint(20161),
            "dropID":new Sint(57)
         }]);
         this.waveDic["TOWER0110"] = new BossWaveVO([{
            "id":new Sint(3017),
            "attID":new Sint(30172),
            "aiID":new Sint(30171),
            "dropID":new Sint(57)
         },{
            "id":new Sint(3018),
            "attID":new Sint(30182),
            "aiID":new Sint(30181),
            "dropID":new Sint(58)
         }]);
         this.waveDic["TOWER021"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2017),
            "attID":new Sint(20172),
            "aiID":new Sint(20171),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20172),
            "aiID":new Sint(20171),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20172),
            "aiID":new Sint(20171),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20172),
            "aiID":new Sint(20171),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20172),
            "aiID":new Sint(20171),
            "dropID":new Sint(59)
         }]);
         this.waveDic["TOWER022"] = new BossWaveVO([{
            "id":new Sint(3021),
            "attID":new Sint(30212),
            "aiID":new Sint(30212),
            "dropID":new Sint(80)
         }]);
         this.waveDic["TOWER023"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2021),
            "attID":new Sint(20212),
            "aiID":new Sint(20211),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20212),
            "aiID":new Sint(20211),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20191),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20191),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20212),
            "aiID":new Sint(20211),
            "dropID":new Sint(59)
         }]);
         this.waveDic["TOWER024"] = new BossWaveVO([{
            "id":new Sint(3023),
            "attID":new Sint(30232),
            "aiID":new Sint(30231),
            "dropID":new Sint(80)
         }]);
         this.waveDic["TOWER025"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2018),
            "attID":new Sint(20182),
            "aiID":new Sint(20181),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20182),
            "aiID":new Sint(20181),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20212),
            "aiID":new Sint(20211),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20191),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20212),
            "aiID":new Sint(20211),
            "dropID":new Sint(59)
         }]);
         this.waveDic["TOWER026"] = new BossWaveVO([{
            "id":new Sint(4005),
            "attID":new Sint(40053),
            "aiID":new Sint(40051),
            "dropID":new Sint(80)
         },{
            "id":new Sint(3020),
            "attID":new Sint(30202),
            "aiID":new Sint(30202),
            "dropID":new Sint(59)
         }]);
         this.waveDic["TOWER027"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2023),
            "attID":new Sint(20232),
            "aiID":new Sint(20231),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20221),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20202),
            "aiID":new Sint(20201),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20191),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20212),
            "aiID":new Sint(20211),
            "dropID":new Sint(59)
         }]);
         this.waveDic["TOWER028"] = new BossWaveVO([{
            "id":new Sint(3024),
            "attID":new Sint(30242),
            "aiID":new Sint(30241),
            "dropID":new Sint(80)
         },{
            "id":new Sint(3025),
            "attID":new Sint(30252),
            "aiID":new Sint(30251),
            "dropID":new Sint(59)
         }]);
         this.waveDic["TOWER029"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2023),
            "attID":new Sint(20232),
            "aiID":new Sint(20231),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20202),
            "aiID":new Sint(20201),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20212),
            "aiID":new Sint(20211),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20191),
            "dropID":new Sint(59)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20232),
            "aiID":new Sint(20231),
            "dropID":new Sint(59)
         }]);
         this.waveDic["TOWER0210"] = new BossWaveVO([{
            "id":new Sint(3026),
            "attID":new Sint(30262),
            "aiID":new Sint(30261),
            "dropID":new Sint(59)
         },{
            "id":new Sint(3027),
            "attID":new Sint(30272),
            "aiID":new Sint(30271),
            "dropID":new Sint(60)
         }]);
         this.waveDic["TOWER101"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20127),
            "aiID":new Sint(20121),
            "dropID":new Sint(131)
         }]);
         this.waveDic["TOWER102"] = new BossWaveVO([{
            "id":new Sint(4006),
            "attID":new Sint(40062),
            "aiID":new Sint(40062),
            "dropID":new Sint(132)
         }]);
         this.waveDic["TOWER103"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20234),
            "aiID":new Sint(20231),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20193),
            "aiID":new Sint(20191),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20127),
            "aiID":new Sint(20121),
            "dropID":new Sint(131)
         }]);
         this.waveDic["TOWER104"] = new BossWaveVO([{
            "id":new Sint(4007),
            "attID":new Sint(40072),
            "aiID":new Sint(40072),
            "dropID":new Sint(132)
         }]);
         this.waveDic["TOWER105"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20234),
            "aiID":new Sint(20231),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20193),
            "aiID":new Sint(20191),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20127),
            "aiID":new Sint(20121),
            "dropID":new Sint(131)
         }]);
         this.waveDic["TOWER106"] = new BossWaveVO([{
            "id":new Sint(4008),
            "attID":new Sint(40082),
            "aiID":new Sint(40082),
            "dropID":new Sint(132)
         }]);
         this.waveDic["TOWER107"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20127),
            "aiID":new Sint(20121),
            "dropID":new Sint(131)
         }]);
         this.waveDic["TOWER108"] = new BossWaveVO([{
            "id":new Sint(3032),
            "attID":new Sint(30322),
            "aiID":new Sint(30322),
            "dropID":new Sint(132)
         }]);
         this.waveDic["TOWER109"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20193),
            "aiID":new Sint(20191),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20224),
            "aiID":new Sint(20221),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20234),
            "aiID":new Sint(20231),
            "dropID":new Sint(131)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20127),
            "aiID":new Sint(20121),
            "dropID":new Sint(131)
         }]);
         this.waveDic["TOWER1010"] = new BossWaveVO([{
            "id":new Sint(3028),
            "attID":new Sint(30283),
            "aiID":new Sint(30282),
            "dropID":new Sint(133)
         },{
            "id":new Sint(3029),
            "attID":new Sint(30292),
            "aiID":new Sint(30292),
            "dropID":new Sint(132)
         }]);
         this.waveDic["TOWER111"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20242),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20242),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20242),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20242),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20242),
            "dropID":new Sint(167)
         }]);
         this.waveDic["TOWER112"] = new BossWaveVO([{
            "id":new Sint(3034),
            "attID":new Sint(30342),
            "aiID":new Sint(30342),
            "dropID":new Sint(168)
         }]);
         this.waveDic["TOWER113"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2025),
            "attID":new Sint(20252),
            "aiID":new Sint(20252),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20252),
            "aiID":new Sint(20252),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20252),
            "aiID":new Sint(20252),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20252),
            "aiID":new Sint(20252),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20252),
            "aiID":new Sint(20252),
            "dropID":new Sint(167)
         }]);
         this.waveDic["TOWER114"] = new BossWaveVO([{
            "id":new Sint(3035),
            "attID":new Sint(30352),
            "aiID":new Sint(30352),
            "dropID":new Sint(168)
         }]);
         this.waveDic["TOWER115"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2028),
            "attID":new Sint(20282),
            "aiID":new Sint(20282),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20282),
            "aiID":new Sint(20282),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20282),
            "aiID":new Sint(20282),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20282),
            "aiID":new Sint(20282),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20282),
            "aiID":new Sint(20282),
            "dropID":new Sint(167)
         }]);
         this.waveDic["TOWER116"] = new BossWaveVO([{
            "id":new Sint(3042),
            "attID":new Sint(30422),
            "aiID":new Sint(30422),
            "dropID":new Sint(168)
         }]);
         this.waveDic["TOWER117"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2029),
            "attID":new Sint(20292),
            "aiID":new Sint(20292),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20292),
            "aiID":new Sint(20292),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20292),
            "aiID":new Sint(20292),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20292),
            "aiID":new Sint(20292),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20292),
            "aiID":new Sint(20292),
            "dropID":new Sint(167)
         }]);
         this.waveDic["TOWER118"] = new BossWaveVO([{
            "id":new Sint(3043),
            "attID":new Sint(30432),
            "aiID":new Sint(30432),
            "dropID":new Sint(168)
         }]);
         this.waveDic["TOWER119"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20312),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20312),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20312),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20312),
            "dropID":new Sint(167)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20312),
            "dropID":new Sint(167)
         }]);
         this.waveDic["TOWER1110"] = new BossWaveVO([{
            "id":new Sint(3037),
            "attID":new Sint(30372),
            "aiID":new Sint(30372),
            "dropID":new Sint(168)
         },{
            "id":new Sint(3038),
            "attID":new Sint(30382),
            "aiID":new Sint(30382),
            "dropID":new Sint(169)
         }]);
         this.waveDic["TOWER121"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2032),
            "attID":new Sint(20322),
            "aiID":new Sint(20322),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20322),
            "aiID":new Sint(20322),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20322),
            "aiID":new Sint(20322),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20322),
            "aiID":new Sint(20322),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20322),
            "aiID":new Sint(20322),
            "dropID":new Sint(170)
         }]);
         this.waveDic["TOWER122"] = new BossWaveVO([{
            "id":new Sint(3039),
            "attID":new Sint(30392),
            "aiID":new Sint(30392),
            "dropID":new Sint(171)
         }]);
         this.waveDic["TOWER123"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2033),
            "attID":new Sint(20332),
            "aiID":new Sint(20332),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20332),
            "aiID":new Sint(20332),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20332),
            "aiID":new Sint(20332),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20332),
            "aiID":new Sint(20332),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20332),
            "aiID":new Sint(20332),
            "dropID":new Sint(170)
         }]);
         this.waveDic["TOWER124"] = new BossWaveVO([{
            "id":new Sint(3040),
            "attID":new Sint(30402),
            "aiID":new Sint(30402),
            "dropID":new Sint(171)
         }]);
         this.waveDic["TOWER125"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2036),
            "attID":new Sint(20362),
            "aiID":new Sint(20362),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20362),
            "aiID":new Sint(20362),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20362),
            "aiID":new Sint(20362),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20362),
            "aiID":new Sint(20362),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20362),
            "aiID":new Sint(20362),
            "dropID":new Sint(170)
         }]);
         this.waveDic["TOWER126"] = new BossWaveVO([{
            "id":new Sint(3044),
            "attID":new Sint(30442),
            "aiID":new Sint(30442),
            "dropID":new Sint(171)
         }]);
         this.waveDic["TOWER127"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(170)
         }]);
         this.waveDic["TOWER128"] = new BossWaveVO([{
            "id":new Sint(3045),
            "attID":new Sint(30452),
            "aiID":new Sint(30452),
            "dropID":new Sint(171)
         }]);
         this.waveDic["TOWER129"] = new EnemyWaveVO(0,5,[{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20412),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20412),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20412),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20412),
            "dropID":new Sint(170)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20412),
            "dropID":new Sint(170)
         }]);
         this.waveDic["TOWER1210"] = new BossWaveVO([{
            "id":new Sint(3046),
            "attID":new Sint(30462),
            "aiID":new Sint(30462),
            "dropID":new Sint(171)
         },{
            "id":new Sint(3047),
            "attID":new Sint(30472),
            "aiID":new Sint(30472),
            "dropID":new Sint(172)
         }]);
      }
      
      public function findWave(param1:int, param2:int, param3:int) : Object
      {
         return this.waveDic["TOWER" + param1 + param2 + param3];
      }
      
      public function findTotal(param1:int, param2:int) : int
      {
         var _loc3_:int = 0;
         var _loc5_:String = null;
         var _loc4_:String = "TOWER" + param1 + param2;
         for(_loc5_ in this.waveDic)
         {
            if(_loc5_.search(_loc4_) != -1)
            {
               _loc3_++;
            }
         }
         return _loc3_;
      }
   }
}

