package mogames.gameData.shop.vo
{
   import file.GoodConfig;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   import utils.TxtUtil;
   
   public class ShopZongziVO extends BaseShopVO
   {
      private var _zzID:Sint = new Sint(10016);
      
      public function ShopZongziVO(param1:int, param2:int, param3:int)
      {
         super(param1,param2,param3);
         priceType = 2;
      }
      
      override public function usePrice(param1:int) : void
      {
         BagProxy.instance().useItems(this._zzID.v,param1 * price.v);
      }
      
      override public function checkLack(param1:int = 1) : LackVO
      {
         if(BagProxy.instance().getGoodNum(this._zzID.v) < param1 * price.v)
         {
            return new LackVO("背包月饼不足！");
         }
         return null;
      }
      
      override public function askStr(param1:int) : String
      {
         return "确定消耗" + TxtUtil.setColor("月饼X" + price.v * param1) + "兑换" + param1 + "个" + TxtUtil.setColor(GoodConfig.instance().getGoodName(goodID.v),"99ff00") + "？";
      }
   }
}

