package citrus.view
{
   public interface ISpriteView
   {
      function handleArtReady(param1:ICitrusArt) : void;
      
      function handleArtChanged(param1:ICitrusArt) : void;
      
      function getBody() : *;
      
      function get x() : Number;
      
      function get y() : Number;
      
      function get z() : Number;
      
      function get width() : Number;
      
      function get height() : Number;
      
      function get depth() : Number;
      
      function get velocity() : Array;
      
      function get parallaxX() : Number;
      
      function get parallaxY() : Number;
      
      function get rotation() : Number;
      
      function get group() : uint;
      
      function get visible() : Boolean;
      
      function get touchable() : Boolean;
      
      function get view() : *;
      
      function get art() : ICitrusArt;
      
      function get animation() : String;
      
      function get inverted() : Boolean;
      
      function get offsetX() : Number;
      
      function get offsetY() : Number;
      
      function get registration() : String;
   }
}

