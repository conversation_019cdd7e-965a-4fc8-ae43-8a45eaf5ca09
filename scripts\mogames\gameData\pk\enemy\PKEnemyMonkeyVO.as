package mogames.gameData.pk.enemy
{
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.heroSkill.sunwukong.SkillHYJJVO;
   import mogames.gameData.heroSkill.sunwukong.SkillQJSLVO;
   
   public class PKEnemyMonkeyVO extends PKEnemyVO
   {
      public function PKEnemyMonkeyVO(param1:int)
      {
         allSkills = [];
         allSkills.push(new HurtSkillVO(1001,this,0));
         allSkills.push(new SkillQJSLVO(1002,this,1));
         allSkills.push(new SkillHYJJVO(1003,this,2));
         allSkills.push(new HurtSkillVO(1004,this,3));
         allSkills.push(new HurtSkillVO(1005,this,4));
         super(1001,param1);
      }
   }
}

