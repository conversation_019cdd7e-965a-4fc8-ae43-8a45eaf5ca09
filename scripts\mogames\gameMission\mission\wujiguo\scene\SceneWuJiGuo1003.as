package mogames.gameMission.mission.wujiguo.scene
{
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.wujiguo.WuJiGuoMission;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameTask.Task11013;
   import mogames.gameUI.fuben.shuilao.ShuiLaoEnterModule;
   
   public class SceneWuJiGuo1003 extends BaseScene
   {
      private var _npc:CitrusMCSprite;
      
      private var _lieKing:CitrusMCSprite;
      
      private var _standKing:CitrusMCSprite;
      
      private var _task:Task11013;
      
      public function SceneWuJiGuo1003(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         sceneType = 1;
         EffectManager.instance().playBGM("BGM_DANGER3");
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.initTask();
         this.initNPC();
         this.initLieKing();
      }
      
      private function initLieKing() : void
      {
         if(this.isUnlock)
         {
            return;
         }
         this._lieKing = new CitrusMCSprite("MC_LIE_KING_CLIP",{
            "width":144,
            "height":61,
            "x":1020,
            "y":276,
            "group":2
         });
         if(this._task.isActive)
         {
            this._lieKing.changeAnimation("idle1",true);
            this._lieKing.addMouseListener(this._task.saveKing);
         }
         else
         {
            this._lieKing.changeAnimation("idle0",true);
         }
         Layers.addCEChild(this._lieKing);
      }
      
      private function handlerSave() : void
      {
         this._lieKing.kill = true;
         this._standKing = new CitrusMCSprite("MC_STAND_KING_CLIP",{
            "width":80,
            "height":132,
            "x":1110,
            "y":390,
            "group":2
         });
         this._standKing.changeAnimation("idle",true);
         Layers.addCEChild(this._standKing);
         this.updateNPC();
      }
      
      private function initTask() : void
      {
         if(TaskProxy.instance().hasTask(11012) && !TaskProxy.instance().isTaskFinish(11012))
         {
            showDialog("STORY0043");
            TaskProxy.instance().setComplete(11012);
         }
         if(this.isUnlock)
         {
            return;
         }
         this._task = new Task11013(this.handlerSave);
      }
      
      private function initNPC() : void
      {
         this._npc = new CitrusMCSprite("MC_JING_LONG_WANG_CLIP",{
            "width":44,
            "height":116,
            "x":1335,
            "y":400,
            "group":2
         });
         this._npc.changeAnimation("idle0",true);
         this._npc.addMouseListener(this.onLongWang);
         add(this._npc);
         this.updateNPC();
      }
      
      private function updateNPC() : void
      {
         if(this.isUnlock)
         {
            this._npc.changeAnimation("idle1",true);
         }
      }
      
      private function onLongWang() : void
      {
         if(!this.isUnlock)
         {
            this._task.onClickLongWang();
         }
         else
         {
            ShuiLaoEnterModule.instance().init((_mission as WuJiGuoMission).enterShuiLao);
         }
      }
      
      private function get isUnlock() : Boolean
      {
         return FlagProxy.instance().isComplete(401);
      }
      
      override public function destroy() : void
      {
         this._lieKing = null;
         this._standKing = null;
         this._task = null;
         this._npc = null;
         super.destroy();
      }
   }
}

