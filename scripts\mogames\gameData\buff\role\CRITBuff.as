package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class CRITBuff extends BaseBuff
   {
      public function CRITBuff()
      {
         super(1021);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         _roleVO.skillCRIT.v += _buffVO.argDic.arg0;
         _roleVO.updateCRIT();
         if(_role.isReady)
         {
            EffectManager.instance().addHeadWord("暴击提升" + _buffVO.argDic.arg0 * 0.1 + "%",_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override protected function onEnd() : void
      {
         _roleVO.skillPDEF.v -= _buffVO.argDic.arg0;
         _roleVO.updateCRIT();
         destroy();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         if(!_buffVO.argDic.skin)
         {
            return null;
         }
         _skin.setupSequence(AssetManager.findAnimation(_buffVO.argDic.skin),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

