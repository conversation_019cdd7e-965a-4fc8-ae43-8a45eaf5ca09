package mogames.gameMission.mission.tiangong
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.enemy.*;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameSystem.CitrusTimer;
   
   public class SceneTianGong extends BossScene
   {
      private var _currentWave:int = 0;
      private var _totalWaves:int = 15;
      private var _waveTimer:CitrusTimer;
      private var _bossList:Array;
      
      public function SceneTianGong(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_BATTLE1");
         setWinPosition(new Rectangle(200,170,480,140),new Point(700,386));
         this._waveTimer = new CitrusTimer();
         this.initBossList();
      }
      
      private function initBossList() : void
      {
         // 天宫boss rush - 14波各种boss + 最后如来佛祖
         this._bossList = [
            // 第1波：寅将军
            {bossClass: BOSSYinJiangJun, dataID: 30021, name: "寅将军"},
            // 第2波：沙僧
            {bossClass: BOSSShaSeng, dataID: 30311, name: "沙僧"},
            // 第3波：小白龙
            {bossClass: BOSSXiaoBaiLong, dataID: 30321, name: "小白龙"},
            // 第4波：黄风怪
            {bossClass: BOSSHuangFengGuai, dataID: 30051, name: "黄风怪"},
            // 第5波：红孩儿
            {bossClass: BOSSHongHaiEr, dataID: 30121, name: "红孩儿"},
            // 第6波：虎大仙
            {bossClass: BOSSHuDaXian, dataID: 30191, name: "虎大仙"},
            // 第7波：灵感王
            {bossClass: BOSSLingGanWang, dataID: 30231, name: "灵感王"},
            // 第8波：金角大王
            {bossClass: BOSSJinJiaoDaWang, dataID: 30251, name: "金角大王"},
            // 第9波：银角大王
            {bossClass: BOSSYinJiaoDaWang, dataID: 30261, name: "银角大王"},
            // 第10波：白象精
            {bossClass: BOSSBaiXiangJing, dataID: 30401, name: "白象精"},
            // 第11波：大鹏精
            {bossClass: BOSSDaPengJing, dataID: 30411, name: "大鹏精"},
            // 第12波：牛魔王
            {bossClass: BOSSNiuMoWang, dataID: 30421, name: "牛魔王"},
            // 第13波：九尾狐
            {bossClass: BOSSJiuWeiHu, dataID: 30111, name: "九尾狐"},
            // 第14波：蝎子精
            {bossClass: BOSSXieZiJing, dataID: 30431, name: "蝎子精"},
            // 第15波：如来佛祖（最终boss）
            {bossClass: BOSSFoShou, dataID: 30011, name: "如来佛祖"}
         ];
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         _mission.cleanLoadUI();
         MiniMsgMediator.instance().showAutoMsg("天宫试炼开始！第1波：" + this._bossList[0].name);
         this.addBOSS();
         this.startBossBattle();
      }

      override protected function addBOSS() : void
      {
         this.startNextWave();
      }
      
      private function startNextWave() : void
      {
         if(this._currentWave >= this._totalWaves)
         {
            // 所有波次完成
            this.handlerAllWavesComplete();
            return;
         }
         
         var bossInfo:Object = this._bossList[this._currentWave];
         var BossClass:Class = bossInfo.bossClass;
         
         // 创建当前波次的boss
         _boss = new BossClass();
         _boss.x = 1200;
         _boss.y = 200;
         _boss.initData(bossInfo.dataID, bossInfo.dataID);
         _boss.target = _mission.onePlayer;
         add(_boss);

         // 设置boss事件监听
         _boss.onRole.add(this.listenBOSS);

         // 显示当前波次信息
         var waveNum:int = this._currentWave + 1;
         if(waveNum == this._totalWaves)
         {
            MiniMsgMediator.instance().showAutoMsg("最终试炼：" + bossInfo.name + "！", 480, 200);
         }
         else
         {
            MiniMsgMediator.instance().showAutoMsg("第" + waveNum + "波：" + bossInfo.name, 480, 200);
         }

         // 启动boss战斗
         setHeroEnable(true);
         _boss.activeEnemy(false);
         _boss.aiEnabled = true;
      }
      
      override protected function listenBOSS(param1:String) : void
      {
         if(param1 == "dead")
         {
            this._currentWave++;
            
            if(this._currentWave >= this._totalWaves)
            {
               // 最后一波完成，胜利
               this.handlerWin();
            }
            else
            {
               // 准备下一波
               MiniMsgMediator.instance().showAutoMsg("击败！准备下一波...", 480, 200);
               this._waveTimer.setTimeOut(2, this.startNextWave);
            }
         }
      }
      
      private function handlerAllWavesComplete() : void
      {
         MiniMsgMediator.instance().showAutoMsg("恭喜！天宫试炼全部完成！", 480, 200);
         this.handlerWin();
      }
      
      override protected function handlerWin() : void
      {
         if(this._isWin)
         {
            return;
         }
         this._isWin = true;
         
         setRoleEnable(false);
         MiniMsgMediator.instance().showAutoMsg("大闹天宫成功！获得天宫神器！", 480, 200);
         
         // 设置天宫完成标志
         FlagProxy.instance().setValue(1050, 1);
         
         this._winTimer.setTimeOut(3, function():void {
            showDrop();
         });
      }
      
      override public function destroy() : void
      {
         if(this._waveTimer)
         {
            this._waveTimer.destroy();
            this._waveTimer = null;
         }
         super.destroy();
      }
   }
}
