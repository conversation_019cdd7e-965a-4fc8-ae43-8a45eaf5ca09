package mogames.gameMission.mission.leiyinsi
{
   import citrus.core.CitrusObject;
   import citrus.objects.CitrusSprite;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.enemy.BaseEnemy;
   import mogames.gameSystem.CitrusTimer;
   import utils.MathUtil;
   
   public class TrapFoXiang extends CitrusSprite
   {
      private var _mc:CitrusMC;
      
      private var _timer:CitrusTimer;
      
      private var _keep:CitrusTimer;
      
      public function TrapFoXiang()
      {
         super("TrapFoXiang",{
            "width":274,
            "height":310,
            "group":2
         });
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("TRAP_FO_XIANG_CLIP"));
         this._mc.changeAnimation("stand",true);
         this.view = this._mc;
         this._timer = new CitrusTimer();
         this._keep = new CitrusTimer();
      }
      
      public function start() : void
      {
         this.changeBuff();
      }
      
      private function restart() : void
      {
         this._mc.changeAnimation("stand",true);
         this._timer.setTimeOut(20,this.changeBuff);
      }
      
      public function stop() : void
      {
         this._mc.changeAnimation("stand",true);
         this._timer.pause();
         this._keep.pause();
      }
      
      private function changeBuff() : void
      {
         var _loc3_:BaseEnemy = null;
         var _loc1_:Vector.<CitrusObject> = _ce.state.getObjectsByType(BaseEnemy);
         var _loc2_:int = int(MathUtil.checkOdds(500));
         for each(_loc3_ in _loc1_)
         {
            if(!(_loc3_.isDead || !_loc3_.aiEnabled))
            {
               if(_loc2_ == 0)
               {
                  _loc3_.addBuff(new BuffVO(1018,9,null));
               }
               else
               {
                  _loc3_.addBuff(new BuffVO(1019,9,null));
               }
            }
         }
         this._mc.changeAnimation("attack" + _loc2_,true);
         this._keep.setTimeOut(10,this.restart);
      }
      
      override public function destroy() : void
      {
         this._timer.destroy();
         this._timer = null;
         this._keep.destroy();
         this._keep = null;
         this._mc.destroy();
         this._mc = null;
         super.destroy();
      }
   }
}

