package mogames.gameData.tower
{
   import mogames.gameData.base.Sint;
   import utils.TxtUtil;
   
   public class TowerFloorVO
   {
      public var tower:Sint;
      
      public var floor:Sint;
      
      public var time:Sint;
      
      public var hit:Sint;
      
      public var needs:Array;
      
      public var drops:Array;
      
      public function TowerFloorVO(param1:int, param2:int, param3:Array, param4:Array)
      {
         super();
         this.tower = new Sint(param1);
         this.floor = new Sint(param2);
         this.time = new Sint();
         this.hit = new Sint();
         this.needs = param3;
         this.drops = param4;
      }
      
      public function setTime(param1:int) : void
      {
         if(this.time.v == 0)
         {
            this.time.v = param1;
         }
         if(this.time.v < param1)
         {
            return;
         }
         this.time.v = param1;
      }
      
      public function setHit(param1:int) : void
      {
         if(this.hit.v > param1)
         {
            return;
         }
         this.hit.v = param1;
      }
      
      public function setGetBoss(param1:int) : void
      {
         var _loc2_:BossNeedVO = this.findBossFlag(param1);
         if(!_loc2_)
         {
            return;
         }
         _loc2_.setGet();
      }
      
      public function get isComplete() : Boolean
      {
         var _loc1_:BossNeedVO = null;
         for each(_loc1_ in this.needs)
         {
            if(!_loc1_.hasGet)
            {
               return false;
            }
         }
         return true;
      }
      
      public function findBossFlag(param1:int) : BossNeedVO
      {
         var _loc2_:BossNeedVO = null;
         for each(_loc2_ in this.needs)
         {
            if(_loc2_.bossID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get timeStr() : String
      {
         return TxtUtil.formatTime(this.time.v);
      }
      
      public function get hurtStr() : String
      {
         return this.hit.v + "次";
      }
      
      public function get saveData() : String
      {
         var _loc2_:BossNeedVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.needs)
         {
            _loc1_.push(_loc2_.saveData);
         }
         return [this.tower.v,this.floor.v,this.time.v,this.hit.v,_loc1_.join("A")].join("H");
      }
      
      public function set loadData(param1:Array) : void
      {
         var _loc5_:BossNeedVO = null;
         var _loc6_:Array = null;
         this.time.v = int(param1[2]);
         this.hit.v = int(param1[3]);
         var _loc2_:Array = String(param1[4]).split("A");
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            _loc6_ = String(_loc2_[_loc3_]).split("B");
            _loc5_ = this.findBossFlag(int(_loc6_[0]));
            if(_loc5_)
            {
               _loc5_.isGet = int(_loc6_[1]);
            }
            _loc3_++;
         }
      }
   }
}

