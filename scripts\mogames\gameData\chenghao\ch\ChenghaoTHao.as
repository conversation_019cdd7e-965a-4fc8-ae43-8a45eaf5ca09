package mogames.gameData.chenghao.ch
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.chenghao.BaseChenghaoVO;
   
   public class ChenghaoTHao extends BaseChenghaoVO
   {
      public function ChenghaoTHao(param1:int, param2:int, param3:String)
      {
         super(param1,param2,param3);
      }
      
      override public function get hasGet() : Boolean
      {
         return MasterProxy.instance().sysMoney.v >= 5000;
      }
   }
}

