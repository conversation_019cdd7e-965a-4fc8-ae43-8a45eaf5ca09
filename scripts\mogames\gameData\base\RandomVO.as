package mogames.gameData.base
{
   import mogames.gameData.ConstData;
   
   public class RandomVO
   {
      public var min:Sint;
      
      public var max:Sint;
      
      public function RandomVO(param1:int, param2:int)
      {
         super();
         this.min = new Sint(param1);
         this.max = new Sint(param2);
      }
      
      public function randomValue(param1:<PERSON><PERSON><PERSON> = false) : Number
      {
         if(param1 || this.min.v == this.max.v)
         {
            return this.max.v;
         }
         return Math.random() * (this.max.v + ConstData.DATA_NUM1.v - this.min.v) + this.min.v;
      }
   }
}

