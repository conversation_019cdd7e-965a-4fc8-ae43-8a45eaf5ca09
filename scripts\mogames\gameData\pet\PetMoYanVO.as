package mogames.gameData.pet
{
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetHurtSkillVO;
   
   public class PetMoYanVO extends PetGameVO
   {
      public function PetMoYanVO()
      {
         allSkills = this.newActiveSkills();
         super(1101);
      }
      
      override public function newActiveSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new PetHurtSkillVO(11011,this));
         _loc1_.push(new PetHurtSkillVO(11012,this));
         _loc1_.push(new PetHurtSkillVO(11013,this));
         _loc1_.push(new PetHurtSkillVO(11014,this));
         _loc1_.push(new PetHurtSkillVO(11015,this));
         _loc1_.push(new PetHurtSkillVO(11016,this));
         return _loc1_;
      }
   }
}

