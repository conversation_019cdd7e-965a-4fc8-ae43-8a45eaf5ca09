package mogames.gameData.shop.vo
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.forge.NeedVO;
   
   public class GameCostVO extends BaseCostVO
   {
      private var _needGold:Sint;
      
      private var _needList:Array;
      
      public function GameCostVO(param1:int, param2:int, param3:Array = null)
      {
         super(param1);
         this._needGold = new Sint(param2);
         this._needList = param3;
      }
      
      override public function useStuff() : void
      {
         var _loc1_:NeedVO = null;
         MasterProxy.instance().changeGold(-this._needGold.v);
         for each(_loc1_ in this._needList)
         {
            BagProxy.instance().useItems(_loc1_.id.v,_loc1_.num.v);
         }
      }
      
      override public function get isLack() : LackVO
      {
         var _loc1_:NeedVO = null;
         if(MasterProxy.instance().gameGold.v < this._needGold.v)
         {
            return new LackVO("铜钱不足！");
         }
         for each(_loc1_ in this._needList)
         {
            if(_loc1_.isLack)
            {
               return new LackVO(_loc1_.needName + "不足！");
            }
         }
         return null;
      }
      
      override public function get needStr() : String
      {
         var _loc2_:NeedVO = null;
         var _loc1_:Array = [];
         if(this._needGold.v > 0)
         {
            _loc1_.push("铜钱X" + this._needGold.v);
         }
         for each(_loc2_ in this._needList)
         {
            _loc1_.push(_loc2_.askStr);
         }
         return _loc1_.join(",");
      }
   }
}

