package mogames.gameData.mall.vo
{
   import file.GoodConfig;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import utils.TxtUtil;
   
   public class MallTicketVO extends BaseMallVO
   {
      public function MallTicketVO(param1:int, param2:BaseRewardVO, param3:int, param4:int = 0)
      {
         super(param1,param2,param3,param4);
         priceType = 2;
      }
      
      override public function checkLack(param1:int = 1) : LackVO
      {
         return MasterProxy.instance().checkLack(price.v * param1,2);
      }
      
      override public function askStr(param1:int) : String
      {
         return "确定花费" + TxtUtil.setColor("银锭X" + price.v * param1) + "购买" + param1 + "个" + TxtUtil.setColor(GoodConfig.instance().getGoodName(mallGood.id.v),"99ff00") + "？";
      }
   }
}

