package mogames.gameData.good
{
   import file.ArgConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.good.constvo.ConstEquipVO;
   
   public class PetEquipVO extends GameEquipVO
   {
      public function PetEquipVO(param1:ConstEquipVO)
      {
         super(param1);
         _hparg.v = 0.13;
         _atkarg.v = 0.02;
         _pdefarg.v = 0.03;
         _mdefarg.v = 0.03;
      }
      
      override public function setLevel(param1:int) : void
      {
         level.v = param1;
         if(baseHP.v != 0)
         {
            totalHP.v = level.v != 0 ? int(Math.ceil(baseHP.v * (ConstData.DATA_NUM1.v + Math.pow(level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _hparg.v) * _quality.value.v)) : int(Math.ceil(baseHP.v * _quality.value.v));
         }
         if(baseMP.v != 0)
         {
            totalMP.v = level.v != 0 ? int(Math.ceil(baseMP.v * (ConstData.DATA_NUM1.v + Math.pow(level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _mparg.v) * _quality.value.v)) : int(Math.ceil(baseMP.v * _quality.value.v));
         }
         if(baseATK.v != 0)
         {
            totalATK.v = level.v != 0 ? int(Math.ceil(baseATK.v * (ConstData.DATA_NUM1.v + Math.pow(level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _atkarg.v) * _quality.value.v)) : int(Math.ceil(baseATK.v * _quality.value.v));
         }
         if(basePDEF.v != 0)
         {
            totalPDEF.v = level.v != 0 ? int(Math.ceil(basePDEF.v * (ConstData.DATA_NUM1.v + Math.pow(level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _pdefarg.v) * _quality.value.v)) : int(Math.ceil(basePDEF.v * _quality.value.v));
         }
         if(baseMDEF.v != 0)
         {
            totalMDEF.v = level.v != 0 ? int(Math.ceil(baseMDEF.v * (ConstData.DATA_NUM1.v + Math.pow(level.v + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _mdefarg.v) * _quality.value.v)) : int(Math.ceil(baseMDEF.v * _quality.value.v));
         }
         if(baseCRIT.v != 0)
         {
            totalCRIT.v = level.v != 0 ? int(Math.ceil((baseCRIT.v * (ConstData.DATA_NUM1.v + Math.pow(level.v,ConstData.DATA_NUM2.v) * _critarg.v) + level.v) * _quality.value.v)) : int(Math.ceil(baseCRIT.v * _quality.value.v));
         }
         if(baseMISS.v != 0)
         {
            totalMISS.v = level.v != 0 ? int(Math.ceil((baseMISS.v * (ConstData.DATA_NUM1.v + Math.pow(level.v,ConstData.DATA_NUM2.v) * _missarg.v) + level.v) * _quality.value.v)) : int(Math.ceil(baseMISS.v * _quality.value.v));
         }
         totalLUCK.v = baseLUCK.v;
         countScore();
         if(owner)
         {
            owner.updateEquip();
         }
      }
      
      override public function get forgeInfor() : String
      {
         var _loc1_:int = level.v + 1;
         var _loc2_:Array = [];
         if(baseHP.v != 0)
         {
            _loc2_.push("生命+" + Math.ceil(baseHP.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _hparg.v) * _quality.value.v));
         }
         if(baseMP.v != 0)
         {
            _loc2_.push("法力+" + Math.ceil(baseMP.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _mparg.v) * _quality.value.v));
         }
         if(baseATK.v != 0)
         {
            _loc2_.push("攻击+" + Math.ceil(baseATK.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _atkarg.v) * _quality.value.v));
         }
         if(basePDEF.v != 0)
         {
            _loc2_.push("物防+" + Math.ceil(basePDEF.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _pdefarg.v) * _quality.value.v));
         }
         if(baseMDEF.v != 0)
         {
            _loc2_.push("魔防+" + Math.ceil(baseMDEF.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_ + ConstData.DATA_NUM1.v,ConstData.DATA_NUM2.v) * _mdefarg.v) * _quality.value.v));
         }
         if(baseCRIT.v != 0)
         {
            _loc2_.push("暴击+" + Math.ceil((baseCRIT.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_,ConstData.DATA_NUM2.v) * _critarg.v) + _loc1_) * _quality.value.v));
         }
         if(baseMISS.v != 0)
         {
            _loc2_.push("闪避+" + Math.ceil((baseMISS.v * (ConstData.DATA_NUM1.v + Math.pow(_loc1_,ConstData.DATA_NUM2.v) * _missarg.v) + _loc1_) * _quality.value.v));
         }
         return _loc2_.join("<br>");
      }
      
      override public function set quality(param1:int) : void
      {
         _quality = ArgConfig.instance().findQuality(param1,isFashion);
      }
   }
}

