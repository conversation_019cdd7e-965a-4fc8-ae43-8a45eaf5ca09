package file
{
   import mogames.gameData.forge.NeedVO;
   import mogames.gameData.petEquip.PEHCVO;
   
   public class PEHCConfig
   {
      private static var _instance:PEHCConfig;
      
      private var _hcVec:Vector.<PEHCVO>;
      
      public function PEHCConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : PEHCConfig
      {
         if(!_instance)
         {
            _instance = new PEHCConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._hcVec = new Vector.<PEHCVO>();
         this._hcVec.push(new PEHCVO(50101,46001,10000,[new NeedVO(50101,5),new NeedVO(13104,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50105,46002,10000,[new NeedVO(50105,5),new NeedVO(13306,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50109,46003,10000,[new NeedVO(50109,5),new NeedVO(13117,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50113,46004,10000,[new NeedVO(50113,5),new NeedVO(13118,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50201,46051,50000,[new NeedVO(50201,5),new NeedVO(13120,5),new NeedVO(13122,5)]));
         this._hcVec.push(new PEHCVO(50205,46052,50000,[new NeedVO(50205,5),new NeedVO(13130,5),new NeedVO(13132,5)]));
         this._hcVec.push(new PEHCVO(50102,47001,10000,[new NeedVO(50102,5),new NeedVO(13309,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50106,47002,10000,[new NeedVO(50106,5),new NeedVO(13107,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50110,47003,10000,[new NeedVO(50110,5),new NeedVO(13314,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50114,47004,10000,[new NeedVO(50114,5),new NeedVO(13119,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50202,47051,50000,[new NeedVO(50202,5),new NeedVO(13121,5),new NeedVO(13122,5)]));
         this._hcVec.push(new PEHCVO(50206,47052,50000,[new NeedVO(50206,5),new NeedVO(13131,5),new NeedVO(13132,5)]));
         this._hcVec.push(new PEHCVO(50103,48001,10000,[new NeedVO(50103,5),new NeedVO(13104,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50107,48002,10000,[new NeedVO(50107,5),new NeedVO(13306,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50111,48003,10000,[new NeedVO(50111,5),new NeedVO(13110,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50115,48004,10000,[new NeedVO(50115,5),new NeedVO(13118,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50203,48051,50000,[new NeedVO(50203,5),new NeedVO(13120,5),new NeedVO(13122,5)]));
         this._hcVec.push(new PEHCVO(50207,48052,50000,[new NeedVO(50207,5),new NeedVO(13130,5),new NeedVO(13132,5)]));
         this._hcVec.push(new PEHCVO(50104,49001,10000,[new NeedVO(50104,5),new NeedVO(13309,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50108,49002,10000,[new NeedVO(50108,5),new NeedVO(13107,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50112,49003,10000,[new NeedVO(50112,5),new NeedVO(13116,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50116,49004,10000,[new NeedVO(50116,5),new NeedVO(13119,5),new NeedVO(13108,5)]));
         this._hcVec.push(new PEHCVO(50204,49051,50000,[new NeedVO(50204,5),new NeedVO(13121,5),new NeedVO(13122,5)]));
         this._hcVec.push(new PEHCVO(50208,49052,50000,[new NeedVO(50208,5),new NeedVO(13131,5),new NeedVO(13132,5)]));
      }
      
      public function fincHCVO(param1:int) : PEHCVO
      {
         var _loc2_:PEHCVO = null;
         for each(_loc2_ in this._hcVec)
         {
            if(_loc2_.clipID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

