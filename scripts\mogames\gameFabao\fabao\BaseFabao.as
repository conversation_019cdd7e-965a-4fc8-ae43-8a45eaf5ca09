package mogames.gameFabao.fabao
{
   import citrus.objects.CitrusSprite;
   import flash.geom.Point;
   import mogames.AssetManager;
   import mogames.gameData.ConstRole;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.view.FabaoBaseView;
   import mogames.gameRole.co.BaseRole;
   import mogames.gameRole.co.IRole;
   import utils.MethodUtil;
   
   public class BaseFabao extends CitrusSprite implements IRole
   {
      protected var _hitTargets:Array = [];
      
      protected var _targetPos:Point = new Point();
      
      protected var _offset:Point = new Point(50,0);
      
      protected var _fabaoView:FabaoBaseView;
      
      protected var _fabaoVO:GameFabaoVO;
      
      protected var _fabaoData:Object;
      
      protected var _fabaoList:Array;
      
      protected var _controlenabled:Boolean;
      
      public var owner:BaseRole;
      
      public var easing:Point = new Point(0.1,0.1);
      
      public var channel:int;
      
      public function BaseFabao(param1:GameFabaoVO, param2:int, param3:int)
      {
         this._fabaoVO = param1;
         super(param1.constFabao.iname,{
            "width":param2,
            "height":param3,
            "group":4
         });
         registration = "center";
      }
      
      protected function createFabaoData() : void
      {
      }
      
      protected function createView(param1:FabaoBaseView) : void
      {
         this._fabaoView = param1;
         this._fabaoView.setOwner(this);
         this._fabaoView.setBody(AssetManager.newMCRes(this._fabaoVO.constFabao.mcSkin));
         this.view = this._fabaoView;
         MethodUtil.setMousable(view,false);
         this._fabaoView.changeStatus(ConstRole.STAND,true);
         if(this._fabaoVO.isCD)
         {
            MethodUtil.gray(this._fabaoView,true);
         }
      }
      
      override public function update(param1:Number) : void
      {
         this.followTarget();
         this.checkSkill();
         this.checkGray();
         this.checkDead();
         this.checkTime();
         this.updateAnimation();
      }
      
      public function followTarget() : void
      {
         if(!this.owner || this.owner.isDead)
         {
            return;
         }
         inverted = this.owner.inverted;
         if(this.owner.inverted)
         {
            this._targetPos.x = this.owner.x + this.owner.width * 0.5 + this._offset.x + width * 0.5;
         }
         else
         {
            this._targetPos.x = this.owner.x - this.owner.width * 0.5 - this._offset.x - width * 0.5;
         }
         this._targetPos.y = this.owner.y - this.owner.height * 0.2 - this._offset.y - height * 0.5;
         x += (this._targetPos.x - x) * this.easing.x;
         y += (this._targetPos.y - y) * this.easing.y;
      }
      
      private function checkTime() : void
      {
         if(!this._fabaoVO.isCD || this._fabaoView.isSkill)
         {
            return;
         }
         this._fabaoView.showTime(this._fabaoVO.leftTime);
         this._fabaoView.updateTime();
      }
      
      private function checkSkill() : void
      {
         if(!this.owner || this.owner.isDead)
         {
            return;
         }
         if(!this._controlenabled || this._fabaoView.isSkill)
         {
            return;
         }
         if(_ce.input.justDid("FABAO",this.channel))
         {
            if(this._fabaoVO.isCD)
            {
               EffectManager.instance().addHeadWord("法宝冷却中",x,y - height * 0.5);
            }
            else
            {
               this.handlerSkill();
            }
         }
      }
      
      public function handlerSkill() : void
      {
         if(this._fabaoVO.isCD)
         {
            return;
         }
         this._fabaoView.isSkill = true;
         this._fabaoVO.isCD = true;
      }
      
      private function checkGray() : void
      {
         if(!this._fabaoVO.isCD && !this._fabaoView.isSkill)
         {
            MethodUtil.gray(this._fabaoView,false);
         }
      }
      
      private function checkDead() : void
      {
         if(Boolean(this.owner) && this.owner.isDead)
         {
            this.setEnable(false);
         }
      }
      
      private function updateAnimation() : void
      {
         if(this._fabaoView.isSkill)
         {
            this._fabaoView.changeStatus(ConstRole.SKILL_ONE,false);
         }
         else
         {
            this._fabaoView.changeStatus(ConstRole.STAND,true);
         }
      }
      
      public function handlerSkillEnd() : void
      {
         this._fabaoView.isSkill = false;
         MethodUtil.gray(this._fabaoView,true);
         if(this._fabaoVO)
         {
            this._fabaoVO.startCD(this._fabaoData.cdTime.v);
         }
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return this._hitTargets.indexOf(param1) != -1;
      }
      
      public function addTargets(param1:IRole) : void
      {
         this._hitTargets[this._hitTargets.length] = param1;
      }
      
      public function cleanTargets() : void
      {
         this._hitTargets.length = 0;
      }
      
      public function hitBack() : void
      {
      }
      
      public function get dirX() : int
      {
         return _inverted ? -1 : 1;
      }
      
      public function setEnable(param1:Boolean) : void
      {
         visible = param1;
         updateCallEnabled = param1;
         this._controlenabled = param1;
         if(!param1 && Boolean(this._fabaoView))
         {
            this._fabaoView.cleanTime();
         }
      }
      
      override public function destroy() : void
      {
         if(this._fabaoView.isSkill && Boolean(this._fabaoVO))
         {
            this._fabaoVO.startCD(this._fabaoData.cdTime.v);
         }
         this._fabaoVO = null;
         this._fabaoView.destroy();
         super.destroy();
      }
   }
}

