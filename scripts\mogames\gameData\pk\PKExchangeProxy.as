package mogames.gameData.pk
{
   import file.PKExchangeConfig;
   import mogames.gameData.pk.vo.PKExchangeVO;
   
   public class PKExchangeProxy
   {
      private static var _instance:PKExchangeProxy;
      
      public var list:Array;
      
      public function PKExchangeProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : PKExchangeProxy
      {
         if(!_instance)
         {
            _instance = new PKExchangeProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.list = PKExchangeConfig.instance().newList();
      }
      
      public function get saveData() : String
      {
         var _loc2_:PKExchangeVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.list)
         {
            _loc1_.push(_loc2_.saveData);
         }
         return _loc1_.join("T");
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:Array = null;
         var _loc4_:PKExchangeVO = null;
         var _loc5_:* = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc5_ in _loc2_)
         {
            _loc3_ = _loc5_.split("H");
            _loc4_ = this.findExchangeVO(int(_loc3_[0]));
            if(_loc4_)
            {
               _loc4_.flag.v = int(_loc3_[1]);
            }
         }
      }
      
      public function daliyRefresh() : void
      {
         var _loc1_:PKExchangeVO = null;
         for each(_loc1_ in this.list)
         {
            _loc1_.flag.v = 0;
         }
      }
      
      private function findExchangeVO(param1:int) : PKExchangeVO
      {
         var _loc2_:PKExchangeVO = null;
         for each(_loc2_ in this.list)
         {
            if(_loc2_.shopID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

