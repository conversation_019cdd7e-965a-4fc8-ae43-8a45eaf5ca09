package file
{
   import mogames.gameData.base.RandomVO;
   import mogames.gameData.forge.NeedVO;
   import mogames.gameData.good.GameEquipVO;
   import mogames.gameData.xilian.XiLianVO;
   
   public class ZBXLConfig
   {
      private static var _instance:ZBXLConfig;
      
      private var _xilianVec:Vector.<XiLianVO>;
      
      private var _randList:Array;
      
      public function ZBXLConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : ZBXLConfig
      {
         if(!_instance)
         {
            _instance = new ZBXLConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._xilianVec = new Vector.<XiLianVO>();
         this._xilianVec.push(new XiLianVO(20001,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(20002,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(20003,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(20004,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(20005,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(21001,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(21002,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(21003,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(21004,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(21005,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(22001,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(22002,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(22003,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(22004,500,new NeedVO(10021,2),new NeedVO(13321,1)));
         this._xilianVec.push(new XiLianVO(22005,500,new NeedVO(10021,2),new NeedVO(13322,1)));
         this._xilianVec.push(new XiLianVO(20006,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(20007,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(20008,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(20009,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(20010,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(21006,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(21007,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(21008,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(21009,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(21010,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(22006,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(22007,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(22008,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(22009,2000,new NeedVO(10021,2),new NeedVO(13321,2)));
         this._xilianVec.push(new XiLianVO(22010,2000,new NeedVO(10021,2),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(20011,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(20012,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(20013,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(20014,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(20015,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(21011,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(21012,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(21013,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(21014,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(21015,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(22011,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(22012,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(22013,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(22014,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(22015,5000,new NeedVO(10022,3),new NeedVO(13322,2)));
         this._xilianVec.push(new XiLianVO(20016,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(20017,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(20018,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(20019,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(20020,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(21016,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(21017,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(21018,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(21019,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(21020,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(22016,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(22017,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(22018,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(22019,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(22020,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(20021,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(20022,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(20023,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(20024,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(20025,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(21021,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(21022,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(21023,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(21024,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(21025,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(22021,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(22022,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(22023,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(22024,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(22025,15000,new NeedVO(10024,7),new NeedVO(13326,4)));
         this._xilianVec.push(new XiLianVO(23001,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(23002,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(23003,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(23004,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(23005,10000,new NeedVO(10023,5),new NeedVO(13325,3)));
         this._xilianVec.push(new XiLianVO(23008,5000,new NeedVO(10022,5),new NeedVO(13322,3)));
         this._xilianVec.push(new XiLianVO(23009,20000,new NeedVO(10024,5),new NeedVO(13326,3)));
         this._xilianVec.push(new XiLianVO(23010,30000,new NeedVO(10024,5),new NeedVO(13326,5)));
         this._randList = [];
         this._randList.push(new RandomVO(2,6));
         this._randList.push(new RandomVO(1,3));
         this._randList.push(new RandomVO(1,3));
         this._randList.push(new RandomVO(1,2));
         this._randList.push(new RandomVO(1,2));
         this._randList.push(new RandomVO(1,2));
         this._randList.push(new RandomVO(1,2));
         this._randList.push(new RandomVO(1,1));
      }
      
      public function handlerXiLian(param1:GameEquipVO) : void
      {
         var _loc2_:RandomVO = null;
         if(param1.baseHP.v != 0 && param1.baseHP.v < param1.constEquip.baseHP.vo.max.v)
         {
            _loc2_ = this._randList[0];
            param1.baseHP.v += _loc2_.randomValue();
            param1.baseHP.v = Math.min(param1.baseHP.v,param1.constEquip.baseHP.vo.max.v);
         }
         if(param1.baseMP.v != 0 && param1.baseMP.v < param1.constEquip.baseMP.vo.max.v)
         {
            _loc2_ = this._randList[1];
            param1.baseMP.v += _loc2_.randomValue();
            param1.baseMP.v = Math.min(param1.baseMP.v,param1.constEquip.baseMP.vo.max.v);
         }
         if(param1.baseATK.v != 0 && param1.baseATK.v < param1.constEquip.baseATK.vo.max.v)
         {
            _loc2_ = this._randList[2];
            param1.baseATK.v += _loc2_.randomValue();
            param1.baseATK.v = Math.min(param1.baseATK.v,param1.constEquip.baseATK.vo.max.v);
         }
         if(param1.basePDEF.v != 0 && param1.basePDEF.v < param1.constEquip.basePDEF.vo.max.v)
         {
            _loc2_ = this._randList[3];
            param1.basePDEF.v += _loc2_.randomValue();
            param1.basePDEF.v = Math.min(param1.basePDEF.v,param1.constEquip.basePDEF.vo.max.v);
         }
         if(param1.baseMDEF.v != 0 && param1.baseMDEF.v < param1.constEquip.baseMDEF.vo.max.v)
         {
            _loc2_ = this._randList[4];
            param1.baseMDEF.v += _loc2_.randomValue();
            param1.baseMDEF.v = Math.min(param1.baseMDEF.v,param1.constEquip.baseMDEF.vo.max.v);
         }
         if(param1.baseMISS.v != 0 && param1.baseMISS.v < param1.constEquip.baseMISS.vo.max.v)
         {
            _loc2_ = this._randList[5];
            param1.baseMISS.v += _loc2_.randomValue();
            param1.baseMISS.v = Math.min(param1.baseMISS.v,param1.constEquip.baseMISS.vo.max.v);
         }
         if(param1.baseCRIT.v != 0 && param1.baseCRIT.v < param1.constEquip.baseCRIT.vo.max.v)
         {
            _loc2_ = this._randList[6];
            param1.baseCRIT.v += _loc2_.randomValue();
            param1.baseCRIT.v = Math.min(param1.baseCRIT.v,param1.constEquip.baseCRIT.vo.max.v);
         }
         if(param1.baseLUCK.v != 0 && param1.baseLUCK.v < param1.constEquip.baseLUCK.vo.max.v)
         {
            param1.baseLUCK.v += 1;
            param1.baseLUCK.v = Math.min(param1.baseLUCK.v,param1.constEquip.baseLUCK.vo.max.v);
         }
         param1.setLevel(param1.level.v);
      }
      
      public function findXiLianVO(param1:GameEquipVO) : XiLianVO
      {
         var _loc2_:XiLianVO = null;
         for each(_loc2_ in this._xilianVec)
         {
            if(_loc2_.id.v == param1.constEquip.id.v)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

