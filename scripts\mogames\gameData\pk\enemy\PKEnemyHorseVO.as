package mogames.gameData.pk.enemy
{
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.heroSkill.xiaobailong.SkillBaiLongBaVO;
   
   public class PKEnemyHorseVO extends PKEnemyVO
   {
      public function PKEnemyHorseVO(param1:int)
      {
         allSkills = [];
         allSkills.push(new HurtSkillVO(1006,this,0));
         allSkills.push(new HurtSkillVO(1007,this,1));
         allSkills.push(new HurtSkillVO(1008,this,2));
         allSkills.push(new SkillBaiLongBaVO(1009,this,3));
         allSkills.push(new HurtSkillVO(1010,this,4));
         super(1002,param1);
      }
   }
}

