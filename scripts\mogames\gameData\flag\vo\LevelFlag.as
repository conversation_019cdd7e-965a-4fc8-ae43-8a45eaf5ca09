package mogames.gameData.flag.vo
{
   public class LevelFlag extends NumFlag
   {
      private var _default:int = 5;
      
      public function LevelFlag(param1:int)
      {
         super(param1);
         _cur.v = this._default;
      }
      
      override public function setValue(param1:int) : void
      {
         _cur.v = param1;
      }
      
      override public function isComplete() : Boolean
      {
         return false;
      }
      
      override public function dailyRefresh() : void
      {
      }
   }
}

