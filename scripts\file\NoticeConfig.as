package file
{
   import utils.TxtUtil;
   
   public class NoticeConfig
   {
      private static var _instance:NoticeConfig;
      
      public var title:String;
      
      public var notice:String;
      
      public function NoticeConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : NoticeConfig
      {
         if(!_instance)
         {
            _instance = new NoticeConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.title = "更新公告V13.9";
         this.notice = TxtUtil.setColor("亲爱的西游迷们：","FFFFFF") + "<br>" + TxtUtil.setColor("《西游灭妖传》V13.9来啦！","FFFFFF") + "<br>" + TxtUtil.setColor("1、西游迎新春活动:（2.24-3.28）","FF00FF") + "<br>" + TxtUtil.setColor("   充值大礼包","FFFFFF") + "<br>" + TxtUtil.setColor("2、竞技场奖励调整：+1圣·鹏魔王橙卡","00FF00") + "<br>" + TxtUtil.setColor("3、每周限购调整：宠物新手礼包","FF00FF") + "<br>" + TxtUtil.setColor("4、3月签到盒礼包","00FF00") + "<br>";
      }
   }
}

