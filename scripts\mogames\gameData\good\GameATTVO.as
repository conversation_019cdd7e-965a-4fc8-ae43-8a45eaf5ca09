package mogames.gameData.good
{
   import mogames.gameData.good.constvo.ConstATTVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   import mogames.gameData.role.HeroGameVO;
   
   public class GameATTVO extends GameGoodVO
   {
      protected var _att:ConstATTVO;
      
      public function GameATTVO(param1:ConstGoodVO)
      {
         super(param1);
         this._att = param1 as ConstATTVO;
      }
      
      public function addEquipATT(param1:HeroGameVO) : void
      {
         if(this._att.baseHP)
         {
            if(this._att.baseHP.isPer)
            {
               param1.perHP.v += this._att.baseHP.value.v;
            }
            else
            {
               param1.equipHP.v += this._att.baseHP.value.v;
            }
         }
         if(this._att.baseMP)
         {
            if(this._att.baseMP.isPer)
            {
               param1.perMP.v += this._att.baseMP.value.v;
            }
            else
            {
               param1.equipMP.v += this._att.baseMP.value.v;
            }
         }
         if(this._att.baseATK)
         {
            if(this._att.baseATK.isPer)
            {
               param1.perATK.v += this._att.baseATK.value.v;
            }
            else
            {
               param1.equipATK.v += this._att.baseATK.value.v;
            }
         }
         if(this._att.basePDEF)
         {
            if(this._att.basePDEF.isPer)
            {
               param1.perPDEF.v += this._att.basePDEF.value.v;
            }
            else
            {
               param1.equipPDEF.v += this._att.basePDEF.value.v;
            }
         }
         if(this._att.baseMDEF)
         {
            if(this._att.baseMDEF.isPer)
            {
               param1.perMDEF.v += this._att.baseMDEF.value.v;
            }
            else
            {
               param1.equipMDEF.v += this._att.baseMDEF.value.v;
            }
         }
         if(this._att.baseCRIT.v != 0)
         {
            param1.equipCRIT.v += this._att.baseCRIT.v;
         }
         if(this._att.baseMISS.v != 0)
         {
            param1.equipMISS.v += this._att.baseMISS.v;
         }
         if(this._att.baseLUCK.v != 0)
         {
            param1.equipLUCK.v += this._att.baseLUCK.v;
         }
      }
      
      public function get attInfor() : String
      {
         var _loc1_:Array = [];
         if(this._att.baseHP)
         {
            if(this._att.baseHP.isPer)
            {
               _loc1_.push("生命+" + int(this._att.baseHP.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("生命+" + this._att.baseHP.value.v);
            }
         }
         if(this._att.baseMP)
         {
            if(this._att.baseMP.isPer)
            {
               _loc1_.push("法力+" + int(this._att.baseMP.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("法力+" + this._att.baseMP.value.v);
            }
         }
         if(this._att.baseATK)
         {
            if(this._att.baseATK.isPer)
            {
               _loc1_.push("攻击+" + int(this._att.baseATK.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("攻击+" + this._att.baseATK.value.v);
            }
         }
         if(this._att.basePDEF)
         {
            if(this._att.basePDEF.isPer)
            {
               _loc1_.push("物防+" + int(this._att.basePDEF.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("物防+" + this._att.basePDEF.value.v);
            }
         }
         if(this._att.baseMDEF)
         {
            if(this._att.baseMDEF.isPer)
            {
               _loc1_.push(",魔防+" + int(this._att.baseMDEF.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("魔防+" + this._att.baseMDEF.value.v);
            }
         }
         if(this._att.baseCRIT.v != 0)
         {
            _loc1_.push("暴击+" + this._att.baseCRIT.v);
         }
         if(this._att.baseMISS.v != 0)
         {
            _loc1_.push("闪避+" + this._att.baseMISS.v);
         }
         if(this._att.baseLUCK.v != 0)
         {
            _loc1_.push("幸运+" + this._att.baseLUCK.v);
         }
         return _loc1_.join(",");
      }
      
      public function get score() : int
      {
         return 0;
      }
   }
}

