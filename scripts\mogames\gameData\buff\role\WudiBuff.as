package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameObj.display.BMCSprite;
   import mogames.gameRole.co.RoleBaseView;
   
   public class WudiBuff extends BaseBuff
   {
      public function WudiBuff()
      {
         super(1012);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         (_role.roleView as RoleBaseView).setWudi(true);
      }
      
      override protected function onEnd() : void
      {
         if(_role)
         {
            (_role.roleView as RoleBaseView).setWudi(false);
         }
         destroy();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         if(!_buffVO.argDic.skin)
         {
            return null;
         }
         _skin.setupSequence(AssetManager.findAnimation(_buffVO.argDic.skin),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

