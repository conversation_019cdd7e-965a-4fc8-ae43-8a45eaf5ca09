package mogames.gameMission.mission.xiaoleiyin
{
   import file.BuffConfig;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameRole.enemy.BOSSHuangMei;
   import mogames.gameRole.enemy.BaseEnemy;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusRender;
   import mogames.gameSystem.CitrusTimer;
   
   public class SceneXiaoLeiYin extends BossScene
   {
      private var _lights:Array;
      
      private var _tais:Array;
      
      private var _buffTimer:CitrusTimer;
      
      private var _index:int;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      public function SceneXiaoLeiYin(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(3108,170,480,140),new Point(3214,436));
         EffectManager.instance().playBGM("BGM_DANGER4");
         this._buffTimer = new CitrusTimer(true);
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2042,1);
            TaskProxy.instance().addTask(11026);
         };
         super.handlerInit();
         this.layoutLights();
         this.layoutTais();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2042))
         {
            showDialog("STORY0068",func);
         }
      }
      
      private function layoutLights() : void
      {
         var _loc2_:TrapCandleLight = null;
         this._lights = [];
         var _loc1_:int = 0;
         while(_loc1_ < 6)
         {
            _loc2_ = new TrapCandleLight();
            _loc2_.x = 228 + _loc1_ * 482;
            _loc2_.y = 400;
            Layers.addCEChild(_loc2_);
            this._lights[_loc1_] = _loc2_;
            _loc1_++;
         }
      }
      
      private function layoutTais() : void
      {
         var _loc1_:int = 0;
         var _loc2_:TrapCandleTai = null;
         this._tais = [];
         _loc1_ = 0;
         while(_loc1_ < 2)
         {
            _loc2_ = new TrapCandleTai();
            _loc2_.x = 3080 + _loc1_ * 575;
            _loc2_.y = 400;
            Layers.addCEChild(_loc2_);
            this._tais[_loc1_] = _loc2_;
            _loc1_++;
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[154,250,150,100],[634,250,150,100]],19011,this.triggerEnd0,this.addEnemyBuff);
         this._eTrigger1 = new EnemyTrigger(_mission,[[634,250,150,100],[1115,250,150,100]],19012,this.triggerEnd,this.addEnemyBuff);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1115,250,150,100],[1596,250,150,100]],19013,this.triggerEnd,this.addEnemyBuff);
         this._eTrigger3 = new EnemyTrigger(_mission,[[2085,250,150,100],[2565,250,150,100]],19014,this.triggerEnd3,this.addEnemyBuff);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1060,450),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1433,450),1,new Rectangle(0,0,2320,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2415,450),1,new Rectangle(960,0,2320,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(3380,450),1,new Rectangle(2880,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function startBattle() : void
      {
         startBossBattle("BGM_BATTLE0");
         this._buffTimer.setInterval(12,0,this.addBossBuff,null,false);
         this._buffTimer.startNone();
         CitrusRender.instance().add(this.removeBossBuff);
      }
      
      private function triggerEnd3() : void
      {
         this.triggerEnd();
         this.addBOSS();
         this.stopTrapLight();
      }
      
      private function triggerEnd0() : void
      {
         this.triggerEnd();
         CitrusLock.instance().lock(new Rectangle(0,0,1318,600),false,false,false,true);
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function stopTrapLight() : void
      {
         var _loc1_:TrapCandleLight = null;
         for each(_loc1_ in this._lights)
         {
            _loc1_.stopTrap();
         }
      }
      
      private function addEnemyBuff(param1:BaseEnemy) : void
      {
         var _loc2_:int = Math.random() * BuffConfig.CANDLE_BUFF.length;
         param1.addBuff(new BuffVO(BuffConfig.CANDLE_BUFF[_loc2_],3600,{"hurt":new Sint(80)}));
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHuangMei();
         _boss.x = 3680;
         _boss.y = 280;
         _boss.initData(30341,30341,87);
         _boss.target = _mission.curTarget;
         add(_boss);
         (_boss as BOSSHuangMei).nitanX = 2980;
      }
      
      private function addBossBuff() : void
      {
         if(_boss.hasWuDiBuff)
         {
            return;
         }
         this._index = int(Math.random() * BuffConfig.CANDLE_BUFF.length);
         _boss.addBuff(new BuffVO(BuffConfig.CANDLE_BUFF[this._index],3600,{"hurt":new Sint(80)}));
      }
      
      private function removeBossBuff() : void
      {
         if(this._tais[0].index != this._tais[1].index)
         {
            return;
         }
         if(this._tais[0].index != this._index)
         {
            return;
         }
         _boss.removeBuff(BuffConfig.CANDLE_BUFF[this._index]);
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11026);
         CitrusRender.instance().remove(this.removeBossBuff);
         this._buffTimer.pause();
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         this._lights = null;
         this._tais = null;
         CitrusRender.instance().remove(this.removeBossBuff);
         this._buffTimer.destroy();
         this._buffTimer = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         super.destroy();
      }
   }
}

