package citrus.view.spriteview
{
   import citrus.core.CitrusEngine;
   import citrus.physics.APhysicsEngine;
   import citrus.physics.IDebugView;
   import flash.display.Sprite;
   import flash.events.Event;
   
   public class SpritePhysicsDebugView extends Sprite
   {
      private var _physicsEngine:APhysicsEngine;
      
      private var _debugView:IDebugView;
      
      public function SpritePhysicsDebugView()
      {
         super();
         this._physicsEngine = CitrusEngine.getInstance().state.getFirstObjectByType(APhysicsEngine) as APhysicsEngine;
         this._debugView = new this._physicsEngine.realDebugView();
         addEventListener(Event.ADDED_TO_STAGE,this._addedToStage);
      }
      
      private function _addedToStage(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this._addedToStage);
         this._debugView.initialize();
         addEventListener(Event.REMOVED_FROM_STAGE,this._removedFromStage);
      }
      
      private function _removedFromStage(param1:Event) : void
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this._removedFromStage);
         this._debugView.destroy();
         this._physicsEngine = null;
         this._debugView = null;
      }
      
      public function update() : void
      {
         this._debugView.update();
      }
      
      public function debugMode(param1:uint) : void
      {
         this._debugView.debugMode(param1);
      }
      
      public function get debugView() : IDebugView
      {
         return this._debugView;
      }
   }
}

