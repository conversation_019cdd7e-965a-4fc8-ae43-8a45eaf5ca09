package file
{
   import flash.utils.Dictionary;
   import mogames.gameData.dialog.DialogVO;
   import utils.TxtUtil;
   
   public class StoryConfig
   {
      private static var _instance:StoryConfig;
      
      private var storyDic:Dictionary;
      
      public function StoryConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.storyDic = new Dictionary();
         this.storyDic["STORY0001"] = new Vector.<DialogVO>();
         this.storyDic["STORY0001"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","这如来老儿还敢跟俺打赌，俺老孙一个筋斗十万八千里，此间都已经是尽头了吧！在这解手做个记号，免得那如来不承认，嘿嘿。"));
         this.storyDic["STORY0001"].push(new DialogVO(1,"如来佛祖","BODY_RU_LAI","你个尿精猴头，并未曾离开我手掌呢！你看看周围！"));
         this.storyDic["STORY0001"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG2","（大惊）什么！有这等事？！我得再去查探一下！"));
         this.storyDic["STORY0001"].push(new DialogVO(1,"如来佛祖","BODY_RU_LAI","猴头哪里走，还不束手就擒！"));
         this.storyDic["STORY0002"] = new Vector.<DialogVO>();
         this.storyDic["STORY0002"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG1","佛祖啊，我错了！"));
         this.storyDic["STORY0002"].push(new DialogVO(1,"如来佛祖","BODY_RU_LAI","猴头，今日就收了你的修为，佛手将化作五行山，你在下面好好闭门思过吧！"));
         this.storyDic["STORY0002"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG2","啊！！！！！！！"));
         this.storyDic["STORY0003"] = new Vector.<DialogVO>();
         this.storyDic["STORY0003"].push(new DialogVO(1,"如来佛祖","BODY_RU_LAI","猴头听命！你的师父唐僧即将经过，你须做好准备， 助其西天取经成功，便赐你正果金身！"));
         this.storyDic["STORY0003"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG4","多谢佛祖，终于等到这一天了！"));
         this.storyDic["STORY0004"] = new Vector.<DialogVO>();
         this.storyDic["STORY0004"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG4","师父！师父！快快救我！"));
         this.storyDic["STORY0004"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG5","一只野猴，有何要救？"));
         this.storyDic["STORY0004"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG4","师父你有所不知......（孙悟空道出原委）......"));
         this.storyDic["STORY0004"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG5","你扰乱天庭，被我佛压在这五行山下，如今是否醒悟？"));
         this.storyDic["STORY0004"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG4","我在这山下已有五百年......我真的知错了。"));
         this.storyDic["STORY0004"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG5","看你本性不坏，便随了我佛，同我西天取经，可好？"));
         this.storyDic["STORY0004"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG4","但求师父揭了这山上的符咒，还我自由！徒儿愿意同师父西天取经！"));
         this.storyDic["STORY0005"] = new Vector.<DialogVO>();
         this.storyDic["STORY0005"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","哈哈！俺老孙终于出来了！！！师父在上，请受徒儿一拜！"));
         this.storyDic["STORY0005"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG3","好徒儿，为师给你取个法号名曰【行者】，这就随我一同前往西天吧。"));
         this.storyDic["STORY0005"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","好的，师父！前方乃双叉岭，徒儿来给您抬行李！"));
         this.storyDic["STORY0006"] = new Vector.<DialogVO>();
         this.storyDic["STORY0006"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","龙师弟，以后就跟俺老孙一块保护师父！"));
         this.storyDic["STORY0006"].push(new DialogVO(0,"小白龙","BODY_LONG_MA3","好的大师兄！我比较弱，你也要多照顾照顾我哈！"));
         this.storyDic["STORY0006"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG3","两位徒儿真懂事呀！我们出发吧！"));
         this.storyDic["STORY0007"] = new Vector.<DialogVO>();
         this.storyDic["STORY0007"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","雕虫小技，不堪一击！"));
         this.storyDic["STORY0007"].push(new DialogVO(1,"如来佛祖","BODY_RU_LAI","你个猴头，休得放肆！"));
         this.storyDic["STORY0008"] = new Vector.<DialogVO>();
         this.storyDic["STORY0008"].push(new DialogVO(1,"寅将军","BODY_YIN_JIANG_JUN","哇哈哈，刚抓了个打铁的，这会又有一个送上门来的，可以饱餐一顿了！"));
         this.storyDic["STORY0008"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","呔！小妖报上名来，俺不杀无名之辈！"));
         this.storyDic["STORY0008"].push(new DialogVO(1,"寅将军","BODY_YIN_JIANG_JUN","听好了，你爷爷我名号【寅将军】！不自量力的毛猴子，看招！"));
         this.storyDic["STORY0009"] = new Vector.<DialogVO>();
         this.storyDic["STORY0009"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG5","悟空，这是何处啊？"));
         this.storyDic["STORY0009"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","这里就是俺老孙的家【水帘洞】啦！"));
         this.storyDic["STORY0009"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG3","不错不错！可以称的上是世外桃源了。"));
         this.storyDic["STORY0009"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","哈哈，刚才我的徒子徒孙说是要跟我们走，不过要先跟我们切磋一下~"));
         this.storyDic["STORY0009"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG3","嗯嗯，点到为止！"));
         this.storyDic["STORY0010"] = new Vector.<DialogVO>();
         this.storyDic["STORY0010"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG5","悟空，为师的佛衣不见了，这可是观音大士赠与的宝物啊！"));
         this.storyDic["STORY0010"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","这...如何是好！前方来者何人？咦，原来是观世音菩萨。"));
         this.storyDic["STORY0010"].push(new DialogVO(1,"观世音","BODY_GUAN_SHI_YIN","正好路过此地，看到你们有难，便来指点一二。此处名曰黑风山，山里有个黑风洞，东西或许在那里。"));
         this.storyDic["STORY0010"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","多谢菩萨指点，那老孙去去便回！"));
         this.storyDic["STORY0011"] = new Vector.<DialogVO>();
         this.storyDic["STORY0011"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","嗯？到这边已经没路了......"));
         this.storyDic["STORY0011"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","咦，这个" + TxtUtil.setColor("藤蔓") + "似乎有人动过！嗯......看起来十分脆弱，我且" + TxtUtil.setColor("攻击") + "一下试试！"));
         this.storyDic["STORY0012"] = new Vector.<DialogVO>();
         this.storyDic["STORY0012"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","妖怪！是不是你偷了俺师父的佛衣？"));
         this.storyDic["STORY0012"].push(new DialogVO(1,"黑熊精","BODY_HEI_XIONG_JING","不错，就是我拿的，怎么？"));
         this.storyDic["STORY0012"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","还挺老实！速速还来，不然饶不了你！"));
         this.storyDic["STORY0012"].push(new DialogVO(1,"黑熊精","BODY_HEI_XIONG_JING","我好怕怕~我就不还，看你能怎样？"));
         this.storyDic["STORY0012"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","呔，吃俺老孙一棒！"));
         this.storyDic["STORY0013"] = new Vector.<DialogVO>();
         this.storyDic["STORY0013"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","妖怪站住！快快放了俺师父！知道俺是谁吗！"));
         this.storyDic["STORY0013"].push(new DialogVO(1,"黄风怪","BODY_HUANG_FENG_GUAI","呦，这不是当年的那个弼马温嘛！啊哈哈哈！"));
         this.storyDic["STORY0013"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","气死老孙也，俺要与你大战三百回合！"));
         this.storyDic["STORY0013"].push(new DialogVO(1,"黄风怪","BODY_HUANG_FENG_GUAI","你黄爷爷在洞府等你！你得快点，不然，你师父早就进了爷爷的肚皮咯！"));
         this.storyDic["STORY0014"] = new Vector.<DialogVO>();
         this.storyDic["STORY0014"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG4","悟空，快快救我！"));
         this.storyDic["STORY0014"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG2","啊，师父！！！"));
         this.storyDic["STORY0014"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","妖怪，识相的就赶紧放下我师父！"));
         this.storyDic["STORY0014"].push(new DialogVO(1,"黄风怪","BODY_HUANG_FENG_GUAI","哈哈哈，你还是先问问爷爷我手里的刀吧！"));
         this.storyDic["STORY0015"] = new Vector.<DialogVO>();
         this.storyDic["STORY0015"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","师父，前面貌似有个大户人家！我们去那边化缘休息一下吧！"));
         this.storyDic["STORY0015"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","好的，走了这么许久也是该歇歇了。"));
         this.storyDic["STORY0016"] = new Vector.<DialogVO>();
         this.storyDic["STORY0016"].push(new DialogVO(1,"猪八戒","BODY_ZHU_BA_JIE4","老丈人，你就让老猪我进去吧，我一定会对你女儿好的！你再不让我进去，我可要硬闯了！"));
         this.storyDic["STORY0016"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","人家不让你进去你还要硬闯，这么野蛮！"));
         this.storyDic["STORY0017"] = new Vector.<DialogVO>();
         this.storyDic["STORY0017"].push(new DialogVO(1,"猪八戒","BODY_ZHU_BA_JIE0","嘿，我这暴脾气！你是哪根葱？莫不是想找打？"));
         this.storyDic["STORY0017"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","哟，那我倒要见识见识你有什么能耐。"));
         this.storyDic["STORY0017"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","悟空，不可莽撞。这位施主，有话好好说。"));
         this.storyDic["STORY0017"].push(new DialogVO(1,"猪八戒","BODY_ZHU_BA_JIE0","臭和尚，老猪我正没处出气呢，看打！"));
         this.storyDic["STORY0017"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","师父小心！"));
         this.storyDic["STORY0018"] = new Vector.<DialogVO>();
         this.storyDic["STORY0018"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","悟空，为师有点饿了。"));
         this.storyDic["STORY0018"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","师父，我这就去给您找点吃的。"));
         this.storyDic["STORY0018"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","大师兄，这一带妖气很重啊！有【照妖镜】就好了，能让变成人的妖怪现原形。"));
         this.storyDic["STORY0018"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","没事，俺老孙不怕。"));
         this.storyDic["STORY0018"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","早去早回。"));
         this.storyDic["STORY0018"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE3","大师兄大师兄，多给老猪找点好吃的，俺等你哦！"));
         this.storyDic["STORY0019"] = new Vector.<DialogVO>();
         this.storyDic["STORY0019"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","哼，你个妖怪，你是逃不过我的火眼金睛的！"));
         this.storyDic["STORY0019"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG0","悟空，休得无礼！明明是个人，非要说人家是妖！"));
         this.storyDic["STORY0019"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG4","师父，他是妖怪变的啊，俺老孙......"));
         this.storyDic["STORY0019"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG0","你给我住嘴！"));
         this.storyDic["STORY0021"] = new Vector.<DialogVO>();
         this.storyDic["STORY0021"].push(new DialogVO(0,"白龙马","BODY_LONG_MA4","大师兄，你打死白骨精被师父逐走后，师父就是在这边被妖怪抓走的。"));
         this.storyDic["STORY0021"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","唉，师父他肉眼凡胎，不识妖怪，可委屈了俺老孙......"));
         this.storyDic["STORY0021"].push(new DialogVO(0,"白龙马","BODY_LONG_MA4","师父是菩萨心肠，被蛊惑也是正常，您心里有数。"));
         this.storyDic["STORY0021"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE2","哎呀，别说废话了，赶紧找师父吧！"));
         this.storyDic["STORY0021"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","嗯，这片林子妖气弥漫，又像个迷宫，我们要小心行事。"));
         this.storyDic["STORY0023"] = new Vector.<DialogVO>();
         this.storyDic["STORY0023"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG5","徒弟们仔细看，前方遇山高，恐怕有虎狼阻挡。"));
         this.storyDic["STORY0023"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","师父，此地唤作【平顶山】，你不要担心，只要有俺老孙在，就是塌下天来，可保无事！"));
         this.storyDic["STORY0024"] = new Vector.<DialogVO>();
         this.storyDic["STORY0024"].push(new DialogVO(1,"银角大王","BODY_YIN_JIAO","此树是我开,此路是我载！若想从此过，留下唐僧来！"));
         this.storyDic["STORY0024"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","唉，没文化真可怕......"));
         this.storyDic["STORY0024"].push(new DialogVO(1,"银角大王","BODY_YIN_JIAO","......"));
         this.storyDic["STORY0025"] = new Vector.<DialogVO>();
         this.storyDic["STORY0025"].push(new DialogVO(1,"刘伯钦","BODY_LIU_BO_QIN0","来人啊，救命啊！"));
         this.storyDic["STORY0025"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","阿弥陀佛，施主被何人囚禁于此？"));
         this.storyDic["STORY0025"].push(new DialogVO(1,"刘伯钦","BODY_LIU_BO_QIN0","大师，我本是住在双叉岭附近的铁匠，近日来了只虎精，把我抓住关在这牢笼里，说是晚上要吃了我...呜呜呜！"));
         this.storyDic["STORY0025"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","莫慌，让俺老孙把这牢笼砸开！"));
         this.storyDic["STORY0025"].push(new DialogVO(1,"刘伯钦","BODY_LIU_BO_QIN0","这牢笼坚硬无比，恐怕只能靠" + TxtUtil.setColor("【钥匙】") + "才能打开了，那钥匙应该在那妖怪身上。"));
         this.storyDic["STORY0025"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","俺这就去会会那只妖怪！"));
         this.storyDic["STORY0027"] = new Vector.<DialogVO>();
         this.storyDic["STORY0027"].push(new DialogVO(1,"刘伯钦","BODY_LIU_BO_QIN0","呜呜呜，终于得救了，可是我现在也无家可归了啊！"));
         this.storyDic["STORY0027"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","去【花果山】吧，就在这双叉岭西方不远处！那是老孙的家！"));
         this.storyDic["STORY0027"].push(new DialogVO(1,"刘伯钦","BODY_LIU_BO_QIN0","啊！多谢恩人！听说那妖怪藏有" + TxtUtil.setColor("【法宝图纸】","99ff00") + "！"));
         this.storyDic["STORY0027"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","【法宝图纸】可是好东西。施主一路注意安全！"));
         this.storyDic["STORY0027"].push(new DialogVO(1,"刘伯钦","BODY_LIU_BO_QIN1","小师父，要是想让我给你磨磨兵器，就来找我！"));
         this.storyDic["STORY0028"] = new Vector.<DialogVO>();
         this.storyDic["STORY0028"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE1","大师兄，这边看起来好阴森，我们还是跑吧赶紧！"));
         this.storyDic["STORY0028"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","你个呆子！那妖怪害的俺老孙受那么大委屈，就算把这洞翻开来，我也要找到它！"));
         this.storyDic["STORY0028"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","大师兄，我帮你！"));
         this.storyDic["STORY0029"] = new Vector.<DialogVO>();
         this.storyDic["STORY0029"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","可恶啊，到底跑哪里去了！"));
         this.storyDic["STORY0029"].push(new DialogVO(1,"白骨精","BODY_BAI_GU_JING","哈哈哈，你个野猴子，你正站在我的脚下呢！干掉你我就能舒舒坦坦吃唐僧了！受死吧！"));
         this.storyDic["STORY0029"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","呀呀呀，气死我也！看棒！"));
         this.storyDic["STORY0030"] = new Vector.<DialogVO>();
         this.storyDic["STORY0030"].push(new DialogVO(0,"众人","BODY_ALL0","老妖道，快些放了俺们师父，不然你老命难保！"));
         this.storyDic["STORY0030"].push(new DialogVO(1,"黄袍怪","BODY_HUANG_PAO_GUAI","切克闹！切克闹！一起上！"));
         this.storyDic["STORY0031"] = new Vector.<DialogVO>();
         this.storyDic["STORY0031"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG3","（长舒一口气）总算得救了。"));
         this.storyDic["STORY0031"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","妖怪，受死吧！"));
         this.storyDic["STORY0031"].push(new DialogVO(1,"灵吉菩萨","BODY_LING_JI_PU_SA","阿弥陀佛，孙行者，且慢！"));
         this.storyDic["STORY0031"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","咦，灵吉菩萨！你怎么来了？"));
         this.storyDic["STORY0031"].push(new DialogVO(1,"灵吉菩萨","BODY_LING_JI_PU_SA","能否把这小妖交给贫僧处置？如碰到法宝上的难题，可以来找我！"));
         this.storyDic["STORY0031"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","（原来这妖怪有后台呀~）行吧，卖你个人情！"));
         this.storyDic["STORY0032"] = new Vector.<DialogVO>();
         this.storyDic["STORY0032"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","呼，总算找到佛衣了！"));
         this.storyDic["STORY0032"].push(new DialogVO(1,"观世音","BODY_GUAN_SHI_YIN","佛衣已寻回，你们师徒继续西行去吧。"));
         this.storyDic["STORY0032"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","多谢菩萨。"));
         this.storyDic["STORY0032"].push(new DialogVO(1,"观世音","BODY_GUAN_SHI_YIN","我看你们一路困难重重，我去花果山呆一阵，有什么需要，就来花果山找我。"));
         this.storyDic["STORY0032"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","哈哈，菩萨也喜欢俺老孙那地儿！欢迎欢迎！"));
         this.storyDic["STORY0033"] = new Vector.<DialogVO>();
         this.storyDic["STORY0033"].push(new DialogVO(1,"镇元大仙","BODY_ZHEN_YUAN0","唐僧！孙悟空！尔等好不识好歹！我令童子好生招待你们，你们却将我的人参树给砸了！"));
         this.storyDic["STORY0033"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","是你的童子有错在先！俺老孙受不了他们的气！"));
         this.storyDic["STORY0033"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","悟空住嘴！是我们的错！那依大仙所见，怎么解决一下？"));
         this.storyDic["STORY0033"].push(new DialogVO(1,"镇元大仙","BODY_ZHEN_YUAN0","怎么解决？反正救不活人参树，有你们好果子吃！"));
         this.storyDic["STORY0033"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","多大点事！等好吧，老孙给你救活了！"));
         this.storyDic["STORY0033"].push(new DialogVO(1,"镇元大仙","BODY_ZHEN_YUAN0","好！你若能救活，这片" + TxtUtil.setColor("药田","99ff00") + "亦可赠送与你！"));
         this.storyDic["STORY0034"] = new Vector.<DialogVO>();
         this.storyDic["STORY0034"].push(new DialogVO(1,"镇元大仙","BODY_ZHEN_YUAN0","咦？你怎么还在这？找到救树的法子了吗？"));
         this.storyDic["STORY0035"] = new Vector.<DialogVO>();
         this.storyDic["STORY0035"].push(new DialogVO(1,"镇元大仙","BODY_ZHEN_YUAN0","咦？你怎么还在这？找到救树的法子了吗？"));
         this.storyDic["STORY0035"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","你看这是什么！"));
         this.storyDic["STORY0035"].push(new DialogVO(1,"镇元大仙","BODY_ZHEN_YUAN5","啊，这不是观音大士的" + TxtUtil.setColor("【甘泉】","99ff00") + "嘛！这下树真有救了！"));
         this.storyDic["STORY0035"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","哈哈，这就把树给救活！"));
         this.storyDic["STORY0036"] = new Vector.<DialogVO>();
         this.storyDic["STORY0036"].push(new DialogVO(1,"镇元大仙","BODY_ZHEN_YUAN5","活了！甚好，甚好啊！"));
         this.storyDic["STORY0036"].push(new DialogVO(1,"镇元大仙","BODY_ZHEN_YUAN5","既然你救活了人参树，那老夫也说话算数，这药田给你用了！"));
         this.storyDic["STORY0036"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","哈哈哈，太好了！"));
         this.storyDic["STORY0036"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG3","阿弥陀佛，多谢大仙！"));
         this.storyDic["STORY0036"].push(new DialogVO(1,"镇元大仙","BODY_ZHEN_YUAN5","需要什么药草苗子，直接来找我！"));
         this.storyDic["STORY0037"] = new Vector.<DialogVO>();
         this.storyDic["STORY0037"].push(new DialogVO(1,"百花羞","BODY_BAI_HUA_XIU","多谢小师傅出手相救。"));
         this.storyDic["STORY0037"].push(new DialogVO(0,"MASTER","5","不客气，请问下姑娘， 有没有看到一个和尚？"));
         this.storyDic["STORY0037"].push(new DialogVO(1,"百花羞","BODY_BAI_HUA_XIU","小师傅，我本是宝象国的三公主，十三年前被那黄袍怪抢走到今日，前些日子看他将一个和尚给抓来了，不知是否是你在寻找的那位？"));
         this.storyDic["STORY0037"].push(new DialogVO(0,"MASTER","5","可能是的！姑娘是否知道被抓到哪里了？"));
         this.storyDic["STORY0037"].push(new DialogVO(1,"百花羞","BODY_BAI_HUA_XIU","在林子深处，我左边的那个门可以直接通往黄袍怪，如果想打其它宝贝，只要按照" + TxtUtil.setColor("吊起【石头】的方向","99ff00") + "位置进入也能找到。"));
         this.storyDic["STORY0037"].push(new DialogVO(0,"MASTER","5","多谢姑娘！此处危险，姑娘早些离开吧！"));
         this.storyDic["STORY0037"].push(new DialogVO(1,"百花羞","BODY_BAI_HUA_XIU","我已离家多年，不想让家人看到我这番落魄模样，所以先去那【花果山】休息时日，梳妆打扮一番再回去。小师傅如果需要什么帮助，就去那找我吧！"));
         this.storyDic["STORY0038"] = new Vector.<DialogVO>();
         this.storyDic["STORY0038"].push(new DialogVO(1,"猪八戒","BODY_ZHU_BA_JIE2","哎呦，猴哥，我老猪突然肚子好疼！"));
         this.storyDic["STORY0038"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","你个呆子，又吃坏东西了吧？"));
         this.storyDic["STORY0038"].push(new DialogVO(1,"猪八戒","BODY_ZHU_BA_JIE4","刚你们正和那黄风怪周旋的时候，老猪看到洞里好吃的挺多，忍不住吃了几个......"));
         this.storyDic["STORY0038"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG5","八戒，你个吃货！悟空，给他寻些药草治治吧。"));
         this.storyDic["STORY0038"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","嗯。想必那黄风洞内应该有不少奇花异草，我去去便回！"));
         this.storyDic["STORY0039"] = new Vector.<DialogVO>();
         this.storyDic["STORY0039"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG2","啊啊啊啊啊啊！！！悟空快来救为师！！悟空~~~~！"));
         this.storyDic["STORY0039"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG2","不好，师父被抓了！"));
         this.storyDic["STORY0040"] = new Vector.<DialogVO>();
         this.storyDic["STORY0040"].push(new DialogVO(1,"太上老君","BODY_LAO_JUN","唐长老，大圣——————"));
         this.storyDic["STORY0040"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","咦，这不是太上老君嘛！你怎么来了！"));
         this.storyDic["STORY0040"].push(new DialogVO(1,"太上老君","BODY_LAO_JUN","近日这乌鸡国不太平，皇宫内妖气弥漫，国王也性情大变，不知大圣可帮贫道调查一二？"));
         this.storyDic["STORY0040"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","没问题，小事包在俺老孙身上！"));
         this.storyDic["STORY0040"].push(new DialogVO(1,"太上老君","BODY_LAO_JUN","那贫道在花果山静候佳音，有什么要帮忙的随时来找我。"));
         this.storyDic["STORY0041"] = new Vector.<DialogVO>();
         this.storyDic["STORY0041"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","果然有大妖，竟然假扮乌鸡国国王至今！"));
         this.storyDic["STORY0041"].push(new DialogVO(1,"太上老君","BODY_LAO_JUN","大圣果然厉害，任何妖怪都逃不过大圣的火眼金睛！"));
         this.storyDic["STORY0041"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","那是！不过真正的国王哪里去了？"));
         this.storyDic["STORY0041"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG5","徒儿莫急。为师昨晚有个国王托梦与我，说被压在" + TxtUtil.setColor("【水井】") + "里出不来！"));
         this.storyDic["STORY0041"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","难道是真国王托梦于师父？老孙这就去打探一二。"));
         this.storyDic["STORY0042"] = new Vector.<DialogVO>();
         this.storyDic["STORY0042"].push(new DialogVO(1,"太上老君","BODY_LAO_JUN","大圣留步——————"));
         this.storyDic["STORY0042"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","老官儿，找我何事？"));
         this.storyDic["STORY0042"].push(new DialogVO(1,"太上老君","BODY_LAO_JUN","西天一路困难重重，我这有颗丹药，可以让大圣修为增进一二，以助大圣一臂之力。"));
         this.storyDic["STORY0042"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","呀，有这好东西不早拿出来！"));
         this.storyDic["STORY0042"].push(new DialogVO(1,"太上老君","BODY_LAO_JUN","贫道对炼丹也略懂一二，如果能够" + TxtUtil.setColor("集齐必要材料") + "，就可以来找我合成丹药哦！"));
         this.storyDic["STORY0043"] = new Vector.<DialogVO>();
         this.storyDic["STORY0043"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","没想到这水井之下居然有这般景象。"));
         this.storyDic["STORY0043"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","猴哥你快看前面！"));
         this.storyDic["STORY0043"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","咦，那似乎是井龙王。那台子上躺着的人莫非就是国王？"));
         this.storyDic["STORY0044"] = new Vector.<DialogVO>();
         this.storyDic["STORY0044"].push(new DialogVO(1,"井龙王","BODY_JING_LONG_WANG","原来是大圣——————唉！"));
         this.storyDic["STORY0044"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","井龙王为何唉声叹气？那位是不是当今的国王？"));
         this.storyDic["STORY0044"].push(new DialogVO(1,"井龙王","BODY_JING_LONG_WANG","老国王因凡人之躯，本王只能用法力保护了他肉身，但魂魄已经十之去九，除非用【还魂丹】救活。"));
         this.storyDic["STORY0044"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","这简单，老孙这就去找太上老君炼丹。"));
         this.storyDic["STORY0045"] = new Vector.<DialogVO>();
         this.storyDic["STORY0045"].push(new DialogVO(1,"乌鸡国王","BODY_WU_JI_GUO_WANG","感谢各位大仙的救命之恩啊！"));
         this.storyDic["STORY0045"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","小事小事，老国王言重了，我们还得继续西行，就此别过了。"));
         this.storyDic["STORY0045"].push(new DialogVO(1,"乌鸡国王","BODY_WU_JI_GUO_WANG","各位长老一路走好。"));
         this.storyDic["STORY0046"] = new Vector.<DialogVO>();
         this.storyDic["STORY0046"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE5","师父，前面有群小孩，老猪去问问黑水河怎么走。"));
         this.storyDic["STORY0046"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","你这样子会吓坏小孩子的，还是为师去问问。"));
         this.storyDic["STORY0046"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG5","施主请问......?!"));
         this.storyDic["STORY0046"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG2","啊————————"));
         this.storyDic["STORY0046"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","不好，师父被那群小孩抓走了！"));
         this.storyDic["STORY0047"] = new Vector.<DialogVO>();
         this.storyDic["STORY0047"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","小孩，你是头头是吧，快把俺师父给放了！可认识俺齐天大圣！"));
         this.storyDic["STORY0047"].push(new DialogVO(1,"红孩儿","BODY_HONG_HAI_ER","野猴子，你可知道我父亲是谁！大名鼎鼎的牛魔王！"));
         this.storyDic["STORY0047"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","哦？这么巧！我和你父亲是兄弟啊，这样算来，你是我大侄子呐！哈哈哈！"));
         this.storyDic["STORY0047"].push(new DialogVO(1,"红孩儿","BODY_HONG_HAI_ER","呸，我家哪有你这样的亲戚！野猴子，看枪！"));
         this.storyDic["STORY0048"] = new Vector.<DialogVO>();
         this.storyDic["STORY0048"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","万万没想到现在的小孩都有这样的本事！一时大意转眼就不见踪影了。"));
         this.storyDic["STORY0048"].push(new DialogVO(1,"猪八戒","BODY_ZHU_BA_JIE5","猴哥，你看前面，有扇好大的门！"));
         this.storyDic["STORY0048"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","嗯？旁边有个开关，或许那小子就躲那门后面去了！"));
         this.storyDic["STORY0049"] = new Vector.<DialogVO>();
         this.storyDic["STORY0049"].push(new DialogVO(1,"唐僧","BODY_TANG_SENG4","徒儿们——————"));
         this.storyDic["STORY0049"].push(new DialogVO(0,"众人","BODY_ALL0","师父别慌，我们这就来救你。"));
         this.storyDic["STORY0049"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","大侄子，赶紧把你师祖爷爷给放了吧，免得伤了和气！"));
         this.storyDic["STORY0049"].push(new DialogVO(1,"红孩儿","BODY_HONG_HAI_ER","哼！别以为赢了分身就很了不起，刚是我一时大意，野猴子，这次我要出绝招了！"));
         this.storyDic["STORY0050"] = new Vector.<DialogVO>();
         this.storyDic["STORY0050"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","大师兄，避水珠如何？"));
         this.storyDic["STORY0050"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","果然是好宝贝，在水下轻松自如！"));
         this.storyDic["STORY0050"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE5","猴哥你快看，前面似乎有个洞府！"));
         this.storyDic["STORY0050"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","走，上去看看，师父估计就被抓到那里去了！"));
         this.storyDic["STORY0051"] = new Vector.<DialogVO>();
         this.storyDic["STORY0051"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","呆子，饿不饿，是不是出去找点吃的？"));
         this.storyDic["STORY0051"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE3","这个好，我打听过了，前面有个三清殿，供台上有不少贡品！"));
         this.storyDic["STORY0051"].push(new DialogVO(0,"小白龙","BODY_LONG_MA3","那咱么赶紧去吧，我好饿！"));
         this.storyDic["STORY0051"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","俺老孙也都快饿扁了，顺便给师傅也带点。"));
         this.storyDic["STORY0052"] = new Vector.<DialogVO>();
         this.storyDic["STORY0052"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE2","猴哥，三清殿大门紧闭，进不去呀！"));
         this.storyDic["STORY0052"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","呆子，没看到上面有钥匙孔么，找到钥匙不就能进了么？"));
         this.storyDic["STORY0052"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE3","对呀，还是猴哥聪明，我们找找！"));
         this.storyDic["STORY0053"] = new Vector.<DialogVO>();
         this.storyDic["STORY0053"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","咦，这妖怪身上怎么会有这三清殿的钥匙？"));
         this.storyDic["STORY0053"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE3","猴哥，管那么多干啥，先填饱肚子！"));
         this.storyDic["STORY0053"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","呆子，只想着吃！路上就听闻这三清殿里有三位大仙，莫非是妖怪变幻而来？"));
         this.storyDic["STORY0053"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE3","饿死了，老猪先去吃了！"));
         this.storyDic["STORY0053"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","......这呆子，一句话没听进去。"));
         this.storyDic["STORY0054"] = new Vector.<DialogVO>();
         this.storyDic["STORY0054"].push(new DialogVO(0,"小白龙","BODY_LONG_MA0","好你个灵感大王，竟然用童男童女祭祀！别以为躲进通天河我们就治不了你！"));
         this.storyDic["STORY0054"].push(new DialogVO(1,"灵感大王","BODY_LING_GAN_WANG","哎呦，我当时谁，原来是西海三太子，这里可不是西海，你管不着！"));
         this.storyDic["STORY0054"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","别和他废话，妖怪先吃俺老孙一棒！"));
         this.storyDic["STORY0054"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","大师兄小心那些毒贝！"));
         this.storyDic["STORY0055"] = new Vector.<DialogVO>();
         this.storyDic["STORY0055"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE5","猴哥快看，那是什么？"));
         this.storyDic["STORY0055"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","嗯？这不是通天葫芦嘛！居然在这妖怪身上！"));
         this.storyDic["STORY0056"] = new Vector.<DialogVO>();
         this.storyDic["STORY0056"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","咦，那葫芦好像往花果山方向飞去了！"));
         this.storyDic["STORY0056"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","我们去花果山看看。"));
         this.storyDic["STORY0057"] = new Vector.<DialogVO>();
         this.storyDic["STORY0057"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","大师兄，怎么办？没路了，师傅应该就被关在前面的峰顶！"));
         this.storyDic["STORY0057"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE5","大伙快来，这里的云有古怪！"));
         this.storyDic["STORY0057"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","咦，这股妖气怎么有点熟悉,大家小心点！"));
         this.storyDic["STORY0058"] = new Vector.<DialogVO>();
         this.storyDic["STORY0058"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","我说怎么觉得熟悉，原来是太上老君的坐骑在此作怪！哼哼，就不怕太上老君收了你！"));
         this.storyDic["STORY0058"].push(new DialogVO(1,"独角兜大王","BODY_DU_JIAO","哼，少来唬我，老牛我来下界这么久，也不见主人找来!"));
         this.storyDic["STORY0058"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","少废话，赶紧放了我师傅，不然！！！！"));
         this.storyDic["STORY0058"].push(new DialogVO(1,"独角兜大王","BODY_DU_JIAO","不然怎么？别人怕你，俺老牛不怕你，看招！"));
         this.storyDic["STORY0059"] = new Vector.<DialogVO>();
         this.storyDic["STORY0059"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","大师兄，那女妖怪就是将师父给掳到这洞府里了！"));
         this.storyDic["STORY0059"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","嗯，我们一路杀过去！"));
         this.storyDic["STORY0060"] = new Vector.<DialogVO>();
         this.storyDic["STORY0060"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","大师兄，你来看看这是什么？"));
         this.storyDic["STORY0060"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","咦，这里怎么会有木系镇碑？"));
         this.storyDic["STORY0060"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE3","猴哥，这回只有我能进，你们都进不去了吧！"));
         this.storyDic["STORY0060"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","呆子，镇碑里的怪物虽然你能克制它们，但也要小心点，有好东西别私吞！"));
         this.storyDic["STORY0061"] = new Vector.<DialogVO>();
         this.storyDic["STORY0061"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE2","猴哥，这里哪？怎么这么热！"));
         this.storyDic["STORY0061"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG2","当年俺老孙大闹天宫，踢翻了太上老君的炼丹炉，火炭从天而落，便形成了火焰山！"));
         this.storyDic["STORY0061"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG2","实在是太热了，当地百姓也深受这毒热之苦。悟空，想想有没有办法。"));
         this.storyDic["STORY0061"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","有了，我这就去摩云洞找兄长牛魔王借扇芭蕉扇一用！"));
         this.storyDic["STORY0062"] = new Vector.<DialogVO>();
         this.storyDic["STORY0062"].push(new DialogVO(1,"铁扇公主","BODY_TIE_SHAN","你这泼猴！为何坑陷我儿子？！"));
         this.storyDic["STORY0062"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG4","啊，这从何说起啊？令郎是谁？"));
         this.storyDic["STORY0062"].push(new DialogVO(1,"铁扇公主","BODY_TIE_SHAN","我儿是枯松涧火云洞圣婴大王红孩儿！我们正没处寻你报仇，你今上门纳命，我岂肯饶你！看剑！"));
         this.storyDic["STORY0062"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG4","嫂嫂听我解释！"));
         this.storyDic["STORY0063"] = new Vector.<DialogVO>();
         this.storyDic["STORY0063"].push(new DialogVO(1,"牛魔王","BODY_NIU_MO_WANG","这个猢狲！害我子，欺我妻，还有脸过来有事求我，上来吃我一棍！"));
         this.storyDic["STORY0063"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","哥要说打，弟也不惧，但求芭蕉扇一用好过火焰山！"));
         this.storyDic["STORY0063"].push(new DialogVO(1,"牛魔王","BODY_NIU_MO_WANG","少说废话，看棍！"));
         this.storyDic["STORY0064"] = new Vector.<DialogVO>();
         this.storyDic["STORY0064"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG2","悟空，这流沙河如此湍急，我们该如何过去啊！"));
         this.storyDic["STORY0064"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","师父莫急，前面好像有个人，我去询问询问。"));
         this.storyDic["STORY0065"] = new Vector.<DialogVO>();
         this.storyDic["STORY0065"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","你好，请问......"));
         this.storyDic["STORY0065"].push(new DialogVO(1,"沙僧","BODY_SHA_SENG3","呀，我正饿着呢，有送上门来给我吃的！"));
         this.storyDic["STORY0066"] = new Vector.<DialogVO>();
         this.storyDic["STORY0066"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","怎么觉得悬空的古钟有点奇怪？"));
         this.storyDic["STORY0066"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE2","猴哥，盗取金光寺宝贝的妖贼就在前面，那到底追不追？"));
         this.storyDic["STORY0066"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","追，多注意留意那些古钟。"));
         this.storyDic["STORY0067"] = new Vector.<DialogVO>();
         this.storyDic["STORY0067"].push(new DialogVO(1,"九头虫","BODY_JIU_TOU_CHONG","原来是取经的和尚！怎么着，你要当出头鸟？"));
         this.storyDic["STORY0067"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","哼，你偷人家的宝物，害苦了金光寺僧人，俺老孙就是要替他们出头！"));
         this.storyDic["STORY0067"].push(new DialogVO(1,"九头虫","BODY_JIU_TOU_CHONG","既然这样，就来吧！刀剑无眼，要是伤了你的性命，误了你去取经可别怪我！"));
         this.storyDic["STORY0067"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","你这妖怪，何德何能，敢开大口！走上来，吃老孙一棒！"));
         this.storyDic["STORY0068"] = new Vector.<DialogVO>();
         this.storyDic["STORY0068"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG3","徒弟们，我们到了小雷音寺啦！阿弥陀佛！"));
         this.storyDic["STORY0068"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","嗯？感觉好像不大对劲......待俺老孙火眼金睛看个究竟！"));
         this.storyDic["STORY0068"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG2","啊！！！"));
         this.storyDic["STORY0068"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG2","大师兄，师父被掳走了！"));
         this.storyDic["STORY0068"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","可恶，这妖怪真是胆大包天，居然敢假扮如来！"));
         this.storyDic["STORY0069"] = new Vector.<DialogVO>();
         this.storyDic["STORY0069"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG3","悟空快看，那边有个闪闪发光的箱子！"));
         this.storyDic["STORY0069"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","诶？莫不是里面有啥宝贝？俺打开看看！"));
         this.storyDic["STORY0070"] = new Vector.<DialogVO>();
         this.storyDic["STORY0070"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","哈哈，果然里面有好东西呀！竟然是" + TxtUtil.setColor("【驭兽袋图谱】！","99ff00")));
         this.storyDic["STORY0070"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG3","可以养个【宠物】宝宝跟我们一起去取经了！"));
         this.storyDic["STORY0071"] = new Vector.<DialogVO>();
         this.storyDic["STORY0071"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","你们两人快回去保护师傅！前方哪里来的妖精？看到老孙还想跑！！"));
         this.storyDic["STORY0071"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG2","好！大师兄，你自己小心！"));
         this.storyDic["STORY0072"] = new Vector.<DialogVO>();
         this.storyDic["STORY0072"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","猴哥，这里怎么这么臭，老猪我快吃不消了！"));
         this.storyDic["STORY0072"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","这里应该是稀柿沟了，这回看它哪里跑！哼哼！"));
         this.storyDic["STORY0072"].push(new DialogVO(1,"蟒蛇精","BODY_MANG_SHE_JING","我和你们无冤无仇何必赶尽杀绝？"));
         this.storyDic["STORY0072"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","吃了庄里这么牛马，还抓了南山老和尚，作恶滔天，先吃俺老孙一棒！"));
         this.storyDic["STORY0073"] = new Vector.<DialogVO>();
         this.storyDic["STORY0073"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","这里好高，真要爬上去，我有点恐高啊！"));
         this.storyDic["STORY0073"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","呆子，你不是会飞么！有什么难的！"));
         this.storyDic["STORY0073"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","前面在金圣宫吃多了，有点飞不动了！"));
         this.storyDic["STORY0074"] = new Vector.<DialogVO>();
         this.storyDic["STORY0074"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","黄毛，赶紧放了金圣宫娘娘，不然，哼哼！！"));
         this.storyDic["STORY0074"].push(new DialogVO(1,"赛太岁","BODY_JIN_MAO_HOU","不然怎样，别人怕，我可不怕你这猴头！看我法宝紫金铃！"));
         this.storyDic["STORY0075"] = new Vector.<DialogVO>();
         this.storyDic["STORY0075"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","猴哥，这盘丝洞可比白骨洞恐怖多了啊！"));
         this.storyDic["STORY0075"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG4","是啊，也不知道师傅怎么样了。"));
         this.storyDic["STORY0075"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","大家镇定，师傅定是被那蜘蛛精掳到洞府深处了！"));
         this.storyDic["STORY0075"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","那我们赶紧去救师傅吧！"));
         this.storyDic["STORY0076"] = new Vector.<DialogVO>();
         this.storyDic["STORY0076"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","那蜘蛛精跑的好生快！"));
         this.storyDic["STORY0076"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","她不是说要找帮手么，估计就在这里了！"));
         this.storyDic["STORY0076"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","大师兄，此处看起来十分凶险，我们得小心一点！"));
         this.storyDic["STORY0077"] = new Vector.<DialogVO>();
         this.storyDic["STORY0077"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG2","猴哥，这里就是狮驼岭了吧，听说有三个大魔头！"));
         this.storyDic["STORY0077"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE3","有猴哥在，管他三个、五个的魔头，都不在话下。"));
         this.storyDic["STORY0077"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","呆子，别废话了，前面来了几个巡山小妖，我们去探探路。"));
         this.storyDic["STORY0078"] = new Vector.<DialogVO>();
         this.storyDic["STORY0078"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG2","这里就是清华洞了吧？！"));
         this.storyDic["STORY0078"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","应该是，比丘国国丈应该就在里面，这回看它往哪里跑。"));
         this.storyDic["STORY0078"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE3","猴哥，前方的蘑菇很诡异，要小心点。"));
         this.storyDic["STORY0079"] = new Vector.<DialogVO>();
         this.storyDic["STORY0079"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","猴哥，那老鼠精在哪？我怎么没看到"));
         this.storyDic["STORY0079"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","在那~站住，还我们师傅！"));
         this.storyDic["STORY0079"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","老鼠精这回看你往哪里逃！"));
         this.storyDic["STORY0080"] = new Vector.<DialogVO>();
         this.storyDic["STORY0080"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG3","徒儿们快看，我们到了天竺地界了！"));
         this.storyDic["STORY0080"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","太好了，看来离取到真经不远了呢！"));
         this.storyDic["STORY0080"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE3","哈哈哈，这回我要大吃一顿，让如来请客！"));
         this.storyDic["STORY0080"].push(new DialogVO(0,"小白龙","BODY_LONG_MA3","二师兄就知道吃，啊哈哈！"));
         this.storyDic["STORY0080"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG3","再过一阵子就苦尽甘来了！"));
         this.storyDic["STORY0081"] = new Vector.<DialogVO>();
         this.storyDic["STORY0081"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG3","悟空快来救救我！我又被抓了~"));
         this.storyDic["STORY0081"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","诶？这就来，这妖怪贼的很！"));
         this.storyDic["STORY0081"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","猴哥，又是这冰冻机关，要小心！"));
         this.storyDic["STORY0082"] = new Vector.<DialogVO>();
         this.storyDic["STORY0082"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","猴哥，这妖怪真是无语啊！"));
         this.storyDic["STORY0082"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG4","是啊，贼心不改，把我们兵器都盗走了。"));
         this.storyDic["STORY0082"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG5","走！去豹头山找他算账去。"));
         this.storyDic["STORY0083"] = new Vector.<DialogVO>();
         this.storyDic["STORY0083"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG2","大师兄，前方山顶一片黑云！"));
         this.storyDic["STORY0083"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","不好中计了！！"));
         this.storyDic["STORY0084"] = new Vector.<DialogVO>();
         this.storyDic["STORY0084"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","咦~师傅人呢？！"));
         this.storyDic["STORY0084"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG2","师傅~师傅~"));
         this.storyDic["STORY0084"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","兄弟们不用再叫了，师父已经被妖精捉走了！你们两个和其他的僧人一起回寺看守行李马匹，我跟着这股狂风去追！"));
         this.storyDic["STORY0085"] = new Vector.<DialogVO>();
         this.storyDic["STORY0085"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","猴哥，师傅让那兔子精给抓走了，那兔子挺标致的~~~"));
         this.storyDic["STORY0085"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","师傅能不能经受住那兔子的诱惑呀！"));
         this.storyDic["STORY0085"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","别废话了，赶紧去救师傅吧！"));
         this.storyDic["STORY0086"] = new Vector.<DialogVO>();
         this.storyDic["STORY0086"].push(new DialogVO(0,"沙僧","BODY_SHA_SENG2","大师兄，这些无字经书咋办？"));
         this.storyDic["STORY0086"].push(new DialogVO(0,"猪八戒","BODY_ZHU_BA_JIE4","猴哥，那2个和尚欺人太甚！！！竟然骗我们师徒四人！"));
         this.storyDic["STORY0086"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG0","气死老孙了，走，找阿难迦叶说理去！"));
         this.storyDic["STORY_XLN"] = new Vector.<DialogVO>();
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小龙女","BODY_LONG_NV4","哎哎哎，天天闷在家里太无聊了！"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小白龙","BODY_LONG_MA3","表妹，好久不见啊！"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小龙女","BODY_LONG_NV3","哇，表哥，你来啦！我要你陪我玩，不许走啦！"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小白龙","BODY_LONG_MA3","我现在有任务在身，不方便久留呢！"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小龙女","BODY_LONG_NV3","我听说了，你们几个是在保护唐僧去西天！我也要去！"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","妹妹，这可不是闹着玩的，一路很辛苦的，你还是在家乖乖待着吧！"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小龙女","BODY_LONG_NV0","哼，瞧不起人家！我偏要去！不让我去，小心我用火烧你屁股！"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","这......你老爸应该不会同意让你出去的吧。"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小龙女","BODY_LONG_NV3","哎呀放心啦，老头儿天天忙的，哪有空理我！"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小白龙","BODY_LONG_MA5","真拿你没办法。出去散散心也好，不过你也要乖一点。"));
         this.storyDic["STORY_XLN"].push(new DialogVO(0,"小龙女","BODY_LONG_NV3","知道啦！太棒了，可以出去玩咯！"));
         this.storyDic["STORY_PU_XIAN"] = new Vector.<DialogVO>();
         this.storyDic["STORY_PU_XIAN"].push(new DialogVO(0,"普贤菩萨","BODY_PU_XIAN","妖岛上的妖怪法力都比较高强！要小心！"));
         this.storyDic["STORY_RU_LAI0"] = new Vector.<DialogVO>();
         this.storyDic["STORY_RU_LAI0"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","弟子玄奘，奉东土大唐皇帝旨意，遥诣宝山，拜求真经，以济众生。"));
         this.storyDic["STORY_RU_LAI0"].push(new DialogVO(0,"如来佛祖","BODY_RU_LAI","玄奘前来是否求真经三十五部，共计一万五千一百四十四卷？"));
         this.storyDic["STORY_RU_LAI0"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","佛主料事如神！望我佛祖垂恩，早赐回国。"));
         this.storyDic["STORY_RU_LAI0"].push(new DialogVO(0,"如来佛祖","BODY_RU_LAI","经书早已准备好，不料昨日被某个圣妖偷去。"));
         this.storyDic["STORY_RU_LAI0"].push(new DialogVO(0,"孙悟空","BODY_SUN_WU_KONG3","哈哈，如来啊，在你大本营东西都能被偷啊，怎么这么挫~"));
         this.storyDic["STORY_RU_LAI0"].push(new DialogVO(0,"唐僧","BODY_TANG_SENG5","悟空，休得无礼。不知佛祖可有那圣妖下落？可让我几个劣徒前去找回。"));
         this.storyDic["STORY_RU_LAI0"].push(new DialogVO(0,"如来佛祖","BODY_RU_LAI","甚好！那圣妖往东北方向的妖岛逃去，妖岛常年妖气围绕，一路可要小心为妙！"));
         this.storyDic["STORY_RU_LAI1"] = new Vector.<DialogVO>();
         this.storyDic["STORY_RU_LAI1"].push(new DialogVO(0,"如来佛祖","BODY_RU_LAI","经历九九八十一难方可取得真经！"));
         this.storyDic["STORY_WEN_SHU"] = new Vector.<DialogVO>();
         this.storyDic["STORY_WEN_SHU"].push(new DialogVO(1,"文殊菩萨","BODY_WEN_SHU","妖岛上的妖怪法力都比较高强！要小心！"));
      }
      
      public static function instance() : StoryConfig
      {
         if(!_instance)
         {
            _instance = new StoryConfig();
         }
         return _instance;
      }
      
      public function findDialog(param1:String) : Vector.<DialogVO>
      {
         return this.storyDic[param1];
      }
   }
}

