package mogames.gameData.fuben.TZJS
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameData.role.HeroProxy;
   
   public class TZJSLimitVO
   {
      public var title:String;
      
      public var lvLimit:Sint;
      
      public function TZJSLimitVO(param1:String, param2:int)
      {
         super();
         this.title = param1;
         this.lvLimit = new Sint(param2);
      }
      
      public function get canEnter() : <PERSON><PERSON><PERSON>
      {
         var _loc2_:HeroGameVO = null;
         var _loc1_:Array = HeroProxy.instance().teamHero;
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_.level.v < this.lvLimit.v)
            {
               return false;
            }
         }
         return true;
      }
   }
}

