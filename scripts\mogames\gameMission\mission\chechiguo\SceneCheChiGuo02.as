package mogames.gameMission.mission.chechiguo
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameObj.trap.TrapFallCi;
   import mogames.gameObj.trap.TrapTuCi;
   import mogames.gameRole.enemy.BOSSLuDaXian;
   import mogames.gameRole.enemy.BOSSSanYaoHeTi;
   import mogames.gameRole.enemy.BOSSYangDaXian;
   import mogames.gameSystem.CitrusRender;
   import mogames.gameSystem.CitrusTimer;
   
   public class SceneCheChiGuo02 extends BossScene
   {
      private var _traps:Array;
      
      private var _trapTimer:CitrusTimer;
      
      private var _luBoss:BOSSLuDaXian;
      
      private var _yangBoss:BOSSYangDaXian;
      
      private var _falData:Object = {
         "hurt":new Sint(270),
         "hurtTime":new Snum(1.5),
         "interval":new Snum(12)
      };
      
      private var _tuciData:Object = {
         "interval":new Snum(8),
         "hurt":new Sint(290),
         "hurtTime":new Snum(0.5)
      };
      
      private var _enemyArgs:Array = [{
         "id":new Sint(2017),
         "attID":new Sint(20171),
         "aiID":new Sint(20171),
         "dropID":new Sint(0)
      },{
         "id":new Sint(2018),
         "attID":new Sint(20181),
         "aiID":new Sint(20181),
         "dropID":new Sint(0)
      },{
         "id":new Sint(2019),
         "attID":new Sint(20191),
         "aiID":new Sint(20191),
         "dropID":new Sint(0)
      },{
         "id":new Sint(2019),
         "attID":new Sint(20191),
         "aiID":new Sint(20191),
         "dropID":new Sint(0)
      }];
      
      public function SceneCheChiGuo02(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_BATTLE0");
         setWinPosition(new Rectangle(270,342,446,110),new Point(480,414));
         this._trapTimer = new CitrusTimer(true);
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.initFallTrap();
         this.addLuBoss();
         this.addYangBoss();
         CitrusRender.instance().add(this.checkBossDead);
      }
      
      private function initTuciTrap() : void
      {
         var _loc2_:TrapTuCi = null;
         var _loc1_:int = 0;
         while(_loc1_ < 2)
         {
            _loc2_ = new TrapTuCi();
            _loc2_.x = 265 + _loc1_ * 313;
            _loc2_.y = 447;
            _loc2_.createHitHurt(this._tuciData.hurt.v,this._tuciData.hurtTime.v,false,0);
            _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",5,5);
            _loc2_.initTimer(this._tuciData.interval.v);
            add(_loc2_);
            _loc1_++;
         }
      }
      
      private function initFallTrap() : void
      {
         var _loc1_:int = 0;
         var _loc2_:TrapFallCi = null;
         this._traps = [];
         _loc1_ = 0;
         while(_loc1_ < 2)
         {
            _loc2_ = new TrapFallCi();
            _loc2_.x = 60 + _loc1_ * 700;
            _loc2_.y = -408;
            _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",0,0);
            _loc2_.createHitHurt(this._falData.hurt.v,this._falData.hurtTime.v,false);
            add(_loc2_);
            this._traps[_loc1_] = _loc2_;
            _loc1_++;
         }
         this._trapTimer.setInterval(this._falData.interval.v + 2,0,this.activeFallTrap,null,false);
         this._trapTimer.startNone();
      }
      
      private function activeFallTrap() : void
      {
         var _loc1_:TrapFallCi = null;
         for each(_loc1_ in this._traps)
         {
            _loc1_.showTuCi();
         }
      }
      
      private function addLuBoss() : void
      {
         this._luBoss = new BOSSLuDaXian();
         this._luBoss.x = 700;
         this._luBoss.y = 450;
         this._luBoss.initData(30201,30201);
         this._luBoss.target = _mission.onePlayer;
         add(this._luBoss);
         this._luBoss.activeEnemy(false);
      }
      
      private function addYangBoss() : void
      {
         this._yangBoss = new BOSSYangDaXian();
         this._yangBoss.x = 500;
         this._yangBoss.y = 450;
         this._yangBoss.initData(30211,30211);
         this._yangBoss.target = _mission.onePlayer;
         this._yangBoss.enemyList = this._enemyArgs.slice();
         add(this._yangBoss);
         this._yangBoss.activeEnemy(false);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSSanYaoHeTi();
         _boss.x = 480;
         _boss.y = 311;
         _boss.initData(30221,30221,63);
         _boss.target = _mission.onePlayer;
         add(_boss);
         _boss.activeEnemy(false);
         _boss.aiEnabled = true;
         _boss.onRole.add(listenBOSS);
         EffectManager.instance().playBGM("BGM_BATTLE1");
      }
      
      private function checkBossDead() : void
      {
         if(!this._luBoss.isDead || !this._yangBoss.isDead)
         {
            return;
         }
         this.addBOSS();
         CitrusRender.instance().remove(this.checkBossDead);
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11017);
         this._trapTimer.pause();
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         CitrusRender.instance().remove(this.checkBossDead);
         this._luBoss = null;
         this._yangBoss = null;
         this._trapTimer.destroy();
         this._trapTimer = null;
         super.destroy();
      }
   }
}

