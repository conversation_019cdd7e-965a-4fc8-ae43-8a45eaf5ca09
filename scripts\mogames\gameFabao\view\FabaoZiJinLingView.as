package mogames.gameFabao.view
{
   import mogames.gameData.ConstRole;
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoZiJinLing;
   
   public class FabaoZiJinLingView extends FabaoBaseView
   {
      private var _fabao:FabaoZiJinLing;
      
      public function FabaoZiJinLingView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         this.addSound(param1);
         super.updateCurrentFrame(param1);
         if(!mcATK)
         {
            return;
         }
         if(curStatus == ConstRole.SKILL_ONE)
         {
            this._fabao.dispatchSkillOne();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as FabaoZiJinLing;
      }
      
      override protected function addSound(param1:int) : void
      {
         if(param1 == 54)
         {
            EffectManager.instance().playAudio("BOOM");
         }
         else if(param1 == 79)
         {
            EffectManager.instance().playAudio("FIRE_EXPLORE");
         }
         else if(param1 == 105)
         {
            EffectManager.instance().playAudio("SS_SKILL_THREE");
         }
      }
   }
}

