package mogames.gameMission.mission.qilinshan
{
   import flash.display.Bitmap;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerY;
   import mogames.gameObj.box2d.MoveTile;
   import mogames.gameObj.trap.TrapJinJi;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneQiLinShan01 extends BaseScene
   {
      private var _clouds:Array = [{
         "skin":"MC_MIDDLE_CLOUD_CLIP",
         "width":140,
         "height":26,
         "posX":206,
         "posY":2112
      },{
         "skin":"MC_MIDDLE_CLOUD_CLIP",
         "width":140,
         "height":26,
         "posX":443,
         "posY":1902
      },{
         "skin":"MC_MIDDLE_CLOUD_CLIP",
         "width":140,
         "height":26,
         "posX":696,
         "posY":2112
      },{
         "skin":"MC_MIDDLE_CLOUD_CLIP",
         "width":140,
         "height":26,
         "posX":376,
         "posY":1334
      },{
         "skin":"MC_LARGE_CLOUD_CLIP",
         "width":265,
         "height":26,
         "posX":208,
         "posY":712
      },{
         "skin":"MC_SMALL_CLOUD_CLIP",
         "width":63,
         "height":24,
         "posX":864,
         "posY":680
      },{
         "skin":"MC_SMALL_CLOUD_CLIP",
         "width":63,
         "height":24,
         "posX":864,
         "posY":400
      },{
         "skin":"MC_SMALL_CLOUD_CLIP",
         "width":63,
         "height":24,
         "posX":864,
         "posY":200
      },{
         "skin":"MC_MIDDLE_CLOUD_CLIP",
         "width":140,
         "height":26,
         "posX":556,
         "posY":252
      },{
         "skin":"MC_MIDDLE_CLOUD_CLIP",
         "width":140,
         "height":26,
         "posX":96,
         "posY":252
      }];
      
      private var _traps:Array = [{
         "skin":"MC_TRAP_JIN_JI01",
         "width":302,
         "height":42,
         "posX":-57,
         "posY":406
      },{
         "skin":"MC_TRAP_JIN_JI02",
         "width":372,
         "height":40,
         "posX":438,
         "posY":406
      },{
         "skin":"MC_TRAP_JIN_JI03",
         "width":146,
         "height":40,
         "posX":824,
         "posY":848
      }];
      
      private var _pTrigger0:LocalTriggerY;
      
      private var _pTrigger1:LocalTriggerY;
      
      private var _pTrigger2:LocalTriggerY;
      
      private var _pTrigger3:LocalTriggerY;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      public function SceneQiLinShan01(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [MoveTile];
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_SUNSHINE");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2045,1);
            TaskProxy.instance().addTask(11029);
         };
         super.handlerInit();
         this.layoutClouds();
         this.layoutTraps();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2045))
         {
            showDialog("STORY0073",func);
         }
      }
      
      private function layoutTraps() : void
      {
         var _loc1_:Object = null;
         var _loc2_:TrapJinJi = null;
         for each(_loc1_ in this._traps)
         {
            _loc2_ = new TrapJinJi(_loc1_.skin,_loc1_.width,_loc1_.height);
            Layers.addCEChild(_loc2_);
            _loc2_.x = _loc1_.posX;
            _loc2_.y = _loc1_.posY;
            _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",2,2);
            _loc2_.createHitHurt(500,1,false);
         }
      }
      
      private function layoutClouds() : void
      {
         var _loc1_:Object = null;
         var _loc2_:MoveTile = null;
         var _loc3_:MoveTile = null;
         var _loc4_:BaseCloud = null;
         for each(_loc1_ in this._clouds)
         {
            _loc4_ = new BaseCloud(_loc1_.skin,{
               "width":_loc1_.width,
               "height":_loc1_.height,
               "group":2,
               "oneWay":true
            });
            _loc4_.setLocation(_loc1_.posX,_loc1_.posY);
            _loc4_.setTime(1,5);
            Layers.addCEChild(_loc4_);
         }
         _loc2_ = new MoveTile("moveCloud0",{
            "x":708,
            "y":1470,
            "width":80,
            "height":18,
            "startX":708,
            "startY":1470,
            "endX":878,
            "endY":1470,
            "group":2,
            "oneWay":true
         });
         _loc2_.view = new Bitmap(GameInLoader.instance().findAsset("PIC_MOVING_CLOUD"));
         Layers.addCEChild(_loc2_);
         _loc3_ = new MoveTile("moveCloud1",{
            "x":386,
            "y":1024,
            "width":80,
            "height":18,
            "startX":386,
            "startY":1024,
            "endX":573,
            "endY":1024,
            "group":2,
            "oneWay":true
         });
         _loc3_.view = new Bitmap(GameInLoader.instance().findAsset("PIC_MOVING_CLOUD"));
         Layers.addCEChild(_loc3_);
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,1800,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[233,2025,150,100],[701,2025,150,100]],21011,this.triggerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[233,1365,150,100],[701,1365,150,100]],21012,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[345,617,150,100],[741,729,150,100]],21013,this.triggerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[127,238,150,100],[701,176,150,100]],21014,this.triggerEnd3);
         this._pTrigger0 = new LocalTriggerY(_mission.onePlayer,new Point(515,2200),1);
         this._pTrigger1 = new LocalTriggerY(_mission.onePlayer,new Point(515,1610),1);
         this._pTrigger2 = new LocalTriggerY(_mission.onePlayer,new Point(515,950),1);
         this._pTrigger3 = new LocalTriggerY(_mission.onePlayer,new Point(515,430),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(0);
      }
      
      private function triggerEnd3() : void
      {
         unlock();
         addLeaveDoor(340,334);
      }
      
      override protected function listenLeave() : void
      {
         (_mission as QiLinShanMission).onLeaveDoor();
      }
      
      override public function destroy() : void
      {
         this._clouds = null;
         this._traps = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         super.destroy();
      }
   }
}

