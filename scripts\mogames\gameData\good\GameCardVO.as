package mogames.gameData.good
{
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class GameCardVO extends GameATTVO
   {
      public function GameCardVO(param1:ConstGoodVO)
      {
         super(param1);
      }
      
      override public function get score() : int
      {
         var _loc1_:int = 0;
         if(_att.baseHP)
         {
            _loc1_ += _att.baseHP.value.v / 0.8;
         }
         if(_att.baseMP)
         {
            _loc1_ += _att.baseMP.value.v / 0.3;
         }
         if(_att.baseATK)
         {
            _loc1_ += _att.baseATK.value.v / 0.2;
         }
         if(_att.basePDEF)
         {
            _loc1_ += _att.basePDEF.value.v / 0.2;
         }
         if(_att.baseMDEF)
         {
            _loc1_ += _att.baseMDEF.value.v / 0.2;
         }
         if(_att.baseCRIT.v != 0)
         {
            _loc1_ += _att.baseCRIT.v / 0.06;
         }
         if(_att.baseMISS.v != 0)
         {
            _loc1_ += _att.baseMISS.v / 0.08;
         }
         return _loc1_;
      }
      
      public function get isBoss() : Boolean
      {
         return String(constVO.id.v).charAt(1) == "0";
      }
   }
}

