package mogames.gameMission.mission.tuqiu
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.enemy.yinglong.BOSSYingLong;
   
   public class SceneTuQiu extends BossScene
   {
      public function SceneTuQiu(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_BATTLE0");
         setWinPosition(new Rectangle(200,170,480,140),new Point(700,386));
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this.addBOSS();
         startBossBattle();
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSYingLong();
         _boss.x = 1200;
         _boss.y = 200;
         _boss.initData(30591,30591,205);
         _boss.target = _mission.onePlayer;
         add(_boss);
      }
   }
}

