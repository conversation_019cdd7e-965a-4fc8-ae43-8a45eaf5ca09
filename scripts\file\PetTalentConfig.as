package file
{
   import mogames.gameData.base.RandomVO;
   import mogames.gameData.drop.UseHandler;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.forge.NeedVO;
   
   public class PetTalentConfig
   {
      private static var _instance:PetTalentConfig;
      
      private var _list:Vector.<RandomVO>;
      
      public var costVO:BaseUseVO;
      
      public function PetTalentConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : PetTalentConfig
      {
         if(!_instance)
         {
            _instance = new PetTalentConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<RandomVO>();
         this._list[0] = new RandomVO(17,35);
         this._list[1] = new RandomVO(11,24);
         this._list[2] = new RandomVO(14,25);
         this._list[3] = new RandomVO(14,25);
         this.costVO = new BaseUseVO(10000,[new NeedVO(16757,1)]);
      }
      
      public function get costStr() : String
      {
         var _loc1_:Array = [this.costVO.goldStr,UseHandler.instance().parseNeed(this.costVO.needList)];
         return _loc1_.join(",");
      }
      
      public function findRandom(param1:int) : RandomVO
      {
         return this._list[param1];
      }
   }
}

