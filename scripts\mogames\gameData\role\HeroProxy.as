package mogames.gameData.role
{
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameData.heroSkill.CombineSkillVO;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.MathUtil;
   import utils.TxtUtil;
   
   public class HeroProxy
   {
      public static var isNewHero:Boolean;
      
      private static var _instance:HeroProxy;
      
      public var monkeyVO:HeroMonkeyVO;
      
      public var horseVO:HeroHorseVO;
      
      public var pigVO:HeroPigVO;
      
      public var bonzeVO:HeroBonzeVO;
      
      public var drakanVO:HeroDrakanVO;
      
      public var allCombines:Array;
      
      private var levels:Array = [0,5,10,15,20,25,30,35,40,45,50];
      
      public function HeroProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : HeroProxy
      {
         if(!_instance)
         {
            _instance = new HeroProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.monkeyVO = new HeroMonkeyVO();
         this.horseVO = null;
         this.pigVO = null;
         this.bonzeVO = null;
         this.drakanVO = null;
         isNewHero = false;
      }
      
      public function set loadData(param1:Object) : void
      {
         this.startNew();
         if(!param1)
         {
            return;
         }
         if(param1.monkey == "none")
         {
            this.monkeyVO = null;
         }
         else
         {
            this.monkeyVO = new HeroMonkeyVO();
            this.monkeyVO.parseLoadData(param1.monkey);
         }
         if(param1.horse == "none")
         {
            this.horseVO = null;
         }
         else
         {
            this.horseVO = new HeroHorseVO();
            this.horseVO.parseLoadData(param1.horse);
         }
         if(param1.pig == "none")
         {
            this.pigVO = null;
         }
         else
         {
            this.pigVO = new HeroPigVO();
            this.pigVO.parseLoadData(param1.pig);
         }
         if(param1.bonze == "none")
         {
            this.bonzeVO = null;
         }
         else
         {
            this.bonzeVO = new HeroBonzeVO();
            this.bonzeVO.parseLoadData(param1.bonze);
         }
         if(param1.drakan == "none")
         {
            this.drakanVO = null;
         }
         else
         {
            this.drakanVO = new HeroDrakanVO();
            this.drakanVO.parseLoadData(param1.drakan);
         }
         this.combineData = param1.skills;
      }
      
      public function get saveData() : Object
      {
         return {
            "monkey":(this.monkeyVO == null ? "none" : this.monkeyVO.collectSaveData()),
            "horse":(this.horseVO == null ? "none" : this.horseVO.collectSaveData()),
            "pig":(this.pigVO == null ? "none" : this.pigVO.collectSaveData()),
            "bonze":(this.bonzeVO == null ? "none" : this.bonzeVO.collectSaveData()),
            "drakan":(this.drakanVO == null ? "none" : this.drakanVO.collectSaveData()),
            "skills":this.combineData
         };
      }
      
      public function set combineData(param1:String) : void
      {
         var _loc3_:* = null;
         var _loc4_:Array = null;
         var _loc5_:CombineSkillVO = null;
         if(!param1 || param1 == "")
         {
            return;
         }
         var _loc2_:Array = param1.split("S");
         for each(_loc3_ in _loc2_)
         {
            _loc4_ = _loc3_.split("A");
            _loc5_ = this.findCombineSkill(int(_loc4_[0]));
            _loc5_.parseLoadData(_loc4_);
         }
      }
      
      public function get combineData() : String
      {
         var _loc2_:CombineSkillVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.allCombines)
         {
            _loc1_.push(_loc2_.collectSaveData());
         }
         return _loc1_.join("S");
      }
      
      public function unlockHero(param1:int) : void
      {
         switch(param1)
         {
            case 1002:
               this.horseVO = new HeroHorseVO();
               break;
            case 1003:
               this.pigVO = new HeroPigVO();
               break;
            case 1004:
               this.bonzeVO = new HeroBonzeVO();
               break;
            case 1005:
               this.drakanVO = new HeroDrakanVO();
         }
      }
      
      public function collectAllHero() : Array
      {
         return [this.monkeyVO,this.horseVO,this.pigVO,this.bonzeVO,this.drakanVO];
      }
      
      public function get enableHero() : Array
      {
         var _loc3_:HeroGameVO = null;
         var _loc1_:Array = [];
         var _loc2_:Array = this.collectAllHero();
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_ != null)
            {
               _loc1_.push(_loc3_);
            }
         }
         return _loc1_;
      }
      
      public function get teamHero() : Array
      {
         var _loc3_:HeroGameVO = null;
         var _loc1_:Array = [];
         var _loc2_:Array = this.collectAllHero();
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_ && _loc3_.teamSequence <= 2 && _loc3_.teamSequence > 0)
            {
               _loc1_.push(_loc3_);
            }
         }
         _loc1_.sortOn("teamSequence",Array.NUMERIC);
         return _loc1_;
      }
      
      public function heroIndex(param1:int) : int
      {
         var _loc2_:Array = this.collectAllHero();
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            if(_loc2_[_loc3_])
            {
               if(_loc2_[_loc3_].id.v == param1)
               {
                  return _loc3_;
               }
            }
            _loc3_++;
         }
         return 0;
      }
      
      public function parseHeroID(param1:int) : int
      {
         var _loc2_:Array = this.collectAllHero();
         if(_loc2_[param1])
         {
            return _loc2_[param1].id.v;
         }
         return 0;
      }
      
      public function findHeroByIndex(param1:int) : HeroGameVO
      {
         var _loc2_:Array = this.collectAllHero();
         return _loc2_[param1];
      }
      
      public function findHero(param1:int) : HeroGameVO
      {
         var _loc3_:HeroGameVO = null;
         var _loc2_:Array = this.collectAllHero();
         for each(_loc3_ in _loc2_)
         {
            if(Boolean(_loc3_) && _loc3_.id.v == param1)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function findCombineSkill(param1:int) : CombineSkillVO
      {
         var _loc2_:CombineSkillVO = null;
         for each(_loc2_ in this.allCombines)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function initBattleData() : void
      {
         var _loc2_:HeroGameVO = null;
         var _loc1_:Array = this.collectAllHero();
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_)
            {
               _loc2_.initBattleData();
            }
         }
      }
      
      public function cleanBattleData() : void
      {
         var _loc2_:HeroGameVO = null;
         var _loc1_:Array = this.collectAllHero();
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_)
            {
               _loc2_.cleanBattleData();
            }
         }
      }
      
      public function get totalBR() : int
      {
         var _loc2_:int = 0;
         var _loc3_:HeroGameVO = null;
         var _loc1_:Array = HeroProxy.instance().enableHero;
         for each(_loc3_ in _loc1_)
         {
            _loc2_ += _loc3_.battleRate;
         }
         return _loc2_;
      }
      
      public function get isLearnSkill() : Boolean
      {
         var _loc2_:HeroGameVO = null;
         var _loc1_:Array = this.collectAllHero();
         for each(_loc2_ in _loc1_)
         {
            if(Boolean(_loc2_) && _loc2_.isLearnSkill)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get isOnlyPig() : Boolean
      {
         if(!this.pigVO)
         {
            return false;
         }
         var _loc1_:Array = HeroProxy.instance().teamHero;
         if(_loc1_.length >= 2 || _loc1_.length <= 0)
         {
            return false;
         }
         if(_loc1_[0] is HeroPigVO)
         {
            return true;
         }
         return false;
      }
      
      public function get isOnlyDrakan() : Boolean
      {
         if(!this.drakanVO)
         {
            return false;
         }
         var _loc1_:Array = HeroProxy.instance().teamHero;
         if(_loc1_.length >= 2 || _loc1_.length <= 0)
         {
            return false;
         }
         if(_loc1_[0] is HeroDrakanVO)
         {
            return true;
         }
         return false;
      }
      
      public function get hasBiShuiZhu() : Boolean
      {
         var _loc2_:HeroGameVO = null;
         var _loc1_:Array = HeroProxy.instance().teamHero;
         for each(_loc2_ in _loc1_)
         {
            if(!(_loc2_.id.v == 1002 || _loc2_.id.v == 1005))
            {
               if(!this.isBiShuiZhu(_loc2_.fabao))
               {
                  PromptMediator.instance().showPrompt("出战角色未装备避水珠，无法进入水下！" + TxtUtil.setColor("（小白龙和小龙女无需避水珠）","FFFF00"),null,null,true);
                  return false;
               }
            }
         }
         return true;
      }
      
      public function hasFabao(param1:int) : Boolean
      {
         var _loc3_:HeroGameVO = null;
         var _loc2_:Array = this.collectAllHero();
         for each(_loc3_ in _loc2_)
         {
            if(!(!_loc3_ || !_loc3_.fabao))
            {
               if(_loc3_.fabao.constEquip.id.v == param1)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      private function isBiShuiZhu(param1:GameFabaoVO) : Boolean
      {
         if(!param1 || param1.constFabao.id.v != 23004)
         {
            return false;
         }
         return true;
      }
      
      public function get isEnterPK() : Boolean
      {
         var _loc2_:HeroGameVO = null;
         var _loc1_:Array = this.enableHero;
         if(_loc1_.length < 5)
         {
            return false;
         }
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_.level.v < 10)
            {
               return false;
            }
         }
         return true;
      }
      
      public function countLevelWD(param1:int) : int
      {
         var _loc2_:HeroGameVO = this.findHero(param1);
         if(!_loc2_)
         {
            return 0;
         }
         return MathUtil.checkWD(_loc2_.level.v,this.levels);
      }
   }
}

