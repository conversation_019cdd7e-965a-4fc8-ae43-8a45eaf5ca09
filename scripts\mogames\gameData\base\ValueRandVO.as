package mogames.gameData.base
{
   import file.ArgConfig;
   
   public class ValueRandVO
   {
      public var vo:RandomVO;
      
      public var type:Sint;
      
      public function ValueRandVO(param1:int, param2:int, param3:int = 0)
      {
         super();
         this.vo = new RandomVO(param1,param2);
         this.type = new Sint(param3);
      }
      
      public function get isPer() : Boolean
      {
         return this.type.v == 1;
      }
      
      public function normalRandom() : String
      {
         var _loc1_:int = this.vo.min.v;
         var _loc2_:int = this.vo.max.v;
         if(this.isPer)
         {
            return _loc1_ + "% ~ " + _loc2_ + "%";
         }
         return _loc1_ + " ~ " + _loc2_;
      }
      
      public function toString(param1:int = 99, param2:Boolean = false) : String
      {
         if(param1 == 99)
         {
            return this.randomQuality();
         }
         return this.singleQuality(param1,param2);
      }
      
      public function randomQuality() : String
      {
         var _loc1_:int = this.vo.min.v;
         var _loc2_:int = Math.ceil(this.vo.max.v * 1.8);
         if(this.isPer)
         {
            return _loc1_ + "% ~ " + _loc2_ + "%";
         }
         return _loc1_ + " ~ " + _loc1_;
      }
      
      public function singleQuality(param1:int, param2:Boolean) : String
      {
         var _loc3_:ArgVO = ArgConfig.instance().findQuality(param1,param2);
         var _loc4_:int = Math.ceil(this.vo.min.v * _loc3_.value.v);
         var _loc5_:int = Math.ceil(this.vo.max.v * _loc3_.value.v);
         if(this.isPer)
         {
            return _loc4_ + "% ~ " + _loc5_ + "%";
         }
         return _loc4_ + " ~ " + _loc5_;
      }
   }
}

