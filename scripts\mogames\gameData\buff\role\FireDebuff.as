package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class FireDebuff extends HurtBuff
   {
      public function FireDebuff()
      {
         super(1005);
      }
      
      override public function get buffSkin() : BMCSprite
      {
         _skin.setupSequence(AssetManager.findAnimation("EFFECT_BURN_CLIP"),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height;
         return _skin;
      }
   }
}

