package mogames.gameMission.mission.huangquanlu
{
   import citrus.objects.CitrusSprite;
   import flash.display.MovieClip;
   import mogames.Layers;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.co.HurtEffect;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   import mogames.gameRole.hero.BaseHero;
   import utils.TxtUtil;
   
   public class MoveTangSeng extends CitrusSprite implements IRole
   {
      private var _mc:CitrusMC;
      
      private var _hurtEffect:HurtEffect;
      
      private var _dead:Boolean;
      
      private var _hurtCount:int;
      
      public var owner:BaseHero;
      
      public var maxCount:int;
      
      public var speed:Number;
      
      public var dis:int;
      
      public function MoveTangSeng()
      {
         updateCallEnabled = false;
         super("MoveTangSeng",{
            "width":48,
            "height":112,
            "group":3
         });
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("MOVE_TANG_SENG_CLIP") as MovieClip);
         this._mc.changeAnimation("move",true);
         this.view = this._mc;
         this._hurtEffect = new HurtEffect();
         BattleMediator.instance().onSwitch.add(this.listenSwitch);
      }
      
      protected function listenSwitch(param1:BaseHero) : void
      {
         this.owner = param1;
      }
      
      override public function update(param1:Number) : void
      {
         if(this._dead)
         {
            return;
         }
         if(!this.owner || this.owner.isDead)
         {
            return;
         }
         if(this.distance <= this.dis + this.speed && this.distance >= this.dis - this.speed)
         {
            return;
         }
         if(this.distance < this.dis)
         {
            x += this.speed;
            _inverted = false;
         }
         else
         {
            _inverted = true;
            x -= this.speed;
         }
      }
      
      public function get distance() : int
      {
         return Math.abs(x - this.owner.x);
      }
      
      private function listenHurt(param1:BaseHitVO) : void
      {
         if(this._dead)
         {
            return;
         }
         if(Boolean(param1.source) && param1.source.hasTargets(this))
         {
            return;
         }
         if(!this._mc.mc.mcAreaHurt || !this._mc.mc.mcAreaHurt.hitTestObject(param1.range))
         {
            return;
         }
         if(param1.source)
         {
            param1.source.addTargets(this);
         }
         ++this._hurtCount;
         if(this._hurtCount >= this.maxCount)
         {
            this.handlerDead();
         }
         else
         {
            this._hurtEffect.setupTarget(this._mc);
         }
         EffectManager.instance().addBMCEffect(param1.hurtSkin,Layers.ceEffectLayer,x,y);
         EffectManager.instance().playAudio(param1.hurtSound);
         EffectManager.instance().addHeadWord(TxtUtil.setColor("唐僧生命剩余" + (this.maxCount - this._hurtCount) + "点！"),x,y - height);
      }
      
      public function start() : void
      {
         updateCallEnabled = true;
         BattleMediator.instance().onEnemyATK.add(this.listenHurt);
      }
      
      public function handlerDead() : void
      {
         this._dead = true;
         this._mc.changeAnimation("dead",false);
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return false;
      }
      
      public function addTargets(param1:IRole) : void
      {
      }
      
      public function cleanTargets() : void
      {
      }
      
      public function hitBack() : void
      {
      }
      
      public function get isDead() : Boolean
      {
         return this._dead;
      }
      
      override public function destroy() : void
      {
         BattleMediator.instance().onEnemyATK.remove(this.listenHurt);
         this._mc.destroy();
         this._mc = null;
         this.owner = null;
         this._hurtEffect.destroy();
         this._hurtEffect = null;
         super.destroy();
      }
   }
}

