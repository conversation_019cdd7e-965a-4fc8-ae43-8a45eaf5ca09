package citrus.input
{
   import citrus.core.CitrusEngine;
   import citrus.core.citrus_internal;
   import citrus.input.controllers.Keyboard;
   import org.osflash.signals.Signal;
   
   use namespace citrus_internal;
   
   public class Input
   {
      protected var _ce:CitrusEngine;
      
      protected var _timeActive:int = 0;
      
      protected var _enabled:Boolean = true;
      
      protected var _initialized:<PERSON><PERSON><PERSON>;
      
      protected var _controllers:Vector.<InputController>;
      
      protected var _actions:Vector.<InputAction>;
      
      public var clearDisposedActionsInterval:uint = 480;
      
      public var triggersEnabled:Boolean = true;
      
      protected var _routeActions:<PERSON><PERSON>an = false;
      
      protected var _routeChannel:uint;
      
      internal var actionON:Signal;
      
      internal var actionOFF:Signal;
      
      internal var actionCHANGE:Signal;
      
      public var keyboard:Keyboard;
      
      public function Input()
      {
         super();
         this._controllers = new Vector.<InputController>();
         this._actions = new Vector.<InputAction>();
         this.actionON = new Signal(InputAction);
         this.actionOFF = new Signal(InputAction);
         this.actionCHANGE = new Signal(InputAction);
         this.actionON.add(this.doActionON);
         this.actionOFF.add(this.doActionOFF);
         this.actionCHANGE.add(this.doActionCHANGE);
         this._ce = CitrusEngine.getInstance();
      }
      
      public function initialize() : void
      {
         if(this._initialized)
         {
            return;
         }
         this.keyboard = new Keyboard("keyboard");
         this._initialized = true;
      }
      
      public function addController(param1:InputController) : void
      {
         if(this._controllers.lastIndexOf(param1) < 0)
         {
            this._controllers.push(param1);
         }
      }
      
      public function addAction(param1:InputAction) : void
      {
         if(this._actions.lastIndexOf(param1) < 0)
         {
            this._actions[this._actions.length] = param1;
         }
      }
      
      public function controllerExists(param1:String) : Boolean
      {
         var _loc2_:InputController = null;
         for each(_loc2_ in this._controllers)
         {
            if(param1 == _loc2_.name)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getControllerByName(param1:String) : InputController
      {
         var _loc2_:InputController = null;
         for each(_loc2_ in this._controllers)
         {
            if(param1 == _loc2_.name)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function hasDone(param1:String, param2:int = -1) : InputAction
      {
         var _loc3_:InputAction = null;
         for each(_loc3_ in this._actions)
         {
            _loc3_.name == param1 && (param2 > -1 ? (this._routeActions ? this._routeChannel == param2 : _loc3_.channel == param2) : true);
            if(_loc3_.phase == InputPhase.END)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function isDoing(param1:String, param2:int = -1) : InputAction
      {
         var _loc3_:InputAction = null;
         for each(_loc3_ in this._actions)
         {
            _loc3_.name == param1 && (param2 > -1 ? (this._routeActions ? this._routeChannel == param2 : _loc3_.channel == param2) : true);
            if(_loc3_.time > 1 && _loc3_.phase < InputPhase.END)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function justDid(param1:String, param2:int = -1) : InputAction
      {
         var _loc3_:InputAction = null;
         for each(_loc3_ in this._actions)
         {
            _loc3_.name == param1 && (param2 > -1 ? (this._routeActions ? this._routeChannel == param2 : _loc3_.channel == param2) : true);
            if(_loc3_.time == 1)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function getAction(param1:String, param2:int = -1, param3:InputController = null, param4:int = -1) : InputAction
      {
         var _loc5_:InputAction = null;
         for each(_loc5_ in this._actions)
         {
            param1 == _loc5_.name && (param2 == -1 ? true : (this._routeActions ? this._routeChannel == param2 : _loc5_.channel == param2));
            param3 != null ? _loc5_.controller == param3 : true;
            if(param4 == -1 ? true : _loc5_.phase == param4)
            {
               return _loc5_;
            }
         }
         return null;
      }
      
      public function getActions(param1:int = -1, param2:InputController = null, param3:int = -1) : Vector.<InputAction>
      {
         var _loc5_:InputAction = null;
         var _loc4_:Vector.<InputAction> = new Vector.<InputAction>();
         for each(_loc5_ in this._actions)
         {
            param1 == -1 ? true : (this._routeActions ? this._routeChannel == param1 : _loc5_.channel == param1);
            param2 != null ? _loc5_.controller == param2 : true;
            if(param3 == -1 ? true : _loc5_.phase == param3)
            {
               _loc4_.push(_loc5_);
            }
         }
         return _loc4_;
      }
      
      internal function doActionON(param1:InputAction) : void
      {
         var _loc2_:InputAction = null;
         if(!this.triggersEnabled)
         {
            param1.dispose();
            return;
         }
         for each(_loc2_ in this._actions)
         {
            if(_loc2_.eq(param1))
            {
               _loc2_._phase = InputPhase.BEGIN;
               param1.dispose();
               return;
            }
         }
         param1._phase = InputPhase.BEGIN;
         this._actions[this._actions.length] = param1;
      }
      
      internal function doActionOFF(param1:InputAction) : void
      {
         var _loc2_:InputAction = null;
         if(!this.triggersEnabled)
         {
            param1.dispose();
            return;
         }
         for each(_loc2_ in this._actions)
         {
            if(_loc2_.eq(param1))
            {
               _loc2_._phase = InputPhase.END;
               _loc2_._value = param1._value;
               _loc2_._message = param1._message;
               param1.dispose();
               return;
            }
         }
      }
      
      internal function doActionCHANGE(param1:InputAction) : void
      {
         var _loc2_:InputAction = null;
         if(!this.triggersEnabled)
         {
            param1.dispose();
            return;
         }
         for each(_loc2_ in this._actions)
         {
            if(_loc2_.eq(param1))
            {
               _loc2_._phase = InputPhase.ON;
               _loc2_._value = param1._value;
               _loc2_._message = param1._message;
               param1.dispose();
               return;
            }
         }
         param1._phase = InputPhase.BEGIN;
         this._actions[this._actions.length] = param1;
      }
      
      citrus_internal function update() : void
      {
         var _loc1_:InputController = null;
         var _loc2_:String = null;
         if(InputAction.disposed.length > 0 && this._timeActive % this.clearDisposedActionsInterval == 0)
         {
            InputAction.clearDisposed();
         }
         ++this._timeActive;
         if(!this._enabled)
         {
            return;
         }
         for each(_loc1_ in this._controllers)
         {
            if(_loc1_.updateEnabled && _loc1_.enabled)
            {
               _loc1_.update();
            }
         }
         for(_loc2_ in this._actions)
         {
            ++InputAction(this._actions[_loc2_]).itime;
            if(this._actions[_loc2_].phase > InputPhase.END)
            {
               this._actions[_loc2_].dispose();
               this._actions.splice(uint(_loc2_),1);
            }
            else if(this._actions[_loc2_].phase !== InputPhase.ON)
            {
               ++this._actions[_loc2_]._phase;
            }
         }
      }
      
      public function removeController(param1:InputController) : void
      {
         var _loc2_:int = int(this._controllers.lastIndexOf(param1));
         this.stopActionsOf(param1);
         this._controllers.splice(_loc2_,1);
      }
      
      public function stopActionsOf(param1:InputController, param2:int = -1) : void
      {
         var _loc3_:InputAction = null;
         for each(_loc3_ in this._actions)
         {
            if(_loc3_.controller == param1)
            {
               if(param2 > -1)
               {
                  if(_loc3_.channel == param2)
                  {
                     _loc3_._phase = InputPhase.ENDED;
                  }
               }
               else
               {
                  _loc3_._phase = InputPhase.ENDED;
               }
            }
         }
      }
      
      public function resetActions() : void
      {
         this._actions.length = 0;
      }
      
      public function addOrSetAction(param1:InputAction) : void
      {
         var _loc2_:InputAction = null;
         for each(_loc2_ in this._actions)
         {
            if(_loc2_.eq(param1))
            {
               _loc2_._phase = param1.phase;
               _loc2_._value = param1.value;
               return;
            }
         }
         this._actions[this._actions.length] = param1;
      }
      
      public function getActionsSnapshot() : Vector.<InputAction>
      {
         var _loc2_:InputAction = null;
         var _loc1_:Vector.<InputAction> = new Vector.<InputAction>();
         for each(_loc2_ in this._actions)
         {
            _loc1_.push(_loc2_.clone());
         }
         return _loc1_;
      }
      
      public function startRouting(param1:uint) : void
      {
         this._routeActions = true;
         this._routeChannel = param1;
      }
      
      public function stopRouting() : void
      {
         this._routeActions = false;
         this._routeChannel = 0;
      }
      
      public function isRouting() : Boolean
      {
         return this._routeActions;
      }
      
      public function get enabled() : Boolean
      {
         return this._enabled;
      }
      
      public function set enabled(param1:Boolean) : void
      {
         var _loc2_:InputController = null;
         if(this._enabled == param1)
         {
            return;
         }
         for each(_loc2_ in this._controllers)
         {
            _loc2_.enabled = param1;
         }
         this._enabled = param1;
      }
      
      private function destroyControllers() : void
      {
         var _loc1_:InputController = null;
         for each(_loc1_ in this._controllers)
         {
            _loc1_.destroy();
         }
         this._controllers.length = 0;
         this._actions.length = 0;
      }
      
      public function destroy() : void
      {
         this.destroyControllers();
         this.actionON.removeAll();
         this.actionOFF.removeAll();
         this.actionCHANGE.removeAll();
         this.resetActions();
         InputAction.clearDisposed();
      }
   }
}

