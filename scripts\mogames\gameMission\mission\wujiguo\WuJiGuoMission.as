package mogames.gameMission.mission.wujiguo
{
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.fuben.shuilao.ShuiLaoVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameObj.trap.TrapTuCi;
   
   public class WuJiGuoMission extends BaseMission
   {
      private var _trapData:Object = {
         "interval":new Snum(8),
         "hurt":new Sint(120),
         "hurtTime":new Snum(0.5)
      };
      
      public var shuilaoVO:ShuiLaoVO;
      
      public function WuJiGuoMission()
      {
         super();
      }
      
      public function createTrap(param1:int, param2:int) : void
      {
         var _loc3_:TrapTuCi = new TrapTuCi();
         _loc3_.x = param1;
         _loc3_.y = param2;
         _loc3_.createHitHurt(this._trapData.hurt.v,this._trapData.hurtTime.v,false,0);
         _loc3_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",5,5);
         _loc3_.initTimer(this._trapData.interval.v);
         Layers.addCEChild(_loc3_);
      }
      
      public function enterShuiLao(param1:ShuiLaoVO) : void
      {
         this.shuilaoVO = param1;
         if(!param1)
         {
            return;
         }
         this.handlerTouchDoor(1004,84,434);
      }
      
      public function leaveShuiLao() : void
      {
         this.shuilaoVO = null;
         this.handlerTouchDoor(1003,1170,445);
      }
      
      override protected function handlerTouchDoor(param1:int, param2:int, param3:int) : void
      {
         if(param1 == 1003 && !HeroProxy.instance().hasBiShuiZhu)
         {
            return;
         }
         super.handlerTouchDoor(param1,param2,param3);
      }
      
      override public function cleanMission() : void
      {
         this._trapData = null;
         this.shuilaoVO = null;
         super.cleanMission();
      }
   }
}

