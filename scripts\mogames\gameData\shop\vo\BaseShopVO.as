package mogames.gameData.shop.vo
{
   import file.GoodConfig;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.GameGoodVO;
   
   public class BaseShopVO
   {
      public var shopID:int;
      
      public var goodID:Sint;
      
      public var price:Sint;
      
      public var priceType:int;
      
      public function BaseShopVO(param1:int, param2:int, param3:int)
      {
         super();
         this.shopID = param1;
         this.goodID = new Sint(param2);
         this.price = new Sint(param3);
      }
      
      public function newGood(param1:int = 1) : GameGoodVO
      {
         if(this.goodID.v == 0)
         {
            return null;
         }
         var _loc2_:GameGoodVO = GoodConfig.instance().newGood(this.goodID.v);
         _loc2_.amount.v = param1;
         return _loc2_;
      }
      
      public function usePrice(param1:int) : void
      {
      }
      
      public function checkLack(param1:int = 1) : LackVO
      {
         return null;
      }
      
      public function askStr(param1:int) : String
      {
         return "";
      }
   }
}

