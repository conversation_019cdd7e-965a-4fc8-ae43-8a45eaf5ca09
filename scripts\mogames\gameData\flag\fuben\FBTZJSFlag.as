package mogames.gameData.flag.fuben
{
   import mogames.gameData.ConstData;
   import mogames.gameData.MasterProxy;
   
   public class FBTZJSFlag extends FBBaseFlag
   {
      public function FBTZJSFlag(param1:int, param2:int)
      {
         super(param1,param2);
      }
      
      override public function get totalFree() : int
      {
         if(MasterProxy.instance().gameVIP.v >= 2)
         {
            return _totalFree.v + ConstData.DATA_NUM2.v;
         }
         return _totalFree.v;
      }
   }
}

