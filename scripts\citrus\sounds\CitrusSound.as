package citrus.sounds
{
   import citrus.core.CitrusEngine;
   import citrus.core.citrus_internal;
   import citrus.events.CitrusEvent;
   import citrus.events.CitrusEventDispatcher;
   import citrus.events.CitrusSoundEvent;
   import flash.events.ErrorEvent;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.media.Sound;
   import flash.media.SoundTransform;
   import flash.net.URLRequest;
   
   use namespace citrus_internal;
   
   public class CitrusSound extends CitrusEventDispatcher
   {
      public var hideParamWarnings:Boolean = false;
      
      protected var _name:String;
      
      protected var _soundTransform:SoundTransform;
      
      protected var _sound:Sound;
      
      protected var _ioerror:Boolean = false;
      
      protected var _loadedRatio:Number = 0;
      
      protected var _loaded:Boolean = false;
      
      protected var _group:CitrusSoundGroup;
      
      protected var _isPlaying:Boolean = false;
      
      protected var _urlReq:URLRequest;
      
      protected var _volume:Number = 1;
      
      protected var _panning:Number = 0;
      
      protected var _mute:Boolean = false;
      
      protected var _paused:Boolean = false;
      
      protected var _ce:CitrusEngine;
      
      public var loops:int = 0;
      
      internal var soundInstances:Vector.<CitrusSoundInstance>;
      
      public var permanent:Boolean = false;
      
      public var autoload:Boolean = false;
      
      public function CitrusSound(param1:String, param2:Object = null)
      {
         super();
         this._ce = CitrusEngine.getInstance();
         this._ce.sound.citrus_internal::addDispatchChild(this);
         this._name = param1;
         if(!("sound" in param2) || param2["sound"] == null)
         {
            throw new Error(String(String(this) + " sound " + param1 + " has no sound param defined."));
         }
         this.soundInstances = new Vector.<CitrusSoundInstance>();
         this.setParams(param2);
         if(this.autoload)
         {
            this.load();
         }
      }
      
      public function load() : void
      {
         if(this._urlReq && this._loadedRatio == 0 && !this._sound.isBuffering)
         {
            this._ioerror = false;
            this._loaded = false;
            this._sound.addEventListener(IOErrorEvent.IO_ERROR,this.onIOError);
            this._sound.addEventListener(ProgressEvent.PROGRESS,this.onProgress);
            this._sound.load(this._urlReq);
         }
      }
      
      public function unload() : void
      {
         if(this._sound.isBuffering)
         {
            this._sound.close();
         }
         this._sound.removeEventListener(IOErrorEvent.IO_ERROR,this.onIOError);
         this._sound.removeEventListener(ProgressEvent.PROGRESS,this.onProgress);
         this.sound = this._urlReq;
      }
      
      public function play() : CitrusSoundInstance
      {
         return new CitrusSoundInstance(this,true,true);
      }
      
      public function createInstance(param1:Boolean = false, param2:Boolean = true) : CitrusSoundInstance
      {
         return new CitrusSoundInstance(this,param1,param2);
      }
      
      public function resume() : void
      {
         var _loc1_:CitrusSoundInstance = null;
         for each(_loc1_ in this.soundInstances)
         {
            if(_loc1_.isPaused)
            {
               _loc1_.resume();
            }
         }
      }
      
      public function pause() : void
      {
         var _loc1_:CitrusSoundInstance = null;
         for each(_loc1_ in this.soundInstances)
         {
            if(_loc1_.isPlaying)
            {
               _loc1_.pause();
            }
         }
      }
      
      public function stop() : void
      {
         var _loc1_:CitrusSoundInstance = null;
         for each(_loc1_ in this.soundInstances)
         {
            if(_loc1_.isPlaying || _loc1_.isPaused)
            {
               _loc1_.stop();
            }
         }
      }
      
      protected function onIOError(param1:ErrorEvent) : void
      {
         this.unload();
         this._ioerror = true;
         dispatchEvent(new CitrusSoundEvent(CitrusSoundEvent.SOUND_ERROR,this,null) as CitrusEvent);
      }
      
      protected function onProgress(param1:ProgressEvent) : void
      {
         this._loadedRatio = this._sound.bytesLoaded / this._sound.bytesTotal;
         if(this._loadedRatio == 1)
         {
            this._loaded = true;
            dispatchEvent(new CitrusSoundEvent(CitrusSoundEvent.SOUND_LOADED,this,null));
         }
      }
      
      internal function resetSoundTransform(param1:Boolean = false) : SoundTransform
      {
         var _loc2_:CitrusSoundInstance = null;
         if(this._soundTransform == null)
         {
            this._soundTransform = new SoundTransform();
         }
         if(this._group != null)
         {
            this._soundTransform.volume = this._mute || this._group._mute || this._ce.sound.masterMute ? 0 : this._volume * this._group._volume * this._ce.sound.masterVolume;
            this._soundTransform.pan = this._panning;
         }
         else
         {
            this._soundTransform.volume = this._mute || this._ce.sound.masterMute ? 0 : this._volume * this._ce.sound.masterVolume;
            this._soundTransform.pan = this._panning;
         }
         if(param1)
         {
            for each(_loc2_ in this.soundInstances)
            {
               _loc2_.resetSoundTransform(false);
            }
         }
         return this._soundTransform;
      }
      
      public function set sound(param1:Object) : void
      {
         if(this._sound)
         {
            this._sound.removeEventListener(IOErrorEvent.IO_ERROR,this.onIOError);
            this._sound.removeEventListener(ProgressEvent.PROGRESS,this.onProgress);
         }
         if(param1 is String)
         {
            this._urlReq = new URLRequest(param1 as String);
            this._sound = new Sound();
         }
         else if(param1 is Class)
         {
            this._sound = new (Class(param1))();
            this._ioerror = false;
            this._loadedRatio = 1;
            this._loaded = true;
         }
         else if(param1 is Sound)
         {
            this._sound = param1 as Sound;
            this._loadedRatio = 1;
            this._loaded = true;
         }
         else
         {
            if(!(param1 is URLRequest))
            {
               throw new Error("CitrusSound, " + param1 + "is not a valid sound paramater");
            }
            this._urlReq = param1 as URLRequest;
            this._sound = new Sound();
         }
      }
      
      public function get sound() : Object
      {
         return this._sound;
      }
      
      public function get isPlaying() : Boolean
      {
         var _loc1_:CitrusSoundInstance = null;
         for each(_loc1_ in this.soundInstances)
         {
            if(_loc1_.isPlaying)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get isPaused() : Boolean
      {
         var _loc1_:CitrusSoundInstance = null;
         for each(_loc1_ in this.soundInstances)
         {
            if(_loc1_.isPaused)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get group() : *
      {
         return this._group;
      }
      
      public function set volume(param1:Number) : void
      {
         if(this._volume != param1)
         {
            this._volume = param1;
            this.resetSoundTransform(true);
         }
      }
      
      public function set panning(param1:Number) : void
      {
         if(this._panning != param1)
         {
            this._panning = param1;
            this.resetSoundTransform(true);
         }
      }
      
      public function set mute(param1:Boolean) : void
      {
         if(this._mute != param1)
         {
            this._mute = param1;
            this.resetSoundTransform(true);
         }
      }
      
      public function set group(param1:*) : void
      {
         this._group = CitrusEngine.getInstance().sound.getGroup(param1);
         if(this._group)
         {
            this._group.addSound(this);
         }
      }
      
      public function setGroup(param1:CitrusSoundGroup) : void
      {
         this._group = param1;
      }
      
      internal function destroy() : void
      {
         var _loc1_:CitrusSoundInstance = null;
         if(this._sound)
         {
            this._sound.removeEventListener(IOErrorEvent.IO_ERROR,this.onIOError);
            this._sound.removeEventListener(ProgressEvent.PROGRESS,this.onProgress);
         }
         if(this._group)
         {
            this._group.removeSound(this);
         }
         this._soundTransform = null;
         this._sound = null;
         for each(_loc1_ in this.soundInstances)
         {
            _loc1_.stop();
         }
         removeEventListeners();
         this._ce.sound.citrus_internal::removeDispatchChild(this);
      }
      
      public function get loadedRatio() : Number
      {
         return this._loadedRatio;
      }
      
      public function get loaded() : Boolean
      {
         return this._loaded;
      }
      
      public function get ioerror() : Boolean
      {
         return this._ioerror;
      }
      
      public function get volume() : Number
      {
         return this._volume;
      }
      
      public function get panning() : Number
      {
         return this._panning;
      }
      
      public function get mute() : Boolean
      {
         return this._mute;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function get soundTransform() : SoundTransform
      {
         return this._soundTransform;
      }
      
      public function get ready() : Boolean
      {
         if(this._sound)
         {
            if(this._sound.isURLInaccessible)
            {
               return false;
            }
            if(this._sound.isBuffering || this._loadedRatio > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get instances() : Vector.<CitrusSoundInstance>
      {
         return this.soundInstances.slice();
      }
      
      public function getInstance(param1:int) : CitrusSoundInstance
      {
         if(this.soundInstances.length > param1 + 1)
         {
            return this.soundInstances[param1];
         }
         return null;
      }
      
      protected function setParams(param1:Object) : void
      {
         var param:String = null;
         var params:Object = param1;
         for(param in params)
         {
            try
            {
               if(params[param] == "true")
               {
                  this[param] = true;
               }
               else if(params[param] == "false")
               {
                  this[param] = false;
               }
               else
               {
                  this[param] = params[param];
               }
            }
            catch(e:Error)
            {
               if(hideParamWarnings)
               {
               }
            }
         }
      }
   }
}

