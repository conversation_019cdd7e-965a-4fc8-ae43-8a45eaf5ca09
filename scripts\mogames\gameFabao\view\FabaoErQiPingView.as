package mogames.gameFabao.view
{
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoErQiPing;
   
   public class FabaoErQiPingView extends FabaoBaseView
   {
      private var _fabao:FabaoErQiPing;
      
      public function FabaoErQiPingView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         this.addSound(param1);
         if(param1 == 50)
         {
            this._fabao.handlerSkillOne();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as FabaoErQiPing;
      }
      
      override protected function addSound(param1:int) : void
      {
         if(param1 == 35)
         {
            EffectManager.instance().playAudio("SKILL_YU_JIN_PING");
         }
      }
   }
}

