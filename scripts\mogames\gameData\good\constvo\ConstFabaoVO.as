package mogames.gameData.good.constvo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.ValueRandVO;
   
   public class ConstFabaoVO extends ConstEquipVO
   {
      public var wuxing:Sint;
      
      public function ConstFabaoVO(param1:int, param2:String, param3:String, param4:int, param5:int, param6:ValueRandVO, param7:ValueRandVO, param8:ValueRandVO, param9:ValueRandVO, param10:ValueRandVO, param11:ValueRandVO, param12:ValueRandVO, param13:ValueRandVO, param14:Array, param15:int, param16:String, param17:String = "", param18:String = "")
      {
         this.wuxing = new Sint(param4);
         super(param1,param2,param3,1,0,param5,0,0,param6,param7,param8,param9,param10,param11,param12,param13,param14,param15,param16,param17,param18);
      }
   }
}

