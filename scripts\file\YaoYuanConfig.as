package file
{
   import mogames.gameData.shop.vo.BaseCostVO;
   import mogames.gameData.shop.vo.GameCostVO;
   import mogames.gameData.yaoyuan.vo.HerbGroudVO;
   
   public class YaoYuanConfig
   {
      private static var _instance:YaoYuanConfig;
      
      private var _grounds:Vector.<HerbGroudVO>;
      
      private var _openList:Vector.<BaseCostVO>;
      
      public function YaoYuanConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.initYaoyuan();
         this.initOpenList();
      }
      
      public static function instance() : YaoYuanConfig
      {
         if(!_instance)
         {
            _instance = new YaoYuanConfig();
         }
         return _instance;
      }
      
      private function initYaoyuan() : void
      {
         this._grounds = new Vector.<HerbGroudVO>();
         this._grounds.push(new HerbGroudVO(0,1,0));
         this._grounds.push(new HerbGroudVO(302,2,0));
         this._grounds.push(new HerbGroudVO(303,3,1));
         this._grounds.push(new HerbGroudVO(304,4,1));
         this._grounds.push(new HerbGroudVO(305,5,2));
         this._grounds.push(new HerbGroudVO(306,6,2));
         this._grounds.push(new HerbGroudVO(307,7,3));
         this._grounds.push(new HerbGroudVO(308,8,3));
         this._grounds.push(new HerbGroudVO(309,9,4));
         this._grounds.push(new HerbGroudVO(310,10,4));
      }
      
      private function initOpenList() : void
      {
         this._openList = new Vector.<BaseCostVO>();
         this._openList.push(new GameCostVO(2,500));
         this._openList.push(new GameCostVO(3,1000));
         this._openList.push(new GameCostVO(4,2000));
         this._openList.push(new GameCostVO(5,3000));
         this._openList.push(new GameCostVO(6,4000));
         this._openList.push(new GameCostVO(7,20000));
         this._openList.push(new GameCostVO(8,40000));
         this._openList.push(new GameCostVO(9,60000));
         this._openList.push(new GameCostVO(10,80000));
      }
      
      public function findOpen(param1:int) : BaseCostVO
      {
         var _loc2_:BaseCostVO = null;
         for each(_loc2_ in this._openList)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findGround(param1:int) : HerbGroudVO
      {
         var _loc2_:HerbGroudVO = null;
         for each(_loc2_ in this._grounds)
         {
            if(_loc2_.index.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

