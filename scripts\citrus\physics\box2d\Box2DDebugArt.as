package citrus.physics.box2d
{
   import Box2D.Dynamics.b2DebugDraw;
   import citrus.core.CitrusEngine;
   import citrus.physics.IDebugView;
   import flash.display.Sprite;
   import flash.geom.Matrix;
   
   public class Box2DDebugArt implements IDebugView
   {
      private var _box2D:Box2D;
      
      private var _debugDrawer:b2DebugDraw;
      
      private var _sprite:Sprite;
      
      private var _ce:CitrusEngine;
      
      public function Box2DDebugArt()
      {
         super();
         this._ce = CitrusEngine.getInstance();
         this._box2D = this._ce.state.getFirstObjectByType(Box2D) as Box2D;
         this._debugDrawer = new b2DebugDraw();
         this._sprite = new Sprite();
         this._sprite.name = "debug view";
         this._debugDrawer.SetSprite(this._sprite);
         this._debugDrawer.SetDrawScale(this._box2D.scale);
         this._debugDrawer.SetFlags(b2DebugDraw.e_shapeBit | b2DebugDraw.e_jointBit);
         this._box2D.world.SetDebugDraw(this._debugDrawer);
         this._sprite.alpha = 0.5;
      }
      
      public function initialize() : void
      {
         this._ce.stage.addChild(this._sprite);
      }
      
      public function update() : void
      {
         if(this._box2D.visible)
         {
            this._box2D.world.DrawDebugData();
         }
      }
      
      public function destroy() : void
      {
         this._ce.stage.removeChild(this._sprite);
         this._debugDrawer = null;
         this._box2D = null;
      }
      
      public function debugMode(param1:uint) : void
      {
         this._debugDrawer.SetFlags(param1);
      }
      
      public function get debugDrawer() : *
      {
         return this._debugDrawer;
      }
      
      public function set transformMatrix(param1:Matrix) : void
      {
         this._sprite.transform.matrix = param1;
      }
      
      public function get transformMatrix() : Matrix
      {
         return this._sprite.transform.matrix;
      }
      
      public function get visibility() : Boolean
      {
         return this._sprite.visible;
      }
      
      public function set visibility(param1:Boolean) : void
      {
         this._sprite.visible = param1;
      }
   }
}

