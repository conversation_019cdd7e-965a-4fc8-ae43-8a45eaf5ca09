package mogames.gameFabao.view
{
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoYuJinPing;
   
   public class FabaoYuJinPingView extends FabaoBaseView
   {
      private var _fabao:FabaoYuJinPing;
      
      public function FabaoYuJinPingView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         this.addSound(param1);
         if(param1 == 50)
         {
            this._fabao.addBuff();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as FabaoYuJinPing;
      }
      
      override protected function addSound(param1:int) : void
      {
         if(param1 == 22)
         {
            EffectManager.instance().playAudio("SKILL_YU_JIN_PING");
         }
      }
   }
}

