package mogames.gameMission.mission.huanghuaguan
{
   import citrus.objects.CitrusSprite;
   import flash.geom.Point;
   import mogames.Layers;
   import mogames.gameData.ConstRole;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameObj.bullet.LocationBullet;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameSystem.CitrusTimer;
   
   public class TrapGongNu extends CitrusSprite
   {
      private var _mc:CitrusMC;
      
      private var _timer:CitrusTimer;
      
      private var _arg:Object;
      
      public function TrapGongNu()
      {
         super("TrapGongNu",{
            "width":154,
            "height":130,
            "group":3
         });
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("TRAP_GONG_NU_CLIP"),this.onComplete,this.updateFrame);
         this._mc.changeAnimation("stand",true);
         this.view = this._mc;
         this._timer = new CitrusTimer();
      }
      
      public function initData(param1:Object) : void
      {
         this._arg = param1;
         _inverted = this._arg.inverted;
         this.initTimer();
      }
      
      private function initTimer() : void
      {
         var onTimer:Function = null;
         onTimer = function():void
         {
            _mc.changeAnimation("attack",false);
         };
         this._timer.setTimeOut(this._arg.interval,onTimer);
      }
      
      private function updateFrame(param1:int) : void
      {
         var _loc3_:Point = null;
         var _loc4_:LocationBullet = null;
         if(param1 != 22)
         {
            return;
         }
         var _loc2_:int = 0;
         while(_loc2_ < this._arg.num)
         {
            _loc3_ = new Point();
            if(_inverted)
            {
               _loc3_.x = x - (440 - 150 * _loc2_);
            }
            else
            {
               _loc3_.x = x + (440 - 150 * _loc2_);
            }
            _loc3_.y = y + 435;
            _loc4_ = new LocationBullet(4,_loc3_,true,{
               "x":x,
               "y":y,
               "width":172,
               "height":24,
               "group":3
            });
            _loc4_.createView("MC_GONG_NU_JIAN_CLIP",36,36,68,-8);
            _loc4_.createHitEffect("EFFECT_WATER_BOOM0","BONE_BALL_HIT",2,2);
            _loc4_.createHitHurt(this._arg.hurt,0.5,false,1);
            _loc4_.creatHitBuff(ConstRole.BUFF_ICE,1);
            _loc4_.isRotation = true;
            Layers.addCEChild(_loc4_);
            _loc2_++;
         }
      }
      
      private function onComplete(param1:String) : void
      {
         if(param1 != "attack")
         {
            return;
         }
         this._mc.changeAnimation("stand",true);
         this.initTimer();
      }
      
      override public function destroy() : void
      {
         this._mc.destroy();
         this._mc = null;
         this._timer.destroy();
         this._timer = null;
         super.destroy();
      }
   }
}

