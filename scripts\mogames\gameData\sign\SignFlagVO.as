package mogames.gameData.sign
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.good.GameGoodVO;
   
   public class SignFlagVO
   {
      public var index:Sint;
      
      public var flag:Sint;
      
      public var need:Sint;
      
      public var vip:Sint;
      
      public var rewards:Array;
      
      public function SignFlagVO(param1:int, param2:int, param3:int, param4:int, param5:Array)
      {
         super();
         this.index = new Sint(param1);
         this.flag = new Sint(param2);
         this.need = new Sint(param3);
         this.vip = new Sint(param4);
         this.rewards = param5;
      }
      
      public function get isDouble() : Boolean
      {
         return MasterProxy.instance().gameVIP.v >= this.vip.v;
      }
      
      public function get rewardList() : Array
      {
         var _loc2_:GameGoodVO = null;
         var _loc1_:Array = RewardHandler.instance().newGiftReward(this.rewards);
         if(!this.isDouble)
         {
            return _loc1_;
         }
         for each(_loc2_ in _loc1_)
         {
            _loc2_.amount.v *= 2;
         }
         return _loc1_;
      }
   }
}

