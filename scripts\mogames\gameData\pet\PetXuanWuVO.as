package mogames.gameData.pet
{
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetHurtSkillVO;
   
   public class PetXuanWuVO extends PetGameVO
   {
      public function PetXuanWuVO()
      {
         allSkills = this.newActiveSkills();
         super(1108);
      }
      
      override public function newActiveSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new PetHurtSkillVO(11081,this));
         _loc1_.push(new PetHurtSkillVO(11082,this));
         _loc1_.push(new PetHurtSkillVO(11083,this));
         _loc1_.push(new PetHurtSkillVO(11084,this));
         _loc1_.push(new PetHurtSkillVO(11085,this));
         return _loc1_;
      }
   }
}

