package mogames.gameData.shop.vo
{
   import file.GoodConfig;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.LackVO;
   import utils.TxtUtil;
   
   public class ShopGoldVO extends BaseShopVO
   {
      public function ShopGoldVO(param1:int, param2:int, param3:int)
      {
         super(param1,param2,param3);
         priceType = 0;
      }
      
      override public function usePrice(param1:int) : void
      {
         MasterProxy.instance().changeGold(-param1 * price.v);
      }
      
      override public function checkLack(param1:int = 1) : LackVO
      {
         return MasterProxy.instance().checkLack(price.v * param1,0);
      }
      
      override public function askStr(param1:int) : String
      {
         return "确定花费" + TxtUtil.setColor("铜钱X" + price.v * param1) + "购买" + param1 + "个" + TxtUtil.setColor(GoodConfig.instance().getGoodName(goodID.v),"99ff00") + "？";
      }
   }
}

