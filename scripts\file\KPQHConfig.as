package file
{
   import mogames.gameData.card.CardForgeVO;
   import mogames.gameData.forge.NeedVO;
   
   public class KPQHConfig
   {
      private static var _instance:KPQHConfig;
      
      private var _list:Array;
      
      public function KPQHConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : KPQHConfig
      {
         if(!_instance)
         {
            _instance = new KPQHConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new CardForgeVO(71005,71501,20000,new NeedVO(14107,1),[new NeedVO(50000,1)],900);
         this._list[this._list.length] = new CardForgeVO(71501,71502,30000,new NeedVO(14107,1),[new NeedVO(50000,2)],900);
         this._list[this._list.length] = new CardForgeVO(71502,71503,40000,new NeedVO(14107,1),[new NeedVO(50000,3)],900);
         this._list[this._list.length] = new CardForgeVO(71503,71504,50000,new NeedVO(14107,1),[new NeedVO(50000,4)],900);
         this._list[this._list.length] = new CardForgeVO(71010,71506,20000,new NeedVO(14107,1),[new NeedVO(50000,1)],900);
         this._list[this._list.length] = new CardForgeVO(71506,71507,30000,new NeedVO(14107,1),[new NeedVO(50000,2)],900);
         this._list[this._list.length] = new CardForgeVO(71507,71508,40000,new NeedVO(14107,1),[new NeedVO(50000,3)],900);
         this._list[this._list.length] = new CardForgeVO(71508,71509,50000,new NeedVO(14107,1),[new NeedVO(50000,4)],900);
         this._list[this._list.length] = new CardForgeVO(71015,71511,20000,new NeedVO(14107,1),[new NeedVO(50000,1)],900);
         this._list[this._list.length] = new CardForgeVO(71511,71512,30000,new NeedVO(14107,1),[new NeedVO(50000,2)],900);
         this._list[this._list.length] = new CardForgeVO(71512,71513,40000,new NeedVO(14107,1),[new NeedVO(50000,3)],900);
         this._list[this._list.length] = new CardForgeVO(71513,71514,50000,new NeedVO(14107,1),[new NeedVO(50000,4)],900);
         this._list[this._list.length] = new CardForgeVO(71020,71516,20000,new NeedVO(14107,1),[new NeedVO(50000,1)],900);
         this._list[this._list.length] = new CardForgeVO(71516,71517,30000,new NeedVO(14107,1),[new NeedVO(50000,2)],900);
         this._list[this._list.length] = new CardForgeVO(71517,71518,40000,new NeedVO(14107,1),[new NeedVO(50000,3)],900);
         this._list[this._list.length] = new CardForgeVO(71518,71519,50000,new NeedVO(14107,1),[new NeedVO(50000,4)],900);
         this._list[this._list.length] = new CardForgeVO(71025,71521,20000,new NeedVO(14107,1),[new NeedVO(50000,1)],900);
         this._list[this._list.length] = new CardForgeVO(71521,71522,30000,new NeedVO(14107,1),[new NeedVO(50000,2)],900);
         this._list[this._list.length] = new CardForgeVO(71522,71523,40000,new NeedVO(14107,1),[new NeedVO(50000,3)],900);
         this._list[this._list.length] = new CardForgeVO(71523,71524,50000,new NeedVO(14107,1),[new NeedVO(50000,4)],900);
         this._list[this._list.length] = new CardForgeVO(71030,71526,20000,new NeedVO(14107,1),[new NeedVO(50000,1)],900);
         this._list[this._list.length] = new CardForgeVO(71526,71527,30000,new NeedVO(14107,1),[new NeedVO(50000,2)],900);
         this._list[this._list.length] = new CardForgeVO(71527,71528,40000,new NeedVO(14107,1),[new NeedVO(50000,3)],900);
         this._list[this._list.length] = new CardForgeVO(71528,71529,50000,new NeedVO(14107,1),[new NeedVO(50000,4)],900);
         this._list[this._list.length] = new CardForgeVO(71035,71531,20000,new NeedVO(14107,1),[new NeedVO(50000,1)],900);
         this._list[this._list.length] = new CardForgeVO(71531,71532,30000,new NeedVO(14107,1),[new NeedVO(50000,2)],900);
         this._list[this._list.length] = new CardForgeVO(71532,71533,40000,new NeedVO(14107,1),[new NeedVO(50000,3)],900);
         this._list[this._list.length] = new CardForgeVO(71533,71534,50000,new NeedVO(14107,1),[new NeedVO(50000,4)],900);
         this._list[this._list.length] = new CardForgeVO(71040,71536,20000,new NeedVO(14107,1),[new NeedVO(50000,1)],900);
         this._list[this._list.length] = new CardForgeVO(71536,71537,30000,new NeedVO(14107,1),[new NeedVO(50000,2)],900);
         this._list[this._list.length] = new CardForgeVO(71537,71538,40000,new NeedVO(14107,1),[new NeedVO(50000,3)],900);
         this._list[this._list.length] = new CardForgeVO(71538,71539,50000,new NeedVO(14107,1),[new NeedVO(50000,4)],900);
         this._list[this._list.length] = new CardForgeVO(71045,71541,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71541,71542,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71542,71543,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71543,71544,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71050,71546,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71546,71547,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71547,71548,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71548,71549,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71055,71551,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71551,71552,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71552,71553,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71553,71554,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71060,71556,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71556,71557,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71557,71558,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71558,71559,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71065,71561,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71561,71562,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71562,71563,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71563,71564,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71070,71566,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71566,71567,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71567,71568,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71568,71569,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71075,71571,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71571,71572,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71572,71573,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71573,71574,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71080,71576,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71576,71577,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71577,71578,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71578,71579,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71085,71581,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71581,71582,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71582,71583,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71583,71584,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71090,71586,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71586,71587,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71587,71588,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71588,71589,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71095,71591,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71591,71592,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71592,71593,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71593,71594,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71100,71596,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71596,71597,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71597,71598,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71598,71599,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71105,71601,20000,new NeedVO(14107,1),[new NeedVO(50000,2)],800);
         this._list[this._list.length] = new CardForgeVO(71601,71602,30000,new NeedVO(14107,1),[new NeedVO(50000,3)],710);
         this._list[this._list.length] = new CardForgeVO(71602,71603,40000,new NeedVO(14107,1),[new NeedVO(50000,4)],600);
         this._list[this._list.length] = new CardForgeVO(71603,71604,50000,new NeedVO(14107,1),[new NeedVO(50000,5)],500);
         this._list[this._list.length] = new CardForgeVO(71110,71606,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71606,71607,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71607,71608,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71608,71609,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71115,71611,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71611,71612,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71612,71613,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71613,71614,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71120,71616,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71616,71617,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71617,71618,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71618,71619,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71125,71621,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71621,71622,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71622,71623,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71623,71624,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71130,71626,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71626,71627,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71627,71628,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71628,71629,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71135,71631,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71631,71632,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71632,71633,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71633,71634,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71140,71636,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71636,71637,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71637,71638,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71638,71639,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71145,71641,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71641,71642,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71642,71643,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71643,71644,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71150,71646,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71646,71647,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71647,71648,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71648,71649,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71155,71651,20000,new NeedVO(14107,1),[new NeedVO(50000,3)],750);
         this._list[this._list.length] = new CardForgeVO(71651,71652,30000,new NeedVO(14107,1),[new NeedVO(50000,4)],650);
         this._list[this._list.length] = new CardForgeVO(71652,71653,40000,new NeedVO(14107,1),[new NeedVO(50000,5)],550);
         this._list[this._list.length] = new CardForgeVO(71653,71654,50000,new NeedVO(14107,1),[new NeedVO(50000,6)],450);
         this._list[this._list.length] = new CardForgeVO(71160,71656,20000,new NeedVO(14107,1),[new NeedVO(50000,4)],710);
         this._list[this._list.length] = new CardForgeVO(71656,71657,30000,new NeedVO(14107,1),[new NeedVO(50000,5)],600);
         this._list[this._list.length] = new CardForgeVO(71657,71658,40000,new NeedVO(14107,1),[new NeedVO(50000,6)],500);
         this._list[this._list.length] = new CardForgeVO(71658,71659,50000,new NeedVO(14107,1),[new NeedVO(50000,7)],400);
         this._list[this._list.length] = new CardForgeVO(71165,71661,20000,new NeedVO(14107,1),[new NeedVO(50000,4)],710);
         this._list[this._list.length] = new CardForgeVO(71661,71662,30000,new NeedVO(14107,1),[new NeedVO(50000,5)],600);
         this._list[this._list.length] = new CardForgeVO(71662,71663,40000,new NeedVO(14107,1),[new NeedVO(50000,6)],500);
         this._list[this._list.length] = new CardForgeVO(71663,71664,50000,new NeedVO(14107,1),[new NeedVO(50000,7)],400);
         this._list[this._list.length] = new CardForgeVO(71170,71666,20000,new NeedVO(14107,1),[new NeedVO(50000,4)],710);
         this._list[this._list.length] = new CardForgeVO(71666,71667,30000,new NeedVO(14107,1),[new NeedVO(50000,5)],600);
         this._list[this._list.length] = new CardForgeVO(71667,71668,40000,new NeedVO(14107,1),[new NeedVO(50000,6)],500);
         this._list[this._list.length] = new CardForgeVO(71668,71669,50000,new NeedVO(14107,1),[new NeedVO(50000,7)],400);
         this._list[this._list.length] = new CardForgeVO(71175,71671,20000,new NeedVO(14107,1),[new NeedVO(50000,4)],710);
         this._list[this._list.length] = new CardForgeVO(71671,71672,30000,new NeedVO(14107,1),[new NeedVO(50000,5)],600);
         this._list[this._list.length] = new CardForgeVO(71672,71673,40000,new NeedVO(14107,1),[new NeedVO(50000,6)],500);
         this._list[this._list.length] = new CardForgeVO(71673,71674,50000,new NeedVO(14107,1),[new NeedVO(50000,7)],400);
         this._list[this._list.length] = new CardForgeVO(71180,71676,20000,new NeedVO(14107,1),[new NeedVO(50000,4)],710);
         this._list[this._list.length] = new CardForgeVO(71676,71677,30000,new NeedVO(14107,1),[new NeedVO(50000,5)],600);
         this._list[this._list.length] = new CardForgeVO(71677,71678,40000,new NeedVO(14107,1),[new NeedVO(50000,6)],500);
         this._list[this._list.length] = new CardForgeVO(71678,71679,50000,new NeedVO(14107,1),[new NeedVO(50000,7)],400);
         this._list[this._list.length] = new CardForgeVO(71185,71681,20000,new NeedVO(14107,1),[new NeedVO(50000,4)],710);
         this._list[this._list.length] = new CardForgeVO(71681,71682,30000,new NeedVO(14107,1),[new NeedVO(50000,5)],600);
         this._list[this._list.length] = new CardForgeVO(71682,71683,40000,new NeedVO(14107,1),[new NeedVO(50000,6)],500);
         this._list[this._list.length] = new CardForgeVO(71683,71684,50000,new NeedVO(14107,1),[new NeedVO(50000,7)],400);
         this._list[this._list.length] = new CardForgeVO(71190,71686,20000,new NeedVO(14107,1),[new NeedVO(50000,4)],710);
         this._list[this._list.length] = new CardForgeVO(71686,71687,30000,new NeedVO(14107,1),[new NeedVO(50000,5)],600);
         this._list[this._list.length] = new CardForgeVO(71687,71688,40000,new NeedVO(14107,1),[new NeedVO(50000,6)],500);
         this._list[this._list.length] = new CardForgeVO(71688,71689,50000,new NeedVO(14107,1),[new NeedVO(50000,7)],400);
         this._list[this._list.length] = new CardForgeVO(71195,71691,20000,new NeedVO(14107,1),[new NeedVO(50000,4)],710);
         this._list[this._list.length] = new CardForgeVO(71691,71692,30000,new NeedVO(14107,1),[new NeedVO(50000,5)],600);
         this._list[this._list.length] = new CardForgeVO(71692,71693,40000,new NeedVO(14107,1),[new NeedVO(50000,6)],500);
         this._list[this._list.length] = new CardForgeVO(71693,71694,50000,new NeedVO(14107,1),[new NeedVO(50000,7)],400);
         this._list[this._list.length] = new CardForgeVO(71200,71696,20000,new NeedVO(14107,1),[new NeedVO(50000,4)],710);
         this._list[this._list.length] = new CardForgeVO(71696,71697,30000,new NeedVO(14107,1),[new NeedVO(50000,5)],600);
         this._list[this._list.length] = new CardForgeVO(71697,71698,40000,new NeedVO(14107,1),[new NeedVO(50000,6)],500);
         this._list[this._list.length] = new CardForgeVO(71693,71694,50000,new NeedVO(14107,1),[new NeedVO(50000,7)],400);
         this._list[this._list.length] = new CardForgeVO(70005,70501,50000,new NeedVO(14107,1),[new NeedVO(50005,1)],1000);
         this._list[this._list.length] = new CardForgeVO(70501,70502,60000,new NeedVO(14107,1),[new NeedVO(50005,2)],1000);
         this._list[this._list.length] = new CardForgeVO(70502,70503,70000,new NeedVO(14107,1),[new NeedVO(50005,3)],1000);
         this._list[this._list.length] = new CardForgeVO(70503,70504,80000,new NeedVO(14107,1),[new NeedVO(50005,4)],1000);
         this._list[this._list.length] = new CardForgeVO(70504,70505,90000,new NeedVO(14107,1),[new NeedVO(50005,5)],1000);
         this._list[this._list.length] = new CardForgeVO(70010,70506,50000,new NeedVO(14107,1),[new NeedVO(50005,1)],1000);
         this._list[this._list.length] = new CardForgeVO(70506,70507,60000,new NeedVO(14107,1),[new NeedVO(50005,2)],1000);
         this._list[this._list.length] = new CardForgeVO(70507,70508,70000,new NeedVO(14107,1),[new NeedVO(50005,3)],1000);
         this._list[this._list.length] = new CardForgeVO(70508,70509,80000,new NeedVO(14107,1),[new NeedVO(50005,4)],1000);
         this._list[this._list.length] = new CardForgeVO(70509,70510,90000,new NeedVO(14107,1),[new NeedVO(50005,5)],1000);
         this._list[this._list.length] = new CardForgeVO(70015,70511,50000,new NeedVO(14107,1),[new NeedVO(50005,1)],1000);
         this._list[this._list.length] = new CardForgeVO(70511,70512,60000,new NeedVO(14107,1),[new NeedVO(50005,2)],1000);
         this._list[this._list.length] = new CardForgeVO(70512,70513,70000,new NeedVO(14107,1),[new NeedVO(50005,3)],1000);
         this._list[this._list.length] = new CardForgeVO(70513,70514,80000,new NeedVO(14107,1),[new NeedVO(50005,4)],1000);
         this._list[this._list.length] = new CardForgeVO(70514,70515,90000,new NeedVO(14107,1),[new NeedVO(50005,5)],1000);
         this._list[this._list.length] = new CardForgeVO(70020,70516,50000,new NeedVO(14107,1),[new NeedVO(50005,1)],1000);
         this._list[this._list.length] = new CardForgeVO(70516,70517,60000,new NeedVO(14107,1),[new NeedVO(50005,2)],1000);
         this._list[this._list.length] = new CardForgeVO(70517,70518,70000,new NeedVO(14107,1),[new NeedVO(50005,3)],1000);
         this._list[this._list.length] = new CardForgeVO(70518,70519,80000,new NeedVO(14107,1),[new NeedVO(50005,4)],1000);
         this._list[this._list.length] = new CardForgeVO(70519,70520,90000,new NeedVO(14107,1),[new NeedVO(50005,5)],1000);
         this._list[this._list.length] = new CardForgeVO(70025,70521,50000,new NeedVO(14107,1),[new NeedVO(50005,1)],1000);
         this._list[this._list.length] = new CardForgeVO(70521,70522,60000,new NeedVO(14107,1),[new NeedVO(50005,2)],1000);
         this._list[this._list.length] = new CardForgeVO(70522,70523,70000,new NeedVO(14107,1),[new NeedVO(50005,3)],1000);
         this._list[this._list.length] = new CardForgeVO(70523,70524,80000,new NeedVO(14107,1),[new NeedVO(50005,4)],1000);
         this._list[this._list.length] = new CardForgeVO(70524,70525,90000,new NeedVO(14107,1),[new NeedVO(50005,5)],1000);
         this._list[this._list.length] = new CardForgeVO(70030,70526,50000,new NeedVO(14107,1),[new NeedVO(50005,1)],1000);
         this._list[this._list.length] = new CardForgeVO(70526,70527,60000,new NeedVO(14107,1),[new NeedVO(50005,2)],1000);
         this._list[this._list.length] = new CardForgeVO(70527,70528,70000,new NeedVO(14107,1),[new NeedVO(50005,3)],1000);
         this._list[this._list.length] = new CardForgeVO(70528,70529,80000,new NeedVO(14107,1),[new NeedVO(50005,4)],1000);
         this._list[this._list.length] = new CardForgeVO(70529,70530,90000,new NeedVO(14107,1),[new NeedVO(50005,5)],1000);
         this._list[this._list.length] = new CardForgeVO(70035,70531,50000,new NeedVO(14107,1),[new NeedVO(50005,1)],1000);
         this._list[this._list.length] = new CardForgeVO(70531,70532,60000,new NeedVO(14107,1),[new NeedVO(50005,2)],1000);
         this._list[this._list.length] = new CardForgeVO(70532,70533,70000,new NeedVO(14107,1),[new NeedVO(50005,3)],1000);
         this._list[this._list.length] = new CardForgeVO(70533,70534,80000,new NeedVO(14107,1),[new NeedVO(50005,4)],1000);
         this._list[this._list.length] = new CardForgeVO(70534,70535,90000,new NeedVO(14107,1),[new NeedVO(50005,5)],1000);
         this._list[this._list.length] = new CardForgeVO(70040,70536,50000,new NeedVO(14107,1),[new NeedVO(50005,1)],1000);
         this._list[this._list.length] = new CardForgeVO(70536,70537,60000,new NeedVO(14107,1),[new NeedVO(50005,2)],1000);
         this._list[this._list.length] = new CardForgeVO(70537,70538,70000,new NeedVO(14107,1),[new NeedVO(50005,3)],1000);
         this._list[this._list.length] = new CardForgeVO(70538,70539,80000,new NeedVO(14107,1),[new NeedVO(50005,4)],1000);
         this._list[this._list.length] = new CardForgeVO(70539,70540,90000,new NeedVO(14107,1),[new NeedVO(50005,5)],1000);
         this._list[this._list.length] = new CardForgeVO(70045,70541,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70541,70542,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70542,70543,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70543,70544,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70544,70545,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70050,70546,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70546,70547,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70547,70548,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70548,70549,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70549,70550,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70055,70551,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70551,70552,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70552,70553,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70553,70554,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70554,70555,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70060,70556,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70556,70557,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70557,70558,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70558,70559,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70559,70560,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70065,70561,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70561,70562,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70562,70563,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70563,70564,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70564,70565,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70070,70566,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70566,70567,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70567,70568,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70568,70569,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70569,70570,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70075,70571,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70571,70572,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70572,70573,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70573,70574,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70574,70575,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70080,70576,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70576,70577,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70577,70578,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70578,70579,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70579,70580,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70085,70581,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70581,70582,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70582,70583,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70583,70584,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70584,70585,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70090,70586,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70586,70587,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70587,70588,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70588,70589,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70589,70590,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70095,70591,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70591,70592,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70592,70593,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70593,70594,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70594,70595,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70100,70596,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70596,70597,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70597,70598,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70598,70599,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70599,70600,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70105,70601,50000,new NeedVO(14107,1),[new NeedVO(50005,2)],900);
         this._list[this._list.length] = new CardForgeVO(70601,70602,60000,new NeedVO(14107,1),[new NeedVO(50005,3)],800);
         this._list[this._list.length] = new CardForgeVO(70602,70603,70000,new NeedVO(14107,1),[new NeedVO(50005,4)],700);
         this._list[this._list.length] = new CardForgeVO(70603,70604,80000,new NeedVO(14107,1),[new NeedVO(50005,5)],600);
         this._list[this._list.length] = new CardForgeVO(70604,70605,90000,new NeedVO(14107,1),[new NeedVO(50005,6)],300);
         this._list[this._list.length] = new CardForgeVO(70110,70606,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70606,70607,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70607,70608,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70608,70609,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70609,70610,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70115,70611,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70611,70612,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70612,70613,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70613,70614,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70614,70615,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70120,70616,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70616,70617,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70617,70618,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70618,70619,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70619,70620,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70125,70621,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70621,70622,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70622,70623,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70623,70624,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70624,70625,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70130,70626,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70626,70627,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70627,70628,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70628,70629,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70629,70630,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70135,70631,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70631,70632,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70632,70633,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70633,70634,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70634,70635,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70140,70636,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70636,70637,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70637,70638,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70638,70639,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70639,70640,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70145,70641,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70641,70642,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70642,70643,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70643,70644,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70644,70645,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70150,70646,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70646,70647,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70647,70648,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70648,70649,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70649,70650,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70155,70651,50000,new NeedVO(14107,1),[new NeedVO(50005,3)],850);
         this._list[this._list.length] = new CardForgeVO(70651,70652,60000,new NeedVO(14107,1),[new NeedVO(50005,4)],750);
         this._list[this._list.length] = new CardForgeVO(70652,70653,70000,new NeedVO(14107,1),[new NeedVO(50005,5)],650);
         this._list[this._list.length] = new CardForgeVO(70653,70654,80000,new NeedVO(14107,1),[new NeedVO(50005,6)],550);
         this._list[this._list.length] = new CardForgeVO(70654,70655,90000,new NeedVO(14107,1),[new NeedVO(50005,7)],300);
         this._list[this._list.length] = new CardForgeVO(70160,70656,50000,new NeedVO(14107,1),[new NeedVO(50005,4)],800);
         this._list[this._list.length] = new CardForgeVO(70656,70657,60000,new NeedVO(14107,1),[new NeedVO(50005,5)],700);
         this._list[this._list.length] = new CardForgeVO(70657,70658,70000,new NeedVO(14107,1),[new NeedVO(50005,6)],600);
         this._list[this._list.length] = new CardForgeVO(70658,70659,80000,new NeedVO(14107,1),[new NeedVO(50005,7)],500);
         this._list[this._list.length] = new CardForgeVO(70659,70660,90000,new NeedVO(14107,2),[new NeedVO(50005,8)],250);
         this._list[this._list.length] = new CardForgeVO(70165,70661,50000,new NeedVO(14107,1),[new NeedVO(50005,4)],800);
         this._list[this._list.length] = new CardForgeVO(70661,70662,60000,new NeedVO(14107,1),[new NeedVO(50005,5)],700);
         this._list[this._list.length] = new CardForgeVO(70662,70663,70000,new NeedVO(14107,1),[new NeedVO(50005,6)],600);
         this._list[this._list.length] = new CardForgeVO(70663,70664,80000,new NeedVO(14107,1),[new NeedVO(50005,7)],500);
         this._list[this._list.length] = new CardForgeVO(70664,70665,90000,new NeedVO(14107,2),[new NeedVO(50005,8)],250);
         this._list[this._list.length] = new CardForgeVO(70170,70666,50000,new NeedVO(14107,1),[new NeedVO(50005,4)],800);
         this._list[this._list.length] = new CardForgeVO(70666,70667,60000,new NeedVO(14107,1),[new NeedVO(50005,5)],700);
         this._list[this._list.length] = new CardForgeVO(70667,70668,70000,new NeedVO(14107,1),[new NeedVO(50005,6)],600);
         this._list[this._list.length] = new CardForgeVO(70668,70669,80000,new NeedVO(14107,1),[new NeedVO(50005,7)],500);
         this._list[this._list.length] = new CardForgeVO(70669,70670,90000,new NeedVO(14107,2),[new NeedVO(50005,8)],250);
         this._list[this._list.length] = new CardForgeVO(70175,70671,50000,new NeedVO(14107,1),[new NeedVO(50005,4)],800);
         this._list[this._list.length] = new CardForgeVO(70671,70672,60000,new NeedVO(14107,1),[new NeedVO(50005,5)],700);
         this._list[this._list.length] = new CardForgeVO(70672,70673,70000,new NeedVO(14107,1),[new NeedVO(50005,6)],600);
         this._list[this._list.length] = new CardForgeVO(70673,70674,80000,new NeedVO(14107,1),[new NeedVO(50005,7)],500);
         this._list[this._list.length] = new CardForgeVO(70674,70675,90000,new NeedVO(14107,2),[new NeedVO(50005,8)],250);
         this._list[this._list.length] = new CardForgeVO(70180,70676,50000,new NeedVO(14107,1),[new NeedVO(50005,4)],800);
         this._list[this._list.length] = new CardForgeVO(70676,70677,60000,new NeedVO(14107,1),[new NeedVO(50005,5)],700);
         this._list[this._list.length] = new CardForgeVO(70677,70678,70000,new NeedVO(14107,1),[new NeedVO(50005,6)],600);
         this._list[this._list.length] = new CardForgeVO(70678,70679,80000,new NeedVO(14107,1),[new NeedVO(50005,7)],500);
         this._list[this._list.length] = new CardForgeVO(70679,70680,90000,new NeedVO(14107,2),[new NeedVO(50005,8)],250);
         this._list[this._list.length] = new CardForgeVO(70185,70681,50000,new NeedVO(14107,1),[new NeedVO(50005,4)],800);
         this._list[this._list.length] = new CardForgeVO(70681,70682,60000,new NeedVO(14107,1),[new NeedVO(50005,5)],700);
         this._list[this._list.length] = new CardForgeVO(70682,70683,70000,new NeedVO(14107,1),[new NeedVO(50005,6)],600);
         this._list[this._list.length] = new CardForgeVO(70683,70684,80000,new NeedVO(14107,1),[new NeedVO(50005,7)],500);
         this._list[this._list.length] = new CardForgeVO(70684,70685,90000,new NeedVO(14107,2),[new NeedVO(50005,8)],250);
         this._list[this._list.length] = new CardForgeVO(70190,70686,50000,new NeedVO(14107,1),[new NeedVO(50005,4)],800);
         this._list[this._list.length] = new CardForgeVO(70686,70687,60000,new NeedVO(14107,1),[new NeedVO(50005,5)],700);
         this._list[this._list.length] = new CardForgeVO(70687,70688,70000,new NeedVO(14107,1),[new NeedVO(50005,6)],600);
         this._list[this._list.length] = new CardForgeVO(70688,70689,80000,new NeedVO(14107,1),[new NeedVO(50005,7)],500);
         this._list[this._list.length] = new CardForgeVO(70689,70690,90000,new NeedVO(14107,2),[new NeedVO(50005,8)],250);
         this._list[this._list.length] = new CardForgeVO(70195,70691,50000,new NeedVO(14107,1),[new NeedVO(50005,4)],800);
         this._list[this._list.length] = new CardForgeVO(70691,70692,60000,new NeedVO(14107,1),[new NeedVO(50005,5)],700);
         this._list[this._list.length] = new CardForgeVO(70692,70693,70000,new NeedVO(14107,1),[new NeedVO(50005,6)],600);
         this._list[this._list.length] = new CardForgeVO(70693,70694,80000,new NeedVO(14107,1),[new NeedVO(50005,7)],500);
         this._list[this._list.length] = new CardForgeVO(70694,70695,90000,new NeedVO(14107,2),[new NeedVO(50005,8)],250);
         this._list[this._list.length] = new CardForgeVO(70200,70696,50000,new NeedVO(14107,1),[new NeedVO(50005,4)],800);
         this._list[this._list.length] = new CardForgeVO(70696,70697,60000,new NeedVO(14107,1),[new NeedVO(50005,5)],700);
         this._list[this._list.length] = new CardForgeVO(70697,70698,70000,new NeedVO(14107,1),[new NeedVO(50005,6)],600);
         this._list[this._list.length] = new CardForgeVO(70698,70699,80000,new NeedVO(14107,1),[new NeedVO(50005,7)],500);
         this._list[this._list.length] = new CardForgeVO(70699,70700,90000,new NeedVO(14107,2),[new NeedVO(50005,8)],250);
         this._list[this._list.length] = new CardForgeVO(70955,70901,100000,new NeedVO(14107,1),[new NeedVO(50008,1)],800);
         this._list[this._list.length] = new CardForgeVO(70901,70902,150000,new NeedVO(14107,1),[new NeedVO(50008,2)],700);
         this._list[this._list.length] = new CardForgeVO(70902,70903,200000,new NeedVO(14107,1),[new NeedVO(50008,3)],600);
         this._list[this._list.length] = new CardForgeVO(70903,70904,250000,new NeedVO(14107,1),[new NeedVO(50008,4)],500);
         this._list[this._list.length] = new CardForgeVO(70904,70905,666666,new NeedVO(14107,5),[new NeedVO(50008,8)],250);
         this._list[this._list.length] = new CardForgeVO(70960,70906,100000,new NeedVO(14107,1),[new NeedVO(50008,1)],800);
         this._list[this._list.length] = new CardForgeVO(70906,70907,150000,new NeedVO(14107,1),[new NeedVO(50008,2)],700);
         this._list[this._list.length] = new CardForgeVO(70907,70908,200000,new NeedVO(14107,1),[new NeedVO(50008,3)],600);
         this._list[this._list.length] = new CardForgeVO(70908,70909,250000,new NeedVO(14107,1),[new NeedVO(50008,4)],500);
         this._list[this._list.length] = new CardForgeVO(70909,70910,666666,new NeedVO(14107,5),[new NeedVO(50008,8)],250);
         this._list[this._list.length] = new CardForgeVO(70965,70911,100000,new NeedVO(14107,1),[new NeedVO(50008,1)],800);
         this._list[this._list.length] = new CardForgeVO(70911,70912,150000,new NeedVO(14107,1),[new NeedVO(50008,2)],700);
         this._list[this._list.length] = new CardForgeVO(70912,70913,200000,new NeedVO(14107,1),[new NeedVO(50008,3)],600);
         this._list[this._list.length] = new CardForgeVO(70913,70914,250000,new NeedVO(14107,1),[new NeedVO(50008,4)],500);
         this._list[this._list.length] = new CardForgeVO(70914,70915,666666,new NeedVO(14107,5),[new NeedVO(50008,8)],250);
         this._list[this._list.length] = new CardForgeVO(70970,70916,100000,new NeedVO(14107,1),[new NeedVO(50008,1)],800);
         this._list[this._list.length] = new CardForgeVO(70916,70917,150000,new NeedVO(14107,1),[new NeedVO(50008,2)],700);
         this._list[this._list.length] = new CardForgeVO(70917,70918,200000,new NeedVO(14107,1),[new NeedVO(50008,3)],600);
         this._list[this._list.length] = new CardForgeVO(70918,70919,250000,new NeedVO(14107,1),[new NeedVO(50008,4)],500);
         this._list[this._list.length] = new CardForgeVO(70919,70920,666666,new NeedVO(14107,5),[new NeedVO(50008,8)],250);
         this._list[this._list.length] = new CardForgeVO(70975,70921,100000,new NeedVO(14107,1),[new NeedVO(50008,1)],800);
         this._list[this._list.length] = new CardForgeVO(70921,70922,150000,new NeedVO(14107,1),[new NeedVO(50008,2)],700);
         this._list[this._list.length] = new CardForgeVO(70922,70923,200000,new NeedVO(14107,1),[new NeedVO(50008,3)],600);
         this._list[this._list.length] = new CardForgeVO(70923,70924,250000,new NeedVO(14107,1),[new NeedVO(50008,4)],500);
         this._list[this._list.length] = new CardForgeVO(70924,70925,666666,new NeedVO(14107,5),[new NeedVO(50008,8)],250);
         this._list[this._list.length] = new CardForgeVO(70980,70926,100000,new NeedVO(14107,1),[new NeedVO(50008,1)],800);
         this._list[this._list.length] = new CardForgeVO(70926,70927,150000,new NeedVO(14107,1),[new NeedVO(50008,2)],700);
         this._list[this._list.length] = new CardForgeVO(70927,70928,200000,new NeedVO(14107,1),[new NeedVO(50008,3)],600);
         this._list[this._list.length] = new CardForgeVO(70928,70929,250000,new NeedVO(14107,1),[new NeedVO(50008,4)],500);
         this._list[this._list.length] = new CardForgeVO(70929,70930,666666,new NeedVO(14107,5),[new NeedVO(50008,8)],250);
         this._list[this._list.length] = new CardForgeVO(70985,70931,100000,new NeedVO(14107,1),[new NeedVO(50008,1)],800);
         this._list[this._list.length] = new CardForgeVO(70931,70932,150000,new NeedVO(14107,1),[new NeedVO(50008,2)],700);
         this._list[this._list.length] = new CardForgeVO(70932,70933,200000,new NeedVO(14107,1),[new NeedVO(50008,3)],600);
         this._list[this._list.length] = new CardForgeVO(70933,70934,250000,new NeedVO(14107,1),[new NeedVO(50008,4)],500);
         this._list[this._list.length] = new CardForgeVO(70934,70935,666666,new NeedVO(14107,5),[new NeedVO(50008,8)],250);
      }
      
      public function findVO(param1:int) : CardForgeVO
      {
         var _loc2_:CardForgeVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.oldID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

