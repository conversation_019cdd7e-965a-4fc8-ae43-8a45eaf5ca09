package mogames.gameData.pet.vo
{
   import mogames.gameData.role.vo.RoleConstVO;
   
   public class PetConstVO extends RoleConstVO
   {
      public var WH:Array;
      
      public function PetConstVO(param1:int, param2:int, param3:Array)
      {
         this.WH = param3;
         super(param1,param2,0,0,0,0);
      }
      
      public function parseWidth(param1:int) : int
      {
         return this.WH[param1][0];
      }
      
      public function parseHeight(param1:int) : int
      {
         return this.WH[param1][1];
      }
   }
}

