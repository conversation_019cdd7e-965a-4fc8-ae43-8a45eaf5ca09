package mogames.gameData.yaoyuan.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   
   public class HerbGroudVO
   {
      public var index:Sint;
      
      public var quality:Sint;
      
      private var _flagID:Sint;
      
      public function HerbGroudVO(param1:int, param2:int, param3:int)
      {
         super();
         this._flagID = new Sint(param1);
         this.index = new Sint(param2);
         this.quality = new Sint(param3);
      }
      
      public function setOpen() : void
      {
         FlagProxy.instance().setValue(this._flagID.v,1);
      }
      
      public function checkCanGrow(param1:int) : Boolean
      {
         return param1 <= this.quality.v;
      }
      
      public function get isOpen() : Boolean
      {
         if(this._flagID.v == 0)
         {
            return true;
         }
         return FlagProxy.instance().isComplete(this._flagID.v);
      }
   }
}

