package citrus.physics
{
   import citrus.core.CitrusObject;
   import citrus.view.ICitrusArt;
   
   public class APhysicsEngine extends CitrusObject
   {
      protected var _visible:Boolean = false;
      
      protected var _touchable:Boolean = false;
      
      protected var _group:uint = 1;
      
      protected var _view:*;
      
      protected var _realDebugView:*;
      
      protected var _art:ICitrusArt;
      
      public function APhysicsEngine(param1:String, param2:Object = null)
      {
         updateCallEnabled = true;
         super(param1,param2);
      }
      
      public function getBody() : *
      {
         return null;
      }
      
      public function get debugView() : IDebugView
      {
         var _loc1_:* = _ce.state.view.getArt(this);
         if(_loc1_ && Boolean(_loc1_.content))
         {
            return _loc1_.content.debugView as IDebugView;
         }
         return null;
      }
      
      public function get realDebugView() : *
      {
         return this._realDebugView;
      }
      
      public function get view() : *
      {
         return this._view;
      }
      
      public function set view(param1:*) : void
      {
         this._view = param1;
      }
      
      public function get art() : ICitrusArt
      {
         return this._art;
      }
      
      public function get x() : Number
      {
         return 0;
      }
      
      public function get y() : Number
      {
         return 0;
      }
      
      public function get z() : Number
      {
         return 0;
      }
      
      public function get width() : Number
      {
         return 0;
      }
      
      public function get height() : Number
      {
         return 0;
      }
      
      public function get depth() : Number
      {
         return 0;
      }
      
      public function get velocity() : Array
      {
         return null;
      }
      
      public function get parallaxX() : Number
      {
         return 1;
      }
      
      public function get parallaxY() : Number
      {
         return 1;
      }
      
      public function get rotation() : Number
      {
         return 0;
      }
      
      public function get group() : uint
      {
         return this._group;
      }
      
      public function set group(param1:uint) : void
      {
         this._group = param1;
      }
      
      public function get visible() : Boolean
      {
         return this._visible;
      }
      
      public function set visible(param1:Boolean) : void
      {
         this._visible = param1;
      }
      
      public function get touchable() : Boolean
      {
         return this._touchable;
      }
      
      public function set touchable(param1:Boolean) : void
      {
         this._touchable = param1;
      }
      
      public function get animation() : String
      {
         return "";
      }
      
      public function get inverted() : Boolean
      {
         return false;
      }
      
      public function get offsetX() : Number
      {
         return 0;
      }
      
      public function get offsetY() : Number
      {
         return 0;
      }
      
      public function get registration() : String
      {
         return "topLeft";
      }
      
      public function handleArtReady(param1:ICitrusArt) : void
      {
         this._art = param1;
      }
      
      public function handleArtChanged(param1:ICitrusArt) : void
      {
      }
   }
}

