package file
{
   import mogames.gameData.rank.vo.RankDicVO;
   
   public class RankDicConfig
   {
      private static var _instance:RankDicConfig;
      
      public var list:Array;
      
      public function RankDicConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : RankDicConfig
      {
         if(!_instance)
         {
            _instance = new RankDicConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.list = [];
         this.list.push(new RankDicVO("角色",["总战力","战力","生命","攻击","物防","魔防"]));
         this.list.push(new RankDicVO("宠物战力",["魔炎","蛇灵","金灵","水灵","冰麒麟","火凤凰"]));
         this.list.push(new RankDicVO("关卡通关",["双叉岭","黑风洞","黄风洞","白虎岭","黑松林","平顶山","乌鸡国","火云洞","黑水河","车迟国","通天河","金兜山","琵琶洞","火焰山","乱石山","小雷音","七绝山","麒麟山","盘丝洞","黄花观","狮驼岭","清华洞","陷空山","隐雾山","豹头山","竹节山","青龙山"]));
         this.list.push(new RankDicVO("镇妖塔通关",["降妖塔一层","降妖塔二层","降妖塔三层"]));
      }
   }
}

