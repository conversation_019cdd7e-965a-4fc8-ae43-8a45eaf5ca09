package mogames.gameMission.mission.gaolaozhuang
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.dialog.DialogVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameRole.co.BOSSZhuBaJie;
   import mogames.gameUI.dialog.DialogMediator;
   import mogames.gameUI.fuben.TZJS.TZJSEnterModule;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.MathUtil;
   
   public class SceneGaoLaoZhuang extends BossScene
   {
      private var _bossTrigger:LocalTriggerX;
      
      private var loseWords:Array = ["三十六计，逃为上，再见了，下次别再见。","给我点馒头，我会考虑下","都被你打成猪头了，叫我怎么娶媳妇。"];
      
      public function SceneGaoLaoZhuang(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_GAOLAOZHUANG");
         setWinPosition(new Rectangle(530,223,614,156),new Point(695,395));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2008,1);
         };
         _mission.cleanLoadUI();
         this._bossTrigger = new LocalTriggerX(_mission.onePlayer,new Point(780,432),1);
         this._bossTrigger.okFunc = this.showStory;
         this._bossTrigger.start();
         this.addBOSS();
         if(!FlagProxy.instance().isComplete(2008))
         {
            showDialog("STORY0015",func);
         }
      }
      
      private function showStory() : void
      {
         var func:Function = null;
         func = function():void
         {
            _boss.setInverted(true);
            showDialog("STORY0017",startBattle);
            FlagProxy.instance().setValue(2025,1);
         };
         if(TZJSEnterModule.TZJS_DIFF.v == 0 && !FlagProxy.instance().isComplete(2025))
         {
            showDialog("STORY0016",func);
         }
         else
         {
            this.startBattle();
         }
      }
      
      private function startBattle() : void
      {
         _boss.setInverted(true);
         _boss.target = _mission.curTarget;
         startBossBattle("BGM_BATTLE0");
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSZhuBaJie();
         _boss.x = 1180;
         _boss.y = 340;
         _boss.setInverted(false);
         this.checkDiff();
         add(_boss);
      }
      
      private function checkDiff() : void
      {
         var _loc1_:int = TZJSEnterModule.TZJS_DIFF.v;
         if(_loc1_ == 0)
         {
            _boss.initData(30161,30161);
         }
         else if(_loc1_ == 1)
         {
            _boss.initData(30162,30162,50);
         }
         else if(_loc1_ == 2)
         {
            _boss.initData(30163,30163,51);
         }
         else if(_loc1_ == 3)
         {
            _boss.initData(30164,30164,52);
         }
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         MissionManager.instance().handlerWin(false);
         _mission.missionVO.setComplete();
      }
      
      override protected function handlerWin() : void
      {
         Layers.lockGame = false;
         if(HeroProxy.instance().pigVO == null && TZJSEnterModule.TZJS_DIFF.v == 0)
         {
            this.startZhaoXiang();
         }
         else
         {
            this.showWinDrop();
         }
      }
      
      private function startZhaoXiang() : void
      {
         var okFunc:Function = null;
         okFunc = function():void
         {
            var _loc1_:Vector.<DialogVO> = new Vector.<DialogVO>();
            if(MathUtil.checkOdds(400))
            {
               _loc1_.push(new DialogVO(1,"猪八戒","BODY_ZHU_BA_JIE5",loseWords[int(Math.random() * loseWords.length)]));
               DialogMediator.instance().init(_loc1_,loseDialogEnd);
            }
            else
            {
               _loc1_.push(new DialogVO(1,"猪八戒","BODY_ZHU_BA_JIE4","为了能将功折罪，我老猪愿保取经人去西天取经。"));
               DialogMediator.instance().init(_loc1_,winDialogEnd);
               HeroProxy.isNewHero = true;
            }
         };
         PromptMediator.instance().showPrompt("猪八戒被打成虚弱状态，是否降服？\n（成功后成为队友）",okFunc,this.showWinDrop);
      }
      
      private function winDialogEnd() : void
      {
         HeroProxy.instance().unlockHero(1003);
         HeroProxy.instance().pigVO.setLevel(5);
         FlagProxy.instance().setValue(100300,5);
         _boss.visible = false;
         EffectManager.instance().addScreenEffect(16777215);
         MiniMsgMediator.instance().showAutoMsg("【猪八戒】已加入队伍！",480,200,"NEW_FUNCTION");
         this.showWinDrop();
      }
      
      private function loseDialogEnd() : void
      {
         MiniMsgMediator.instance().showAutoMsg("降服失败！",480,300,"ZHAOMU_FAIL");
         this.showWinDrop();
      }
      
      private function showWinDrop() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         this._bossTrigger.destroy();
         this._bossTrigger = null;
         super.destroy();
      }
   }
}

