package file
{
   import mogames.gameData.fashion.FashionSkillVO;
   
   public class FashionSkillConfig
   {
      private static var _instance:FashionSkillConfig;
      
      private var _list:Vector.<FashionSkillVO>;
      
      public function FashionSkillConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : FashionSkillConfig
      {
         if(!_instance)
         {
            _instance = new FashionSkillConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._list = new Vector.<FashionSkillVO>();
         this._list.push(new FashionSkillVO(1,34111,35011));
         this._list.push(new FashionSkillVO(2,34112,35012));
         this._list.push(new FashionSkillVO(3,34113,35013));
         this._list.push(new FashionSkillVO(4,34114,35014));
         this._list.push(new FashionSkillVO(8,34115,35015));
         this._list.push(new FashionSkillVO(5,34801,35901));
         this._list.push(new FashionSkillVO(5,34802,35902));
         this._list.push(new FashionSkillVO(5,34803,35903));
         this._list.push(new FashionSkillVO(5,34804,35904));
         this._list.push(new FashionSkillVO(5,34806,35906));
         this._list.push(new FashionSkillVO(5,34807,35907));
         this._list.push(new FashionSkillVO(5,34808,35908));
         this._list.push(new FashionSkillVO(5,34809,35909));
         this._list.push(new FashionSkillVO(5,34811,35911));
         this._list.push(new FashionSkillVO(5,34812,35912));
         this._list.push(new FashionSkillVO(5,34813,35913));
         this._list.push(new FashionSkillVO(5,34814,35914));
         this._list.push(new FashionSkillVO(5,34816,35916));
         this._list.push(new FashionSkillVO(5,34817,35917));
         this._list.push(new FashionSkillVO(5,34818,35918));
         this._list.push(new FashionSkillVO(5,34819,35919));
         this._list.push(new FashionSkillVO(5,34821,35921));
         this._list.push(new FashionSkillVO(5,34822,35922));
         this._list.push(new FashionSkillVO(5,34823,35923));
         this._list.push(new FashionSkillVO(5,34824,35924));
         this._list.push(new FashionSkillVO(5,34826,35926));
         this._list.push(new FashionSkillVO(5,34827,35927));
         this._list.push(new FashionSkillVO(5,34828,35928));
         this._list.push(new FashionSkillVO(5,34829,35929));
         this._list.push(new FashionSkillVO(5,34831,35931));
         this._list.push(new FashionSkillVO(5,34832,35932));
         this._list.push(new FashionSkillVO(5,34833,35933));
         this._list.push(new FashionSkillVO(5,34834,35934));
         this._list.push(new FashionSkillVO(6,34121,35021));
         this._list.push(new FashionSkillVO(6,34122,35022));
         this._list.push(new FashionSkillVO(6,34123,35023));
         this._list.push(new FashionSkillVO(6,34124,35024));
         this._list.push(new FashionSkillVO(6,34125,35025));
         this._list.push(new FashionSkillVO(7,34131,35031));
         this._list.push(new FashionSkillVO(7,34132,35032));
         this._list.push(new FashionSkillVO(7,34134,35034));
         this._list.push(new FashionSkillVO(7,34133,35033));
         this._list.push(new FashionSkillVO(7,34135,35035));
      }
      
      public function findSkill(param1:int, param2:int) : int
      {
         var _loc3_:FashionSkillVO = null;
         for each(_loc3_ in this._list)
         {
            if(_loc3_.sbID.v == param1 && _loc3_.sjID.v == param2)
            {
               return _loc3_.skillID;
            }
         }
         return 0;
      }
   }
}

