package mogames.gameData.pet
{
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetHurtSkillVO;
   
   public class PetShuiLingVO extends PetGameVO
   {
      public function PetShuiLingVO()
      {
         allSkills = this.newActiveSkills();
         super(1104);
      }
      
      override public function newActiveSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new PetHurtSkillVO(11041,this));
         _loc1_.push(new PetHurtSkillVO(11042,this));
         _loc1_.push(new PetHurtSkillVO(11043,this));
         _loc1_.push(new PetHurtSkillVO(11044,this));
         _loc1_.push(new PetHurtSkillVO(11045,this,2));
         return _loc1_;
      }
   }
}

