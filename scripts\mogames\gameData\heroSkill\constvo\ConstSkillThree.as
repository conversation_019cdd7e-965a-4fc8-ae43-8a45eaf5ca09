package mogames.gameData.heroSkill.constvo
{
   import mogames.gameData.heroSkill.SkillLevelVO;
   
   public class ConstSkillThree extends ConstSkillVO
   {
      public function ConstSkillThree(param1:int, param2:String, param3:String, param4:String, param5:String)
      {
         super(param1,param2,param3,param4,param5);
      }
      
      override protected function initCondition() : void
      {
         super.initCondition();
         _lvVec.push(new SkillLevelVO(0,3,600));
         _lvVec.push(new SkillLevelVO(1,8,1200));
         _lvVec.push(new SkillLevelVO(2,13,1800));
         _lvVec.push(new SkillLevelVO(3,18,2400));
         _lvVec.push(new SkillLevelVO(4,23,3000));
         _lvVec.push(new SkillLevelVO(5,28,3600));
         _lvVec.push(new SkillLevelVO(6,33,4200));
         _lvVec.push(new SkillLevelVO(7,38,4800));
         _lvVec.push(new SkillLevelVO(8,43,5400));
         _lvVec.push(new SkillLevelVO(9,48,6000));
      }
   }
}

