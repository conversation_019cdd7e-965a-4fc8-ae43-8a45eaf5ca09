package mogames.gameFabao.view
{
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoBiShuiZhu;
   
   public class FabaoBiShuiZhuView extends FabaoBaseView
   {
      private var _fabao:FabaoBiShuiZhu;
      
      public function FabaoBiShuiZhuView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         this.addSound(param1);
         if(param1 == 45)
         {
            this._fabao.addBuff();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as FabaoBiShuiZhu;
      }
      
      override protected function addSound(param1:int) : void
      {
         if(param1 == 45)
         {
            EffectManager.instance().playAudio("SKILL_YU_JIN_PING");
         }
      }
   }
}

