package mogames.gameData.mission
{
   import mogames.gameData.base.Sint;
   import mogames.gameRole.HeroManager;
   
   public class EnemyWaveVO
   {
      public var id:Sint;
      
      public var num:Sint;
      
      public var list:Array;
      
      public function EnemyWaveVO(param1:int, param2:int, param3:Array)
      {
         super();
         this.id = new Sint(param1);
         this.num = new Sint(param2);
         this.list = param3;
      }
      
      public function get firstList() : Array
      {
         return this.list.slice(0,this.total);
      }
      
      public function get leftList() : Array
      {
         var _loc1_:int = this.list.length - this.total;
         return this.list.slice(this.total);
      }
      
      public function get total() : int
      {
         if(HeroManager.isDouble)
         {
            return Math.min(3,this.num.v);
         }
         return this.num.v;
      }
   }
}

