package citrus.utils
{
   import citrus.core.CitrusObject;
   import citrus.view.ICitrusArt;
   import flash.display.Loader;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.utils.Dictionary;
   import org.osflash.signals.Signal;
   
   public class LoadManager
   {
      public var onLoaded:Signal;
      
      public var onLoadComplete:Signal;
      
      private var _bytesLoaded:Dictionary;
      
      private var _bytesTotal:Dictionary;
      
      private var _objects:Dictionary;
      
      private var _numLoadersLoading:Number = 0;
      
      public function LoadManager()
      {
         super();
         this.onLoaded = new Signal(CitrusObject,ICitrusArt);
         this.onLoadComplete = new Signal();
      }
      
      public function destroy() : void
      {
         this.onLoaded.removeAll();
         this.onLoadComplete.removeAll();
      }
      
      public function get bytesLoaded() : Number
      {
         var _loc2_:Number = NaN;
         var _loc1_:Number = 0;
         for each(_loc2_ in this._bytesLoaded)
         {
            _loc1_ += _loc2_;
         }
         return _loc1_;
      }
      
      public function get bytesTotal() : Number
      {
         var _loc2_:Number = NaN;
         var _loc1_:Number = 0;
         for each(_loc2_ in this._bytesTotal)
         {
            _loc1_ += _loc2_;
         }
         return _loc1_;
      }
      
      public function add(param1:*, param2:CitrusObject, param3:Number = 1) : Boolean
      {
         var _loc4_:Loader = null;
         var _loc5_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc7_:int = 0;
         var _loc8_:Boolean = false;
         if(param1 is Loader || Boolean(param1.loader))
         {
            if(this._numLoadersLoading == 0)
            {
               this._bytesLoaded = new Dictionary();
               this._bytesTotal = new Dictionary();
               this._objects = new Dictionary();
            }
            ++this._numLoadersLoading;
            _loc4_ = param1 is Loader ? param1 as Loader : param1.loader as Loader;
            _loc4_.contentLoaderInfo.addEventListener(ProgressEvent.PROGRESS,this.handleLoaderProgress);
            _loc4_.contentLoaderInfo.addEventListener(Event.COMPLETE,this.handleLoaderComplete);
            _loc4_.contentLoaderInfo.addEventListener(IOErrorEvent.IO_ERROR,this.handleLoaderError);
            this._bytesLoaded[_loc4_] = 0;
            this._bytesTotal[_loc4_] = 0;
            this._objects[_loc4_] = {
               "co":param2,
               "art":param1
            };
            return true;
         }
         if(param1 is Sprite)
         {
            _loc5_ = param3 - 1;
            _loc6_ = Sprite(param1).numChildren;
            _loc7_ = 0;
            while(_loc7_ < _loc6_)
            {
               _loc8_ = this.add(Sprite(param1).getChildAt(_loc7_),param2,_loc5_);
               if(_loc8_)
               {
                  return true;
               }
               _loc7_++;
            }
            return false;
         }
         return false;
      }
      
      private function handleLoaderProgress(param1:ProgressEvent) : void
      {
         this._bytesLoaded[param1.target.loader] = param1.bytesLoaded;
         this._bytesTotal[param1.target.loader] = param1.bytesTotal;
      }
      
      private function handleLoaderComplete(param1:Event) : void
      {
         var _loc2_:CitrusObject = this._objects[param1.target.loader].co;
         var _loc3_:ICitrusArt = this._objects[param1.target.loader].art;
         this.onLoaded.dispatch(_loc2_,this._objects[param1.target.loader].art as ICitrusArt);
         this.clearLoader(param1.target.loader);
         if(this._numLoadersLoading == 0)
         {
            this.onLoadComplete.dispatch();
         }
      }
      
      private function handleLoaderError(param1:IOErrorEvent) : void
      {
         this.clearLoader(param1.target.loader);
         if(this._numLoadersLoading == 0)
         {
            this.onLoadComplete.dispatch();
         }
      }
      
      private function clearLoader(param1:Loader) : void
      {
         param1.contentLoaderInfo.removeEventListener(ProgressEvent.PROGRESS,this.handleLoaderProgress);
         param1.contentLoaderInfo.removeEventListener(Event.COMPLETE,this.handleLoaderComplete);
         param1.contentLoaderInfo.removeEventListener(IOErrorEvent.IO_ERROR,this.handleLoaderError);
         --this._numLoadersLoading;
         delete this._bytesTotal[param1];
         delete this._bytesTotal[param1];
         delete this._objects[param1];
      }
   }
}

