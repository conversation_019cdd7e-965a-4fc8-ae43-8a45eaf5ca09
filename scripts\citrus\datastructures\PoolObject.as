package citrus.datastructures
{
   import citrus.core.CitrusObject;
   import citrus.core.IState;
   import citrus.core.citrus_internal;
   import citrus.view.ACitrusView;
   import org.osflash.signals.Signal;
   
   use namespace citrus_internal;
   
   public class PoolObject extends DoublyLinkedList
   {
      protected var _poolType:Class;
      
      protected var _defaultParams:Object;
      
      protected var _poolSize:uint = 0;
      
      protected var _poolGrowthRate:uint = 0;
      
      protected var _isCitrusObjectPool:Boolean;
      
      citrus_internal var state:IState;
      
      public var onCreate:Signal;
      
      public var onDispose:Signal;
      
      public var onRecycle:Signal;
      
      public var onDestroy:Signal;
      
      protected var _freeListHead:DoublyLinkedListNode = null;
      
      protected var _freeCount:uint = 0;
      
      protected var gc:Vector.<DoublyLinkedListNode>;
      
      public function PoolObject(param1:Class, param2:Object, param3:uint, param4:Boolean)
      {
         super();
         this._poolType = param1;
         this._defaultParams = param2;
         this._poolGrowthRate = param3;
         this._isCitrusObjectPool = param4;
         this.onCreate = new Signal(this._poolType);
         this.onDispose = new Signal(this._poolType);
         this.onRecycle = new Signal(this._poolType);
         this.onDestroy = new Signal(this._poolType);
         this.gc = new Vector.<DoublyLinkedListNode>();
      }
      
      public function initializePool(param1:uint = 1) : void
      {
         this._poolSize = param1;
         this.increasePoolSize(this._poolSize);
      }
      
      protected function increasePoolSize(param1:uint, param2:Object = null) : void
      {
         var _loc4_:DoublyLinkedListNode = null;
         param2 = this.mergeParams(param2);
         var _loc3_:int = 0;
         while(_loc3_ < param1)
         {
            _loc4_ = new DoublyLinkedListNode();
            this._create(_loc4_,param2);
            this._dispose(_loc4_);
            if(this._freeListHead)
            {
               this._freeListHead.prev = _loc4_;
               _loc4_.next = this._freeListHead;
               this._freeListHead = _loc4_;
            }
            else
            {
               this._freeListHead = _loc4_;
            }
            ++this._freeCount;
            _loc3_++;
         }
      }
      
      public function get(param1:Object = null) : DoublyLinkedListNode
      {
         var _loc2_:DoublyLinkedListNode = null;
         if(!this._freeListHead)
         {
            this.increasePoolSize(this._poolGrowthRate,param1);
         }
         _loc2_ = this._freeListHead;
         if(_loc2_.next)
         {
            this._freeListHead = _loc2_.next;
            this._freeListHead.prev = null;
            _loc2_.next = null;
         }
         else
         {
            this._freeListHead = null;
         }
         if(head != null)
         {
            tail.next = _loc2_;
            _loc2_.prev = tail;
            tail = _loc2_;
         }
         else
         {
            head = tail = _loc2_;
         }
         ++_count;
         --this._freeCount;
         this._recycle(_loc2_,param1);
         return _loc2_;
      }
      
      protected function _create(param1:DoublyLinkedListNode, param2:Object = null) : void
      {
         this.onCreate.dispatch(param1.data as this._poolType,param2);
      }
      
      protected function _recycle(param1:DoublyLinkedListNode, param2:Object = null) : void
      {
         this.onRecycle.dispatch(param1.data as this._poolType,param2);
      }
      
      protected function _dispose(param1:DoublyLinkedListNode) : void
      {
         this.onDispose.dispatch(param1.data as this._poolType);
         (param1.data as this._poolType).kill = false;
      }
      
      protected function _destroy(param1:DoublyLinkedListNode) : void
      {
         this.onDestroy.dispatch(param1.data as this._poolType);
      }
      
      protected function mergeParams(param1:Object) : Object
      {
         var _loc3_:String = null;
         var _loc2_:Object = {};
         for(_loc3_ in this._defaultParams)
         {
            _loc2_[_loc3_] = this._defaultParams[_loc3_];
         }
         for(_loc3_ in param1)
         {
            _loc2_[_loc3_] = param1[_loc3_];
         }
         return _loc2_;
      }
      
      public function updatePhysics(param1:Number) : void
      {
         var _loc2_:DoublyLinkedListNode = head;
         while(_loc2_ != null)
         {
            if((_loc2_.data as this._poolType).updateCallEnabled)
            {
               (_loc2_.data as this._poolType).update(param1);
            }
            if("kill" in (_loc2_.data as this._poolType) && Boolean((_loc2_.data as this._poolType).kill))
            {
               this.gc.push(_loc2_);
            }
            _loc2_ = _loc2_.next;
         }
         if(Boolean(this.gc) && this.gc.length > 0)
         {
            for each(_loc2_ in this.gc)
            {
               this.disposeFromData(_loc2_.data);
            }
            this.gc.length = 0;
         }
      }
      
      public function isDataDisposed(param1:*) : Boolean
      {
         var _loc2_:DoublyLinkedListNode = this._freeListHead;
         while(_loc2_ != null)
         {
            if(_loc2_.data == param1)
            {
               return true;
            }
            _loc2_ = _loc2_.next;
         }
         return false;
      }
      
      public function updateArt(param1:ACitrusView) : void
      {
         var _loc2_:DoublyLinkedListNode = head;
         while(_loc2_ != null)
         {
            (_loc2_.data as this._poolType).update(param1);
            _loc2_ = _loc2_.next;
         }
      }
      
      public function getNodeFromData(param1:*) : DoublyLinkedListNode
      {
         var _loc2_:DoublyLinkedListNode = head;
         while(_loc2_ != null)
         {
            if(_loc2_.data == param1)
            {
               return _loc2_;
            }
            _loc2_ = _loc2_.next;
         }
         return null;
      }
      
      public function disposeNode(param1:DoublyLinkedListNode) : DoublyLinkedListNode
      {
         if(param1 == head)
         {
            head = param1.next;
            if(head != null)
            {
               head.prev = null;
            }
         }
         else
         {
            param1.prev.next = param1.next;
         }
         if(param1 == tail)
         {
            tail = param1.prev;
            if(tail != null)
            {
               tail.next = null;
            }
         }
         else
         {
            param1.next.prev = param1.prev;
         }
         param1.prev = null;
         if(this._freeListHead)
         {
            this._freeListHead.prev = param1;
            param1.next = this._freeListHead;
            this._freeListHead = param1;
         }
         else
         {
            this._freeListHead = param1;
            param1.next = null;
         }
         --_count;
         ++this._freeCount;
         this._dispose(param1);
         return param1;
      }
      
      public function disposeFromData(param1:*) : DoublyLinkedListNode
      {
         var _loc2_:DoublyLinkedListNode = this.getNodeFromData(param1 as this._poolType);
         if(_loc2_)
         {
            return this.disposeNode(_loc2_);
         }
         throw new Error("This data is already disposed :",param1);
      }
      
      public function disposeAll() : void
      {
         while(head)
         {
            this.disposeNode(head);
         }
      }
      
      public function killAll() : void
      {
         var _loc1_:DoublyLinkedListNode = head;
         while(_loc1_)
         {
            (_loc1_.data as CitrusObject).kill = true;
            _loc1_ = _loc1_.next;
         }
      }
      
      public function foreachDisposed(param1:Function) : Boolean
      {
         var _loc2_:DoublyLinkedListNode = this._freeListHead;
         while(_loc2_)
         {
            if(param1(_loc2_.data as this._poolType))
            {
               return true;
            }
            _loc2_ = _loc2_.next;
         }
         return false;
      }
      
      public function foreachRecycled(param1:Function) : Boolean
      {
         var _loc2_:DoublyLinkedListNode = head;
         while(_loc2_)
         {
            if(param1(_loc2_.data as this._poolType))
            {
               return true;
            }
            _loc2_ = _loc2_.next;
         }
         return false;
      }
      
      public function foreach(param1:Function) : Boolean
      {
         var _loc2_:DoublyLinkedListNode = head;
         while(_loc2_)
         {
            if(param1(_loc2_.data as this._poolType))
            {
               return true;
            }
            _loc2_ = _loc2_.next;
         }
         _loc2_ = this._freeListHead;
         while(_loc2_)
         {
            if(param1(_loc2_.data as this._poolType))
            {
               return true;
            }
            _loc2_ = _loc2_.next;
         }
         return false;
      }
      
      public function clear() : void
      {
         var _loc1_:DoublyLinkedListNode = null;
         this.disposeAll();
         while(this._freeListHead)
         {
            _loc1_ = this._freeListHead;
            this._destroy(_loc1_);
            _loc1_.data = null;
            this._freeListHead = _loc1_.next;
            if(this._freeListHead)
            {
               this._freeListHead.prev = null;
            }
            _loc1_.next = null;
            --this._freeCount;
         }
         this._freeListHead = null;
         head = null;
      }
      
      public function destroy() : void
      {
         this.clear();
         this.onCreate.removeAll();
         this.onDestroy.removeAll();
         this.onDispose.removeAll();
         this.onRecycle.removeAll();
         this._defaultParams = null;
         this.gc.length = 0;
         this.gc = null;
      }
      
      override public function get length() : uint
      {
         return _count;
      }
      
      public function get recycledSize() : uint
      {
         return _count;
      }
      
      public function get poolSize() : uint
      {
         return this._freeCount;
      }
      
      public function get allSize() : uint
      {
         return this._freeCount + _count;
      }
      
      public function get isCitrusObjectPool() : Boolean
      {
         return this._isCitrusObjectPool;
      }
   }
}

