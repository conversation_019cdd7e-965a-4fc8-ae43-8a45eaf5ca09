package mogames.gameData.chenghao
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.constvo.ConstATTVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class BaseChenghaoVO
   {
      public var id:Sint;
      
      public var type:int;
      
      public var isGet:Sint;
      
      public var isRecord:Boolean;
      
      protected var _wayInfors:String;
      
      public function BaseChenghaoVO(param1:int, param2:int, param3:String, param4:Boolean = false)
      {
         super();
         this.id = new Sint(param1);
         this.type = param2;
         this._wayInfors = param3;
         this.isGet = new Sint();
         this.isRecord = param4;
      }
      
      public function get hasGet() : Boolean
      {
         return this.isGet.v == 1;
      }
      
      public function get hasOwner() : Boolean
      {
         return ChenghaoProxy.instance().hasOwner(this.id.v) != null;
      }
      
      public function get constChenghao() : ConstGoodVO
      {
         return GoodConfig.instance().findConstGood(this.id.v);
      }
      
      public function get wayInfors() : String
      {
         return this._wayInfors;
      }
      
      public function get attInfors() : String
      {
         var _loc1_:Array = [];
         var _loc2_:ConstATTVO = GoodConfig.instance().findConstGood(this.id.v) as ConstATTVO;
         if(_loc2_.baseHP)
         {
            if(_loc2_.baseHP.isPer)
            {
               _loc1_.push("生命+" + int(_loc2_.baseHP.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("生命+" + _loc2_.baseHP.value.v);
            }
         }
         if(_loc2_.baseMP)
         {
            if(_loc2_.baseMP.isPer)
            {
               _loc1_.push("法力+" + int(_loc2_.baseMP.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("法力+" + _loc2_.baseMP.value.v);
            }
         }
         if(_loc2_.baseATK)
         {
            if(_loc2_.baseATK.isPer)
            {
               _loc1_.push("攻击+" + int(_loc2_.baseATK.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("攻击+" + _loc2_.baseATK.value.v);
            }
         }
         if(_loc2_.basePDEF)
         {
            if(_loc2_.basePDEF.isPer)
            {
               _loc1_.push("物理防御+" + int(_loc2_.basePDEF.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("物理防御+" + _loc2_.basePDEF.value.v);
            }
         }
         if(_loc2_.baseMDEF)
         {
            if(_loc2_.baseMDEF.isPer)
            {
               _loc1_.push("法术防御+" + int(_loc2_.baseMDEF.value.v * 0.1) + "%");
            }
            else
            {
               _loc1_.push("法术防御+" + _loc2_.baseMDEF.value.v);
            }
         }
         if(_loc2_.baseCRIT.v != 0)
         {
            _loc1_.push("暴击+" + _loc2_.baseCRIT.v);
         }
         if(_loc2_.baseMISS.v != 0)
         {
            _loc1_.push("闪避+" + _loc2_.baseMISS.v);
         }
         if(_loc2_.baseLUCK.v != 0)
         {
            _loc1_.push("幸运+" + _loc2_.baseLUCK.v);
         }
         return _loc1_.join("<br>");
      }
      
      public function get saveData() : String
      {
         return this.id.v + "H" + this.isGet.v;
      }
   }
}

