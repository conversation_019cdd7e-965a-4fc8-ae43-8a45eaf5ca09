package file
{
   import flash.utils.Dictionary;
   import mogames.gameData.forge.ForgeVO;
   import mogames.gameData.forge.NeedVO;
   
   public class ForgePEConfig
   {
      private static var _instance:ForgePEConfig;
      
      private var _forgeDic:Dictionary;
      
      public function ForgePEConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : ForgePEConfig
      {
         if(!_instance)
         {
            _instance = new ForgePEConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._forgeDic = new Dictionary();
         this._forgeDic["1&27"] = new Vector.<ForgeVO>();
         this._forgeDic["1&27"].push(new ForgeVO(0,1000,300,new NeedVO(14120,1)));
         this._forgeDic["1&27"].push(new ForgeVO(1,900,600,new NeedVO(14120,2)));
         this._forgeDic["1&27"].push(new ForgeVO(2,800,1200,new NeedVO(14120,3)));
         this._forgeDic["1&27"].push(new ForgeVO(3,700,2400,new NeedVO(14120,4)));
         this._forgeDic["1&27"].push(new ForgeVO(4,600,4800,new NeedVO(14120,5)));
         this._forgeDic["1&27"].push(new ForgeVO(5,500,9600,new NeedVO(14121,3)));
         this._forgeDic["1&27"].push(new ForgeVO(6,400,19200,new NeedVO(14121,4)));
         this._forgeDic["1&27"].push(new ForgeVO(7,300,38400,new NeedVO(14121,5)));
         this._forgeDic["1&27"].push(new ForgeVO(8,200,57600,new NeedVO(14122,5)));
         this._forgeDic["1&27"].push(new ForgeVO(9,100,76800,new NeedVO(14122,6)));
         this._forgeDic["28&42"] = new Vector.<ForgeVO>();
         this._forgeDic["28&42"].push(new ForgeVO(0,1000,300,new NeedVO(14001,3)));
         this._forgeDic["28&42"].push(new ForgeVO(1,900,600,new NeedVO(14001,3)));
         this._forgeDic["28&42"].push(new ForgeVO(2,800,1200,new NeedVO(14001,3)));
         this._forgeDic["28&42"].push(new ForgeVO(3,700,2400,new NeedVO(14001,3)));
         this._forgeDic["28&42"].push(new ForgeVO(4,600,4800,new NeedVO(14001,3)));
         this._forgeDic["28&42"].push(new ForgeVO(5,500,9600,new NeedVO(14002,3)));
         this._forgeDic["28&42"].push(new ForgeVO(6,400,19200,new NeedVO(14002,4)));
         this._forgeDic["28&42"].push(new ForgeVO(7,300,38400,new NeedVO(14002,5)));
         this._forgeDic["28&42"].push(new ForgeVO(8,200,57600,new NeedVO(14003,6)));
         this._forgeDic["28&42"].push(new ForgeVO(9,100,76800,new NeedVO(14003,7)));
         this._forgeDic["43&52"] = new Vector.<ForgeVO>();
         this._forgeDic["43&52"].push(new ForgeVO(0,1000,2000,new NeedVO(14001,5)));
         this._forgeDic["43&52"].push(new ForgeVO(1,900,4000,new NeedVO(14001,5)));
         this._forgeDic["43&52"].push(new ForgeVO(2,800,6000,new NeedVO(14001,5)));
         this._forgeDic["43&52"].push(new ForgeVO(3,700,8000,new NeedVO(14001,5)));
         this._forgeDic["43&52"].push(new ForgeVO(4,600,10000,new NeedVO(14001,5)));
         this._forgeDic["43&52"].push(new ForgeVO(5,500,20000,new NeedVO(14002,5)));
         this._forgeDic["43&52"].push(new ForgeVO(6,400,35000,new NeedVO(14002,6)));
         this._forgeDic["43&52"].push(new ForgeVO(7,300,50000,new NeedVO(14002,7)));
         this._forgeDic["43&52"].push(new ForgeVO(8,200,75000,new NeedVO(14003,8)));
         this._forgeDic["43&52"].push(new ForgeVO(9,100,100000,new NeedVO(14003,9)));
         this._forgeDic["53&62"] = new Vector.<ForgeVO>();
         this._forgeDic["53&62"].push(new ForgeVO(0,1000,4000,new NeedVO(14001,10)));
         this._forgeDic["53&62"].push(new ForgeVO(1,900,8000,new NeedVO(14001,10)));
         this._forgeDic["53&62"].push(new ForgeVO(2,800,12000,new NeedVO(14001,10)));
         this._forgeDic["53&62"].push(new ForgeVO(3,700,16000,new NeedVO(14001,10)));
         this._forgeDic["53&62"].push(new ForgeVO(4,600,20000,new NeedVO(14001,10)));
         this._forgeDic["53&62"].push(new ForgeVO(5,500,40000,new NeedVO(14002,10)));
         this._forgeDic["53&62"].push(new ForgeVO(6,400,70000,new NeedVO(14002,12)));
         this._forgeDic["53&62"].push(new ForgeVO(7,300,100000,new NeedVO(14002,14)));
         this._forgeDic["53&62"].push(new ForgeVO(8,200,150000,new NeedVO(14003,16)));
         this._forgeDic["53&62"].push(new ForgeVO(9,100,200000,new NeedVO(14003,18)));
      }
      
      public function findForgeVO(param1:int, param2:int) : ForgeVO
      {
         var _loc3_:Vector.<ForgeVO> = null;
         var _loc4_:ForgeVO = null;
         if(param1 >= 1 && param1 <= 27)
         {
            _loc3_ = this._forgeDic["1&27"];
         }
         else if(param1 >= 28 && param1 <= 42)
         {
            _loc3_ = this._forgeDic["28&42"];
         }
         else if(param1 >= 43 && param1 <= 52)
         {
            _loc3_ = this._forgeDic["43&52"];
         }
         else if(param1 >= 53 && param1 <= 62)
         {
            _loc3_ = this._forgeDic["53&62"];
         }
         for each(_loc4_ in _loc3_)
         {
            if(_loc4_.level.v == param2)
            {
               return _loc4_;
            }
         }
         return null;
      }
   }
}

