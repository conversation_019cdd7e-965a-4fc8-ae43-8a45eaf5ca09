package mogames.gameMission.mission.shituoling
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.enemy.BOSSDaPengJing;
   import mogames.gameUI.mission.ReviveModule;
   
   public class SceneShiTuoLing02 extends BossScene
   {
      private var _realBOSS:BOSSDaPengJing;
      
      public function SceneShiTuoLing02(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(405,204,428,124),new Point(490,390));
      }
      
      override protected function handlerInit() : void
      {
         this.addBOSS();
         _mission.cleanLoadUI();
         this.startBossBattle("BGM_BATTLE1");
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         setHeroEnable(true);
         this._realBOSS.activeEnemy(false);
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      override protected function addBOSS() : void
      {
         this._realBOSS = new BOSSDaPengJing();
         this._realBOSS.x = 700;
         this._realBOSS.y = 250;
         this._realBOSS.initData(30411,30411,130);
         this._realBOSS.target = _mission.onePlayer;
         add(this._realBOSS);
         this._realBOSS.onRole.add(listenBOSS);
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11032);
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._realBOSS,
            "rect":_dropRect,
            "hero":this._realBOSS.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         this._realBOSS = null;
         ReviveModule.bornPos.setTo(0,0);
         super.destroy();
      }
   }
}

