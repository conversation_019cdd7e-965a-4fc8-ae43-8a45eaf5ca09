package mogames.gameData.drop
{
   import file.GoodConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.bag.CardProxy;
   import mogames.gameData.drop.vo.BaseDropVO;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.good.constvo.ConstCardVO;
   import mogames.gameData.good.constvo.ConstPetVO;
   import mogames.gameData.pet.PetProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.TxtUtil;
   
   public class RewardHandler
   {
      private static var _instance:RewardHandler;
      
      public var rewardNames:String;
      
      private var moneyList:Array;
      
      private var cardList:Array;
      
      private var petList:Array;
      
      private var splitList:Array;
      
      public function RewardHandler()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.moneyList = [];
         this.splitList = [];
         this.cardList = [];
      }
      
      public static function instance() : RewardHandler
      {
         if(!_instance)
         {
            _instance = new RewardHandler();
         }
         return _instance;
      }
      
      public function newGiftReward(param1:Array) : Array
      {
         var _loc3_:BaseRewardVO = null;
         if(!param1)
         {
            return [];
         }
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            if(_loc3_)
            {
               _loc2_.push(_loc3_.newGood());
            }
         }
         return _loc2_;
      }
      
      public function newDropReward(param1:Array) : Array
      {
         var _loc3_:BaseDropVO = null;
         if(!param1)
         {
            return [];
         }
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            if(!(!_loc3_ || !_loc3_.isDrop))
            {
               _loc2_.push(_loc3_.newGood());
            }
         }
         return _loc2_;
      }
      
      public function checkFull(param1:Array, param2:Boolean = true) : Boolean
      {
         var _loc4_:GameGoodVO = null;
         var _loc5_:String = null;
         var _loc3_:Array = [];
         this.cardList = [];
         this.moneyList = [];
         this.petList = [];
         for each(_loc4_ in param1)
         {
            if(this.isVitrual(_loc4_.constVO.id.v))
            {
               this.moneyList.push(_loc4_);
            }
            else if(_loc4_.constVO is ConstCardVO)
            {
               this.cardList.push(_loc4_);
            }
            else if(_loc4_.constVO is ConstPetVO)
            {
               this.petList.push(_loc4_);
            }
            else
            {
               _loc3_.push(_loc4_);
            }
         }
         this.splitList = this.splitItems(_loc3_);
         _loc5_ = BagProxy.instance().checkLack(this.splitList);
         if(_loc5_ == "" && this.checkFullPet(this.petList))
         {
            _loc5_ = "宠物位置栏不足！";
         }
         if(_loc5_ != "")
         {
            if(param2)
            {
               PromptMediator.instance().showPrompt(_loc5_,null,null,true);
            }
            return true;
         }
         return false;
      }
      
      private function checkFullPet(param1:Array) : Boolean
      {
         var _loc2_:int = 0;
         var _loc3_:GameGoodVO = null;
         for each(_loc3_ in param1)
         {
            _loc2_ += _loc3_.amount.v;
         }
         return _loc2_ > PetProxy.instance().leftPet;
      }
      
      public function onReward(param1:Array, param2:Boolean = true, param3:Boolean = true, param4:Boolean = true) : Boolean
      {
         var _loc5_:GameGoodVO = null;
         var _loc6_:GameGoodVO = null;
         var _loc7_:GameGoodVO = null;
         var _loc8_:Array = null;
         this.rewardNames = this.parseRewardName(param1);
         if(this.checkFull(param1,param2))
         {
            return false;
         }
         for each(_loc5_ in this.moneyList)
         {
            this.onGetMoney(_loc5_);
         }
         for each(_loc6_ in this.cardList)
         {
            CardProxy.instance().addCard(_loc6_.constVO.id.v,_loc6_.amount.v);
         }
         for each(_loc7_ in this.petList)
         {
            this.onGetPet(_loc7_);
         }
         for each(_loc8_ in this.splitList)
         {
            BagProxy.instance().addItems(_loc8_);
         }
         if(param3)
         {
            MiniMsgMediator.instance().showAutoMsg("获得：" + this.rewardNames,480,200,"");
         }
         this.moneyList.length = 0;
         this.splitList.length = 0;
         this.cardList.length = 0;
         this.petList.length = 0;
         if(param4)
         {
            EffectManager.instance().playAudio("GET_REWARD");
         }
         return true;
      }
      
      private function onGetMoney(param1:GameGoodVO) : void
      {
         switch(param1.constVO.id.v)
         {
            case BagProxy.GOOD_GOLD.v:
               MasterProxy.instance().changeGold(param1.amount.v);
               break;
            case BagProxy.GOOD_TICKET.v:
               MasterProxy.instance().changeTicket(param1.amount.v);
         }
      }
      
      public function parseRewardName(param1:Array) : String
      {
         var _loc3_:GameGoodVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            _loc2_.push(TxtUtil.setColor(_loc3_.constVO.name + "X" + _loc3_.amount.v,ConstData.GOOD_COLOR1[_loc3_.quality]));
         }
         return _loc2_.join(",");
      }
      
      private function splitItems(param1:Array) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:Array = [];
         var _loc4_:Array = [];
         var _loc5_:Array = [];
         var _loc6_:Array = [];
         var _loc7_:int = 0;
         var _loc8_:int = int(param1.length);
         while(_loc7_ < _loc8_)
         {
            switch(GoodConfig.instance().findGoodType(param1[_loc7_].constVO.id.v))
            {
               case ConstData.GOOD_PROP:
                  _loc2_.push(param1[_loc7_]);
                  break;
               case ConstData.GOOD_EQUIP:
                  _loc3_.push(param1[_loc7_]);
                  break;
               case ConstData.GOOD_FASHION:
                  _loc4_.push(param1[_loc7_]);
                  break;
               case ConstData.GOOD_PET:
                  _loc5_.push(param1[_loc7_]);
                  break;
               case ConstData.GOOD_CLIP:
                  _loc6_.push(param1[_loc7_]);
                  break;
            }
            _loc7_++;
         }
         this.checkItemList(_loc2_);
         this.checkItemList(_loc3_);
         this.checkItemList(_loc4_);
         this.checkItemList(_loc5_);
         this.checkItemList(_loc6_);
         return [_loc2_,_loc3_,_loc4_,_loc5_,_loc6_];
      }
      
      private function checkItemList(param1:Array) : void
      {
         var _loc2_:GameGoodVO = null;
         for each(_loc2_ in param1)
         {
            this.splitPileItem(_loc2_,param1);
         }
      }
      
      private function splitPileItem(param1:GameGoodVO, param2:Array) : void
      {
         var _loc6_:GameGoodVO = null;
         if(param1.amount.v <= BagProxy.BAG_MAX_COUNT.v)
         {
            return;
         }
         var _loc3_:int = int(param1.amount.v / BagProxy.BAG_MAX_COUNT.v);
         var _loc4_:int = param1.amount.v - _loc3_ * BagProxy.BAG_MAX_COUNT.v;
         if(_loc4_ > 0)
         {
            param1.amount.v = _loc4_;
         }
         var _loc5_:int = 0;
         while(_loc5_ < _loc3_)
         {
            _loc6_ = GoodConfig.instance().newGood(param1.constVO.id.v);
            _loc6_.amount.v = BagProxy.BAG_MAX_COUNT.v;
            param2.push(_loc6_);
            _loc5_++;
         }
      }
      
      public function isVitrual(param1:int) : Boolean
      {
         return param1 == BagProxy.GOOD_GOLD.v || param1 == BagProxy.GOOD_TICKET.v;
      }
      
      private function onGetPet(param1:GameGoodVO) : void
      {
         var _loc2_:int = param1.amount.v;
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_)
         {
            PetProxy.instance().addNewPet(new RewardPetHandler().newPet(param1.constVO.id.v));
            _loc3_++;
         }
      }
   }
}

