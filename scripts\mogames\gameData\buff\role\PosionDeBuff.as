package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class PosionDeBuff extends HurtBuff
   {
      public function PosionDeBuff()
      {
         super(1001);
      }
      
      override public function get buffSkin() : BMCSprite
      {
         _skin.setupSequence(AssetManager.findAnimation("EFFECT_BUFF_POSION_CLIP"),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height;
         return _skin;
      }
   }
}

