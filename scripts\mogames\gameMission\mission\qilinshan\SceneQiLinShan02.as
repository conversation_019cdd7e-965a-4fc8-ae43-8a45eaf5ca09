package mogames.gameMission.mission.qilinshan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.enemy.BOSSJinMaoHou;
   import mogames.gameUI.mission.ReviveModule;
   
   public class SceneQiLinShan02 extends BossScene
   {
      private var _clouds:Array = [{
         "skin":"MC_SUPER_LARGE_CLOUD_CLIP",
         "width":730,
         "height":40,
         "posX":480,
         "posY":485,
         "idleTime":9999,
         "showTime":3,
         "oneWay":false
      },{
         "skin":"MC_MIDDLE_CLOUD_CLIP",
         "width":140,
         "height":26,
         "posX":319,
         "posY":300,
         "idleTime":10,
         "showTime":3,
         "oneWay":true
      },{
         "skin":"MC_MIDDLE_CLOUD_CLIP",
         "width":140,
         "height":26,
         "posX":664,
         "posY":300,
         "idleTime":10,
         "showTime":3,
         "oneWay":true
      }];
      
      private var _tempClouds:Array;
      
      private var _realBOSS:BOSSJinMaoHou;
      
      public function SceneQiLinShan02(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(342,245,276,166),new Point(450,365));
         ReviveModule.bornPos.setTo(480,300);
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2046,1);
            startBossBattle();
         };
         super.handlerInit();
         this.layoutClouds();
         this.addBOSS();
         if(!FlagProxy.instance().isComplete(2046))
         {
            showDialog("STORY0074",func);
         }
         else
         {
            this.startBossBattle();
         }
      }
      
      private function layoutClouds() : void
      {
         var _loc1_:Object = null;
         var _loc2_:BaseCloud = null;
         this._tempClouds = [];
         for each(_loc1_ in this._clouds)
         {
            _loc2_ = new BaseCloud(_loc1_.skin,{
               "width":_loc1_.width,
               "height":_loc1_.height,
               "group":2,
               "oneWay":_loc1_.oneWay
            });
            _loc2_.setLocation(_loc1_.posX,_loc1_.posY);
            _loc2_.setTime(_loc1_.idleTime,_loc1_.showTime);
            Layers.addCEChild(_loc2_);
            this._tempClouds.push(_loc2_);
            _loc2_.isPause = true;
         }
      }
      
      private function pauseCloud(param1:Boolean = true) : void
      {
         var _loc2_:BaseCloud = null;
         for each(_loc2_ in this._tempClouds)
         {
            _loc2_.isPause = param1;
            if(param1)
            {
               _loc2_.stopCloud();
            }
         }
      }
      
      override protected function addBOSS() : void
      {
         this._realBOSS = new BOSSJinMaoHou();
         this._realBOSS.x = 771;
         this._realBOSS.y = 200;
         this._realBOSS.initData(30361,30361,93);
         this._realBOSS.target = _mission.curTarget;
         this._realBOSS.setInverted(true);
         add(this._realBOSS);
         this._realBOSS.onRole.add(this.listenBOSS);
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         setHeroEnable(true);
         this._realBOSS.activeEnemy(false);
         EffectManager.instance().playBGM("BGM_BATTLE2");
         this.pauseCloud(false);
      }
      
      override protected function listenBOSS(param1:String) : void
      {
         if(param1 != "onDead")
         {
            return;
         }
         this.pauseCloud();
         super.listenBOSS(param1);
      }
      
      override protected function handlerWin() : void
      {
         this.pauseCloud();
         TaskProxy.instance().setComplete(11029);
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._realBOSS,
            "rect":_dropRect,
            "hero":this._realBOSS.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         this._tempClouds = null;
         this._realBOSS = null;
         ReviveModule.bornPos.setTo(0,0);
         super.destroy();
      }
   }
}

