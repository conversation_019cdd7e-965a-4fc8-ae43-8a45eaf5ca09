package mogames.gameMission.mission.npc.wuzhuangguan
{
   import file.BuyConfig;
   import file.GoodConfig;
   import file.YaoYuanConfig;
   import mogames.Layers;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.flag.FBFlagProxy;
   import mogames.gameData.flag.fuben.CuiShuFlag;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.huoli.HuoLiProxy;
   import mogames.gameData.mall.MallProxy;
   import mogames.gameData.mall.vo.MallMoneyVO;
   import mogames.gameData.shop.vo.BaseCostVO;
   import mogames.gameData.yaoyuan.HerbProxy;
   import mogames.gameData.yaoyuan.vo.HerbGrowVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gamePKG.PKGManager;
   import mogames.gameTask.BaseTaskHandler;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import mogames.gameUI.seed.SeedSelectModule;
   import mogames.gameUI.tip.TipManager;
   import utils.TxtUtil;
   
   public class YaoYuanHandler extends BaseTaskHandler
   {
      private var locations:Array = [[524,878,5],[675,878,5],[825,878,5],[750,759,2],[1030,812,2],[1174,764,2],[641,584,2],[545,584,2],[318,479,2],[222,479,2]];
      
      private var _grounds:Vector.<GroundSprite>;
      
      private var _activeItem:GroundItem;
      
      public function YaoYuanHandler()
      {
         super();
         EventManager.addEventListener(UIEvent.HERB_EVENT,this.onHerb,false,0,true);
      }
      
      public function initYaoYuan() : void
      {
         var _loc1_:Array = null;
         var _loc3_:GroundSprite = null;
         this._grounds = new Vector.<GroundSprite>();
         var _loc2_:int = 0;
         while(_loc2_ < 10)
         {
            _loc1_ = this.locations[_loc2_];
            _loc3_ = new GroundSprite(_loc1_[2],YaoYuanConfig.instance().findGround(_loc2_ + 1));
            _loc3_.x = _loc1_[0];
            _loc3_.y = _loc1_[1];
            Layers.addCEChild(_loc3_);
            this._grounds.push(_loc3_);
            _loc2_++;
         }
         this.showHerb(HerbProxy.instance().list);
      }
      
      private function showHerb(param1:Vector.<HerbGrowVO>) : void
      {
         var _loc2_:HerbGrowVO = null;
         for each(_loc2_ in param1)
         {
            this._grounds[_loc2_.index.v - 1].initHerbVO(_loc2_);
         }
      }
      
      private function onHerb(param1:UIEvent) : void
      {
         if(param1.data.type != "clickHerb")
         {
            return;
         }
         var _loc2_:GroundItem = param1.data.data as GroundItem;
         if(!_loc2_.groundVO.isOpen)
         {
            this.handlerOpen(_loc2_);
         }
         else
         {
            this.handlerHerb(_loc2_);
         }
      }
      
      private function handlerHerb(param1:GroundItem) : void
      {
         this._activeItem = param1;
         if(param1.herbVO == null)
         {
            setHeroEnable(false);
            SeedSelectModule.instance().showSeed(param1.groundVO.quality.v,this.plantSeed,this.resumeHero);
         }
         else if(param1.herbVO.isYouMiao)
         {
            this.cuishuYouMiao(param1);
         }
         else if(param1.herbVO.isChengZhang)
         {
            this.cuishuChengZhang(param1);
         }
         else if(param1.herbVO.isChengShu)
         {
            this.pickup(param1);
         }
         TipManager.instance().removeTip();
      }
      
      private function resumeHero() : void
      {
         setHeroEnable(true);
      }
      
      private function plantSeed(param1:GameGoodVO) : void
      {
         this.resumeHero();
         if(!this._activeItem)
         {
            return;
         }
         var _loc2_:HerbGrowVO = new HerbGrowVO(param1.constVO.id.v);
         HerbProxy.instance().addGrow(_loc2_);
         this._activeItem.initHerbVO(_loc2_);
         BagProxy.instance().useItemByID(param1.constVO.id.v);
         EffectManager.instance().addHeadWord(TxtUtil.setColor("种植完毕！","99ff00"),this._activeItem.parent.x,this._activeItem.parent.y);
         HuoLiProxy.instance().setNum(1006);
         PKGManager.saveWithTip();
      }
      
      private function pickup(param1:GroundItem) : void
      {
         if(RewardHandler.instance().onReward([GoodConfig.instance().newGood(param1.herbVO.constSeed.herbID.v)]))
         {
            HerbProxy.instance().handlerPick(param1.herbVO);
            param1.initHerbVO(null);
         }
      }
      
      private function cuishuChengZhang(param1:GroundItem) : void
      {
         var _loc2_:MallMoneyVO = BuyConfig.instance().list[5];
         var _loc3_:CuiShuFlag = FBFlagProxy.instance().findFlag(107) as CuiShuFlag;
         var _loc4_:String = TxtUtil.setColor("金锭X" + _loc2_.price.v,"ffcc00");
         if(MasterProxy.instance().gameVIP.v < 5)
         {
            if(_loc3_.isBuy)
            {
               this.onBuyChengZhang(_loc3_,param1);
            }
            else
            {
               MiniMsgMediator.instance().showAutoMsg("今日二次催熟次数已用完！");
            }
         }
         else
         {
            if(!_loc3_.isFree && !_loc3_.isBuy)
            {
               MiniMsgMediator.instance().showAutoMsg("今日二次催熟次数已用完！");
               return;
            }
            if(_loc3_.isFree)
            {
               this.onFreeChengZhang(_loc3_,param1);
            }
            else if(_loc3_.isBuy)
            {
               this.onBuyChengZhang(_loc3_,param1);
            }
         }
      }
      
      private function onBuyChengZhang(param1:CuiShuFlag, param2:GroundItem) : void
      {
         var mallVO:MallMoneyVO = null;
         var func:Function = null;
         var confirmCuiShu:Function = null;
         var flag:CuiShuFlag = param1;
         var item:GroundItem = param2;
         func = function():void
         {
            flag.useBuy();
            MallProxy.instance().handlerBuy(mallVO,1,confirmCuiShu);
         };
         confirmCuiShu = function():void
         {
            handlerCuiShu(item);
         };
         mallVO = BuyConfig.instance().list[5];
         var need:String = TxtUtil.setColor("金锭X" + mallVO.price.v,"ffcc00");
         PromptMediator.instance().showPrompt("是否消耗" + need + "催长至成熟期？\n（" + flag.status + "）",func);
      }
      
      private function onFreeChengZhang(param1:CuiShuFlag, param2:GroundItem) : void
      {
         var func:Function = null;
         var flag:CuiShuFlag = param1;
         var item:GroundItem = param2;
         func = function():void
         {
            flag.useFree();
            handlerCuiShu(item);
         };
         PromptMediator.instance().showPrompt("是否催长至成熟期？\n（" + flag.status + "）",func);
      }
      
      private function cuishuYouMiao(param1:GroundItem) : void
      {
         var func:Function = null;
         var item:GroundItem = param1;
         func = function():void
         {
            var _loc1_:LackVO = MasterProxy.instance().checkLack(item.herbVO.growGold,0);
            if(_loc1_)
            {
               MiniMsgMediator.instance().showAutoMsg(_loc1_.str);
               return;
            }
            MasterProxy.instance().changeGold(-item.herbVO.growGold);
            handlerCuiShu(item);
            PKGManager.saveWithTip();
         };
         var need:String = TxtUtil.setColor("铜钱X" + item.herbVO.growGold);
         PromptMediator.instance().showPrompt("是否消耗" + need + "催长至成长期？\n（需消耗：铜钱X" + item.herbVO.growGold + "）",func);
      }
      
      private function handlerCuiShu(param1:GroundItem) : void
      {
         param1.handlerCuiShu();
         EffectManager.instance().addHeadWord(TxtUtil.setColor("已催长！","99ff00"),param1.parent.x,param1.parent.y);
      }
      
      private function handlerOpen(param1:GroundItem) : void
      {
         var vo:BaseCostVO = null;
         var okFunc:Function = null;
         var item:GroundItem = param1;
         okFunc = function():void
         {
            var _loc1_:LackVO = vo.isLack;
            if(_loc1_)
            {
               MiniMsgMediator.instance().showAutoMsg(_loc1_.str);
               return;
            }
            vo.useStuff();
            item.groundVO.setOpen();
            item.updateGround();
            EffectManager.instance().addHeadWord(TxtUtil.setColor("扩建完毕！","99ff00"),item.parent.x,item.parent.y);
         };
         vo = YaoYuanConfig.instance().findOpen(item.groundVO.index.v);
         if(!vo)
         {
            return;
         }
         PromptMediator.instance().showPrompt("是否扩建" + item.groundVO.index.v + "号药田？\n（需消耗：" + vo.needStr + "）",okFunc);
      }
      
      public function destroy() : void
      {
         EventManager.removeEventListener(UIEvent.HERB_EVENT,this.onHerb);
         this._grounds = null;
      }
   }
}

