package mogames.gameData.good.constvo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.ValueRandVO;
   
   public class ConstEquipVO extends ConstGoodVO
   {
      public var suitID:Sint;
      
      public var lvLimit:Sint;
      
      public var roleLimit:Sint;
      
      public var timeLimit:Sint;
      
      public var caoType:Sint;
      
      public var baseHP:ValueRandVO;
      
      public var baseMP:ValueRandVO;
      
      public var baseATK:ValueRandVO;
      
      public var basePDEF:ValueRandVO;
      
      public var baseMDEF:ValueRandVO;
      
      public var baseCRIT:ValueRandVO;
      
      public var baseMISS:ValueRandVO;
      
      public var baseLUCK:ValueRandVO;
      
      public var attList:Array;
      
      public var mcSkin:String;
      
      public var mcURL:String;
      
      public function ConstEquipVO(param1:int, param2:String, param3:String, param4:int, param5:int, param6:int, param7:int, param8:int, param9:ValueRandVO, param10:ValueRandVO, param11:ValueRandVO, param12:ValueRandVO, param13:ValueRandVO, param14:ValueRandVO, param15:ValueRandVO, param16:ValueRandVO, param17:Array, param18:int, param19:String, param20:String = "", param21:String = "")
      {
         super(param1,param2,param3,0,0,param18,param19);
         this.caoType = new Sint(param4);
         this.suitID = new Sint(param5);
         this.lvLimit = new Sint(param6);
         this.roleLimit = new Sint(param7);
         this.timeLimit = new Sint(param8);
         this.baseHP = param9;
         this.baseMP = param10;
         this.baseATK = param11;
         this.basePDEF = param12;
         this.baseMDEF = param13;
         this.baseCRIT = param14;
         this.baseMISS = param15;
         this.baseLUCK = param16;
         this.attList = param17;
         this.mcSkin = param20;
         this.mcURL = param21;
      }
      
      public function get isFashion() : Boolean
      {
         return this.bodyPart == 4 || this.bodyPart == 5;
      }
      
      public function get bodyPart() : int
      {
         return int(String(id.v).charAt(1));
      }
   }
}

