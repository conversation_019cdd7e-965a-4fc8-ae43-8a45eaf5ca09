package mogames.gameData.task.vo
{
   import file.GoodConfig;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.Sint;
   
   public class TaskGoodVO
   {
      public var goodID:Sint;
      
      public var needNum:Sint;
      
      public function TaskGoodVO(param1:int, param2:int)
      {
         super();
         this.goodID = new Sint(param1);
         this.needNum = new Sint(param2);
      }
      
      public function handlerGet() : void
      {
         BagProxy.instance().useItems(this.goodID.v,this.needNum.v);
      }
      
      public function get isComplete() : Boolean
      {
         return BagProxy.instance().getGoodNum(this.goodID.v) >= this.needNum.v;
      }
      
      public function get infor() : String
      {
         var _loc1_:int = BagProxy.instance().getGoodNum(this.goodID.v);
         if(_loc1_ >= this.needNum.v)
         {
            _loc1_ = this.needNum.v;
         }
         return GoodConfig.instance().findConstGood(this.goodID.v).name + "（" + _loc1_ + "/" + this.needNum.v + "）";
      }
   }
}

