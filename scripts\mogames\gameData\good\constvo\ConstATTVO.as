package mogames.gameData.good.constvo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.ValueVO;
   
   public class ConstATTVO extends ConstGoodVO
   {
      public var baseHP:ValueVO;
      
      public var baseMP:ValueVO;
      
      public var baseATK:ValueVO;
      
      public var basePDEF:ValueVO;
      
      public var baseMDEF:ValueVO;
      
      public var baseCRIT:Sint;
      
      public var baseMISS:Sint;
      
      public var baseLUCK:Sint;
      
      public function ConstATTVO(param1:int, param2:String, param3:String, param4:ValueVO, param5:ValueVO, param6:ValueVO, param7:ValueVO, param8:ValueVO, param9:int, param10:int, param11:int, param12:int, param13:int, param14:String)
      {
         super(param1,param2,param3,1,param12,param13,param14);
         this.baseHP = param4;
         this.baseMP = param5;
         this.baseATK = param6;
         this.basePDEF = param7;
         this.baseMDEF = param8;
         this.baseCRIT = new Sint(param9);
         this.baseMISS = new Sint(param10);
         this.baseLUCK = new Sint(param11);
      }
   }
}

