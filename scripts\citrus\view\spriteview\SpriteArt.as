package citrus.view.spriteview
{
   import citrus.core.CitrusEngine;
   import citrus.core.CitrusObject;
   import citrus.core.IState;
   import citrus.physics.APhysicsEngine;
   import citrus.physics.IDebugView;
   import citrus.system.components.ViewComponent;
   import citrus.view.ACitrusCamera;
   import citrus.view.ACitrusView;
   import citrus.view.ICitrusArt;
   import citrus.view.ISpriteView;
   import flash.display.Bitmap;
   import flash.display.DisplayObject;
   import flash.display.Loader;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLRequest;
   import flash.system.ApplicationDomain;
   import flash.system.LoaderContext;
   import flash.utils.Dictionary;
   import flash.utils.getDefinitionByName;
   
   public class SpriteArt extends Sprite implements ICitrusArt
   {
      private static var _loopAnimation:Dictionary = new Dictionary();
      
      private var _content:DisplayObject;
      
      public var loader:Loader;
      
      private var _updateArtEnabled:Boolean = true;
      
      private var _citrusObject:ISpriteView;
      
      private var _physicsComponent:*;
      
      private var _registration:String;
      
      private var _view:*;
      
      private var _animation:String;
      
      public var group:uint;
      
      public function SpriteArt(param1:ISpriteView = null)
      {
         super();
         if(param1)
         {
            this.initialize(param1);
         }
      }
      
      public static function setLoopAnimations(param1:Array) : void
      {
         var _loc2_:* = null;
         for each(_loc2_ in param1)
         {
            _loopAnimation[_loc2_] = true;
         }
      }
      
      public function initialize(param1:ISpriteView) : void
      {
         this._citrusObject = param1;
         CitrusEngine.getInstance().onPlayingChange.add(this._pauseAnimation);
         var _loc2_:IState = CitrusEngine.getInstance().state;
         if(this._citrusObject is ViewComponent && Boolean(_loc2_.getFirstObjectByType(APhysicsEngine) as APhysicsEngine))
         {
            this._physicsComponent = (this._citrusObject as ViewComponent).entity.lookupComponentByName("physics");
         }
         this.name = (this._citrusObject as CitrusObject).name;
         if(_loopAnimation["move"] != true)
         {
            _loopAnimation["move"] = true;
         }
      }
      
      public function get content() : DisplayObject
      {
         return this._content;
      }
      
      public function destroy(param1:Boolean = false) : void
      {
         if(param1)
         {
            if(this._content is AnimationSequence)
            {
               (this._content as AnimationSequence).destroy();
            }
            if(Boolean(this._content) && Boolean(this._content.parent))
            {
               removeChild(this._content);
            }
         }
         else
         {
            CitrusEngine.getInstance().onPlayingChange.remove(this._pauseAnimation);
            this._view = null;
         }
      }
      
      public function moveRegistrationPoint(param1:String) : void
      {
         if(param1 == "topLeft")
         {
            this._content.x = 0;
            this._content.y = 0;
         }
         else if(param1 == "center")
         {
            this._content.x = -this._citrusObject.width / 2;
            this._content.y = -this._citrusObject.height / 2;
         }
      }
      
      public function get registration() : String
      {
         return this._registration;
      }
      
      public function set registration(param1:String) : void
      {
         if(this._registration == param1 || !this._content)
         {
            return;
         }
         this._registration = param1;
         this.moveRegistrationPoint(this._registration);
      }
      
      public function get view() : *
      {
         return this._view;
      }
      
      public function set view(param1:*) : void
      {
         var tmpObj:* = undefined;
         var classString:String = null;
         var suffix:String = null;
         var artClass:Class = null;
         var value:* = param1;
         if(this._view == value)
         {
            return;
         }
         if(Boolean(this._content) && Boolean(this._content.parent))
         {
            this._citrusObject.handleArtChanged(this as ICitrusArt);
            this.destroy(true);
            this._content = null;
         }
         this._view = value;
         if(this._view)
         {
            if(this._view is String)
            {
               classString = this._view;
               suffix = classString.substring(classString.length - 4).toLowerCase();
               if(suffix == ".swf" || suffix == ".png" || suffix == ".gif" || suffix == ".jpg")
               {
                  this.loader = new Loader();
                  this.loader.contentLoaderInfo.addEventListener(Event.COMPLETE,this.handleContentLoaded);
                  this.loader.contentLoaderInfo.addEventListener(IOErrorEvent.IO_ERROR,this.handleContentIOError);
                  this.loader.load(new URLRequest(classString),new LoaderContext(false,ApplicationDomain.currentDomain,null));
               }
               else
               {
                  try
                  {
                     artClass = getDefinitionByName(classString) as Class;
                  }
                  catch(e:Error)
                  {
                     throw new Error("[SpriteArt] could not find class definition for \"" + String(classString) + "\". \n Make sure that you compile it with the project or that its the right classpath.");
                  }
                  tmpObj = new artClass();
                  if(tmpObj is MovieClip)
                  {
                     this._content = new AnimationSequence(tmpObj as MovieClip);
                  }
                  else
                  {
                     if(!(tmpObj is DisplayObject))
                     {
                        throw new Error("[SpriteArt] class" + String(classString) + " does not define a DisplayObject.");
                     }
                     this._content = tmpObj;
                  }
               }
            }
            else if(this._view is Class)
            {
               tmpObj = new this._view();
               if(!(tmpObj is DisplayObject))
               {
                  throw new Error("[SpriteArt] " + String(this._view) + " does not define a DisplayObject.");
               }
               this._content = tmpObj;
            }
            else
            {
               if(!(this._view is DisplayObject))
               {
                  throw new Error("SpriteArt doesn\'t know how to create a graphic object from the provided CitrusObject " + this.citrusObject);
               }
               this._content = this._view;
            }
            if(Boolean(this._content) && Boolean(this._content.hasOwnProperty("initialize")))
            {
               this._content["initialize"](this._citrusObject);
            }
            if(this._content)
            {
               this.moveRegistrationPoint(this._citrusObject.registration);
               addChild(this._content);
               this._citrusObject.handleArtReady(this as ICitrusArt);
            }
         }
      }
      
      public function set updateArtEnabled(param1:Boolean) : void
      {
         this._updateArtEnabled = param1;
      }
      
      public function get updateArtEnabled() : Boolean
      {
         return this._updateArtEnabled;
      }
      
      public function get animation() : String
      {
         return this._animation;
      }
      
      public function set animation(param1:String) : void
      {
         if(this._animation == param1)
         {
            return;
         }
         this._animation = param1;
         var _loc2_:Boolean = Boolean(_loopAnimation[this._animation]);
         if(this._content is AnimationSequence)
         {
            (this._content as AnimationSequence).changeAnimation(this._animation,_loc2_);
         }
      }
      
      public function get citrusObject() : ISpriteView
      {
         return this._citrusObject;
      }
      
      public function update(param1:ACitrusView) : void
      {
         var _loc2_:IDebugView = null;
         if(this._citrusObject.inverted)
         {
            if(scaleX > 0)
            {
               scaleX = -scaleX;
            }
         }
         else if(scaleX < 0)
         {
            scaleX = -scaleX;
         }
         if(this._content is SpritePhysicsDebugView)
         {
            _loc2_ = (this._content as SpritePhysicsDebugView).debugView as IDebugView;
            _loc2_.transformMatrix = param1.camera.transformMatrix;
            _loc2_.visibility = this._citrusObject.visible;
            (this._content as SpritePhysicsDebugView).update();
         }
         else if(this._physicsComponent)
         {
            x = this._physicsComponent.x + (param1.camera.camProxy.x - this._physicsComponent.x) * (1 - this._citrusObject.parallaxX) + this._citrusObject.offsetX * scaleX;
            y = this._physicsComponent.y + (param1.camera.camProxy.y - this._physicsComponent.y) * (1 - this._citrusObject.parallaxY) + this._citrusObject.offsetY;
            rotation = this._physicsComponent.rotation;
         }
         else
         {
            if(param1.camera.parallaxMode == ACitrusCamera.PARALLAX_MODE_DEPTH)
            {
               x = this._citrusObject.x + (param1.camera.camProxy.x - this._citrusObject.x) * (1 - this._citrusObject.parallaxX) + this._citrusObject.offsetX * scaleX;
               y = this._citrusObject.y + (param1.camera.camProxy.y - this._citrusObject.y) * (1 - this._citrusObject.parallaxY) + this._citrusObject.offsetY;
            }
            else
            {
               x = this._citrusObject.x + (param1.camera.camProxy.x + param1.camera.camProxy.offset.x) * (1 - this._citrusObject.parallaxX) + this._citrusObject.offsetX * scaleX;
               y = this._citrusObject.y + (param1.camera.camProxy.y + param1.camera.camProxy.offset.y) * (1 - this._citrusObject.parallaxY) + this._citrusObject.offsetY;
            }
            rotation = this._citrusObject.rotation;
         }
         visible = this._citrusObject.visible;
         mouseChildren = mouseEnabled = this._citrusObject.touchable;
         this.registration = this._citrusObject.registration;
         this.view = this._citrusObject.view;
         this.animation = this._citrusObject.animation;
         this.group = this._citrusObject.group;
      }
      
      private function _pauseAnimation(param1:Boolean) : void
      {
         if(this._content is AnimationSequence)
         {
            if(param1)
            {
               (this._content as AnimationSequence).resume();
            }
            else
            {
               (this._content as AnimationSequence).pause();
            }
         }
      }
      
      private function handleContentLoaded(param1:Event) : void
      {
         var _loc2_:String = null;
         this._content = param1.target.loader.content;
         if(this._content is Bitmap)
         {
            (this._content as Bitmap).smoothing = true;
         }
         if(this._content is MovieClip)
         {
            this._content = new AnimationSequence(this._content as MovieClip);
            _loc2_ = this._animation;
            this._animation = null;
            this.animation = _loc2_;
         }
         this.moveRegistrationPoint(this._citrusObject.registration);
         addChild(this._content);
         this._citrusObject.handleArtReady(this as ICitrusArt);
      }
      
      private function handleContentIOError(param1:IOErrorEvent) : void
      {
         throw new Error(param1.text);
      }
   }
}

