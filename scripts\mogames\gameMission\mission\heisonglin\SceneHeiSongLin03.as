package mogames.gameMission.mission.heisonglin
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.setTimeout;
   import mogames.Layers;
   import mogames.event.UIEvent;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.shuangchaling.SaveNPC;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.TrapTieGai;
   import mogames.gamePKG.PKGManager;
   import mogames.gameRole.enemy.BOSSHuangFengGuai;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.TxtUtil;
   
   public class SceneHeiSongLin03 extends BossScene
   {
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _jiguan:TrapTieGai;
      
      private var _npc:SaveNPC;
      
      public function SceneHeiSongLin03(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         this.layoutTieGai();
         this.layoutNPC();
         _mission.cleanLoadUI();
         this.initEnemyTop();
         this.initEnemyDown();
      }
      
      private function layoutNPC() : void
      {
         if(FlagProxy.instance().isComplete(106))
         {
            return;
         }
         this._npc = new SaveNPC("MC_STORY_NPC_BAIHUAXIU_CLIP",100,200);
         this._npc.x = 560;
         this._npc.y = 970;
         add(this._npc);
         this._npc.mcNPC.addEventListener(UIEvent.NPC_EVENT,this.onNPC,false,0,true);
      }
      
      private function layoutTieGai() : void
      {
         if(_mission.hasMark("isOpen"))
         {
            return;
         }
         this._jiguan = new TrapTieGai("PIC_HEI_SONG_LIN_TIE_GAI");
         this._jiguan.x = 1300;
         this._jiguan.y = 522;
         add(this._jiguan);
      }
      
      private function initEnemyTop() : void
      {
         if(_mission.hasMark("enemy8031"))
         {
            return;
         }
         _mission.setMark("enemy8031");
         this._eTrigger0 = new EnemyTrigger(_mission,[[46,251,150,100],[936,326,150,100]],8031,null);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(750,470),-1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger0.start();
      }
      
      private function initEnemyDown() : void
      {
         this._eTrigger1 = new EnemyTrigger(_mission,[[338,766,150,100],[730,654,150,100],[1055,874,150,100]],8032,null);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(930,1065),-1);
         this._pTrigger1.disY = 50;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger1.okFunc = this.addBOSS;
         this._pTrigger1.start();
      }
      
      private function onNPC(param1:UIEvent) : void
      {
         var func0:Function = null;
         var e:UIEvent = param1;
         func0 = function():void
         {
            showDialog("STORY0037",saveEnd);
         };
         Layers.lockGame = true;
         this._npc.changeAnimation("open",false);
         this._npc.openFunc = func0;
      }
      
      private function saveEnd() : void
      {
         var showPrompt:Function = null;
         showPrompt = function():void
         {
            FlagProxy.instance().setValue(106,1);
            TaskProxy.instance().setComplete(11008);
            TaskProxy.instance().addTask(11009);
            setHeroEnable(true);
            Layers.lockGame = false;
            EffectManager.instance().addScreenEffect(16777215);
            PromptMediator.instance().showPrompt(TxtUtil.setColor("百花羞的【店铺】","99ff00") + "功能已经开放！<br>（花果山找百花羞）",null,null,true,"NEW_FUNCTION");
            PKGManager.saveWithTip();
         };
         this._npc.disappear();
         setTimeout(showPrompt,1000);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHuangFengGuai();
         _boss.x = 370;
         _boss.y = 1021;
         _boss.initData(30042,30042,28);
         _boss.target = _mission.curTarget;
         add(_boss);
         _dropRect = new Rectangle(282,933,370,140);
         startBossBattle("BGM_BATTLE1");
      }
      
      override protected function handlerWin() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
         _boss.kill = true;
         EffectManager.instance().playBGM("BGM_SUNSHINE");
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         this._eTrigger0 = null;
         this._pTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger1 = null;
         this._jiguan = null;
         this._npc = null;
         super.destroy();
      }
   }
}

