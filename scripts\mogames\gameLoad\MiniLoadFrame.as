package mogames.gameLoad
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import mogames.AssetManager;
   import mogames.Layers;
   import utils.MethodUtil;
   
   public class MiniLoadFrame
   {
      private static var _instance:MiniLoadFrame;
      
      private var _lock:Sprite;
      
      private var _panel:MovieClip;
      
      public function MiniLoadFrame()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this._lock = MethodUtil.drawRect(Layers.gameW,Layers.gameH,0.5);
         this._lock.cacheAsBitmap = true;
         Layers.topLayer.addChild(this._lock);
         this._panel = AssetManager.newMCRes("UI_MINI_LOAD_PANEL");
         this._panel.x = (Layers.gameW - this._panel.width) * 0.5;
         this._panel.y = (Layers.gameH - this._panel.height) * 0.5;
         Layers.topLayer.addChild(this._panel);
         MethodUtil.setMousable(this._panel,false);
      }
      
      public static function instance() : MiniLoadFrame
      {
         if(!_instance)
         {
            _instance = new MiniLoadFrame();
         }
         return _instance;
      }
      
      public static function clean() : void
      {
         if(!_instance)
         {
            return;
         }
         _instance.destroy();
         _instance = null;
      }
      
      public function update(param1:int) : void
      {
         this._panel.txt.text = param1 + "%";
         this._panel.gotoAndStop(param1);
      }
      
      private function destroy() : void
      {
         MethodUtil.removeMe(this._lock);
         MethodUtil.removeMe(this._panel);
         this._lock = null;
         this._panel = null;
      }
   }
}

