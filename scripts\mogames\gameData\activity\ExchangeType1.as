package mogames.gameData.activity
{
   import com.adobe.crypto.MD5;
   import flash.net.URLRequest;
   import flash.net.URLRequestMethod;
   import flash.net.URLVariables;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.MsgMediator;
   
   public class ExchangeType1 extends BaseExchange
   {
      public function ExchangeType1(param1:Function)
      {
         super(param1);
         _req = new URLRequest("http://my.4399.com/credit/sn-use");
         KEY = "e3dce39101a6c96946359dff13c3967b";
      }
      
      override public function startExchange(... rest) : void
      {
         MsgMediator.instance().show("处理数据中");
         var _loc2_:URLVariables = new URLVariables();
         _loc2_.uid = _uid;
         _loc2_.product_id = rest[0];
         _loc2_.activation = rest[1];
         _loc2_.self_use = 0;
         _loc2_.time = int(new Date().getTime() * 0.001);
         _loc2_.app_id = 56;
         var _loc3_:String = _loc2_.activation + "||" + _loc2_.app_id + "||" + _loc2_.product_id + "||" + _loc2_.self_use + "||" + _loc2_.time + "||" + _loc2_.uid + "||" + "/credit/sn-use" + "||" + KEY;
         _loc2_.token = MD5.hash(_loc3_);
         _req.data = _loc2_;
         _req.method = URLRequestMethod.POST;
         _loader.load(_req);
      }
      
      override protected function onLoadBack(param1:Object) : void
      {
         super.onLoadBack(param1);
         param1 = JSON.parse(String(param1));
         if(Boolean(param1.code) && int(param1.code) == 100)
         {
            _okFunc();
         }
         else if(param1.code)
         {
            switch(int(param1.code))
            {
               case 201:
                  MiniMsgMediator.instance().showAutoMsg("输入的激活码不存在！");
                  break;
               case 203:
                  MiniMsgMediator.instance().showAutoMsg("你已经使用过该激活码！");
                  break;
               case 204:
                  MiniMsgMediator.instance().showAutoMsg("该激活码已经过期！");
                  break;
               case 205:
                  MiniMsgMediator.instance().showAutoMsg("您使用此礼包的次数达到上限！");
                  break;
               case 206:
                  MiniMsgMediator.instance().showAutoMsg("您今天使用此礼包的次数达到上限！");
            }
         }
      }
   }
}

