package mogames.gameData.buff.role
{
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameData.buff.BuffVO;
   
   public class HurtBuff extends BaseBuff
   {
      protected var _hitVO:BaseHitVO;
      
      public function HurtBuff(param1:int)
      {
         super(param1,true,true);
         this._hitVO = new BaseHitVO();
         this._hitVO.setHurtXY(0,0);
         this._hitVO.WU_XING.v = 0;
         this._hitVO.atkType.v = 1;
         this._hitVO.isCrit = false;
         this._hitVO.isMiss = true;
      }
      
      override public function setBuffVO(param1:BuffVO) : void
      {
         super.setBuffVO(param1);
         this._hitVO.hurt.v = param1.argDic.hurt.v;
      }
      
      override protected function onBuff() : void
      {
         if(_roleVO.isDead())
         {
            return;
         }
         if(_role)
         {
            _role.countHurt(this._hitVO);
            if(_role.isDead)
            {
               _role.handlerDead(this._hitVO.dir);
            }
         }
         else
         {
            _roleVO.countHURT(this._hitVO);
         }
      }
      
      override public function destroy() : void
      {
         super.destroy();
         this._hitVO = null;
      }
   }
}

