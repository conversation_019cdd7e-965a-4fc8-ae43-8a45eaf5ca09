package mogames.gameMission.mission.shituoling
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.huoyundong.TrapHuoZhu;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.TrapJinJi;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BOSSBaiXiangJing;
   import mogames.gameRole.enemy.BOSSShiZiJing;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   
   public class SceneShiTuoLing01 extends BaseScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _huozhuArg:Object = {
         "hurt":new Sint(600),
         "keepTime":new Snum(2),
         "interval":new Snum(10)
      };
      
      private var _huozhus:Array;
      
      private var _curs:Array;
      
      private var _huozhuTimer0:CitrusTimer;
      
      private var _huozhuTimer1:CitrusTimer;
      
      private var _bossLion:BOSSShiZiJing;
      
      private var _bossXiang:BOSSBaiXiangJing;
      
      public function SceneShiTuoLing01(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER3");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2050,1);
            TaskProxy.instance().addTask(11032);
         };
         super.handlerInit();
         this.layoutEnemies();
         this.layoutJinJi();
         this.initHuoZhu();
         if(!FlagProxy.instance().isComplete(2050))
         {
            showDialog("STORY0077",func);
         }
      }
      
      private function layoutJinJi() : void
      {
         var _loc2_:TrapJinJi = null;
         var _loc1_:int = 0;
         while(_loc1_ < 3)
         {
            _loc2_ = new TrapJinJi("MC_TRAP_JIN_JI03",150,65);
            Layers.addCEChild(_loc2_);
            _loc2_.x = 970 + _loc1_ * 413;
            _loc2_.y = 466;
            _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",2,2);
            _loc2_.createHitHurt(1000,1,false);
            _loc1_++;
         }
      }
      
      private function initHuoZhu() : void
      {
         var _loc2_:TrapHuoZhu = null;
         this._huozhus = [];
         this._curs = [];
         var _loc1_:int = 0;
         while(_loc1_ < 4)
         {
            _loc2_ = new TrapHuoZhu();
            _loc2_.initArg(2866 + _loc1_ * 275,536,150);
            _loc2_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",5,5);
            _loc2_.createHitHurt(this._huozhuArg.hurt.v,0,false);
            add(_loc2_);
            this._huozhus[_loc1_] = _loc2_;
            _loc1_++;
         }
         this._huozhuTimer0 = new CitrusTimer();
         this._huozhuTimer1 = new CitrusTimer();
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v,this.showHuoZhu);
      }
      
      private function showHuoZhu() : void
      {
         var _loc3_:int = 0;
         var _loc5_:TrapHuoZhu = null;
         this._curs = [];
         var _loc1_:int = Math.random() * 3 + 2;
         var _loc2_:Array = this._huozhus.slice();
         var _loc4_:int = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = Math.random() * _loc2_.length;
            this._curs[_loc4_] = _loc2_[_loc3_];
            _loc2_.splice(_loc3_,1);
            _loc4_++;
         }
         for each(_loc5_ in this._curs)
         {
            _loc5_.startMove();
         }
         this._huozhuTimer1.setTimeOut(this._huozhuArg.keepTime.v,this.resumeHuoZhu);
      }
      
      private function resumeHuoZhu() : void
      {
         var _loc1_:TrapHuoZhu = null;
         for each(_loc1_ in this._curs)
         {
            _loc1_.resume();
         }
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v,this.showHuoZhu);
      }
      
      private function layoutEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[163,244,150,100],[630,244,150,100]],24011,this.handlerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[2109,244,150,100],[2575,244,150,100]],24012,this.handlerEnd1);
         this._eTrigger2 = new EnemyTrigger(_mission,[[4052,244,150,100],[4518,244,150,100]],24013,this.handlerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(476,412),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1308,412),1,new Rectangle(960,0,960,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2422,412),1,new Rectangle(1920,0,960,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(3290,412),1,new Rectangle(2880,0,960,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(4365,412),1,new Rectangle(3840,0,960,600));
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger2.trigger = this._eTrigger1;
         this._pTrigger4.trigger = this._eTrigger2;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this.activeLion;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this.activeXiang;
         this._pTrigger0.start();
      }
      
      private function handlerEnd0() : void
      {
         this.handlerEnd();
         this._bossLion = new BOSSShiZiJing();
         this._bossLion.x = 1689;
         this._bossLion.y = 209;
         this._bossLion.initData(30391,30391,128);
         this._bossLion.target = _mission.onePlayer;
         add(this._bossLion);
      }
      
      private function activeLion() : void
      {
         setHeroEnable(true);
         this._bossLion.activeEnemy(false);
         this._bossLion.aiEnabled = true;
         this._bossLion.onRole.add(this.listenLion);
         EffectManager.instance().playBGM("BGM_BATTLE1");
      }
      
      protected function listenLion(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._bossLion,
            "rect":new Rectangle(1146,224,500,100),
            "hero":this._bossLion.target
         });
         unlock();
         EffectManager.instance().addGOEffect(3);
         this._pTrigger2.start();
         EffectManager.instance().playBGM("BGM_DANGER4");
      }
      
      private function handlerEnd1() : void
      {
         this.handlerEnd();
         this._bossXiang = new BOSSBaiXiangJing();
         this._bossXiang.x = 3600;
         this._bossXiang.y = 209;
         this._bossXiang.initData(30401,30401,129);
         this._bossXiang.target = _mission.onePlayer;
         add(this._bossXiang);
      }
      
      private function activeXiang() : void
      {
         setHeroEnable(true);
         this._bossXiang.activeEnemy(false);
         this._bossXiang.aiEnabled = true;
         this._bossXiang.onRole.add(this.listenXiang);
         EffectManager.instance().playBGM("BGM_BATTLE1");
      }
      
      protected function listenXiang(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._bossXiang,
            "rect":new Rectangle(3033,224,500,100),
            "hero":this._bossXiang.target
         });
         unlock();
         EffectManager.instance().addGOEffect(3);
         this._pTrigger4.start();
         EffectManager.instance().playBGM("BGM_DANGER4");
      }
      
      private function handlerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      override public function destroy() : void
      {
         this._bossLion = null;
         this._curs = null;
         this._huozhus = null;
         this._huozhuTimer0.destroy();
         this._huozhuTimer1.destroy();
         this._huozhuTimer0 = null;
         this._huozhuTimer1 = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         super.destroy();
      }
   }
}

