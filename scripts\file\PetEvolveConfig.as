package file
{
   import mogames.gameData.forge.NeedVO;
   import mogames.gameData.pet.vo.PetEvolveCostVO;
   
   public class PetEvolveConfig
   {
      private static var _instance:PetEvolveConfig;
      
      private var _list:Vector.<PetEvolveCostVO>;
      
      public function PetEvolveConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : PetEvolveConfig
      {
         if(!_instance)
         {
            _instance = new PetEvolveConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<PetEvolveCostVO>();
         this._list.push(new PetEvolveCostVO(1101,0,50000,[new NeedVO(16761,20)]));
         this._list.push(new PetEvolveCostVO(1102,0,50000,[new NeedVO(16761,20)]));
         this._list.push(new PetEvolveCostVO(1103,0,50000,[new NeedVO(16761,20)]));
         this._list.push(new PetEvolveCostVO(1104,0,50000,[new NeedVO(16761,20)]));
         this._list.push(new PetEvolveCostVO(1106,0,100000,[new NeedVO(16763,20)]));
         this._list.push(new PetEvolveCostVO(1107,0,100000,[new NeedVO(16763,20)]));
         this._list.push(new PetEvolveCostVO(1108,0,100000,[new NeedVO(16763,20)]));
         this._list.push(new PetEvolveCostVO(1101,1,100000,[new NeedVO(16761,20),new NeedVO(16763,10)]));
         this._list.push(new PetEvolveCostVO(1102,1,100000,[new NeedVO(16761,20),new NeedVO(16763,10)]));
         this._list.push(new PetEvolveCostVO(1103,1,100000,[new NeedVO(16761,20),new NeedVO(16763,10)]));
         this._list.push(new PetEvolveCostVO(1104,1,100000,[new NeedVO(16761,20),new NeedVO(16763,10)]));
         this._list.push(new PetEvolveCostVO(1106,1,200000,[new NeedVO(16764,20),new NeedVO(16763,20)]));
         this._list.push(new PetEvolveCostVO(1107,1,200000,[new NeedVO(16765,20),new NeedVO(16763,20)]));
         this._list.push(new PetEvolveCostVO(1108,1,200000,[new NeedVO(16766,20),new NeedVO(16763,20)]));
      }
      
      public function findCost(param1:int, param2:int) : PetEvolveCostVO
      {
         var _loc4_:PetEvolveCostVO = null;
         var _loc3_:Array = this.findPet(param1);
         for each(_loc4_ in _loc3_)
         {
            if(_loc4_.evolve.v == param2)
            {
               return _loc4_;
            }
         }
         return null;
      }
      
      private function findPet(param1:int) : Array
      {
         var _loc3_:PetEvolveCostVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in this._list)
         {
            if(_loc3_.petID.v == param1)
            {
               _loc2_.push(_loc3_);
            }
         }
         return _loc2_;
      }
   }
}

