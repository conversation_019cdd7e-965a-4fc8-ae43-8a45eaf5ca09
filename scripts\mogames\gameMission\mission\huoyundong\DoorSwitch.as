package mogames.gameMission.mission.huoyundong
{
   import citrus.objects.CitrusSprite;
   import citrus.view.ICitrusArt;
   import flash.display.MovieClip;
   import mogames.Layers;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.co.HurtEffect;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   
   public class DoorSwitch extends CitrusSprite implements IRole
   {
      private var _role:CitrusMC;
      
      private var _atkFunc:Function;
      
      private var _hurtEffect:HurtEffect;
      
      public function DoorSwitch(param1:String, param2:int, param3:int, param4:Function)
      {
         this._atkFunc = param4;
         super("huashen",{
            "width":param2,
            "height":param3,
            "group":2
         });
         touchable = true;
         this.createView(param1);
         this._hurtEffect = new HurtEffect();
      }
      
      private function createView(param1:String) : void
      {
         this._role = new CitrusMC();
         this._role.setupMC(GameInLoader.instance().findAsset(param1));
         this.changeAnimation("idle",true);
         this.view = this._role;
      }
      
      override public function handleArtReady(param1:ICitrusArt) : void
      {
         super.handleArtReady(param1);
         BattleMediator.instance().onHeroATK.add(this.listenHurt);
      }
      
      protected function listenHurt(param1:BaseHitVO) : void
      {
         if(param1.atkType.v == 1)
         {
            return;
         }
         if(Boolean(param1.source) && param1.source.hasTargets(this))
         {
            return;
         }
         if(!this.mcHurt || !this.mcHurt.hitTestObject(param1.range))
         {
            return;
         }
         if(param1.source)
         {
            param1.source.addTargets(this);
         }
         this.addHurtEffect(param1);
         this._atkFunc();
      }
      
      private function addHurtEffect(param1:BaseHitVO) : void
      {
         EffectManager.instance().addBMCEffect(param1.hurtSkin,Layers.ceEffectLayer,x + width * 0.5,y + height * 0.5);
         EffectManager.instance().playAudio("GUNHURT");
         this.changeAnimation("active",false);
         this._hurtEffect.setupTarget(this._role);
      }
      
      public function changeAnimation(param1:String, param2:Boolean = true) : void
      {
         this._role.changeAnimation(param1,param2);
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return false;
      }
      
      public function addTargets(param1:IRole) : void
      {
      }
      
      public function cleanTargets() : void
      {
      }
      
      public function hitBack() : void
      {
      }
      
      public function get mcHurt() : MovieClip
      {
         return this._role.mc.mcAreaHurt;
      }
      
      override public function destroy() : void
      {
         BattleMediator.instance().onHeroATK.remove(this.listenHurt);
         this._atkFunc = null;
         this._hurtEffect.destroy();
         this._hurtEffect = null;
         this._role.destroy();
         this._role = null;
         super.destroy();
      }
   }
}

