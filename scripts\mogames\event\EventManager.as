package mogames.event
{
   import flash.events.EventDispatcher;
   
   public class EventManager
   {
      private static var _dispatcher:EventDispatcher = new EventDispatcher();
      
      public function EventManager()
      {
         super();
      }
      
      public static function addEventListener(param1:String, param2:Function, param3:<PERSON><PERSON>an = false, param4:int = 0, param5:<PERSON><PERSON>an = false) : void
      {
         _dispatcher.addEventListener(param1,param2,param3,param4,param5);
      }
      
      public static function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         _dispatcher.removeEventListener(param1,param2,param3);
      }
      
      public static function dispatchEvent(param1:String, param2:* = null) : void
      {
         _dispatcher.dispatchEvent(new UIEvent(param1,param2));
      }
   }
}

