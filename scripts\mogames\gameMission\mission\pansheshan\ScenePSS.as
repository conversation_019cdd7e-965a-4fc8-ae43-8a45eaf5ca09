package mogames.gameMission.mission.pansheshan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.dialog.DialogVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.TrapNiTan;
   import mogames.gameRole.enemy.BOSSXiaoBaiLong;
   import mogames.gameUI.dialog.DialogMediator;
   import mogames.gameUI.dialog.StoryDialogModule;
   import mogames.gameUI.fuben.TZJS.TZJSEnterModule;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.TxtUtil;
   
   public class ScenePSS extends BossScene
   {
      private var _bossTrigger:LocalTriggerX;
      
      public function ScenePSS(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [TrapNiTan];
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_SUNSHINE");
         setWinPosition(new Rectangle(454,188,614,156),new Point(695,395));
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this._bossTrigger = new LocalTriggerX(_mission.onePlayer,new Point(860,420),1);
         this._bossTrigger.okFunc = this.showStory;
         this._bossTrigger.start();
      }
      
      private function showStory() : void
      {
         if(TZJSEnterModule.TZJS_DIFF.v == 0)
         {
            StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(600,87),"何人扰本太子清梦！","BODY_LONG_MA0",this.showXiaoBaiLong,3);
         }
         else
         {
            this.showXiaoBaiLong();
         }
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      private function showXiaoBaiLong() : void
      {
         _boss = new BOSSXiaoBaiLong();
         _boss.x = 1371;
         _boss.y = 215;
         this.checkDiff();
         _boss.target = _mission.curTarget;
         add(_boss);
         _boss.setVelocityX(-10);
         _boss.setVelocityY(-10);
         _boss.forceJump();
         startBossBattle();
      }
      
      private function checkDiff() : void
      {
         var _loc1_:int = TZJSEnterModule.TZJS_DIFF.v;
         if(_loc1_ == 0)
         {
            _boss.initData(30121,30121);
         }
         else if(_loc1_ == 1)
         {
            _boss.initData(30122,30122,19);
         }
         else if(_loc1_ == 2)
         {
            _boss.initData(30123,30123,20);
         }
         else if(_loc1_ == 3)
         {
            _boss.initData(30124,30124,21);
         }
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         _mission.missionVO.setComplete();
         MissionManager.instance().handlerWin(false);
      }
      
      override protected function handlerWin() : void
      {
         Layers.lockGame = false;
         if(HeroProxy.instance().horseVO == null && TZJSEnterModule.TZJS_DIFF.v == 0)
         {
            this.startZhaoXiang();
         }
         else
         {
            this.showWinDrop();
         }
      }
      
      private function startZhaoXiang() : void
      {
         var okFunc:Function = null;
         okFunc = function():void
         {
            var _loc1_:Vector.<DialogVO> = new Vector.<DialogVO>();
            _loc1_.push(new DialogVO(1,"小白龙","BODY_LONG_MA4","啊，原来是大唐高僧，我在这等候多时了！我愿与你们一起前往西天取经！"));
            DialogMediator.instance().init(_loc1_,winDialogEnd);
            HeroProxy.isNewHero = true;
         };
         PromptMediator.instance().showPrompt("小白龙被打成虚弱状态，是否降服？\n（成功后成为队友）",okFunc,this.showWinDrop);
      }
      
      private function winDialogEnd() : void
      {
         HeroProxy.instance().unlockHero(1002);
         HeroProxy.instance().horseVO.setLevel(5);
         FlagProxy.instance().setValue(100200,5);
         _boss.visible = false;
         EffectManager.instance().addScreenEffect(16777215);
         PromptMediator.instance().showPrompt("【小白龙】已加入队伍！<br>" + TxtUtil.setColor("（可在【队伍】界面中让其出战）","99ff00"),null,null,true,"NEW_FUNCTION");
         this.showWinDrop();
      }
      
      private function loseDialogEnd() : void
      {
         MiniMsgMediator.instance().showAutoMsg("降服失败！",480,300,"ZHAOMU_FAIL");
         this.showWinDrop();
      }
      
      private function showWinDrop() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         this._bossTrigger.destroy();
         this._bossTrigger = null;
         super.destroy();
      }
   }
}

