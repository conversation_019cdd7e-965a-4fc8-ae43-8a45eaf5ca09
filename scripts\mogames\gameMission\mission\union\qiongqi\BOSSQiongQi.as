package mogames.gameMission.mission.union.qiongqi
{
   import mogames.Layers;
   import mogames.gameData.ConstRole;
   import mogames.gameData.role.EnemyGameVO;
   import mogames.gameMission.BattleMediator;
   import mogames.gameRole.enemy.BaseBOSS;
   import mogames.gameSystem.SysTimer;
   import utils.MathUtil;
   
   public class BOSSQiongQi extends BaseBOSS
   {
      private var _twoTimer:SysTimer;
      
      private var _fiveTimer:SysTimer;
      
      public function BOSSQiongQi()
      {
         _roleVO = new EnemyGameVO(4020,1);
         super(_roleVO.assetVO.name,{
            "width":_roleVO.constVO.width,
            "height":_roleVO.constVO.height,
            "group":3
         });
         createView(new BossQiongQiView());
         this._twoTimer = new SysTimer();
         this._fiveTimer = new SysTimer();
      }
      
      public function dispatchSkillOne() : void
      {
         _hitVO.init(_roleView.mcATK,_baseVO.argDic.arg0.v,1,checkCrit());
         _hitVO.setHurtXY(2,2);
         BattleMediator.instance().onEnemyATK.dispatch(_hitVO);
      }
      
      public function startSkillTwo() : void
      {
         var func:Function = null;
         func = function():void
         {
            _roleView.skillType = "skilltwo_end";
         };
         setMove(false);
         _roleView.skillType = "skilltwo_loop";
         this._twoTimer.setTimeOut(2,func);
      }
      
      public function dispatchSkillTwo() : void
      {
         _hitVO.init(_roleView.mcATK,_baseVO.argDic.arg1.v,1,checkCrit());
         _hitVO.setHurtXY(2,2);
         _hitVO.setRigor(1,ConstRole.BUFF_STONE);
         BattleMediator.instance().onEnemyATK.dispatch(_hitVO);
      }
      
      public function handlerSkillThree() : void
      {
         var _loc1_:TrapQQHeiLongJuan = null;
         _loc1_ = new TrapQQHeiLongJuan();
         Layers.addCEChild(_loc1_);
         _loc1_.createHitEffect(_roleVO.assetVO.hitSkin,_roleVO.assetVO.hitSound,2,2);
         _loc1_.createHitHurt(_baseVO.argDic.arg2.v,_trickVO.hurtTime.v,checkCrit(),1,_roleVO.constVO.wuxing.v);
         _loc1_.initTimer(3);
         _loc1_.x = x + dirX * 340;
         _loc1_.y = y + height * 0.5;
      }
      
      public function dispatchSkillFour() : void
      {
         _hitVO.init(_roleView.mcATK,_baseVO.argDic.arg3.v,1,checkCrit());
         _hitVO.setHurtXY(10,10);
         BattleMediator.instance().onEnemyATK.dispatch(_hitVO);
      }
      
      public function handlerSkillFive() : void
      {
         this._fiveTimer.setInterval(1,5,this.addHeiDong);
      }
      
      private function addHeiDong() : void
      {
         var _loc1_:TrapQQHeiDong = null;
         _loc1_ = new TrapQQHeiDong();
         Layers.addCEChild(_loc1_);
         _loc1_.createHitEffect(_roleVO.assetVO.hitSkin,_roleVO.assetVO.hitSound,2,2);
         _loc1_.createHitHurt(_baseVO.argDic.arg4.v,_trickVO.hurtTime.v,checkCrit(),1,_roleVO.constVO.wuxing.v);
         _loc1_.x = MathUtil.randomNum(200,1200);
         _loc1_.y = MathUtil.randomNum(200,500);
      }
      
      override public function destroy() : void
      {
         super.destroy();
         this._twoTimer.destroy();
         this._twoTimer = null;
         this._fiveTimer.destroy();
         this._fiveTimer = null;
      }
   }
}

