package mogames.gameMission.mission.kunlun
{
   import citrus.objects.CitrusSprite;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   import mogames.gameSystem.SysTimer;
   
   public class TrapLuWuBingMian extends CitrusSprite implements IRole
   {
      private var _mc:CitrusMC;
      
      private var _hitTargets:Array = [];
      
      private var _hitVO:BaseHitVO;
      
      private var _timer:SysTimer;
      
      public function TrapLuWuBingMian()
      {
         super("TrapLuWuBingMian",{
            "width":220,
            "height":200,
            "group":2
         });
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("TRAP_LU_WU_BING_MIAN"),this.onComplete,this.updateFrame);
         this._mc.changeAnimation("stand",false);
         this.view = this._mc;
         this._timer = new SysTimer();
      }
      
      public function initTime(param1:Number) : void
      {
         var func:Function = null;
         var $time:Number = param1;
         func = function():void
         {
            kill = true;
         };
         this._timer.setTimeOut($time,func);
      }
      
      public function createHitEffect(param1:String, param2:String, param3:int, param4:int) : void
      {
         this._hitVO.hurtSkin = param1;
         this._hitVO.hurtSound = param2;
         this._hitVO.hurtX = param3;
         this._hitVO.hurtY = param4;
      }
      
      public function createHitHurt(param1:int, param2:Number, param3:Boolean, param4:int = 1, param5:int = 0) : void
      {
         this._hitVO.hurt.v = param1;
         this._hitVO.hurtTime.v = param2;
         this._hitVO.atkType.v = param4;
         this._hitVO.WU_XING.v = param5;
      }
      
      public function createOtherBuff(param1:Array) : void
      {
         this._hitVO.buffList = param1;
      }
      
      private function updateFrame(param1:int) : void
      {
         if(!this._mc.mc.mcAreaATK)
         {
            return;
         }
         this._hitVO.range = this._mc.mc.mcAreaATK;
         BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
      }
      
      private function onComplete(param1:String) : void
      {
         if(param1 == "stand")
         {
            this._mc.changeAnimation("attack",true);
         }
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return this._hitTargets.indexOf(param1) != -1;
      }
      
      public function addTargets(param1:IRole) : void
      {
         this._hitTargets[this._hitTargets.length] = param1;
      }
      
      public function cleanTargets() : void
      {
         this._hitTargets.length = 0;
      }
      
      public function hitBack() : void
      {
      }
      
      override public function destroy() : void
      {
         this._hitTargets = null;
         this._mc.destroy();
         this._mc = null;
         this._hitVO = null;
         super.destroy();
      }
   }
}

