package file
{
   import mogames.gameData.fuben.TZQS.TZQSVO;
   
   public class TZQSConfig
   {
      private static var _instance:TZQSConfig;
      
      public var list:Array;
      
      public function TZQSConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : TZQSConfig
      {
         if(!_instance)
         {
            _instance = new TZQSConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.list = [];
         this.list[0] = new TZQSVO(0,500,0,[10000,70991,16121,16757,14000]);
         this.list[1] = new TZQSVO(1,501,500,[10000,70992,16122,16755,14000]);
         this.list[2] = new TZQSVO(2,502,501,[10000,70993,16123,16756,14000]);
         this.list[3] = new TZQSVO(3,503,502,[10000,70994,16124,16759,14000]);
         this.list[4] = new TZQSVO(4,504,503,[10000,70995,16125,16760,14000]);
         this.list[5] = new TZQSVO(5,505,504,[10000,70996,16126,16762,14000]);
         this.list[6] = new TZQSVO(6,506,505,[10000,70997,16127,16752,14000]);
      }
   }
}

