package mogames.gameMission.mission.huoyundong
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameRole.enemy.BOSSHongHaiEr;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   
   public class SceneHuoYunDong1103 extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _tangseng:CitrusMCSprite;
      
      private var _arr0:Array = [368,642,916,1191,1465,1740];
      
      private var _arr1:Array = [378,657,932,1208,1481,1758];
      
      private var _huozhus:Array;
      
      private var _curs:Array;
      
      private var _tielianArg:Object = {
         "hurt":new Sint(100),
         "keepTime":new Snum(5)
      };
      
      private var _huozhuArg:Object = {
         "hurt":new Sint(180),
         "keepTime":new Snum(2),
         "interval":new Snum(15)
      };
      
      private var _huozhuTimer0:CitrusTimer;
      
      private var _huozhuTimer1:CitrusTimer;
      
      private var _enemyArgs:Array = [{
         "id":new Sint(2005),
         "attID":new Sint(20054),
         "aiID":new Sint(20053),
         "dropID":new Sint(47)
      },{
         "id":new Sint(2005),
         "attID":new Sint(20054),
         "aiID":new Sint(20053),
         "dropID":new Sint(47)
      },{
         "id":new Sint(2012),
         "attID":new Sint(20122),
         "aiID":new Sint(20122),
         "dropID":new Sint(47)
      }];
      
      public function SceneHuoYunDong1103(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(1210,305,435,116),new Point(1436,420));
         EffectManager.instance().playBGM("BGM_DANGER0");
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.initEnemies();
         this.initHuoZhu();
         this.initTangseng();
         this.addBOSS();
      }
      
      private function initTangseng() : void
      {
         this._tangseng = new CitrusMCSprite("MC_TANG_SENG_DIAO_CLIP",{
            "width":120,
            "height":416,
            "x":1700,
            "y":-220,
            "group":2
         });
         this._tangseng.changeAnimation("afraid");
         add(this._tangseng);
      }
      
      private function initHuoZhu() : void
      {
         var _loc1_:int = 0;
         var _loc2_:TrapTieLian = null;
         var _loc3_:TrapHuoZhu = null;
         this._huozhus = [];
         this._curs = [];
         _loc1_ = 0;
         while(_loc1_ < 6)
         {
            _loc2_ = new TrapTieLian();
            _loc2_.x = this._arr0[_loc1_];
            _loc2_.y = 501;
            _loc2_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",2,5);
            _loc2_.createHitHurt(this._tielianArg.hurt.v,0,false);
            _loc3_ = new TrapHuoZhu();
            _loc3_.initArg(this._arr1[_loc1_],536,350);
            _loc3_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",5,5);
            _loc3_.createHitHurt(this._huozhuArg.hurt.v,0,false);
            _loc3_.relateTrap = _loc2_;
            add(_loc2_);
            add(_loc3_);
            this._huozhus[_loc1_] = _loc3_;
            _loc1_++;
         }
         this._huozhuTimer0 = new CitrusTimer();
         this._huozhuTimer1 = new CitrusTimer();
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v,this.showHuoZhu);
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[10,322,150,100],[250,322,150,100]],11031,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[538,322,150,100],[892,322,150,100]],11032,this.triggerEnd1);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(460,456),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(840,456),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1414,456),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this.activeBoss;
         this._pTrigger0.start();
      }
      
      private function triggerEnd0() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
         CitrusLock.instance().lock(new Rectangle(0,0,1210,600),false,false,false,true);
      }
      
      private function triggerEnd1() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHongHaiEr();
         _boss.x = 1700;
         _boss.y = 340;
         _boss.initData(30152,30152,49);
         _boss.target = _mission.onePlayer;
         (_boss as BOSSHongHaiEr).enemyList = this._enemyArgs.slice();
         add(_boss);
      }
      
      private function activeBoss() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2024,1);
            startBossBattle("BGM_BATTLE1");
         };
         CitrusLock.instance().lock(new Rectangle(374,0,1538,600),false,false,true,false);
         if(!FlagProxy.instance().isComplete(2024))
         {
            showDialog("STORY0049",func);
         }
         else
         {
            startBossBattle("BGM_BATTLE1");
         }
      }
      
      private function showHuoZhu() : void
      {
         var _loc3_:int = 0;
         var _loc5_:TrapHuoZhu = null;
         this._curs = [];
         var _loc1_:int = Math.random() * 6 + 1;
         var _loc2_:Array = this._huozhus.slice();
         var _loc4_:int = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = Math.random() * _loc2_.length;
            this._curs[_loc4_] = _loc2_[_loc3_];
            _loc2_.splice(_loc3_,1);
            _loc4_++;
         }
         for each(_loc5_ in this._curs)
         {
            _loc5_.startMove();
         }
         this._huozhuTimer1.setTimeOut(this._huozhuArg.keepTime.v,this.resumeHuoZhu);
      }
      
      private function resumeHuoZhu() : void
      {
         var _loc1_:TrapHuoZhu = null;
         for each(_loc1_ in this._curs)
         {
            _loc1_.resume();
            _loc1_.relateTrap.showRed(this._tielianArg.keepTime.v);
         }
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v + this._tielianArg.keepTime.v,this.showHuoZhu);
      }
      
      override protected function handlerWin() : void
      {
         this._tangseng.kill = true;
         this.destroyHuoZhu();
         TaskProxy.instance().setComplete(11014);
         super.handlerWin();
      }
      
      private function destroyHuoZhu() : void
      {
         var _loc1_:TrapHuoZhu = null;
         this._huozhuTimer0.pause();
         this._huozhuTimer1.pause();
         for each(_loc1_ in this._huozhus)
         {
            _loc1_.resume();
            _loc1_.relateTrap.resume();
         }
      }
      
      override public function destroy() : void
      {
         this._curs = null;
         this._huozhus = null;
         this._huozhuTimer0.destroy();
         this._huozhuTimer1.destroy();
         this._huozhuTimer0 = null;
         this._huozhuTimer1 = null;
         this._tangseng = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         super.destroy();
      }
   }
}

