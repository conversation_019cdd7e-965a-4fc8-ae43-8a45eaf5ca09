package mogames.gameData.flag
{
   import mogames.gameData.flag.fuben.CuiShuFlag;
   import mogames.gameData.flag.fuben.FBBaseFlag;
   import mogames.gameData.flag.fuben.FBExpFlag;
   import mogames.gameData.flag.fuben.FBHQLFlag;
   import mogames.gameData.flag.fuben.FBSTSFlag;
   import mogames.gameData.flag.fuben.FBTZJSFlag;
   import mogames.gameData.flag.fuben.FBTZQSFlag;
   import mogames.gameData.flag.fuben.FBTowerFlag;
   import mogames.gameData.flag.fuben.FBXunBaoFlag;
   import mogames.gameData.flag.fuben.ReviveFlag;
   import mogames.gameData.flag.fuben.TaskFlushFlag;
   import mogames.gameData.flag.fuben.TaskResetFlag;
   
   public class FBFlagProxy
   {
      private static var _instance:FBFlagProxy;
      
      private var _flags:Vector.<FBBaseFlag>;
      
      public function FBFlagProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : FBFlagProxy
      {
         if(!_instance)
         {
            _instance = new FBFlagProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._flags = null;
         this._flags = new Vector.<FBBaseFlag>();
         this._flags.push(new FBTZJSFlag(100,3));
         this._flags.push(new FBTZJSFlag(101,3));
         this._flags.push(new FBTZJSFlag(102,3));
         this._flags.push(new FBBaseFlag(103,3));
         this._flags.push(new FBTowerFlag(104,3));
         this._flags.push(new TaskFlushFlag(105,1));
         this._flags.push(new TaskResetFlag(106,1));
         this._flags.push(new CuiShuFlag(107,3,2));
         this._flags.push(new FBBaseFlag(108,3));
         this._flags.push(new ReviveFlag(109,20));
         this._flags.push(new FBExpFlag(110,1));
         this._flags.push(new FBXunBaoFlag(111,1));
         this._flags.push(new FBBaseFlag(112,10));
         this._flags.push(new FBHQLFlag(113,1));
         this._flags.push(new FBSTSFlag(114,1));
         this._flags.push(new FBTZQSFlag(115,2));
         this._flags.push(new FBBaseFlag(116,1,true));
         this._flags.push(new FBBaseFlag(117,1,true));
         this._flags.push(new FBBaseFlag(118,1,true));
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:Array = null;
         var _loc4_:FBBaseFlag = null;
         var _loc5_:* = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc5_ in _loc2_)
         {
            _loc3_ = _loc5_.split("H");
            _loc4_ = this.findFlag(int(_loc3_[0]));
            if(_loc4_)
            {
               _loc4_.loadData = _loc3_;
            }
         }
      }
      
      public function get saveData() : String
      {
         var _loc2_:FBBaseFlag = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._flags)
         {
            _loc1_.push(_loc2_.saveData);
         }
         return _loc1_.join("T");
      }
      
      public function dailyRefresh() : void
      {
         var _loc1_:FBBaseFlag = null;
         for each(_loc1_ in this._flags)
         {
            _loc1_.dailyRefresh();
         }
      }
      
      public function findFlag(param1:int) : FBBaseFlag
      {
         var _loc2_:FBBaseFlag = null;
         for each(_loc2_ in this._flags)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

