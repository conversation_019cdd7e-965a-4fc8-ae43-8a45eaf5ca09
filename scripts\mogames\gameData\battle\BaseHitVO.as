package mogames.gameData.battle
{
   import flash.display.DisplayObject;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameRole.co.IRole;
   
   public class BaseHitVO
   {
      public var HURT_SKIN:String = "";
      
      public var HURT_SOUND:String = "";
      
      public var HURT_X:Number = 1;
      
      public var HURT_Y:Number = 3;
      
      public var WU_XING:Sint = new Sint();
      
      public var LIE_TIME:Snum = new Snum(1);
      
      public var range:DisplayObject;
      
      public var source:IRole;
      
      public var atkType:Sint;
      
      public var isCrit:Boolean;
      
      public var isMiss:Boolean;
      
      public var isDEF:Boolean;
      
      public var isHurt:Boolean;
      
      public var hurt:Sint;
      
      public var countType:int;
      
      public var hurtType:int;
      
      public var hurtSkin:String;
      
      public var hurtSound:String;
      
      public var rigorTime:Snum;
      
      public var hurtTime:Snum;
      
      public var lieTime:Snum;
      
      public var rigorType:String;
      
      public var hurtX:Number;
      
      public var hurtY:Number;
      
      public var dir:int = 0;
      
      public var buffList:Array;
      
      public function BaseHitVO()
      {
         super();
         this.hurt = new Sint();
         this.atkType = new Sint();
         this.rigorTime = new Snum();
         this.hurtTime = new Snum();
         this.lieTime = new Snum(1);
         this.initData();
      }
      
      protected function initData() : void
      {
         this.hurtTime.v = 0.5;
         this.lieTime.v = this.LIE_TIME.v;
         this.rigorTime.v = 0;
         this.hurtType = 0;
         this.hurtX = this.HURT_X;
         this.hurtY = this.HURT_Y;
         this.hurtSkin = this.HURT_SKIN;
         this.hurtSound = this.HURT_SOUND;
         this.isMiss = true;
         this.isDEF = true;
         this.isHurt = true;
         this.buffList = null;
      }
      
      public function init(param1:DisplayObject, param2:int, param3:int, param4:Boolean = false, param5:Boolean = true) : void
      {
         this.initData();
         this.range = param1;
         this.hurt.v = param2;
         this.atkType.v = param3;
         this.setCrit(param4);
         this.isHurt = param5;
      }
      
      public function setCrit(param1:Boolean) : void
      {
         this.isCrit = param1;
         if(param1)
         {
            this.hurtType = 1;
         }
      }
      
      public function setHurtXY(param1:Number, param2:Number) : void
      {
         this.hurtX = param1;
         this.hurtY = param2;
      }
      
      public function setHurtLie(param1:Number) : void
      {
         this.hurtType = 1;
         this.lieTime.v = param1;
      }
      
      public function setRigor(param1:Number, param2:String) : void
      {
         this.rigorTime.v = param1;
         this.rigorType = param2;
      }
      
      public function isRigor() : Boolean
      {
         return this.rigorTime.v > 0;
      }
   }
}

