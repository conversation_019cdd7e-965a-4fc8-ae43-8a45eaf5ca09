package mogames.gameFabao.view
{
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoZhenBJS;
   
   public class FabaoZhenBJSView extends FabaoBaseView
   {
      private var _fabao:FabaoZhenBJS;
      
      public function FabaoZhenBJSView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         if(param1 == 40)
         {
            this._fabao.addXuanFeng();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as FabaoZhenBJS;
      }
   }
}

