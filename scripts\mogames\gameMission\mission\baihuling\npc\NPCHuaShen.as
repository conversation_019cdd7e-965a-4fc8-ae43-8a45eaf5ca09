package mogames.gameMission.mission.baihuling.npc
{
   import citrus.objects.CitrusSprite;
   import citrus.view.ICitrusArt;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import mogames.Layers;
   import mogames.gameData.ConstData;
   import mogames.gameData.ConstRole;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.co.HurtEffect;
   import mogames.gameFabao.fabao.FabaoZhaoYaoJing;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.FilterFactory;
   import utils.MethodUtil;
   
   public class NPCHuaShen extends CitrusSprite implements IRole
   {
      private var _totalHP:int;
      
      private var _hurtSound:String;
      
      private var _role:CitrusMC;
      
      private var _deadType:int;
      
      private var _deadFunc:Function;
      
      private var _saveFunc:Function;
      
      private var _hurtEffect:HurtEffect;
      
      public function NPCHuaShen(param1:int, param2:String, param3:String, param4:int, param5:int)
      {
         this._totalHP = param1;
         this._hurtSound = param2;
         super("huashen",{
            "width":param4,
            "height":param5,
            "group":2
         });
         touchable = true;
         this.createView(param3);
         this._hurtEffect = new HurtEffect();
      }
      
      public function setFunc(param1:Function, param2:Function) : void
      {
         this._saveFunc = param1;
         this._deadFunc = param2;
      }
      
      private function createView(param1:String) : void
      {
         this._role = new CitrusMC();
         this._role.setupMC(GameInLoader.instance().findAsset(param1),this.handlerComplete);
         this._role.buttonMode = true;
         this._role.addEventListener(MouseEvent.MOUSE_OVER,this.onOver,false,0,true);
         this._role.addEventListener(MouseEvent.MOUSE_OUT,this.onOut,false,0,true);
         this._role.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.changeAnimation("help",true);
         this.view = this._role;
      }
      
      override public function handleArtReady(param1:ICitrusArt) : void
      {
         super.handleArtReady(param1);
         BattleMediator.instance().onHeroATK.add(this.listenHurt);
      }
      
      protected function listenHurt(param1:BaseHitVO) : void
      {
         if(this.isDead)
         {
            return;
         }
         if(Boolean(param1.source) && param1.source.hasTargets(this))
         {
            return;
         }
         if(!this.mcHurt || !this.mcHurt.hitTestObject(param1.range))
         {
            return;
         }
         if(param1.source)
         {
            param1.source.addTargets(this);
         }
         if(param1.source is FabaoZhaoYaoJing)
         {
            this._deadType = 1;
            this._totalHP = 0;
         }
         else
         {
            this._totalHP -= param1.hurt.v;
         }
         this.addHurtEffect(param1);
         if(this.isDead)
         {
            this.handlerDead(param1.dir);
         }
      }
      
      private function addHurtEffect(param1:BaseHitVO) : void
      {
         EffectManager.instance().addBMCEffect(param1.hurtSkin,Layers.ceEffectLayer,x + width * 0.5,y + height * 0.5);
         EffectManager.instance().playAudio(param1.hurtSound);
         this.changeAnimation("hurt",false);
         this._hurtEffect.setupTarget(this._role);
         EffectManager.instance().playAudio(this._hurtSound);
      }
      
      private function addHurtNumber(param1:int, param2:int, param3:int) : void
      {
         if(!ConstData.openNum || !visible)
         {
            return;
         }
         EffectManager.instance().addBattleNum(param1,param2,x + width * 0.5,y,param3);
         if(param1 == 1)
         {
            EffectManager.instance().addPicPopWord("PIC_CRIT_WORD",x + width * 0.5,y);
         }
      }
      
      private function handlerDead(param1:int) : void
      {
         this.changeAnimation(ConstRole.DEAD,false);
         this._deadFunc(this._deadType);
      }
      
      public function changeAnimation(param1:String, param2:Boolean = true) : void
      {
         this._role.changeAnimation(param1,param2);
         if(param1 == "open")
         {
            EffectManager.instance().playAudio("OPEN_LOCK");
         }
      }
      
      protected function handlerComplete(param1:String) : void
      {
         if(param1 == "open" || param1 == "hurt")
         {
            this.changeAnimation("stand",true);
         }
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return false;
      }
      
      public function addTargets(param1:IRole) : void
      {
      }
      
      public function cleanTargets() : void
      {
      }
      
      public function hitBack() : void
      {
      }
      
      public function get mcHurt() : MovieClip
      {
         return this._role.mc.mcAreaHurt;
      }
      
      public function get isDead() : Boolean
      {
         return this._totalHP <= 0;
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         var _loc2_:CitrusMC = param1.target as CitrusMC;
         _loc2_.filters = [FilterFactory.getLightFilter()];
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         var _loc2_:CitrusMC = param1.target as CitrusMC;
         _loc2_.filters = [];
      }
      
      private function onDown(param1:MouseEvent) : void
      {
         if(Layers.hasEnemy)
         {
            MiniMsgMediator.instance().showAutoMsg("请先消灭场景里的所有怪物！");
            return;
         }
         this.changeAnimation("open",false);
         this._saveFunc();
         this._role.filters = [];
         MethodUtil.enableBtn(this._role,false,false);
      }
      
      override public function destroy() : void
      {
         BattleMediator.instance().onHeroATK.remove(this.listenHurt);
         this._saveFunc = null;
         this._deadFunc = null;
         this._hurtEffect.destroy();
         this._hurtEffect = null;
         this._role.removeEventListener(MouseEvent.MOUSE_OVER,this.onOver);
         this._role.removeEventListener(MouseEvent.MOUSE_OUT,this.onOut);
         this._role.removeEventListener(MouseEvent.CLICK,this.onDown);
         this._role.destroy();
         this._role = null;
         super.destroy();
      }
   }
}

