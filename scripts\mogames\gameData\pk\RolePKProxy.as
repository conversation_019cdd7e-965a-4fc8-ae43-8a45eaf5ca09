package mogames.gameData.pk
{
   import com.hexagonstar.util.debug.Debug;
   import file.PKRewardConfig;
   import mogames.gameData.base.SArray;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.TimeVO;
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.pk.vo.RolePKVO;
   import mogames.gameData.rank.RankRolePK;
   import mogames.gameData.rank.vo.RankRolePKVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gamePKG.GameProxy;
   import mogames.gamePKG.PKGManager;
   import utils.MathUtil;
   
   public class RolePKProxy
   {
      private static var _instance:RolePKProxy;
      
      public static var modeWins0:SArray = new SArray([7,8,9,10]);
      
      public static var modeWins1:SArray = new SArray([3,4,5,6]);
      
      public static var modeLoses0:SArray = new SArray([2,2,2,2]);
      
      public static var modeLoses1:SArray = new SArray([1,1,1,1]);
      
      public var rooms:Array;
      
      public var roles:Array;
      
      public var isReward:Sint;
      
      public var pkVO:RolePKVO;
      
      public var rankHandler:RankRolePK;
      
      public var pkHandler:PKDataHandler;
      
      public var activeVO:RankRolePKVO;
      
      public var activeType:int;
      
      public var isQieCuo:Boolean;
      
      public function RolePKProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : RolePKProxy
      {
         if(!_instance)
         {
            _instance = new RolePKProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.rooms = [];
         this.roles = [1001,1002,1003,1004,1005];
         this.isReward = new Sint();
         this.pkVO = new RolePKVO();
         this.pkHandler = new PKDataHandler();
         this.rankHandler = new RankRolePK();
      }
      
      public function get saveData() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = "none";
         _loc1_[1] = this.roles.join("H");
         _loc1_[2] = this.pkVO.saveData;
         _loc1_[3] = this.isReward.v;
         _loc1_[4] = this.collectRoom();
         return _loc1_.join("T");
      }
      
      public function set loadData(param1:String) : void
      {
         this.startNew();
         Debug.trace("竞技数据：" + param1);
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         if(_loc2_[0] != "none" && _loc2_.length >= 5)
         {
            return;
         }
         this.roles = MathUtil.arrStringToNum(_loc2_[1].split("H"));
         this.pkVO.loadData = _loc2_[2];
         this.isReward.v = int(_loc2_[3]);
         if(_loc2_[4])
         {
            this.rooms = this.parseRoom(_loc2_[4]);
         }
      }
      
      private function collectRoom() : String
      {
         var _loc2_:RankRolePKVO = null;
         if(!this.rooms || this.rooms.length <= 0)
         {
            return "none";
         }
         var _loc1_:Array = [];
         for each(_loc2_ in this.rooms)
         {
            if(_loc2_)
            {
               _loc1_.push(_loc2_.saveData);
            }
         }
         return _loc1_.join("HH");
      }
      
      private function parseRoom(param1:String) : Array
      {
         var _loc4_:* = null;
         var _loc5_:RankRolePKVO = null;
         if(!param1 || param1 == "none")
         {
            return [];
         }
         var _loc2_:Array = [];
         var _loc3_:Array = param1.split("HH");
         for each(_loc4_ in _loc3_)
         {
            _loc5_ = new RankRolePKVO();
            _loc5_.loadData = _loc4_;
            _loc2_.push(_loc5_);
         }
         return _loc2_;
      }
      
      public function daliyRefresh() : void
      {
         this.rooms = [];
         if(!this.isRewardTime)
         {
            this.isReward.v = 0;
         }
         this.pkVO.weekRefresh();
      }
      
      public function handlerWin() : void
      {
         if(!this.isQieCuo)
         {
            this.activeVO.pkFlags[this.activeType] = 1;
         }
         if(!this.isRewardTime && !this.isQieCuo)
         {
            if(this.activeVO.rank.v < this.rankHandler.findMyRank(0))
            {
               this.pkVO.score.v += modeWins0.findElement(this.activeType);
            }
            else
            {
               this.pkVO.score.v += modeWins1.findElement(this.activeType);
            }
            this.pkVO.addLastPKNum();
         }
         this.pkVO.setWin();
         PKGManager.saveWithTip();
      }
      
      public function handlerLose() : void
      {
         if(!this.isQieCuo)
         {
            this.activeVO.pkFlags[this.activeType] = 1;
         }
         if(!this.isRewardTime && !this.isQieCuo)
         {
            if(this.activeVO.rank.v < this.rankHandler.findMyRank(0))
            {
               this.pkVO.score.v += modeLoses0.findElement(this.activeType);
            }
            else
            {
               this.pkVO.score.v += modeLoses1.findElement(this.activeType);
            }
            this.pkVO.addLastPKNum();
         }
         this.pkVO.setLose();
         PKGManager.saveWithTip();
      }
      
      public function countResult(param1:Boolean) : int
      {
         if(this.isQieCuo)
         {
            return 0;
         }
         if(param1)
         {
            if(this.activeVO.rank.v < this.rankHandler.findMyRank(0))
            {
               return modeWins0.findElement(this.activeType);
            }
            return modeWins1.findElement(this.activeType);
         }
         if(this.activeVO.rank.v < this.rankHandler.findMyRank(0))
         {
            return modeLoses0.findElement(this.activeType);
         }
         return modeLoses1.findElement(this.activeType);
      }
      
      public function handlerReward() : Boolean
      {
         var _loc1_:Array = PKRewardConfig.instance().findReward(this.rankHandler.findMyRank(0),this.pkVO.score.v);
         var _loc2_:Array = RewardHandler.instance().newGiftReward(_loc1_);
         if(RewardHandler.instance().onReward(_loc2_))
         {
            this.isReward.v = 1;
            return true;
         }
         return false;
      }
      
      public function get pkTeams() : Array
      {
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         var _loc3_:int = this.activeType + 1;
         while(_loc2_ < _loc3_)
         {
            _loc1_[_loc2_] = HeroProxy.instance().findHero(this.roles[_loc2_]);
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function setPos(param1:int, param2:int) : void
      {
         var _loc3_:int = int(this.roles[param2]);
         var _loc4_:int = int(this.roles.indexOf(param1));
         if(_loc4_ != -1)
         {
            this.roles[_loc4_] = _loc3_;
         }
         this.roles[param2] = param1;
      }
      
      public function updatePos() : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:Array = [];
         for each(_loc2_ in this.roles)
         {
            if(_loc2_ != 0)
            {
               _loc1_.push(_loc2_);
            }
         }
         this.roles = [0,0,0,0,0];
         _loc3_ = int(_loc1_.length);
         _loc4_ = 0;
         while(_loc4_ < _loc3_)
         {
            this.roles[_loc4_] = _loc1_[_loc4_];
            _loc4_++;
         }
      }
      
      public function get roleList() : Array
      {
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         while(_loc2_ < 5)
         {
            if(this.roles[_loc2_] == 0)
            {
               _loc1_[_loc2_] = null;
            }
            else
            {
               _loc1_[_loc2_] = HeroProxy.instance().findHero(this.roles[_loc2_]);
            }
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function get isRewardTime() : Boolean
      {
         var _loc1_:TimeVO = GameProxy.instance().newTS;
         return _loc1_.week.v == 1;
      }
      
      public function get hasReward() : Boolean
      {
         return this.rankHandler.hasReward && this.isRewardTime && this.isReward.v == 0;
      }
   }
}

