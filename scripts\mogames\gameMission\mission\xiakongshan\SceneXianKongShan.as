package mogames.gameMission.mission.xiakongshan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.TrapJinJi;
   import mogames.gameRole.enemy.BOSSDiYong;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneXianKongShan extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _traps:Array = [746,1327,1853,2697];
      
      public function SceneXianKongShan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER3");
         setWinPosition(new Rectangle(3108,170,480,140),new Point(3214,406));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2052,1);
            TaskProxy.instance().addTask(11034);
         };
         _mission.cleanLoadUI();
         this.layoutTraps();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2052))
         {
            showDialog("STORY0079",func);
         }
      }
      
      private function layoutTraps() : void
      {
         var _loc1_:int = 0;
         var _loc2_:TrapJinJi = null;
         for each(_loc1_ in this._traps)
         {
            _loc2_ = new TrapJinJi("MC_TRAP_JIN_JI03",146,40);
            Layers.addCEChild(_loc2_);
            _loc2_.x = _loc1_;
            _loc2_.y = 471;
            _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",2,2);
            _loc2_.createHitHurt(1000,1,false);
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[282,306,150,100],[493,306,150,100]],26011,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[727,306,150,100],[1000,306,150,100]],26012,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1226,306,150,100],[1556,306,150,100]],26013,this.triggerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[1837,306,150,100],[2167,306,150,100]],26014,this.triggerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(220,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(955,450),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1475,450),1,new Rectangle(440,0,1380,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2088,450),1,new Rectangle(1040,0,1380,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(3380,450),1,new Rectangle(2880,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function startBattle() : void
      {
         this.addBOSS();
         startBossBattle("BGM_BATTLE1");
      }
      
      private function triggerEnd0() : void
      {
         this.triggerEnd();
         CitrusLock.instance().lock(new Rectangle(0,0,1380,600),false,false,false,true);
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSDiYong();
         _boss.x = 3356;
         _boss.y = 450;
         _boss.initData(30431,30431,146);
         _boss.target = _mission.curTarget;
         add(_boss);
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11034);
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         super.destroy();
      }
   }
}

