package file
{
   import mogames.gameData.tujian.TuEquipVO;
   import mogames.gameData.tujian.TuGoodVO;
   
   public class TuJianConfig
   {
      private static var _instance:TuJianConfig;
      
      public var tuEquips:Array;
      
      public var tuProps:Array;
      
      public var tuFBHC:Array;
      
      public var tuFBRL:Array;
      
      public var tuFBDZ:Array;
      
      public var tuSZRL:Array;
      
      public var tuBSHC:Array;
      
      public var tuPEHC:Array;
      
      public function TuJianConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : TuJianConfig
      {
         if(!_instance)
         {
            _instance = new TuJianConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this.tuEquips = [];
         this.tuEquips[0] = [new TuEquipVO(20001,3),new TuEquipVO(21001,3),new TuEquipVO(22001,3),new TuEquipVO(20006,3),new TuEquipVO(21006,3),new TuEquipVO(22006,3),new TuEquipVO(20011,3),new TuEquipVO(21011,3),new TuEquipVO(22011,3),new TuEquipVO(20016,3),new TuEquipVO(21016,3),new TuEquipVO(22016,3),new TuEquipVO(20021,3),new TuEquipVO(21021,3),new TuEquipVO(22021,3),new TuEquipVO(20121,4),new TuEquipVO(21121,4),new TuEquipVO(22121,4)];
         this.tuEquips[1] = [new TuEquipVO(20003,3),new TuEquipVO(21003,3),new TuEquipVO(22003,3),new TuEquipVO(20008,3),new TuEquipVO(21008,3),new TuEquipVO(22008,3),new TuEquipVO(20013,3),new TuEquipVO(21013,3),new TuEquipVO(22013,3),new TuEquipVO(20018,3),new TuEquipVO(21018,3),new TuEquipVO(22018,3),new TuEquipVO(20023,3),new TuEquipVO(21023,3),new TuEquipVO(22023,3),new TuEquipVO(20123,4),new TuEquipVO(21123,4),new TuEquipVO(22123,4)];
         this.tuEquips[2] = [new TuEquipVO(20002,3),new TuEquipVO(21002,3),new TuEquipVO(22002,3),new TuEquipVO(20007,3),new TuEquipVO(21007,3),new TuEquipVO(22007,3),new TuEquipVO(20012,3),new TuEquipVO(21012,3),new TuEquipVO(22012,3),new TuEquipVO(20017,3),new TuEquipVO(21017,3),new TuEquipVO(22017,3),new TuEquipVO(20022,3),new TuEquipVO(21022,3),new TuEquipVO(22022,3),new TuEquipVO(20122,4),new TuEquipVO(21122,4),new TuEquipVO(22122,4)];
         this.tuEquips[3] = [new TuEquipVO(20005,3),new TuEquipVO(21005,3),new TuEquipVO(22005,3),new TuEquipVO(20010,3),new TuEquipVO(21010,3),new TuEquipVO(22010,3),new TuEquipVO(20015,3),new TuEquipVO(21015,3),new TuEquipVO(22015,3),new TuEquipVO(20020,3),new TuEquipVO(21020,3),new TuEquipVO(22020,3),new TuEquipVO(20025,3),new TuEquipVO(21025,3),new TuEquipVO(22025,3),new TuEquipVO(20125,4),new TuEquipVO(21125,4),new TuEquipVO(22125,4)];
         this.tuEquips[4] = [new TuEquipVO(20004,3),new TuEquipVO(21004,3),new TuEquipVO(22004,3),new TuEquipVO(20009,3),new TuEquipVO(21009,3),new TuEquipVO(22009,3),new TuEquipVO(20014,3),new TuEquipVO(21014,3),new TuEquipVO(22014,3),new TuEquipVO(20019,3),new TuEquipVO(21019,3),new TuEquipVO(22019,3),new TuEquipVO(20024,3),new TuEquipVO(21024,3),new TuEquipVO(22024,3),new TuEquipVO(20124,4),new TuEquipVO(21124,4),new TuEquipVO(22124,4)];
         this.tuProps = [new TuGoodVO(14107),new TuGoodVO(14051),new TuGoodVO(14052),new TuGoodVO(14053),new TuGoodVO(14055),new TuGoodVO(14058),new TuGoodVO(14061),new TuGoodVO(14062),new TuGoodVO(14065),new TuGoodVO(14066),new TuGoodVO(14067),new TuGoodVO(14068),new TuGoodVO(14059),new TuGoodVO(14070),new TuGoodVO(14077),new TuGoodVO(14078),new TuGoodVO(14079),new TuGoodVO(14081),new TuGoodVO(14082),new TuGoodVO(14083),new TuGoodVO(14084),new TuGoodVO(14085),new TuGoodVO(14086),new TuGoodVO(14124),new TuGoodVO(14165),new TuGoodVO(14166),new TuGoodVO(14167),new TuGoodVO(14168),new TuGoodVO(14090),new TuGoodVO(14091),new TuGoodVO(14092),new TuGoodVO(14093),new TuGoodVO(14095),new TuGoodVO(14045),new TuGoodVO(14120),new TuGoodVO(14102),new TuGoodVO(14121),new TuGoodVO(14008),new TuGoodVO(14125),new TuGoodVO(14170),new TuGoodVO(50000),new TuGoodVO(50005),new TuGoodVO(16711),new TuGoodVO(14103),new TuGoodVO(14104),new TuGoodVO(14105),new TuGoodVO(14106),new TuGoodVO(16721),new TuGoodVO(16722),new TuGoodVO(14021)
         ,new TuGoodVO(14039),new TuGoodVO(14101),new TuGoodVO(14150),new TuGoodVO(14009),new TuGoodVO(14020),new TuGoodVO(14011),new TuGoodVO(14030),new TuGoodVO(16757),new TuGoodVO(16762),new TuGoodVO(16750),new TuGoodVO(16751),new TuGoodVO(16752),new TuGoodVO(16753),new TuGoodVO(16754),new TuGoodVO(16755),new TuGoodVO(16756),new TuGoodVO(16758),new TuGoodVO(16759),new TuGoodVO(16760),new TuGoodVO(16761),new TuGoodVO(14010),new TuGoodVO(14002),new TuGoodVO(14001),new TuGoodVO(13326),new TuGoodVO(13325),new TuGoodVO(13322),new TuGoodVO(13321),new TuGoodVO(13101),new TuGoodVO(10024),new TuGoodVO(10023),new TuGoodVO(10022),new TuGoodVO(10021),new TuGoodVO(10009),new TuGoodVO(10008),new TuGoodVO(10007),new TuGoodVO(10006),new TuGoodVO(10005),new TuGoodVO(10004),new TuGoodVO(10003),new TuGoodVO(10002),new TuGoodVO(10001)];
         this.tuFBHC = [];
         this.tuFBHC.push([new TuEquipVO(23013,0),new TuGoodVO(12010,1),new TuGoodVO(13140,20)]);
         this.tuFBHC.push([new TuEquipVO(23012,0),new TuGoodVO(12009,1),new TuGoodVO(14130,20),new TuGoodVO(14131,20),new TuGoodVO(14132,20)]);
         this.tuFBHC.push([new TuEquipVO(23010,0),new TuGoodVO(12008,1),new TuGoodVO(13133,30),new TuGoodVO(13134,30),new TuGoodVO(13135,30)]);
         this.tuFBHC.push([new TuEquipVO(23009,0),new TuGoodVO(12007,1),new TuGoodVO(13111,8),new TuGoodVO(13112,8),new TuGoodVO(13113,8)]);
         this.tuFBHC.push([new TuEquipVO(23008,0),new TuGoodVO(12006,1),new TuGoodVO(13103,5),new TuGoodVO(13104,5),new TuGoodVO(13105,3)]);
         this.tuFBHC.push([new TuEquipVO(23001,0),new TuGoodVO(12001,1),new TuGoodVO(13102,5),new TuGoodVO(13103,5),new TuGoodVO(13101,5)]);
         this.tuFBHC.push([new TuEquipVO(23002,0),new TuGoodVO(12002,1),new TuGoodVO(13104,5),new TuGoodVO(13101,5)]);
         this.tuFBHC.push([new TuEquipVO(23003,0),new TuGoodVO(12003,1),new TuGoodVO(13105,10),new TuGoodVO(13106,10)]);
         this.tuFBHC.push([new TuEquipVO(23004,0),new TuGoodVO(12004,1),new TuGoodVO(13105,5),new TuGoodVO(13107,5),new TuGoodVO(13101,5)]);
         this.tuFBHC.push([new TuEquipVO(23005,0),new TuGoodVO(12005,1),new TuGoodVO(13109,5),new TuGoodVO(13108,5)]);
         this.tuFBRL = [];
         this.tuFBRL.push([new TuEquipVO(23013,1),new TuEquipVO(23013,0),new TuGoodVO(13140,66),new TuGoodVO(13146,20)]);
         this.tuFBRL.push([new TuEquipVO(23012,1),new TuEquipVO(23012,0),new TuGoodVO(14130,20),new TuGoodVO(13141,20),new TuGoodVO(13142,20)]);
         this.tuFBRL.push([new TuEquipVO(23012,2),new TuEquipVO(23012,1),new TuGoodVO(14130,20),new TuGoodVO(13144,20),new TuGoodVO(13145,20)]);
         this.tuFBRL.push([new TuEquipVO(23010,1),new TuEquipVO(23010,0),new TuGoodVO(13303,20),new TuGoodVO(13136,20),new TuGoodVO(13137,20)]);
         this.tuFBRL.push([new TuEquipVO(23010,2),new TuEquipVO(23010,1),new TuGoodVO(13313,20),new TuGoodVO(13138,20),new TuGoodVO(13139,20)]);
         this.tuFBRL.push([new TuEquipVO(23010,3),new TuEquipVO(23010,2),new TuGoodVO(16816,20),new TuGoodVO(16817,20),new TuGoodVO(16818,20)]);
         this.tuFBRL.push([new TuEquipVO(23010,4),new TuEquipVO(23010,3),new TuGoodVO(13329,20),new TuGoodVO(16817,20),new TuGoodVO(16818,20)]);
         this.tuFBRL.push([new TuEquipVO(23009,1),new TuEquipVO(23009,0),new TuGoodVO(13303,20),new TuGoodVO(13319,20),new TuGoodVO(13114,10)]);
         this.tuFBRL.push([new TuEquipVO(23009,2),new TuEquipVO(23009,1),new TuGoodVO(13313,20),new TuGoodVO(13320,20),new TuGoodVO(13115,15)]);
         this.tuFBRL.push([new TuEquipVO(23009,3),new TuEquipVO(23009,2),new TuGoodVO(16801,20),new TuGoodVO(16802,20),new TuGoodVO(16803,20)]);
         this.tuFBRL.push([new TuEquipVO(23009,4),new TuEquipVO(23009,3),new TuGoodVO(13329,20),new TuGoodVO(16802,20),new TuGoodVO(16803,20)]);
         this.tuFBRL.push([new TuEquipVO(23008,1),new TuEquipVO(23008,0),new TuGoodVO(13303,5),new TuGoodVO(13104,10),new TuGoodVO(13105,10)]);
         this.tuFBRL.push([new TuEquipVO(23008,2),new TuEquipVO(23008,1),new TuGoodVO(13313,10),new TuGoodVO(13104,20),new TuGoodVO(13105,20)]);
         this.tuFBRL.push([new TuEquipVO(23008,3),new TuEquipVO(23008,2),new TuGoodVO(13143,10),new TuGoodVO(13104,20),new TuGoodVO(13105,20)]);
         this.tuFBRL.push([new TuEquipVO(23008,4),new TuEquipVO(23008,3),new TuGoodVO(13328,20),new TuGoodVO(13104,20),new TuGoodVO(13105,20)]);
         this.tuFBRL.push([new TuEquipVO(23007,1),new TuEquipVO(23007,0),new TuGoodVO(13303,20),new TuGoodVO(13110,10),new TuGoodVO(13319,15)]);
         this.tuFBRL.push([new TuEquipVO(23007,2),new TuEquipVO(23007,1),new TuGoodVO(13313,30),new TuGoodVO(13110,20),new TuGoodVO(13320,20)]);
         this.tuFBRL.push([new TuEquipVO(23007,3),new TuEquipVO(23007,2),new TuGoodVO(13313,30),new TuGoodVO(13110,20),new TuGoodVO(13320,20)]);
         this.tuFBRL.push([new TuEquipVO(23007,4),new TuEquipVO(23007,3),new TuGoodVO(13329,20),new TuGoodVO(13110,20),new TuGoodVO(13320,20)]);
         this.tuFBRL.push([new TuEquipVO(23005,1),new TuEquipVO(23005,0),new TuGoodVO(13310,10),new TuGoodVO(13311,15),new TuGoodVO(13303,20)]);
         this.tuFBRL.push([new TuEquipVO(23005,2),new TuEquipVO(23005,1),new TuGoodVO(13310,30),new TuGoodVO(13313,30),new TuGoodVO(13323,30)]);
         this.tuFBRL.push([new TuEquipVO(23005,3),new TuEquipVO(23005,2),new TuGoodVO(13310,20),new TuGoodVO(13313,20),new TuGoodVO(13323,20)]);
         this.tuFBRL.push([new TuEquipVO(23005,4),new TuEquipVO(23005,3),new TuGoodVO(13328,20),new TuGoodVO(13313,20),new TuGoodVO(13323,20)]);
         this.tuFBRL.push([new TuEquipVO(23004,1),new TuEquipVO(23004,0),new TuGoodVO(13302,8),new TuGoodVO(13304,8),new TuGoodVO(13303,10)]);
         this.tuFBRL.push([new TuEquipVO(23004,2),new TuEquipVO(23004,1),new TuGoodVO(13313,30),new TuGoodVO(13318,30),new TuGoodVO(13107,30)]);
         this.tuFBRL.push([new TuEquipVO(23004,3),new TuEquipVO(23004,2),new TuGoodVO(16813,20),new TuGoodVO(16814,20),new TuGoodVO(16815,20)]);
         this.tuFBRL.push([new TuEquipVO(23004,4),new TuEquipVO(23004,3),new TuGoodVO(13327,20),new TuGoodVO(16814,20),new TuGoodVO(16815,20)]);
         this.tuFBRL.push([new TuEquipVO(23003,1),new TuEquipVO(23003,0),new TuGoodVO(13301,5),new TuGoodVO(13305,5),new TuGoodVO(13303,8)]);
         this.tuFBRL.push([new TuEquipVO(23003,2),new TuEquipVO(23003,1),new TuGoodVO(13313,20),new TuGoodVO(13317,20),new TuGoodVO(13324,20)]);
         this.tuFBRL.push([new TuEquipVO(23003,3),new TuEquipVO(23003,2),new TuGoodVO(16810,20),new TuGoodVO(16811,20),new TuGoodVO(16812,20)]);
         this.tuFBRL.push([new TuEquipVO(23003,4),new TuEquipVO(23003,3),new TuGoodVO(13328,20),new TuGoodVO(16811,20),new TuGoodVO(16812,20)]);
         this.tuFBRL.push([new TuEquipVO(23001,1),new TuEquipVO(23001,0),new TuGoodVO(13308,10),new TuGoodVO(13309,10),new TuGoodVO(13303,5)]);
         this.tuFBRL.push([new TuEquipVO(23001,2),new TuEquipVO(23001,1),new TuGoodVO(13313,30),new TuGoodVO(13316,30),new TuGoodVO(13317,30)]);
         this.tuFBRL.push([new TuEquipVO(23001,3),new TuEquipVO(23001,2),new TuGoodVO(16807,20),new TuGoodVO(16808,20),new TuGoodVO(16809,20)]);
         this.tuFBRL.push([new TuEquipVO(23001,4),new TuEquipVO(23001,3),new TuGoodVO(13327,20),new TuGoodVO(16808,20),new TuGoodVO(16809,20)]);
         this.tuFBRL.push([new TuEquipVO(23002,1),new TuEquipVO(23002,0),new TuGoodVO(13306,10),new TuGoodVO(13307,10)]);
         this.tuFBRL.push([new TuEquipVO(23002,2),new TuEquipVO(23002,1),new TuGoodVO(13313,20),new TuGoodVO(13315,20),new TuGoodVO(13314,20)]);
         this.tuFBRL.push([new TuEquipVO(23002,3),new TuEquipVO(23002,2),new TuGoodVO(16804,20),new TuGoodVO(16805,20),new TuGoodVO(16806,20)]);
         this.tuFBRL.push([new TuEquipVO(23002,4),new TuEquipVO(23002,3),new TuGoodVO(13327,20),new TuGoodVO(16805,20),new TuGoodVO(16806,20)]);
         this.tuFBDZ = [];
         this.tuFBDZ.push([new TuEquipVO(23007,0),new TuEquipVO(23006,3),new TuGoodVO(13110,20),new TuGoodVO(13108,20),new TuGoodVO(13107,20)]);
         this.tuSZRL = [];
         this.tuSZRL.push([new TuEquipVO(34121,1),new TuEquipVO(34121,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(34121,2),new TuEquipVO(34121,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(34121,3),new TuEquipVO(34121,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(35021,1),new TuEquipVO(35021,0),new TuGoodVO(14025,25),new TuGoodVO(14031,25),new TuGoodVO(14029,25)]);
         this.tuSZRL.push([new TuEquipVO(35021,2),new TuEquipVO(35021,1),new TuGoodVO(14027,35),new TuGoodVO(14035,35),new TuGoodVO(14037,35)]);
         this.tuSZRL.push([new TuEquipVO(35021,3),new TuEquipVO(35021,2),new TuGoodVO(14028,25),new TuGoodVO(14027,25),new TuGoodVO(14025,20)]);
         this.tuSZRL.push([new TuEquipVO(34122,1),new TuEquipVO(34122,0),new TuGoodVO(14025,25),new TuGoodVO(14031,25),new TuGoodVO(14029,25)]);
         this.tuSZRL.push([new TuEquipVO(34122,2),new TuEquipVO(34122,1),new TuGoodVO(14027,35),new TuGoodVO(14035,35),new TuGoodVO(14037,35)]);
         this.tuSZRL.push([new TuEquipVO(34122,3),new TuEquipVO(34122,2),new TuGoodVO(14028,25),new TuGoodVO(14027,25),new TuGoodVO(14025,20)]);
         this.tuSZRL.push([new TuEquipVO(35022,1),new TuEquipVO(35022,0),new TuGoodVO(14025,25),new TuGoodVO(14031,25),new TuGoodVO(14029,25)]);
         this.tuSZRL.push([new TuEquipVO(35022,2),new TuEquipVO(35022,1),new TuGoodVO(14027,35),new TuGoodVO(14035,35),new TuGoodVO(14037,35)]);
         this.tuSZRL.push([new TuEquipVO(35022,3),new TuEquipVO(35022,2),new TuGoodVO(14028,25),new TuGoodVO(14027,25),new TuGoodVO(14025,20)]);
         this.tuSZRL.push([new TuEquipVO(34123,1),new TuEquipVO(34123,0),new TuGoodVO(14025,25),new TuGoodVO(14031,25),new TuGoodVO(14029,25)]);
         this.tuSZRL.push([new TuEquipVO(34123,2),new TuEquipVO(34123,1),new TuGoodVO(14027,35),new TuGoodVO(14035,35),new TuGoodVO(14037,35)]);
         this.tuSZRL.push([new TuEquipVO(34123,3),new TuEquipVO(34123,2),new TuGoodVO(14028,25),new TuGoodVO(14027,25),new TuGoodVO(14025,20)]);
         this.tuSZRL.push([new TuEquipVO(35023,1),new TuEquipVO(35023,0),new TuGoodVO(14025,25),new TuGoodVO(14031,25),new TuGoodVO(14029,25)]);
         this.tuSZRL.push([new TuEquipVO(35023,2),new TuEquipVO(35023,1),new TuGoodVO(14027,35),new TuGoodVO(14035,35),new TuGoodVO(14037,35)]);
         this.tuSZRL.push([new TuEquipVO(35023,3),new TuEquipVO(35023,2),new TuGoodVO(14028,25),new TuGoodVO(14027,25),new TuGoodVO(14025,20)]);
         this.tuSZRL.push([new TuEquipVO(34124,1),new TuEquipVO(34124,0),new TuGoodVO(14025,25),new TuGoodVO(14031,25),new TuGoodVO(14029,25)]);
         this.tuSZRL.push([new TuEquipVO(34124,2),new TuEquipVO(34124,1),new TuGoodVO(14027,35),new TuGoodVO(14035,35),new TuGoodVO(14037,35)]);
         this.tuSZRL.push([new TuEquipVO(34124,3),new TuEquipVO(34124,2),new TuGoodVO(14028,25),new TuGoodVO(14027,25),new TuGoodVO(14025,20)]);
         this.tuSZRL.push([new TuEquipVO(35024,1),new TuEquipVO(35024,0),new TuGoodVO(14025,25),new TuGoodVO(14031,25),new TuGoodVO(14029,25)]);
         this.tuSZRL.push([new TuEquipVO(35024,2),new TuEquipVO(35024,1),new TuGoodVO(14027,35),new TuGoodVO(14035,35),new TuGoodVO(14037,35)]);
         this.tuSZRL.push([new TuEquipVO(35024,3),new TuEquipVO(35024,2),new TuGoodVO(14028,25),new TuGoodVO(14027,25),new TuGoodVO(14025,20)]);
         this.tuSZRL.push([new TuEquipVO(34125,1),new TuEquipVO(34125,0),new TuGoodVO(14025,25),new TuGoodVO(14031,25),new TuGoodVO(14029,25)]);
         this.tuSZRL.push([new TuEquipVO(34125,2),new TuEquipVO(34125,1),new TuGoodVO(14027,35),new TuGoodVO(14035,35),new TuGoodVO(14037,35)]);
         this.tuSZRL.push([new TuEquipVO(34125,3),new TuEquipVO(34125,2),new TuGoodVO(14028,25),new TuGoodVO(14027,25),new TuGoodVO(14025,20)]);
         this.tuSZRL.push([new TuEquipVO(35025,1),new TuEquipVO(35025,0),new TuGoodVO(14025,25),new TuGoodVO(14031,25),new TuGoodVO(14029,25)]);
         this.tuSZRL.push([new TuEquipVO(35025,2),new TuEquipVO(35025,1),new TuGoodVO(14027,35),new TuGoodVO(14035,35),new TuGoodVO(14037,35)]);
         this.tuSZRL.push([new TuEquipVO(35025,3),new TuEquipVO(35025,2),new TuGoodVO(14028,25),new TuGoodVO(14027,25),new TuGoodVO(14025,20)]);
         this.tuSZRL.push([new TuEquipVO(34111,1),new TuEquipVO(34111,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(34111,2),new TuEquipVO(34111,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(34111,3),new TuEquipVO(34111,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(35011,1),new TuEquipVO(35011,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(35011,2),new TuEquipVO(35011,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(35011,3),new TuEquipVO(35011,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(34112,1),new TuEquipVO(34112,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(34112,2),new TuEquipVO(34112,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(34112,3),new TuEquipVO(34112,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(35012,1),new TuEquipVO(35012,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(35012,2),new TuEquipVO(35012,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(35012,3),new TuEquipVO(35012,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(34113,1),new TuEquipVO(34113,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(34113,2),new TuEquipVO(34113,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(34113,3),new TuEquipVO(34113,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(35013,1),new TuEquipVO(35013,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(35013,2),new TuEquipVO(35013,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(35013,3),new TuEquipVO(35013,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(34114,1),new TuEquipVO(34114,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(34114,2),new TuEquipVO(34114,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(34114,3),new TuEquipVO(34114,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(35014,1),new TuEquipVO(35014,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(35014,2),new TuEquipVO(35014,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(35014,3),new TuEquipVO(35014,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(34115,1),new TuEquipVO(34115,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(34115,2),new TuEquipVO(34115,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(34115,3),new TuEquipVO(34115,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(35015,1),new TuEquipVO(35015,0),new TuGoodVO(14025,20),new TuGoodVO(14031,20),new TuGoodVO(14029,20)]);
         this.tuSZRL.push([new TuEquipVO(35015,2),new TuEquipVO(35015,1),new TuGoodVO(14027,30),new TuGoodVO(14035,30),new TuGoodVO(14037,30)]);
         this.tuSZRL.push([new TuEquipVO(35015,3),new TuEquipVO(35015,2),new TuGoodVO(14028,20),new TuGoodVO(14027,20),new TuGoodVO(14025,15)]);
         this.tuSZRL.push([new TuEquipVO(34101,1),new TuEquipVO(34101,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(34101,2),new TuEquipVO(34101,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(34101,3),new TuEquipVO(34101,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuSZRL.push([new TuEquipVO(35001,1),new TuEquipVO(35001,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(35001,2),new TuEquipVO(35001,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(35001,3),new TuEquipVO(35001,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuSZRL.push([new TuEquipVO(34102,1),new TuEquipVO(34102,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(34102,2),new TuEquipVO(34102,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(34102,3),new TuEquipVO(34102,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuSZRL.push([new TuEquipVO(35002,1),new TuEquipVO(35002,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(35002,2),new TuEquipVO(35002,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(35002,3),new TuEquipVO(35002,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuSZRL.push([new TuEquipVO(34103,1),new TuEquipVO(34103,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(34103,2),new TuEquipVO(34103,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(34103,3),new TuEquipVO(34103,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuSZRL.push([new TuEquipVO(35003,1),new TuEquipVO(35003,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(35003,2),new TuEquipVO(35003,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(35003,3),new TuEquipVO(35003,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuSZRL.push([new TuEquipVO(34104,1),new TuEquipVO(34104,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(34104,2),new TuEquipVO(34104,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(34104,3),new TuEquipVO(34104,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuSZRL.push([new TuEquipVO(35004,1),new TuEquipVO(35004,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(35004,2),new TuEquipVO(35004,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(35004,3),new TuEquipVO(35004,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuSZRL.push([new TuEquipVO(34105,1),new TuEquipVO(34105,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(34105,2),new TuEquipVO(34105,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(34105,3),new TuEquipVO(34105,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuSZRL.push([new TuEquipVO(35005,1),new TuEquipVO(35005,0),new TuGoodVO(14025,10),new TuGoodVO(14031,10),new TuGoodVO(14029,10)]);
         this.tuSZRL.push([new TuEquipVO(35005,2),new TuEquipVO(35005,1),new TuGoodVO(14027,15),new TuGoodVO(14035,15),new TuGoodVO(14037,15)]);
         this.tuSZRL.push([new TuEquipVO(35005,3),new TuEquipVO(35005,2),new TuGoodVO(14028,15),new TuGoodVO(14027,15),new TuGoodVO(14025,10)]);
         this.tuBSHC = [];
         this.tuBSHC.push([new TuGoodVO(14202,1),new TuGoodVO(14201,5)]);
         this.tuBSHC.push([new TuGoodVO(14212,1),new TuGoodVO(14211,5)]);
         this.tuBSHC.push([new TuGoodVO(14222,1),new TuGoodVO(14221,5)]);
         this.tuBSHC.push([new TuGoodVO(14203,1),new TuGoodVO(14202,5)]);
         this.tuBSHC.push([new TuGoodVO(14213,1),new TuGoodVO(14212,5)]);
         this.tuBSHC.push([new TuGoodVO(14223,1),new TuGoodVO(14222,5)]);
         this.tuBSHC.push([new TuGoodVO(14204,1),new TuGoodVO(14203,5)]);
         this.tuBSHC.push([new TuGoodVO(14214,1),new TuGoodVO(14213,5)]);
         this.tuBSHC.push([new TuGoodVO(14224,1),new TuGoodVO(14223,5)]);
         this.tuBSHC.push([new TuGoodVO(14205,1),new TuGoodVO(14204,5),new TuGoodVO(14000,3)]);
         this.tuBSHC.push([new TuGoodVO(14215,1),new TuGoodVO(14214,5),new TuGoodVO(14000,3)]);
         this.tuBSHC.push([new TuGoodVO(14225,1),new TuGoodVO(14224,5),new TuGoodVO(14000,3)]);
         this.tuPEHC = [];
         this.tuPEHC.push([new TuEquipVO(46052,3),new TuGoodVO(50205,5),new TuGoodVO(13130,5),new TuGoodVO(13132,5)]);
         this.tuPEHC.push([new TuEquipVO(46052,4),new TuGoodVO(13410,15),new TuGoodVO(13411,15),new TuGoodVO(13412,15)]);
         this.tuPEHC.push([new TuEquipVO(46051,3),new TuGoodVO(50201,5),new TuGoodVO(13120,5),new TuGoodVO(13122,5)]);
         this.tuPEHC.push([new TuEquipVO(46051,4),new TuGoodVO(13407,15),new TuGoodVO(13408,15),new TuGoodVO(13409,15)]);
         this.tuPEHC.push([new TuEquipVO(46004,3),new TuGoodVO(50113,5),new TuGoodVO(13118,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(46004,4),new TuGoodVO(13401,15),new TuGoodVO(13402,15),new TuGoodVO(13405,15)]);
         this.tuPEHC.push([new TuEquipVO(46003,3),new TuGoodVO(50109,5),new TuGoodVO(13117,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(46003,4),new TuGoodVO(13401,15),new TuGoodVO(13402,15),new TuGoodVO(13405,15)]);
         this.tuPEHC.push([new TuEquipVO(46002,3),new TuGoodVO(50105,5),new TuGoodVO(13306,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(46002,4),new TuGoodVO(13401,15),new TuGoodVO(13402,15),new TuGoodVO(13405,15)]);
         this.tuPEHC.push([new TuEquipVO(46001,3),new TuGoodVO(50101,5),new TuGoodVO(13104,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(46001,4),new TuGoodVO(13401,15),new TuGoodVO(13402,15),new TuGoodVO(13405,15)]);
         this.tuPEHC.push([new TuEquipVO(47052,3),new TuGoodVO(50206,5),new TuGoodVO(13131,5),new TuGoodVO(13132,5)]);
         this.tuPEHC.push([new TuEquipVO(47052,4),new TuGoodVO(13410,15),new TuGoodVO(13411,15),new TuGoodVO(13412,15)]);
         this.tuPEHC.push([new TuEquipVO(47051,3),new TuGoodVO(50202,5),new TuGoodVO(13121,5),new TuGoodVO(13122,5)]);
         this.tuPEHC.push([new TuEquipVO(47051,4),new TuGoodVO(13407,15),new TuGoodVO(13408,15),new TuGoodVO(13409,15)]);
         this.tuPEHC.push([new TuEquipVO(47004,3),new TuGoodVO(50114,5),new TuGoodVO(13119,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(47004,4),new TuGoodVO(13401,15),new TuGoodVO(13402,15),new TuGoodVO(13403,15)]);
         this.tuPEHC.push([new TuEquipVO(47003,3),new TuGoodVO(50110,5),new TuGoodVO(13314,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(47003,4),new TuGoodVO(13401,15),new TuGoodVO(13402,15),new TuGoodVO(13403,15)]);
         this.tuPEHC.push([new TuEquipVO(47002,3),new TuGoodVO(50106,5),new TuGoodVO(13107,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(47002,4),new TuGoodVO(13401,15),new TuGoodVO(13402,15),new TuGoodVO(13403,15)]);
         this.tuPEHC.push([new TuEquipVO(47001,3),new TuGoodVO(50102,5),new TuGoodVO(13309,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(47001,4),new TuGoodVO(13401,15),new TuGoodVO(13402,15),new TuGoodVO(13403,15)]);
         this.tuPEHC.push([new TuEquipVO(48052,3),new TuGoodVO(50207,5),new TuGoodVO(13130,5),new TuGoodVO(13132,5)]);
         this.tuPEHC.push([new TuEquipVO(48052,4),new TuGoodVO(13410,15),new TuGoodVO(13411,15),new TuGoodVO(13412,15)]);
         this.tuPEHC.push([new TuEquipVO(48051,3),new TuGoodVO(50203,5),new TuGoodVO(13120,5),new TuGoodVO(13122,5)]);
         this.tuPEHC.push([new TuEquipVO(48051,4),new TuGoodVO(13407,15),new TuGoodVO(13408,15),new TuGoodVO(13409,15)]);
         this.tuPEHC.push([new TuEquipVO(48004,3),new TuGoodVO(50115,5),new TuGoodVO(13118,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(48004,4),new TuGoodVO(13404,15),new TuGoodVO(13405,15),new TuGoodVO(13406,15)]);
         this.tuPEHC.push([new TuEquipVO(48003,3),new TuGoodVO(50111,5),new TuGoodVO(13110,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(48003,4),new TuGoodVO(13404,15),new TuGoodVO(13405,15),new TuGoodVO(13406,15)]);
         this.tuPEHC.push([new TuEquipVO(48002,3),new TuGoodVO(50107,5),new TuGoodVO(13306,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(48002,4),new TuGoodVO(13404,15),new TuGoodVO(13405,15),new TuGoodVO(13406,15)]);
         this.tuPEHC.push([new TuEquipVO(48001,3),new TuGoodVO(50103,5),new TuGoodVO(13104,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(48001,4),new TuGoodVO(13404,15),new TuGoodVO(13405,15),new TuGoodVO(13406,15)]);
         this.tuPEHC.push([new TuEquipVO(49052,3),new TuGoodVO(50208,5),new TuGoodVO(13131,5),new TuGoodVO(13132,5)]);
         this.tuPEHC.push([new TuEquipVO(49052,4),new TuGoodVO(13410,15),new TuGoodVO(13411,15),new TuGoodVO(13412,15)]);
         this.tuPEHC.push([new TuEquipVO(49051,3),new TuGoodVO(50204,5),new TuGoodVO(13121,5),new TuGoodVO(13122,5)]);
         this.tuPEHC.push([new TuEquipVO(49051,4),new TuGoodVO(13407,15),new TuGoodVO(13408,15),new TuGoodVO(13409,15)]);
         this.tuPEHC.push([new TuEquipVO(49004,3),new TuGoodVO(50116,5),new TuGoodVO(13119,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(49004,4),new TuGoodVO(13404,15),new TuGoodVO(13403,15),new TuGoodVO(13406,15)]);
         this.tuPEHC.push([new TuEquipVO(49003,3),new TuGoodVO(50112,5),new TuGoodVO(13116,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(49003,4),new TuGoodVO(13404,15),new TuGoodVO(13403,15),new TuGoodVO(13406,15)]);
         this.tuPEHC.push([new TuEquipVO(49002,3),new TuGoodVO(50108,5),new TuGoodVO(13107,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(49002,4),new TuGoodVO(13404,15),new TuGoodVO(13403,15),new TuGoodVO(13406,15)]);
         this.tuPEHC.push([new TuEquipVO(49001,3),new TuGoodVO(50104,5),new TuGoodVO(13309,5),new TuGoodVO(13108,5)]);
         this.tuPEHC.push([new TuEquipVO(49001,4),new TuGoodVO(13404,15),new TuGoodVO(13403,15),new TuGoodVO(13406,15)]);
      }
   }
}

