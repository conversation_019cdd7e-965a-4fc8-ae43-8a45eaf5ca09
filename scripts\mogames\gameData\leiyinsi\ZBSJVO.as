package mogames.gameData.leiyinsi
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.good.GameEquipVO;
   import mogames.gameData.good.constvo.ConstEquipVO;
   
   public class ZBSJVO extends BaseUseVO
   {
      public var oldID:Sint;
      
      private var _newID:Sint;
      
      public function ZBSJVO(param1:int, param2:int, param3:int, param4:Array)
      {
         super(param3,param4);
         this.oldID = new Sint(param1);
         this._newID = new Sint(param2);
      }
      
      public function get constEquip() : ConstEquipVO
      {
         return GoodConfig.instance().findConstGood(this._newID.v) as ConstEquipVO;
      }
      
      public function get newEquipVO() : GameEquipVO
      {
         var _loc1_:GameEquipVO = GoodConfig.instance().newGood(this._newID.v) as GameEquipVO;
         _loc1_.initNewEquip(4,false,false);
         return _loc1_;
      }
   }
}

