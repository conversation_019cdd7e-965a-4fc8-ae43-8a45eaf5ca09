package citrus.input
{
   public class InputAction
   {
      internal static var disposed:Vector.<InputAction> = new Vector.<InputAction>();
      
      private var _name:String;
      
      private var _controller:InputController;
      
      private var _channel:uint;
      
      private var _time:uint = 0;
      
      internal var _value:Number;
      
      internal var _message:String;
      
      internal var _phase:uint;
      
      public function InputAction(param1:String, param2:InputController, param3:uint = 0, param4:Number = 0, param5:String = null, param6:uint = 0, param7:uint = 0)
      {
         super();
         this._name = param1;
         this._controller = param2;
         this._channel = param3;
         this._value = param4;
         this._message = param5;
         this._phase = param6;
         this._time = param7;
      }
      
      public static function create(param1:String, param2:InputController, param3:uint = 0, param4:Number = 0, param5:String = null, param6:uint = 0, param7:uint = 0) : InputAction
      {
         if(disposed.length > 0)
         {
            return disposed.pop().setTo(param1,param2,param3,param4,param5,param6,param7);
         }
         return new InputAction(param1,param2,param3,param4,param5,param6,param7);
      }
      
      public static function clearDisposed() : void
      {
         disposed.length = 0;
      }
      
      public function clone() : InputAction
      {
         return InputAction.create(this._name,this._controller,this._channel,this._value,this._message,this._phase,this._time);
      }
      
      public function comp(param1:InputAction) : Boolean
      {
         return this._name == param1.name && this._channel == param1.channel;
      }
      
      public function eq(param1:InputAction) : Boolean
      {
         return this._name == param1.name && this._controller == param1.controller && this._channel == param1.channel;
      }
      
      public function toString() : String
      {
         return "\n[ Action # name: " + this._name + " channel: " + this._channel + " value: " + this._value + " phase: " + this._phase + " controller: " + this._controller + " time: " + this._time + " ]";
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function get controller() : InputController
      {
         return this._controller;
      }
      
      public function get channel() : uint
      {
         return this._channel;
      }
      
      public function get time() : uint
      {
         return this._time;
      }
      
      public function get value() : Number
      {
         return this._value;
      }
      
      public function get message() : String
      {
         return this._message;
      }
      
      public function get phase() : Number
      {
         return this._phase;
      }
      
      internal function get itime() : uint
      {
         return this._time;
      }
      
      internal function set itime(param1:uint) : void
      {
         this._time = param1;
      }
      
      internal function setTo(param1:String, param2:InputController, param3:uint = 0, param4:Number = 0, param5:String = null, param6:uint = 0, param7:uint = 0) : InputAction
      {
         this._name = param1;
         this._controller = param2;
         this._channel = param3;
         this._value = param4;
         this._message = param5;
         this._phase = param6;
         this._time = param7;
         return this;
      }
      
      public function dispose() : void
      {
         var _loc1_:InputAction = null;
         this._controller = null;
         for each(_loc1_ in disposed)
         {
            if(this == _loc1_)
            {
               return;
            }
         }
         disposed.push(this);
      }
   }
}

