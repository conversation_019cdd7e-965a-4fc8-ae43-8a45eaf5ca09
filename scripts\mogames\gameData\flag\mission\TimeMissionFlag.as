package mogames.gameData.flag.mission
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.role.HeroProxy;
   import utils.TxtUtil;
   
   public class TimeMissionFlag
   {
      public var id:Sint;
      
      public var minTime:Sint;
      
      public var minHurt:Sint;
      
      public var monkeyBR:int;
      
      public function TimeMissionFlag(param1:int)
      {
         super();
         this.id = new Sint(param1);
         this.minTime = new Sint();
         this.minHurt = new Sint();
         this.monkeyBR = 999999;
      }
      
      public function recordTime(param1:int) : void
      {
         if(this.minTime.v == 0)
         {
            this.minTime.v = param1;
         }
         else if(param1 < this.minTime.v)
         {
            this.minTime.v = param1;
            this.monkeyBR = HeroProxy.instance().monkeyVO.battleRate;
         }
      }
      
      public function recordHurt(param1:int) : void
      {
         if(this.minHurt.v == 0)
         {
            this.minHurt.v = param1;
         }
         else if(param1 < this.minHurt.v)
         {
            this.minHurt.v = param1;
         }
      }
      
      public function get timeStr() : String
      {
         return TxtUtil.formatTime(this.minTime.v);
      }
      
      public function get hurtStr() : String
      {
         return this.minHurt.v + "次";
      }
      
      public function get saveData() : String
      {
         return [this.id.v,this.minTime.v,this.minHurt.v,this.monkeyBR].join("H");
      }
      
      public function set loadData(param1:Array) : void
      {
         this.minTime.v = int(param1[1]);
         this.minHurt.v = int(param1[2]);
         if(param1[0])
         {
            this.monkeyBR = int(param1[3]);
         }
      }
   }
}

