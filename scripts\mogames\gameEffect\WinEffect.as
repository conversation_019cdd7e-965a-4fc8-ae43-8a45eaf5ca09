package mogames.gameEffect
{
   import flash.geom.Point;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.gameObj.display.CitrusMC;
   
   public class WinEffect
   {
      private var _mcBG:CitrusMC;
      
      private var _mcWin:CitrusMC;
      
      public function WinEffect()
      {
         super();
         Layers.camera.enabled = false;
         this._mcBG = new CitrusMC();
         this._mcBG.fpsInter = 1;
         this._mcBG.setupMC(AssetManager.newMCRes("EFFTCT_WIN_BG_CLIP"));
         var _loc1_:Point = Layers.findCEInter(2).globalToLocal(new Point(0,0));
         this._mcBG.x = _loc1_.x;
         this._mcBG.y = _loc1_.y;
         Layers.findCEInter(2).addChild(this._mcBG);
         this._mcWin = new CitrusMC();
         this._mcWin.fpsInter = 1;
         this._mcWin.setupMC(AssetManager.newMCRes("EFFTCT_WIN_WORD_CLIP"),this.destroy);
         Layers.effectLayer.addChild(this._mcWin);
         Layers.lockGame = true;
      }
      
      public function start() : void
      {
         this._mcBG.changeAnimation("start",true);
         this._mcWin.changeAnimation("start",false);
         EffectManager.instance().playAudio("EFFECT_WIN");
      }
      
      private function destroy(param1:String) : void
      {
         Layers.camera.enabled = true;
         Layers.lockGame = false;
         this._mcBG.destroy();
         this._mcWin.destroy();
         this._mcBG = null;
         this._mcWin = null;
      }
   }
}

