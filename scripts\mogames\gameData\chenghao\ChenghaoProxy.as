package mogames.gameData.chenghao
{
   import file.ChenghaoConfig;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameData.role.HeroProxy;
   import utils.MathUtil;
   
   public class ChenghaoProxy
   {
      private static var _instance:ChenghaoProxy;
      
      private var _chenghaos:Array;
      
      public function ChenghaoProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : ChenghaoProxy
      {
         if(!_instance)
         {
            _instance = new ChenghaoProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._chenghaos = [];
         this._chenghaos = ChenghaoConfig.instance().newChenghaos();
         this.setGet(65007);
         this.setGet(65009);
         this.setGet(65015);
         this.setGet(65016);
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:Array = null;
         var _loc4_:BaseChenghaoVO = null;
         var _loc5_:* = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc5_ in _loc2_)
         {
            _loc3_ = _loc5_.split("H");
            _loc4_ = this.findChenghao(int(_loc3_[0]));
            if(_loc4_)
            {
               _loc4_.isGet.v = int(_loc3_[1]);
            }
         }
      }
      
      public function get saveData() : String
      {
         var _loc2_:BaseChenghaoVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._chenghaos)
         {
            if(_loc2_.isRecord)
            {
               _loc1_.push(_loc2_.saveData);
            }
         }
         return _loc1_.join("T");
      }
      
      public function checkGood(param1:int) : void
      {
         if(param1 == 10016 && BagProxy.instance().getGoodNum(param1) >= 50)
         {
            this.setGet(65009);
         }
      }
      
      public function checkPK(param1:int) : void
      {
         if(param1 == 1)
         {
            this.setGet(65100);
         }
         else if(param1 == 2)
         {
            this.setGet(65101);
         }
         else if(param1 == 3)
         {
            this.setGet(65102);
         }
         else if(MathUtil.checkInRate(param1,4,10))
         {
            this.setGet(65103);
         }
         else if(MathUtil.checkInRate(param1,11,50))
         {
            this.setGet(65104);
         }
      }
      
      public function isGet(param1:int) : Boolean
      {
         var _loc2_:BaseChenghaoVO = this.findChenghao(param1);
         if(!_loc2_)
         {
            return false;
         }
         return _loc2_.hasGet;
      }
      
      public function setGet(param1:int) : void
      {
         var _loc2_:BaseChenghaoVO = this.findChenghao(param1);
         if(!_loc2_ || !_loc2_.isRecord || _loc2_.hasGet)
         {
            return;
         }
         _loc2_.isGet.v = 1;
      }
      
      public function hasOwner(param1:int) : HeroGameVO
      {
         var _loc3_:HeroGameVO = null;
         var _loc2_:Array = HeroProxy.instance().enableHero;
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.chenghaoID == param1)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function findChenghao(param1:int) : BaseChenghaoVO
      {
         var _loc2_:BaseChenghaoVO = null;
         for each(_loc2_ in this._chenghaos)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findList(param1:int) : Array
      {
         var _loc3_:BaseChenghaoVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in this._chenghaos)
         {
            if(_loc3_.type == param1)
            {
               _loc2_.push(_loc3_);
            }
         }
         return _loc2_;
      }
   }
}

