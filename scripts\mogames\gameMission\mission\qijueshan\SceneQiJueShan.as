package mogames.gameMission.mission.qijueshan
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameRole.enemy.BOSSMangSheJing;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   
   public class SceneQiJueShan extends BossScene
   {
      private var _blues:Array = [[960,425],[1919,375],[2883,440],[3742,465]];
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _mcEye:CitrusMCSprite;
      
      private var _mcJin:CitrusMCSprite;
      
      private var _timer:CitrusTimer;
      
      private var _eyeLabel:Array = ["jin","mu","shui","huo","tu"];
      
      private var _wudiList:Array;
      
      private var _wuxing:int;
      
      public function SceneQiJueShan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER2");
         setWinPosition(new Rectangle(4130,134,512,118),new Point(4390,416));
         this._timer = new CitrusTimer(true);
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2043,1);
            TaskProxy.instance().addTask(11028);
         };
         super.handlerInit();
         this.layoutTraps();
         this.layoutEnemies();
         if(!FlagProxy.instance().isComplete(2043))
         {
            showDialog("STORY0071",func);
         }
      }
      
      private function layoutBossTrap() : void
      {
         var _loc3_:int = 0;
         var _loc4_:HeroGameVO = null;
         this._wudiList = [];
         var _loc1_:Array = [1,2,3,5];
         var _loc2_:Array = HeroProxy.instance().teamHero;
         for each(_loc4_ in _loc2_)
         {
            this._wudiList.push(_loc4_.constVO.wuxing.v);
         }
         if(this._wudiList.length <= 1)
         {
            _loc3_ = int(_loc1_.indexOf(this._wudiList[0]));
            _loc1_.splice(_loc3_,1);
            this._wudiList[1] = _loc1_[int(_loc1_.length * Math.random())];
         }
         this._mcEye = new CitrusMCSprite("EFFECT_SHE_YAN_CLIP",{
            "width":30,
            "height":18,
            "group":3
         });
         this._mcEye.x = 4287;
         this._mcEye.y = 84;
         Layers.addCEChild(this._mcEye);
         this._mcJin = new CitrusMCSprite("EFFECT_MIAN_YI_CLIP",{
            "width":178,
            "height":184,
            "group":3
         });
         this._mcJin.x = 4375;
         this._mcJin.y = 182;
         Layers.addCEChild(this._mcJin);
         this._timer.setInterval(10,0,this.changeWuxing);
      }
      
      private function layoutTraps() : void
      {
         var _loc1_:int = 0;
         var _loc2_:Bitmap = null;
         var _loc3_:TrapQiJueShan = null;
         _loc1_ = 0;
         while(_loc1_ < 4)
         {
            _loc2_ = new Bitmap(GameInLoader.instance().findAsset("PIC_BLUE_CI" + (_loc1_ + 1)) as BitmapData);
            _loc3_ = new TrapQiJueShan(_loc2_);
            Layers.addCEChild(_loc3_);
            _loc3_.x = this._blues[_loc1_][0];
            _loc3_.y = this._blues[_loc1_][1];
            _loc3_.createHurt(1,10,"EFFECT_GUN_ATK","GUNHURT");
            _loc1_++;
         }
      }
      
      private function layoutEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[202,216,150,100],[626,216,150,100]],20011,this.handlerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1159,216,150,100],[1583,216,150,100]],20012,this.handlerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[2113,216,150,100],[2537,216,150,100]],20013,this.handlerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[3103,216,150,100],[3528,216,150,100]],20014,this.triggerEnd3);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,428),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1440,480),1,new Rectangle(960,0,960,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2400,480),1,new Rectangle(1920,0,960,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(3360,480),1,new Rectangle(2880,0,960,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(4238,480),1,new Rectangle(3840,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function handlerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd3() : void
      {
         this.handlerEnd();
         this.addBOSS();
      }
      
      private function startBattle() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2044,1);
            startBossBattle("BGM_BATTLE0");
         };
         if(!FlagProxy.instance().isComplete(2044))
         {
            showDialog("STORY0072",func);
         }
         else
         {
            this.startBossBattle("BGM_BATTLE0");
         }
      }
      
      private function changeWuxing() : void
      {
         this._wuxing = this._wudiList[int(this._wudiList.length * Math.random())];
         (_boss as BOSSMangSheJing).wudiType = this._wuxing;
         this._mcEye.changeAnimation(this._eyeLabel[this._wuxing - 1],true);
         this._mcJin.changeAnimation("start",false);
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         this.layoutBossTrap();
         super.startBossBattle(param1);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSMangSheJing();
         _boss.x = 4640;
         _boss.y = 280;
         _boss.initData(30351,30351,91);
         _boss.target = _mission.curTarget;
         add(_boss);
      }
      
      override protected function handlerWin() : void
      {
         this._timer.pause();
         TaskProxy.instance().setComplete(11028);
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._mcEye = null;
         this._mcJin = null;
         this._timer.destroy();
         this._timer = null;
         super.destroy();
      }
   }
}

