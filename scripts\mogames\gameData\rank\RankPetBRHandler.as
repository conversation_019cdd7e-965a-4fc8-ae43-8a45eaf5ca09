package mogames.gameData.rank
{
   import mogames.gameData.pet.PetProxy;
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.rank.vo.RankPetVO;
   
   public class RankPetBRHandler extends RankHandler
   {
      public var index:int;
      
      private var _petIDs:Array;
      
      public function RankPetBRHandler()
      {
         super([1798,1809,1842,1866,1867,1896]);
         this._petIDs = [1101,1102,1103,1104,1106,1107];
         _msg = "初始化宠物战力排行榜";
      }
      
      override public function parseRank(param1:Array) : Array
      {
         var _loc5_:RankPetVO = null;
         if(!param1 || param1.length <= 0)
         {
            return [];
         }
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         var _loc4_:Array = [];
         while(_loc2_ < _loc3_)
         {
            _loc5_ = new RankPetVO();
            _loc5_.parseData(param1[_loc2_]);
            _loc4_[_loc2_] = _loc5_;
            _loc2_++;
         }
         _loc4_.sortOn("sortIndex",Array.NUMERIC);
         return _loc4_;
      }
      
      override public function submitRank(param1:Function) : void
      {
         var _loc2_:PetGameVO = this.rankPet;
         if(!_loc2_)
         {
            param1();
            return;
         }
         super.submitRank(param1);
      }
      
      override protected function get rankData() : Array
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.newRankData();
         return _loc1_;
      }
      
      protected function newRankData() : Object
      {
         var _loc1_:PetGameVO = this.rankPet;
         var _loc2_:Object = new Object();
         _loc2_.rId = this.curRankID;
         _loc2_.score = _loc1_.battleRate;
         _loc2_.extra = [_loc1_.curName,_loc1_.level.v,_loc1_.curGrow.v,_loc1_.curTalent.v,_loc1_.curWit.v,_loc1_.curLearn.v,_loc1_.curQuality.v];
         return _loc2_;
      }
      
      public function get rankPet() : PetGameVO
      {
         var _loc3_:int = 0;
         var _loc4_:PetGameVO = null;
         var _loc5_:PetGameVO = null;
         var _loc1_:Array = PetProxy.instance().findPets(this._petIDs[this.index]);
         if(_loc1_.length <= 0)
         {
            return null;
         }
         var _loc2_:int = 0;
         for each(_loc5_ in _loc1_)
         {
            _loc3_ = int(_loc5_.battleRate);
            if(_loc3_ > _loc2_)
            {
               _loc2_ = _loc3_;
               _loc4_ = _loc5_;
            }
         }
         return _loc4_;
      }
      
      public function get curRankID() : int
      {
         return rankIDs[this.index];
      }
   }
}

