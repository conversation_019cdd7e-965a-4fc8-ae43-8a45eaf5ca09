package file
{
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameData.buff.BaseTeamBuff;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameData.buff.role.ATKBuff;
   import mogames.gameData.buff.role.ATKDebuff;
   import mogames.gameData.buff.role.CRITBuff;
   import mogames.gameData.buff.role.CandleBuff;
   import mogames.gameData.buff.role.CureBuff;
   import mogames.gameData.buff.role.DEFBuff;
   import mogames.gameData.buff.role.DEFDebuff;
   import mogames.gameData.buff.role.FireDEFBuff;
   import mogames.gameData.buff.role.FireDebuff;
   import mogames.gameData.buff.role.MDEFBuff;
   import mogames.gameData.buff.role.MDEFDeBuff;
   import mogames.gameData.buff.role.MianYiMDEFBuff;
   import mogames.gameData.buff.role.MianYiPDEFBuff;
   import mogames.gameData.buff.role.PDEFBuff;
   import mogames.gameData.buff.role.PDEFDebuff;
   import mogames.gameData.buff.role.PosionDeBuff;
   import mogames.gameData.buff.role.ReverseDebuff;
   import mogames.gameData.buff.role.ShuFuDebuff;
   import mogames.gameData.buff.role.SpeedBuff;
   import mogames.gameData.buff.role.SpeedDebuff;
   import mogames.gameData.buff.role.WenYiDebuff;
   import mogames.gameData.buff.role.WuShuangBuff;
   import mogames.gameData.buff.role.WudiBuff;
   import mogames.gameData.buff.role.YuJinPingBuff;
   import mogames.gameData.buff.team.EXPBuff;
   
   public class BuffConfig
   {
      private static var _instance:BuffConfig;
      
      public static const CANDLE_BUFF:Array = [3001,3002,3003,3004,3005];
      
      public static const PET_AVOID_BUFF:Array = [1004];
      
      public function BuffConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : BuffConfig
      {
         if(!_instance)
         {
            _instance = new BuffConfig();
         }
         return _instance;
      }
      
      public function newBuff(param1:int) : BaseBuff
      {
         switch(param1)
         {
            case 2000:
               return new WuShuangBuff();
            case 1001:
               return new PosionDeBuff();
            case 1002:
               return new SpeedDebuff();
            case 1003:
               return new ATKBuff();
            case 1004:
               return new ReverseDebuff();
            case 1005:
               return new FireDebuff();
            case 1006:
               return new YuJinPingBuff();
            case 1007:
               return new ShuFuDebuff();
            case 1008:
               return new CureBuff();
            case 1009:
               return new MDEFBuff();
            case 1010:
               return new PDEFBuff();
            case 1011:
               return new FireDEFBuff();
            case 1012:
               return new WudiBuff();
            case 1013:
               return new ATKDebuff();
            case 1014:
               return new PDEFDebuff();
            case 1015:
               return new MDEFDeBuff();
            case 1016:
               return new DEFBuff();
            case 1017:
               return new WenYiDebuff();
            case 1018:
               return new MianYiPDEFBuff();
            case 1019:
               return new MianYiMDEFBuff();
            case 1020:
               return new SpeedBuff();
            case 1021:
               return new CRITBuff();
            case 3001:
               return new CandleBuff(3001);
            case 3002:
               return new CandleBuff(3002);
            case 3003:
               return new CandleBuff(3003);
            case 3004:
               return new CandleBuff(3004);
            case 3005:
               return new CandleBuff(3005);
            case 3006:
               return new DEFDebuff();
            default:
               return new BaseBuff(0);
         }
      }
      
      public function newTeamBuff(param1:BuffVO) : BaseTeamBuff
      {
         var _loc2_:BaseTeamBuff = null;
         switch(param1.id.v)
         {
            case 10000:
               _loc2_ = new EXPBuff();
         }
         _loc2_.setBuffVO(param1);
         return _loc2_;
      }
   }
}

