package mogames.gameMission.mission.shilianchang
{
   import flash.geom.Point;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.SLCEnemyTrigger;
   import mogames.gameObj.bullet.LocationBullet;
   import mogames.gameObj.trap.TrapShiLian;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.MathUtil;
   
   public class SceneShiLianChang extends BaseScene
   {
      private var _enemyTrigger:SLCEnemyTrigger;
      
      private var _enemyTimer:CitrusTimer;
      
      private var _windTimer:CitrusTimer;
      
      private var _windArg:Object = {
         "showTime":10,
         "hurt":new Sint(10),
         "hurtTime":new Snum(0.5)
      };
      
      private var _buffTimer:CitrusTimer;
      
      public function SceneShiLianChang(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this._enemyTimer = new CitrusTimer();
         this._enemyTrigger = new SLCEnemyTrigger(_mission,[[96,120,735,170]],this.handlerEnd);
         this._enemyTrigger.initEnemy(4,40);
         this._enemyTimer.setInterval(1,90,this.onTimer,this.onTimeOut);
         this._windTimer = new CitrusTimer(true);
         this._windTimer.setInterval(this._windArg.showTime,0,this.addXuanFeng,null,false);
         this._windTimer.startNone();
         this._buffTimer = new CitrusTimer(true);
         this._buffTimer.setInterval(8,0,this.addBuff,null,false);
         this._buffTimer.startNone();
      }
      
      private function onTimer() : void
      {
         MiniMsgMediator.instance().showMsg("剩余时间：" + this._enemyTimer.leftTime + "秒",480,50);
      }
      
      private function onTimeOut() : void
      {
         if(!this._enemyTrigger)
         {
            return;
         }
         this._enemyTrigger.destroy();
         this.handlerEnd();
      }
      
      private function handlerEnd() : void
      {
         this._enemyTrigger.destroy();
         this._enemyTrigger = null;
         this._enemyTimer.pause();
         this._windTimer.pause();
         this._buffTimer.pause();
         addLeaveDoor(480,330);
         MiniMsgMediator.clean();
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         MissionManager.instance().handlerWin(false);
      }
      
      private function addBuff() : void
      {
         var _loc4_:TrapShiLian = null;
         var _loc1_:Array = [0,1,2];
         var _loc2_:int = Math.random() * 2 + 1;
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_)
         {
            _loc4_ = new TrapShiLian(_loc1_[int(Math.random() * _loc1_.length)]);
            _loc4_.x = MathUtil.randomNum(100,800);
            _loc4_.y = -50;
            Layers.addCEChild(_loc4_);
            _loc3_++;
         }
      }
      
      private function addXuanFeng() : void
      {
         var _loc1_:int = MathUtil.checkOdds(500) ? 1 : -1;
         var _loc2_:int = _loc1_ == 1 ? -300 : 1200;
         var _loc3_:int = _loc1_ == 1 ? 1200 : -300;
         var _loc5_:Point = new Point(_loc3_,354);
         var _loc6_:LocationBullet = new LocationBullet(5,_loc5_,true,{
            "x":_loc2_,
            "y":354,
            "width":118,
            "height":143,
            "group":3
         });
         _loc6_.createView("EFFECT_XUAN_FENG_CLIP",118,143,0,0);
         _loc6_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",10,10);
         _loc6_.createHitHurt(this._windArg.hurt.v,this._windArg.hurtTime.v,false,1);
         _loc6_.isAlpha = false;
         _loc6_.isHurtDead = false;
         _loc6_.cleanFrame = 10;
         add(_loc6_);
         EffectManager.instance().playAudio("XUAN_FENG");
      }
      
      override public function destroy() : void
      {
         if(this._enemyTrigger != null)
         {
            this._enemyTrigger.destroy();
         }
         this._enemyTrigger = null;
         this._enemyTimer.destroy();
         this._enemyTimer = null;
         this._windTimer.destroy();
         this._windTimer = null;
         this._buffTimer.destroy();
         this._buffTimer = null;
         MiniMsgMediator.clean();
         super.destroy();
      }
   }
}

