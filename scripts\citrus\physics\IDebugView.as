package citrus.physics
{
   import flash.geom.Matrix;
   
   public interface IDebugView
   {
      function update() : void;
      
      function debugMode(param1:uint) : void;
      
      function initialize() : void;
      
      function destroy() : void;
      
      function set transformMatrix(param1:Matrix) : void;
      
      function get transformMatrix() : Matrix;
      
      function set visibility(param1:Boolean) : void;
      
      function get visibility() : Boolean;
      
      function get debugDrawer() : *;
   }
}

