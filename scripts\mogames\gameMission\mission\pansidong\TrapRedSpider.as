package mogames.gameMission.mission.pansidong
{
   import citrus.objects.CitrusSprite;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   import mogames.gameSystem.CitrusTimer;
   
   public class TrapRedSpider extends CitrusSprite implements IRole
   {
      private var _mc:CitrusMC;
      
      private var _hitTargets:Array = [];
      
      private var _hitVO:BaseHitVO;
      
      private var _timer:CitrusTimer;
      
      private var _endFunc:Function;
      
      public function TrapRedSpider(param1:Function)
      {
         this._endFunc = param1;
         super("TrapRedSpider",{
            "width":88,
            "height":84,
            "group":2
         });
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._hitVO.hurtSkin = "EFFECT_FIRE_BOOM_CLIP";
         this._hitVO.hurtSound = "FIRE_EXPLORE";
         this._hitVO.atkType.v = 1;
         this._hitVO.WU_XING.v = 0;
         this._hitVO.hurtX = 2;
         this._hitVO.hurtY = 2;
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("TRAP_RED_SPIDER_CLIP"),this.onComplete,this.updateFrame);
         this._mc.changeAnimation("attack",false);
         this.view = this._mc;
         this._timer = new CitrusTimer();
      }
      
      public function createHitHurt(param1:int) : void
      {
         this._hitVO.hurt.v = param1;
      }
      
      private function startLoop() : void
      {
         var func:Function = null;
         func = function():void
         {
            _mc.changeAnimation("end",false);
         };
         this._mc.changeAnimation("loop",true);
         this._timer.setTimeOut(5,func);
      }
      
      private function updateFrame(param1:int) : void
      {
         if(!this._mc.mc.mcAreaATK)
         {
            this.cleanTargets();
            return;
         }
         this._hitVO.range = this._mc.mc.mcAreaATK;
         BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
      }
      
      private function onComplete(param1:String) : void
      {
         if(param1 == "attack")
         {
            this.startLoop();
         }
         else if(param1 == "end")
         {
            this._endFunc();
            kill = true;
         }
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return this._hitTargets.indexOf(param1) != -1;
      }
      
      public function addTargets(param1:IRole) : void
      {
         this._hitTargets[this._hitTargets.length] = param1;
      }
      
      public function cleanTargets() : void
      {
         this._hitTargets.length = 0;
      }
      
      public function hitBack() : void
      {
      }
      
      override public function destroy() : void
      {
         this._endFunc = null;
         this._hitTargets = null;
         this._mc.destroy();
         this._mc = null;
         this._hitVO = null;
         this._timer.destroy();
         this._timer = null;
         super.destroy();
      }
   }
}

