package file
{
   import mogames.gameData.shop.vo.BaseShopVO;
   import mogames.gameData.shop.vo.ShopGoldVO;
   import mogames.gameData.shop.vo.ShopInfoVO;
   import mogames.gameData.shop.vo.ShopZongziVO;
   
   public class ShopConfig
   {
      private static var _instance:ShopConfig;
      
      private var _list:Vector.<BaseShopVO>;
      
      private var _shop:Vector.<ShopInfoVO>;
      
      public function ShopConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : ShopConfig
      {
         if(!_instance)
         {
            _instance = new ShopConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._shop = new Vector.<ShopInfoVO>();
         this._shop.push(new ShopInfoVO(100,"需要什么仙种，尽管挑！",1));
         this._shop.push(new ShopInfoVO(101,"小师傅需要些什么？",2));
         this._shop.push(new ShopInfoVO(102,"快来看看有什么好东西吧。",3));
         this._shop.push(new ShopInfoVO(103,"嫦娥这厢有礼了！",4));
         this._list = new Vector.<BaseShopVO>();
         this._list.push(new ShopGoldVO(100,16004,500));
         this._list.push(new ShopGoldVO(100,16005,500));
         this._list.push(new ShopGoldVO(100,16006,500));
         this._list.push(new ShopGoldVO(100,16007,500));
         this._list.push(new ShopGoldVO(101,16001,200));
         this._list.push(new ShopGoldVO(101,16002,200));
         this._list.push(new ShopGoldVO(101,16003,200));
         this._list.push(new ShopGoldVO(102,13101,500));
         this._list.push(new ShopGoldVO(102,13143,5000));
         this._list.push(new ShopGoldVO(102,12009,100000));
         this._list.push(new ShopGoldVO(102,12010,200000));
         this._list.push(new ShopZongziVO(103,14020,20));
         this._list.push(new ShopZongziVO(103,16753,5));
         this._list.push(new ShopZongziVO(103,16754,5));
         this._list.push(new ShopZongziVO(103,14001,3));
         this._list.push(new ShopZongziVO(103,14002,9));
         this._list.push(new ShopZongziVO(103,14010,8));
         this._list.push(new ShopZongziVO(103,14011,8));
         this._list.push(new ShopZongziVO(103,16104,6));
         this._list.push(new ShopZongziVO(103,16105,6));
         this._list.push(new ShopZongziVO(103,16106,6));
         this._list.push(new ShopZongziVO(103,16107,6));
         this._list.push(new ShopZongziVO(103,16101,4));
         this._list.push(new ShopZongziVO(103,16102,4));
         this._list.push(new ShopZongziVO(103,16103,4));
      }
      
      public function findShopInfo(param1:int) : ShopInfoVO
      {
         var _loc2_:ShopInfoVO = null;
         for each(_loc2_ in this._shop)
         {
            if(_loc2_.shopID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findShop(param1:int) : Array
      {
         var _loc3_:BaseShopVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in this._list)
         {
            if(_loc3_.shopID == param1)
            {
               _loc2_.push(_loc3_);
            }
         }
         return _loc2_;
      }
   }
}

