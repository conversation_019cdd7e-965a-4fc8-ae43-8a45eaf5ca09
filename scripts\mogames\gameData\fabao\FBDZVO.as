package mogames.gameData.fabao
{
   import file.GoodConfig;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.UseHandler;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameData.good.constvo.ConstEquipVO;
   
   public class FBDZVO extends BaseUseVO
   {
      public var oldFB:Sint;
      
      public var newFB:Sint;
      
      public function FBDZVO(param1:int, param2:int, param3:int, param4:Array)
      {
         super(param3,param4);
         this.oldFB = new Sint(param1);
         this.newFB = new Sint(param2);
      }
      
      override public function useStuff() : void
      {
         if(needGold.v != 0)
         {
            MasterProxy.instance().changeGold(-needGold.v);
         }
         UseHandler.instance().useStuff(needList);
      }
      
      public function handlerDZ(param1:GameFabaoVO) : GameFabaoVO
      {
         this.useStuff();
         BagProxy.instance().delItemByVO(param1);
         var _loc2_:GameFabaoVO = GoodConfig.instance().newGood(this.newFB.v) as GameFabaoVO;
         _loc2_.initNewEquip(0,true,false);
         BagProxy.instance().addDirect(_loc2_);
         return _loc2_;
      }
      
      public function get constFabao() : ConstEquipVO
      {
         return GoodConfig.instance().findConstGood(this.newFB.v) as ConstEquipVO;
      }
   }
}

