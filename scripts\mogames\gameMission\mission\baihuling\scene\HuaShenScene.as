package mogames.gameMission.mission.baihuling.scene
{
   import citrus.core.CitrusObject;
   import mogames.Layers;
   import mogames.gameData.dialog.DialogVO;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.baihuling.BaiHuLingMission;
   import mogames.gameMission.mission.baihuling.npc.NPCHuaShen;
   import mogames.gameRole.hero.BaseHero;
   import mogames.gameUI.dialog.DialogMediator;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class HuaShenScene extends BossScene
   {
      protected var _npc:NPCHuaShen;
      
      public function HuaShenScene(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         this.createHuaShen();
         _mission.cleanLoadUI();
         this.initEnemies();
      }
      
      protected function initEnemies() : void
      {
      }
      
      protected function createHuaShen() : void
      {
         this._npc.setFunc(this.saveFunc,this.deadFunc);
      }
      
      protected function saveFunc() : void
      {
      }
      
      protected function deadFunc(param1:int) : void
      {
         if(param1 != 0)
         {
            MiniMsgMediator.instance().showAutoMsg("白骨精真身被削弱！",480,200,"ERROR",false,2000);
            return;
         }
         var _loc2_:Vector.<DialogVO> = new Vector.<DialogVO>();
         _loc2_.push(new DialogVO(1,"唐僧","BODY_TANG_SENG0","恶徒！！！为何伤无辜之人的性命！"));
         DialogMediator.instance().init(_loc2_,this.addHurt);
         setHeroEnable(false);
      }
      
      private function addHurt() : void
      {
         var _loc2_:BaseHero = null;
         var _loc3_:int = 0;
         setHeroEnable(true);
         EffectManager.instance().addScreenEffect(16711680,1);
         var _loc1_:Vector.<CitrusObject> = getObjectsByType(BaseHero);
         for each(_loc2_ in _loc1_)
         {
            if(!_loc2_.isDead)
            {
               _loc3_ = _loc2_.roleVO.totalHP.v * 0.3;
               if(_loc3_ > _loc2_.roleVO.curHP.v)
               {
                  _loc3_ = _loc2_.roleVO.curHP.v - 1;
               }
               _loc2_.roleVO.changeHP(-_loc3_);
            }
         }
         MiniMsgMediator.instance().showAutoMsg("白骨精真身被削弱！但众人生命减少30%！",480,200,"REDUCE_HP",false,2000);
      }
      
      override protected function handlerWin() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
         _boss.kill = true;
         (_mission as BaiHuLingMission).playerBGM();
      }
      
      override public function destroy() : void
      {
         this._npc = null;
         super.destroy();
      }
   }
}

