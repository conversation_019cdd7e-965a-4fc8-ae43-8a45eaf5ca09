package file
{
   import mogames.gameData.drop.vo.BaseDropVO;
   import mogames.gameData.drop.vo.EnemyDropVO;
   import mogames.gameData.drop.vo.EquipDropVO;
   import mogames.gameData.drop.vo.ListDropVO;
   
   public class EnemyDropConfig
   {
      private static var _instance:EnemyDropConfig;
      
      private var _dropVec:Vector.<EnemyDropVO>;
      
      public function EnemyDropConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : EnemyDropConfig
      {
         if(!_instance)
         {
            _instance = new EnemyDropConfig();
         }
         return _instance;
      }
      
      public static function clean() : void
      {
         _instance = null;
      }
      
      public function init() : void
      {
         this._dropVec = new Vector.<EnemyDropVO>();
         this._dropVec.push(new EnemyDropVO(1001,15,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1002,22,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1003,29,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1004,36,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1005,43,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1006,50,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1007,57,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1008,64,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1009,71,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1010,78,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1011,85,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1012,92,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1013,99,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1014,106,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1015,113,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1016,120,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1017,127,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1018,137,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1019,158,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1020,181,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1021,206,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1022,234,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1023,264,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1024,296,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1025,331,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1026,356,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1027,398,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1028,442,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1029,489,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1030,540,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1031,593,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1032,651,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1033,712,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1034,776,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1035,845,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1036,919,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1037,984,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1038,1025,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1039,1066,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1040,1107,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1041,1148,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1042,1189,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1043,1230,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1044,1271,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1045,1312,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1046,1353,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1047,1394,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1048,1435,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1049,1476,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1050,1517,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1051,1541,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1052,1579,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1053,1611,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1054,1645,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1055,1683,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1056,1701,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1057,1739,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(1,15,new BaseDropVO(10000,1,800),[new BaseDropVO(71001,1,1),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,100),new EquipDropVO(20001,0,30),new EquipDropVO(21001,0,30),new BaseDropVO(10001,1,50)]));
         this._dropVec.push(new EnemyDropVO(94,15,new BaseDropVO(10000,1,800),[new BaseDropVO(71006,1,1),new BaseDropVO(11001,1,100)]));
         this._dropVec.push(new EnemyDropVO(2,25,null,[new BaseDropVO(70001,1,5),new BaseDropVO(12002,1,200),new BaseDropVO(10000,100,1000),new BaseDropVO(13102,1,800),new EquipDropVO(20001,3,1000)]));
         this._dropVec.push(new EnemyDropVO(3,17,new BaseDropVO(10000,1,800),[new BaseDropVO(71011,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,150),new BaseDropVO(10002,1,100),new EquipDropVO(20001,0,30),new EquipDropVO(21001,0,10)]));
         this._dropVec.push(new EnemyDropVO(67,16,new BaseDropVO(10000,1,800),[new BaseDropVO(71006,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,150),new EquipDropVO(20001,0,30),new EquipDropVO(21001,0,30)]));
         this._dropVec.push(new EnemyDropVO(4,50,null,[new BaseDropVO(70006,1,6),new BaseDropVO(10000,200,1000),new BaseDropVO(13103,1,800),new EquipDropVO(20003,2,1000),new EquipDropVO(21001,2,1000),new EquipDropVO(21001,3,500),new EquipDropVO(22001,2,1000),new EquipDropVO(22001,3,500)]));
         this._dropVec.push(new EnemyDropVO(5,18,new BaseDropVO(10000,2,800),[new BaseDropVO(71006,1,6),new BaseDropVO(11001,1,200),new BaseDropVO(11005,1,200),new EquipDropVO(22001,0,10),new EquipDropVO(22003,0,10)]));
         this._dropVec.push(new EnemyDropVO(6,20,new BaseDropVO(10000,2,800),[new BaseDropVO(71001,1,6),new BaseDropVO(11001,1,200),new BaseDropVO(11005,1,200),new EquipDropVO(20001,0,10),new EquipDropVO(21001,0,10),new EquipDropVO(21001,1,10),new BaseDropVO(10001,1,50)]));
         this._dropVec.push(new EnemyDropVO(7,22,new BaseDropVO(10000,2,800),[new BaseDropVO(71016,1,6),new BaseDropVO(11001,1,200),new BaseDropVO(11005,1,200),new EquipDropVO(20003,0,10),new EquipDropVO(21003,0,10),new BaseDropVO(10001,1,50)]));
         this._dropVec.push(new EnemyDropVO(8,100,null,[new BaseDropVO(70011,1,60),new BaseDropVO(10000,300,1000),new BaseDropVO(14001,1,100),new BaseDropVO(12006,1,900),new BaseDropVO(13307,1,1000),new BaseDropVO(13104,1,1000),new EquipDropVO(22003,3,600),new EquipDropVO(20003,3,600),new EquipDropVO(21003,3,600),new EquipDropVO(20002,3,600),new EquipDropVO(21002,3,600),new EquipDropVO(22002,3,600),new EquipDropVO(20005,3,600)]));
         this._dropVec.push(new EnemyDropVO(9,19,new BaseDropVO(10000,3,800),[new BaseDropVO(71026,1,6),new BaseDropVO(10014,1,150),new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,110)]));
         this._dropVec.push(new EnemyDropVO(10,26,new BaseDropVO(10000,3,800),[new BaseDropVO(71031,1,6),new BaseDropVO(10014,1,150),new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,110)]));
         this._dropVec.push(new EnemyDropVO(11,28,new BaseDropVO(10000,3,800),[new BaseDropVO(71036,1,6),new BaseDropVO(10014,1,150),new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,110)]));
         this._dropVec.push(new EnemyDropVO(12,19,new BaseDropVO(10000,3,800),[new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,150)]));
         this._dropVec.push(new EnemyDropVO(13,21,new BaseDropVO(10000,3,800),[new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,150)]));
         this._dropVec.push(new EnemyDropVO(14,23,new BaseDropVO(10000,3,800),[new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,150)]));
         this._dropVec.push(new EnemyDropVO(15,200,null,[new BaseDropVO(70016,1,60),new BaseDropVO(10000,400,1000),new BaseDropVO(14001,1,400),new BaseDropVO(13306,1,1000),new BaseDropVO(12001,1,1000),new EquipDropVO(20006,2,400),new EquipDropVO(20008,2,400),new EquipDropVO(20006,3,500),new EquipDropVO(20008,3,500),new EquipDropVO(21005,3,200)]));
         this._dropVec.push(new EnemyDropVO(16,90,null,[new BaseDropVO(11001,1,1000),new BaseDropVO(11005,1,1000)]));
         this._dropVec.push(new EnemyDropVO(17,90,null,[new BaseDropVO(11001,1,1000),new BaseDropVO(11005,1,1000),new EquipDropVO(20006,1,50)]));
         this._dropVec.push(new EnemyDropVO(18,90,null,[new BaseDropVO(11001,1,1000),new BaseDropVO(11005,1,1000),new EquipDropVO(20008,1,50)]));
         this._dropVec.push(new EnemyDropVO(19,150,null,[new BaseDropVO(10000,200,1000),new BaseDropVO(10000,200,1000),new BaseDropVO(10000,200,1000),new BaseDropVO(10000,200,1000),new BaseDropVO(10000,200,1000),new BaseDropVO(13105,1,1000),new BaseDropVO(14001,1,1000),new BaseDropVO(14001,1,100)]));
         this._dropVec.push(new EnemyDropVO(20,250,null,[new BaseDropVO(10000,275,1000),new BaseDropVO(10000,275,1000),new BaseDropVO(10000,275,1000),new BaseDropVO(10000,275,1000),new BaseDropVO(10000,275,1000),new BaseDropVO(13303,1,1000),new BaseDropVO(13303,1,100)]));
         this._dropVec.push(new EnemyDropVO(21,350,null,[new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(13317,1,1000),new BaseDropVO(16752,1,100)]));
         this._dropVec.push(new EnemyDropVO(22,30,new BaseDropVO(10000,4,800),[new BaseDropVO(71011,1,6),new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,150),new BaseDropVO(10002,1,50),new EquipDropVO(21006,0,10)]));
         this._dropVec.push(new EnemyDropVO(23,33,new BaseDropVO(10000,4,800),[new BaseDropVO(71001,1,6),new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,150),new BaseDropVO(10001,1,50),new EquipDropVO(21008,0,10)]));
         this._dropVec.push(new EnemyDropVO(24,36,new BaseDropVO(10000,4,800),[new BaseDropVO(71016,1,6),new BaseDropVO(11001,1,150),new BaseDropVO(11006,1,50),new BaseDropVO(10001,1,50),new EquipDropVO(21006,0,10)]));
         this._dropVec.push(new EnemyDropVO(25,39,new BaseDropVO(10000,4,800),[new BaseDropVO(71021,1,6),new BaseDropVO(11001,1,150),new BaseDropVO(11006,1,50),new EquipDropVO(21008,0,30)]));
         this._dropVec.push(new EnemyDropVO(26,400,null,[new BaseDropVO(70021,1,60),new BaseDropVO(10000,500,1000),new BaseDropVO(13321,1,1000),new BaseDropVO(12003,1,900),new BaseDropVO(13308,1,1000),new BaseDropVO(10003,1,700),new EquipDropVO(21006,2,400),new EquipDropVO(21008,2,400),new EquipDropVO(21007,2,400),new EquipDropVO(21006,3,500),new EquipDropVO(21008,3,500),new EquipDropVO(22005,3,700),new BaseDropVO(50101,1,1000)]));
         this._dropVec.push(new EnemyDropVO(27,100,null,[new BaseDropVO(11001,1,1000),new BaseDropVO(11005,1,1000),new BaseDropVO(13106,1,1000),new EquipDropVO(22006,2,100),new EquipDropVO(20006,2,100)]));
         this._dropVec.push(new EnemyDropVO(28,100,null,[new BaseDropVO(11001,1,1000),new BaseDropVO(11005,1,1000),new BaseDropVO(13106,1,1000),new EquipDropVO(22008,2,100),new EquipDropVO(21006,2,100)]));
         this._dropVec.push(new EnemyDropVO(29,100,null,[new BaseDropVO(11003,1,1000),new BaseDropVO(11005,1,1000),new BaseDropVO(13106,1,1000),new EquipDropVO(22006,2,100),new EquipDropVO(20008,2,100)]));
         this._dropVec.push(new EnemyDropVO(30,100,null,[new BaseDropVO(11003,1,1000),new BaseDropVO(11005,1,1000),new BaseDropVO(13106,1,1000),new EquipDropVO(22008,2,100),new EquipDropVO(21008,2,100)]));
         this._dropVec.push(new EnemyDropVO(31,42,new BaseDropVO(10000,5,800),[new BaseDropVO(71021,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,150),new BaseDropVO(11001,1,50),new BaseDropVO(11006,1,50),new EquipDropVO(20006,1,20)]));
         this._dropVec.push(new EnemyDropVO(32,45,new BaseDropVO(10000,5,800),[new BaseDropVO(71046,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,150),new BaseDropVO(11001,1,50),new BaseDropVO(11006,1,50),new EquipDropVO(21006,1,20)]));
         this._dropVec.push(new EnemyDropVO(33,48,new BaseDropVO(10000,5,800),[new BaseDropVO(71041,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,150),new BaseDropVO(11001,1,50),new BaseDropVO(11006,1,50),new EquipDropVO(20006,1,20)]));
         this._dropVec.push(new EnemyDropVO(34,51,new BaseDropVO(10000,5,800),[new BaseDropVO(71051,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,150),new BaseDropVO(11001,1,50),new BaseDropVO(11006,1,50),new BaseDropVO(10004,1,500),new EquipDropVO(21008,1,20)]));
         this._dropVec.push(new EnemyDropVO(35,600,null,[new BaseDropVO(70026,1,90),new BaseDropVO(10000,600,1000),new BaseDropVO(70031,1,90),new BaseDropVO(70036,1,90),new BaseDropVO(13321,1,1000),new BaseDropVO(13309,1,1000),new EquipDropVO(22008,2,400),new EquipDropVO(22006,2,400),new EquipDropVO(22007,2,400),new EquipDropVO(22008,3,500),new EquipDropVO(22006,3,500),new EquipDropVO(20007,3,500),new BaseDropVO(50102,1,1000)]));
         this._dropVec.push(new EnemyDropVO(36,54,new BaseDropVO(10000,6,800),[new BaseDropVO(71016,1,6),new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,150),new BaseDropVO(10001,1,50)]));
         this._dropVec.push(new EnemyDropVO(37,57,new BaseDropVO(10000,6,800),[new BaseDropVO(71031,1,6),new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,150)]));
         this._dropVec.push(new EnemyDropVO(38,60,new BaseDropVO(10000,6,800),[new BaseDropVO(71051,1,6),new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,150),new BaseDropVO(10004,1,50)]));
         this._dropVec.push(new EnemyDropVO(39,63,new BaseDropVO(10000,6,800),[new BaseDropVO(71046,1,6),new BaseDropVO(11001,1,150),new BaseDropVO(11005,1,150)]));
         this._dropVec.push(new EnemyDropVO(40,800,null,[new BaseDropVO(70041,1,100),new BaseDropVO(10000,700,1000),new BaseDropVO(12004,1,900),new BaseDropVO(13107,1,1000),new EquipDropVO(21007,3,350),new EquipDropVO(21007,2,400),new EquipDropVO(21007,1,900),new EquipDropVO(20010,3,500),new BaseDropVO(50103,1,1000)]));
         this._dropVec.push(new EnemyDropVO(41,300,null,[new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(14001,1,1000),new BaseDropVO(14002,1,400),new BaseDropVO(13301,1,800),new BaseDropVO(13305,1,1000)]));
         this._dropVec.push(new EnemyDropVO(42,300,null,[new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(14001,1,1000),new BaseDropVO(14002,1,400),new BaseDropVO(13302,1,800),new BaseDropVO(13304,1,1000)]));
         this._dropVec.push(new EnemyDropVO(43,300,null,[new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(14001,1,1000),new BaseDropVO(14002,1,400),new BaseDropVO(13311,1,1000)]));
         this._dropVec.push(new EnemyDropVO(44,300,null,[new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(14001,1,1000),new BaseDropVO(14002,1,400),new BaseDropVO(16010,1,1000)]));
         this._dropVec.push(new EnemyDropVO(45,66,new BaseDropVO(10000,7,800),[new BaseDropVO(71056,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,100),new EquipDropVO(20011,0,20)]));
         this._dropVec.push(new EnemyDropVO(95,69,new BaseDropVO(10000,7,800),[new BaseDropVO(71051,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,100),new EquipDropVO(20011,0,20)]));
         this._dropVec.push(new EnemyDropVO(46,72,new BaseDropVO(10000,7,800),[new BaseDropVO(71061,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,100),new EquipDropVO(20011,0,20)]));
         this._dropVec.push(new EnemyDropVO(47,1,new BaseDropVO(10000,7,800),[new BaseDropVO(71041,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,100)]));
         this._dropVec.push(new EnemyDropVO(96,1,new BaseDropVO(10000,7,800),[new BaseDropVO(71036,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,100)]));
         this._dropVec.push(new EnemyDropVO(97,1,new BaseDropVO(10000,7,800),[new BaseDropVO(71006,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,100)]));
         this._dropVec.push(new EnemyDropVO(98,1,new BaseDropVO(10000,7,800),[new BaseDropVO(71016,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,100)]));
         this._dropVec.push(new EnemyDropVO(48,1,null,[new BaseDropVO(11001,1,1000),new BaseDropVO(11005,1,1000),new BaseDropVO(11007,1,150),new BaseDropVO(10000,15,1000),new BaseDropVO(10000,16,1000),new BaseDropVO(10000,17,1000),new EquipDropVO(20011,1,200),new EquipDropVO(20011,2,200)]));
         this._dropVec.push(new EnemyDropVO(49,1000,null,[new BaseDropVO(70046,1,100),new BaseDropVO(10000,800,1000),new BaseDropVO(13108,1,1000),new EquipDropVO(20011,3,300),new EquipDropVO(21011,3,300),new EquipDropVO(22007,3,300),new EquipDropVO(22011,3,300),new EquipDropVO(20011,2,300),new EquipDropVO(21011,2,300),new EquipDropVO(22007,2,300),new EquipDropVO(22011,2,300),new BaseDropVO(50104,1,800)]));
         this._dropVec.push(new EnemyDropVO(50,250,null,[new BaseDropVO(10000,250,1000),new BaseDropVO(10000,250,1000),new BaseDropVO(10000,250,1000),new BaseDropVO(10000,250,1000),new BaseDropVO(10000,250,1000),new BaseDropVO(16108,1,1000),new BaseDropVO(16761,1,700)]));
         this._dropVec.push(new EnemyDropVO(51,350,null,[new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(13315,1,1000),new BaseDropVO(16755,1,80)]));
         this._dropVec.push(new EnemyDropVO(52,450,null,[new BaseDropVO(10000,450,1000),new BaseDropVO(10000,450,1000),new BaseDropVO(10000,450,1000),new BaseDropVO(10000,450,1000),new BaseDropVO(10000,450,1000),new BaseDropVO(13326,1,1000),new BaseDropVO(13326,1,150)]));
         this._dropVec.push(new EnemyDropVO(53,75,new BaseDropVO(10000,8,700),[new BaseDropVO(71066,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,150),new EquipDropVO(20011,0,20)]));
         this._dropVec.push(new EnemyDropVO(99,78,new BaseDropVO(10000,8,700),[new BaseDropVO(71071,1,6),new BaseDropVO(11001,1,100),new BaseDropVO(11005,1,150),new EquipDropVO(20011,0,20)]));
         this._dropVec.push(new EnemyDropVO(54,1200,null,[new BaseDropVO(70051,1,100),new BaseDropVO(10000,900,1000),new BaseDropVO(10008,1,600),new BaseDropVO(13109,1,1000),new BaseDropVO(12005,1,900),new EquipDropVO(20013,3,300),new EquipDropVO(21013,3,300),new EquipDropVO(22013,3,300),new EquipDropVO(20013,2,300),new EquipDropVO(21013,2,300),new EquipDropVO(22013,2,300),new EquipDropVO(21010,3,500),new BaseDropVO(50105,1,1000)]));
         this._dropVec.push(new EnemyDropVO(55,10,null,[new BaseDropVO(11001,1,80),new BaseDropVO(11005,1,80)]));
         this._dropVec.push(new EnemyDropVO(78,100,null,[new BaseDropVO(13319,1,30),new BaseDropVO(14002,1,40),new BaseDropVO(16011,1,50),new BaseDropVO(13310,1,30),new BaseDropVO(14021,1,30),new BaseDropVO(13322,1,100),new BaseDropVO(11002,1,1000),new BaseDropVO(11006,1,1000)]));
         this._dropVec.push(new EnemyDropVO(56,100,null,[new ListDropVO([new BaseDropVO(13319,1,300),new BaseDropVO(14002,1,150),new BaseDropVO(13310,1,150),new BaseDropVO(14021,1,150),new BaseDropVO(13322,1,100),new BaseDropVO(14011,1,150)]),new BaseDropVO(14011,1,300),new BaseDropVO(13319,1,100),new BaseDropVO(14002,1,100),new BaseDropVO(13310,1,150),new BaseDropVO(14021,1,100),new BaseDropVO(14101,1,80),new BaseDropVO(10000,5000,1000),new BaseDropVO(16011,1,1000),new BaseDropVO(13322,1,1000)]));
         this._dropVec.push(new EnemyDropVO(57,15,null,[new BaseDropVO(11001,1,80),new BaseDropVO(11005,1,80),new BaseDropVO(11002,1,80)]));
         this._dropVec.push(new EnemyDropVO(79,200,null,[new BaseDropVO(14201,1,30),new BaseDropVO(14211,1,30),new BaseDropVO(14221,1,30),new BaseDropVO(14025,1,30),new BaseDropVO(14029,1,30),new BaseDropVO(14031,1,30),new BaseDropVO(13313,1,30),new BaseDropVO(13325,1,100),new BaseDropVO(11002,1,1000),new BaseDropVO(11006,1,1000)]));
         this._dropVec.push(new EnemyDropVO(58,200,null,[new ListDropVO([new BaseDropVO(14201,1,140),new BaseDropVO(14211,1,140),new BaseDropVO(14221,1,140),new BaseDropVO(13313,1,150),new BaseDropVO(13325,1,160),new BaseDropVO(14025,1,140),new BaseDropVO(14029,1,140),new BaseDropVO(14031,1,140)]),new BaseDropVO(13325,1,1000),new BaseDropVO(14201,1,50),new BaseDropVO(14211,1,50),new BaseDropVO(14221,1,50),new BaseDropVO(10000,10000,1000),new BaseDropVO(13313,1,1000),new BaseDropVO(14025,1,80),new BaseDropVO(14029,1,80),new BaseDropVO(14031,1,80),new BaseDropVO(13325,1,1000)]));
         this._dropVec.push(new EnemyDropVO(59,20,null,[new BaseDropVO(11001,1,80),new BaseDropVO(11005,1,80),new BaseDropVO(11002,1,80)]));
         this._dropVec.push(new EnemyDropVO(80,300,null,[new BaseDropVO(13320,1,30),new BaseDropVO(14027,1,30),new BaseDropVO(14035,1,30),new BaseDropVO(14037,1,30),new BaseDropVO(13401,1,35),new BaseDropVO(13402,1,35),new BaseDropVO(13403,1,35),new BaseDropVO(11002,1,1000),new BaseDropVO(11006,1,1000)]));
         this._dropVec.push(new EnemyDropVO(60,300,null,[new ListDropVO([new BaseDropVO(13401,1,333),new BaseDropVO(13402,1,334),new BaseDropVO(13403,1,333)]),new ListDropVO([new BaseDropVO(13320,1,500),new BaseDropVO(14027,1,250),new BaseDropVO(14035,1,250),new BaseDropVO(14037,1,250)]),new BaseDropVO(13320,1,30),new BaseDropVO(14027,1,30),new BaseDropVO(14035,1,30),new BaseDropVO(14037,1,30),new BaseDropVO(10000,15000,1000),new BaseDropVO(13401,1,80),new BaseDropVO(13402,1,80),new BaseDropVO(13403,1,80),new BaseDropVO(13319,1,300)]));
         this._dropVec.push(new EnemyDropVO(61,81,new BaseDropVO(10000,9,800),[new BaseDropVO(71076,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11005,1,150),new EquipDropVO(20012,0,20),new EquipDropVO(21012,0,20),new EquipDropVO(22012,0,20)]));
         this._dropVec.push(new EnemyDropVO(100,84,new BaseDropVO(10000,9,800),[new BaseDropVO(71081,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11005,1,150),new EquipDropVO(20012,0,20),new EquipDropVO(21012,0,20),new EquipDropVO(22012,0,20)]));
         this._dropVec.push(new EnemyDropVO(101,87,new BaseDropVO(10000,9,800),[new BaseDropVO(71086,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11005,1,150),new EquipDropVO(20012,0,20),new EquipDropVO(21012,0,20),new EquipDropVO(22012,0,20)]));
         this._dropVec.push(new EnemyDropVO(62,350,null,[new BaseDropVO(70056,1,100),new BaseDropVO(11002,1000,500),new BaseDropVO(11005,1,500),new EquipDropVO(20012,2,200),new EquipDropVO(21012,2,200),new EquipDropVO(22012,2,200),new EquipDropVO(20012,1,200),new EquipDropVO(21012,1,200),new EquipDropVO(22012,1,200)]));
         this._dropVec.push(new EnemyDropVO(63,1400,null,[new BaseDropVO(70061,1,70),new BaseDropVO(70066,1,70),new BaseDropVO(70071,1,60),new BaseDropVO(10000,1000,1000),new EquipDropVO(20012,3,300),new EquipDropVO(21012,3,300),new EquipDropVO(22012,3,300),new EquipDropVO(22010,3,500),new BaseDropVO(50106,1,1000)]));
         this._dropVec.push(new EnemyDropVO(64,90,new BaseDropVO(10000,10,800),[new BaseDropVO(71091,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(102,93,new BaseDropVO(10000,10,800),[new BaseDropVO(71096,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(65,1600,null,[new BaseDropVO(70076,1,100),new BaseDropVO(10000,1100,1000),new BaseDropVO(10009,1,700),new EquipDropVO(20015,3,300),new BaseDropVO(50107,1,1000)]));
         this._dropVec.push(new EnemyDropVO(68,93,new BaseDropVO(10000,11,800),[new BaseDropVO(71101,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(103,96,new BaseDropVO(10000,11,800),[new BaseDropVO(71106,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(69,1800,null,[new BaseDropVO(70081,1,100),new BaseDropVO(10000,1200,1000),new BaseDropVO(50109,1,1000),new BaseDropVO(13314,1,800),new EquipDropVO(22015,3,300),new EquipDropVO(21015,3,300),new BaseDropVO(50108,1,1000),new BaseDropVO(16804,1,800)]));
         this._dropVec.push(new EnemyDropVO(70,100,null,[new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(13316,1,1000),new BaseDropVO(13316,1,500),new BaseDropVO(13316,1,200),new BaseDropVO(14001,1,1000),new BaseDropVO(14002,1,400)]));
         this._dropVec.push(new EnemyDropVO(71,100,null,[new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(13318,1,1000),new BaseDropVO(13318,1,500),new BaseDropVO(13318,1,200),new BaseDropVO(14001,1,1000),new BaseDropVO(14002,1,400)]));
         this._dropVec.push(new EnemyDropVO(72,100,null,[new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(13323,1,1000),new BaseDropVO(13323,1,500),new BaseDropVO(13323,1,200),new BaseDropVO(14001,1,1000),new BaseDropVO(14002,1,400)]));
         this._dropVec.push(new EnemyDropVO(73,100,null,[new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(10000,2000,1000),new BaseDropVO(13324,1,1000),new BaseDropVO(13324,1,500),new BaseDropVO(13324,1,200),new BaseDropVO(14001,1,1000),new BaseDropVO(14002,1,400)]));
         this._dropVec.push(new EnemyDropVO(74,96,new BaseDropVO(10000,12,800),[new BaseDropVO(71026,1,6),new BaseDropVO(71026,1,5),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(104,99,new BaseDropVO(10000,12,800),[new BaseDropVO(71031,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(105,96,new BaseDropVO(10000,12,800),[new BaseDropVO(71036,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(106,99,new BaseDropVO(10000,12,800),[new BaseDropVO(71021,1,6),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(75,2000,null,[new BaseDropVO(70086,1,100),new BaseDropVO(10000,1300,1000),new BaseDropVO(50110,1,1000),new BaseDropVO(13116,1,1000),new EquipDropVO(20016,3,240),new EquipDropVO(20017,3,240),new EquipDropVO(20018,3,240),new EquipDropVO(20016,2,400),new EquipDropVO(20017,2,400),new EquipDropVO(20018,2,400),new BaseDropVO(16805,1,800)]));
         this._dropVec.push(new EnemyDropVO(76,99,new BaseDropVO(10000,13,800),[new BaseDropVO(71061,1,8),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(107,102,new BaseDropVO(10000,13,800),[new BaseDropVO(71106,1,8),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(108,99,new BaseDropVO(10000,13,800),[new BaseDropVO(71016,1,8),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(109,102,new BaseDropVO(10000,13,800),[new BaseDropVO(71051,1,8),new BaseDropVO(11002,1,100),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(77,2200,null,[new BaseDropVO(70091,1,70),new BaseDropVO(10000,1400,1000),new BaseDropVO(50111,1,1000),new BaseDropVO(70096,1,70),new BaseDropVO(13110,1,1000),new EquipDropVO(20020,3,240),new BaseDropVO(16806,1,800)]));
         this._dropVec.push(new EnemyDropVO(81,350,null,[new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(10000,350,1000),new BaseDropVO(16115,1,1000),new BaseDropVO(16115,1,100)]));
         this._dropVec.push(new EnemyDropVO(82,450,null,[new BaseDropVO(10000,450,1000),new BaseDropVO(10000,450,1000),new BaseDropVO(10000,450,1000),new BaseDropVO(10000,450,1000),new BaseDropVO(10000,450,1000),new BaseDropVO(16016,1,1000),new BaseDropVO(16756,1,80),new BaseDropVO(14028,1,1000)]));
         this._dropVec.push(new EnemyDropVO(83,550,null,[new BaseDropVO(10000,550,1000),new BaseDropVO(10000,550,1000),new BaseDropVO(10000,550,1000),new BaseDropVO(10000,550,1000),new BaseDropVO(10000,550,1000),new BaseDropVO(16018,1,600)]));
         this._dropVec.push(new EnemyDropVO(84,102,new BaseDropVO(10000,14,800),[new BaseDropVO(71086,1,9),new BaseDropVO(11002,1,80),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(110,105,new BaseDropVO(10000,14,800),[new BaseDropVO(71101,1,9),new BaseDropVO(11002,1,80),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(111,102,new BaseDropVO(10000,14,800),[new BaseDropVO(71051,1,9),new BaseDropVO(11002,1,80),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(85,2400,null,[new BaseDropVO(70101,1,100),new BaseDropVO(10000,1500,1000),new BaseDropVO(50112,1,1000),new BaseDropVO(13117,1,1000),new EquipDropVO(21017,2,250),new EquipDropVO(21016,2,250),new EquipDropVO(21017,3,150),new EquipDropVO(21016,3,150),new BaseDropVO(16813,1,800)]));
         this._dropVec.push(new EnemyDropVO(86,108,new BaseDropVO(10000,15,800),[new BaseDropVO(71111,1,10),new BaseDropVO(11003,1,50),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(112,105,new BaseDropVO(10000,15,800),[new BaseDropVO(71116,1,10),new BaseDropVO(11003,1,50),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(87,2600,null,[new BaseDropVO(70106,1,100),new BaseDropVO(10000,1600,1000),new EquipDropVO(21018,2,250),new EquipDropVO(21020,2,250),new EquipDropVO(21018,3,150),new EquipDropVO(21020,3,150),new BaseDropVO(13118,1,1000),new BaseDropVO(50113,1,1000),new BaseDropVO(16814,1,800)]));
         this._dropVec.push(new EnemyDropVO(88,10,new BaseDropVO(10000,4,800),[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(89,1,null,[new ListDropVO([new BaseDropVO(16012,1,100),new BaseDropVO(16013,1,100),new BaseDropVO(16014,1,100),new BaseDropVO(16016,1,100),new BaseDropVO(14101,1,50),new BaseDropVO(14011,1,100),new BaseDropVO(14107,1,50),new BaseDropVO(14020,1,50),new BaseDropVO(14030,1,50),new BaseDropVO(14009,1,100),new BaseDropVO(16762,1,100),new BaseDropVO(14002,1,100)]),new ListDropVO([new BaseDropVO(13303,1,100),new BaseDropVO(13313,1,100),new BaseDropVO(13317,1,50),new BaseDropVO(14107,1,50),new BaseDropVO(16751,1,100),new BaseDropVO(16752,1,100),new BaseDropVO(16753,1,100),new BaseDropVO(16754,1,100),new BaseDropVO(16755,1,50),new BaseDropVO(16756,1,50),new BaseDropVO(16761,1,100),new BaseDropVO(16757,1,100)]),new BaseDropVO(10000,100,1000),new BaseDropVO(10000,200,1000),new BaseDropVO(10000,300,1000),new BaseDropVO(10000,400,1000),new BaseDropVO(10000,500,1000),new BaseDropVO(10000,600,1000),new BaseDropVO(10000,700,1000),new BaseDropVO(10000,800,1000),new BaseDropVO(10000
         ,900,1000),new BaseDropVO(10000,1000,1000),new BaseDropVO(16721,1,1000),new BaseDropVO(14008,1,50)]));
         this._dropVec.push(new EnemyDropVO(90,108,new BaseDropVO(10000,16,800),[new BaseDropVO(71121,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(113,111,new BaseDropVO(10000,16,800),[new BaseDropVO(71126,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(91,2800,null,[new BaseDropVO(70111,1,100),new BaseDropVO(10000,1700,1000),new EquipDropVO(22018,2,250),new EquipDropVO(22020,2,250),new EquipDropVO(22018,3,150),new EquipDropVO(22020,3,150),new BaseDropVO(13119,1,1000),new BaseDropVO(50114,1,1000),new BaseDropVO(16815,1,800)]));
         this._dropVec.push(new EnemyDropVO(92,111,new BaseDropVO(10000,17,800),[new BaseDropVO(71131,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(114,114,new BaseDropVO(10000,17,800),[new BaseDropVO(71006,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(115,111,new BaseDropVO(10000,17,800),[new BaseDropVO(71021,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(93,3000,null,[new BaseDropVO(70116,1,100),new BaseDropVO(10000,1800,1000),new EquipDropVO(22016,2,250),new EquipDropVO(22017,2,250),new EquipDropVO(22016,3,150),new EquipDropVO(22017,3,150),new BaseDropVO(50115,1,1000)]));
         this._dropVec.push(new EnemyDropVO(116,114,new BaseDropVO(10000,18,800),[new BaseDropVO(71136,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(117,117,new BaseDropVO(10000,18,800),[new BaseDropVO(71141,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(118,3200,null,[new BaseDropVO(70121,1,100),new BaseDropVO(10000,1900,1000),new EquipDropVO(20022,2,200),new EquipDropVO(20023,2,200),new EquipDropVO(20022,3,150),new EquipDropVO(20023,3,150),new BaseDropVO(50116,1,1000)]));
         this._dropVec.push(new EnemyDropVO(119,2500,null,[new BaseDropVO(14120,1,1000),new BaseDropVO(14120,1,1000),new BaseDropVO(14120,1,1000),new BaseDropVO(14120,1,1000),new BaseDropVO(14120,1,700),new BaseDropVO(14120,1,500),new BaseDropVO(14120,1,200),new BaseDropVO(14121,1,1000),new BaseDropVO(14121,1,1000),new BaseDropVO(14121,1,1000),new BaseDropVO(14121,1,700),new BaseDropVO(14121,1,400),new BaseDropVO(14121,1,150),new BaseDropVO(10000,200,1000),new BaseDropVO(10000,400,1000),new BaseDropVO(10000,600,1000),new BaseDropVO(10000,800,1000),new BaseDropVO(14125,1,150)]));
         this._dropVec.push(new EnemyDropVO(120,1,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(121,1,null,[new BaseDropVO(11002,1,30),new BaseDropVO(11006,1,30)]));
         this._dropVec.push(new EnemyDropVO(122,118,new BaseDropVO(10000,19,800),[new BaseDropVO(71146,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(123,120,new BaseDropVO(10000,19,800),[new BaseDropVO(71151,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(124,1200,null,[new BaseDropVO(70126,1,70),new BaseDropVO(70121,1,70),new BaseDropVO(10000,2000,1000),new EquipDropVO(20021,2,200),new EquipDropVO(20025,2,200),new EquipDropVO(20021,3,150),new EquipDropVO(20025,3,150)]));
         this._dropVec.push(new EnemyDropVO(125,2000,null,[new ListDropVO([new BaseDropVO(13111,1,200),new BaseDropVO(13112,1,200),new BaseDropVO(13113,1,200),new BaseDropVO(13114,1,200),new BaseDropVO(13115,1,200)]),new ListDropVO([new BaseDropVO(13111,1,200),new BaseDropVO(13112,1,200),new BaseDropVO(13113,1,200),new BaseDropVO(13114,1,200),new BaseDropVO(13115,1,200)]),new BaseDropVO(13111,1,150),new BaseDropVO(13112,1,150),new BaseDropVO(13113,1,150),new BaseDropVO(13114,1,100),new BaseDropVO(13115,1,100),new BaseDropVO(12007,1,1000)]));
         this._dropVec.push(new EnemyDropVO(126,122,new BaseDropVO(10000,20,800),[new BaseDropVO(71156,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(127,120,new BaseDropVO(10000,20,800),[new BaseDropVO(71131,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(128,1400,null,[new BaseDropVO(70131,1,60),new BaseDropVO(11003,1,800),new BaseDropVO(11007,1,600)]));
         this._dropVec.push(new EnemyDropVO(129,2000,null,[new BaseDropVO(70136,1,60),new BaseDropVO(11003,1,800),new BaseDropVO(11007,1,500)]));
         this._dropVec.push(new EnemyDropVO(130,2600,null,[new BaseDropVO(70141,1,60),new BaseDropVO(10000,2100,1000),new EquipDropVO(21023,2,300),new EquipDropVO(21023,3,150)]));
         this._dropVec.push(new EnemyDropVO(134,125,new BaseDropVO(10000,21,800),[new BaseDropVO(71161,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(135,130,new BaseDropVO(10000,21,800),[new BaseDropVO(71166,1,10),new BaseDropVO(11003,1,40),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(136,3600,null,[new BaseDropVO(70146,1,100),new BaseDropVO(10000,2200,1000),new EquipDropVO(21025,2,300),new EquipDropVO(21025,3,150)]));
         this._dropVec.push(new EnemyDropVO(144,140,new BaseDropVO(10000,22,800),[new BaseDropVO(71171,1,10),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(145,1,new BaseDropVO(10000,22,800),[new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,20)]));
         this._dropVec.push(new EnemyDropVO(146,4600,null,[new BaseDropVO(70151,1,100),new BaseDropVO(10000,2300,1000),new EquipDropVO(21022,2,300),new EquipDropVO(21022,3,150)]));
         this._dropVec.push(new EnemyDropVO(147,150,new BaseDropVO(10000,23,800),[new BaseDropVO(71131,1,10),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(148,160,new BaseDropVO(10000,23,800),[new BaseDropVO(71176,1,10),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(149,5600,null,[new BaseDropVO(70156,1,100),new BaseDropVO(10000,2400,1000),new EquipDropVO(21021,2,300),new EquipDropVO(21021,3,150)]));
         this._dropVec.push(new EnemyDropVO(150,160,new BaseDropVO(10000,24,800),[new BaseDropVO(71181,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(151,170,new BaseDropVO(10000,24,800),[new BaseDropVO(71161,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(152,6600,null,[new BaseDropVO(70161,1,100),new BaseDropVO(10000,2500,1000),new EquipDropVO(22023,2,300),new EquipDropVO(22023,3,150)]));
         this._dropVec.push(new EnemyDropVO(153,3000,null,[new ListDropVO([new BaseDropVO(13407,1,333),new BaseDropVO(13408,1,333),new BaseDropVO(13409,1,334)]),new ListDropVO([new BaseDropVO(13407,1,333),new BaseDropVO(13408,1,333),new BaseDropVO(13409,1,334)]),new ListDropVO([new BaseDropVO(13120,1,333),new BaseDropVO(13121,1,333),new BaseDropVO(13122,1,334)]),new ListDropVO([new BaseDropVO(13120,1,333),new BaseDropVO(13121,1,333),new BaseDropVO(13122,1,334)]),new BaseDropVO(50201,1,700),new BaseDropVO(50202,1,700),new BaseDropVO(50203,1,700),new BaseDropVO(50204,1,700),new BaseDropVO(13120,1,200),new BaseDropVO(13121,1,200),new BaseDropVO(13122,1,200),new BaseDropVO(16764,1,300)]));
         this._dropVec.push(new EnemyDropVO(154,170,new BaseDropVO(10000,25,800),[new BaseDropVO(71151,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(155,180,new BaseDropVO(10000,25,800),[new BaseDropVO(71171,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(156,7600,null,[new BaseDropVO(70166,1,100),new BaseDropVO(10000,2600,1000),new EquipDropVO(22025,2,300),new EquipDropVO(22025,3,150)]));
         this._dropVec.push(new EnemyDropVO(157,180,new BaseDropVO(10000,26,800),[new BaseDropVO(71181,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(158,190,new BaseDropVO(10000,26,800),[new BaseDropVO(71186,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(159,8600,null,[new BaseDropVO(70171,1,60),new BaseDropVO(10000,2700,1000),new BaseDropVO(70176,1,60),new BaseDropVO(70181,1,60),new EquipDropVO(22022,2,300),new EquipDropVO(22022,3,150)]));
         this._dropVec.push(new EnemyDropVO(160,200,new BaseDropVO(10000,27,800),[new BaseDropVO(71191,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(161,9600,null,[new BaseDropVO(70186,1,100),new BaseDropVO(10000,2800,1000),new EquipDropVO(22021,2,300),new EquipDropVO(22021,3,150)]));
         this._dropVec.push(new EnemyDropVO(162,3000,null,[new ListDropVO([new BaseDropVO(13410,1,333),new BaseDropVO(13411,1,333),new BaseDropVO(13412,1,334)]),new ListDropVO([new BaseDropVO(13410,1,333),new BaseDropVO(13411,1,333),new BaseDropVO(13412,1,334)]),new ListDropVO([new BaseDropVO(13130,1,333),new BaseDropVO(13131,1,333),new BaseDropVO(13132,1,334)]),new ListDropVO([new BaseDropVO(13130,1,333),new BaseDropVO(13131,1,333),new BaseDropVO(13132,1,334)]),new BaseDropVO(50205,1,700),new BaseDropVO(50206,1,700),new BaseDropVO(50207,1,700),new BaseDropVO(50208,1,700),new BaseDropVO(13130,1,200),new BaseDropVO(13131,1,200),new BaseDropVO(13132,1,200),new BaseDropVO(16765,1,300)]));
         this._dropVec.push(new EnemyDropVO(163,210,new BaseDropVO(10000,28,800),[new BaseDropVO(71196,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(164,220,new BaseDropVO(10000,27,800),[new BaseDropVO(71111,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(165,230,new BaseDropVO(10000,26,800),[new BaseDropVO(71116,1,15),new BaseDropVO(11003,1,30),new BaseDropVO(11007,1,15)]));
         this._dropVec.push(new EnemyDropVO(166,10600,null,[new BaseDropVO(70191,1,70),new BaseDropVO(70196,1,70),new BaseDropVO(10000,3000,1000)]));
         this._dropVec.push(new EnemyDropVO(184,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(20004,2,10),new EquipDropVO(20004,3,5)]));
         this._dropVec.push(new EnemyDropVO(185,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(21004,2,10),new EquipDropVO(21004,3,5)]));
         this._dropVec.push(new EnemyDropVO(186,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(22004,2,10),new EquipDropVO(22004,3,5)]));
         this._dropVec.push(new EnemyDropVO(187,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(20009,2,10),new EquipDropVO(20009,3,5)]));
         this._dropVec.push(new EnemyDropVO(188,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(21009,2,10),new EquipDropVO(21009,3,5)]));
         this._dropVec.push(new EnemyDropVO(189,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(22009,2,10),new EquipDropVO(22009,3,5)]));
         this._dropVec.push(new EnemyDropVO(173,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(20014,2,10),new EquipDropVO(20014,3,3)]));
         this._dropVec.push(new EnemyDropVO(174,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(21014,2,10),new EquipDropVO(21014,3,3)]));
         this._dropVec.push(new EnemyDropVO(175,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(22014,2,10),new EquipDropVO(22014,3,3)]));
         this._dropVec.push(new EnemyDropVO(176,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(20019,2,10),new EquipDropVO(20019,3,2)]));
         this._dropVec.push(new EnemyDropVO(177,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(21019,2,10),new EquipDropVO(21019,3,2)]));
         this._dropVec.push(new EnemyDropVO(178,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(22019,2,10),new EquipDropVO(22019,3,2)]));
         this._dropVec.push(new EnemyDropVO(180,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(20024,2,10),new EquipDropVO(20024,3,1)]));
         this._dropVec.push(new EnemyDropVO(181,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(21024,2,10),new EquipDropVO(21024,3,1)]));
         this._dropVec.push(new EnemyDropVO(182,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10),new EquipDropVO(22024,2,10),new EquipDropVO(22024,3,1)]));
         this._dropVec.push(new EnemyDropVO(183,1,new BaseDropVO(10000,5,800),[new BaseDropVO(11003,1,20),new BaseDropVO(11007,1,10)]));
         this._dropVec.push(new EnemyDropVO(190,3000,null,[new ListDropVO([new EquipDropVO(46053,3,250),new EquipDropVO(47053,3,250),new EquipDropVO(48053,3,250),new EquipDropVO(49053,3,250)]),new ListDropVO([new BaseDropVO(13413,1,333),new BaseDropVO(13414,1,333),new BaseDropVO(13415,1,334)]),new ListDropVO([new BaseDropVO(13413,1,333),new BaseDropVO(13414,1,333),new BaseDropVO(13415,1,334)]),new BaseDropVO(16766,1,600)]));
         this._dropVec.push(new EnemyDropVO(131,30,null,[new BaseDropVO(11002,1,50),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(132,500,null,[new BaseDropVO(11002,1,1000),new BaseDropVO(11006,1,1000),new BaseDropVO(13404,1,60),new BaseDropVO(13405,1,60),new BaseDropVO(13406,1,60),new BaseDropVO(16807,1,50),new BaseDropVO(16808,1,50),new BaseDropVO(16809,1,50)]));
         this._dropVec.push(new EnemyDropVO(133,1000,null,[new ListDropVO([new BaseDropVO(13404,1,300),new BaseDropVO(13405,1,300),new BaseDropVO(13406,1,300),new BaseDropVO(16763,1,100)]),new BaseDropVO(16763,1,300),new BaseDropVO(13404,1,150),new BaseDropVO(13405,1,150),new BaseDropVO(13406,1,150),new BaseDropVO(10000,25000,1000),new ListDropVO([new BaseDropVO(16807,1,333),new BaseDropVO(16808,1,333),new BaseDropVO(16809,1,334)]),new BaseDropVO(16807,1,100),new BaseDropVO(16808,1,100),new BaseDropVO(16809,1,100)]));
         this._dropVec.push(new EnemyDropVO(167,30,null,[new BaseDropVO(11002,1,50),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(168,500,null,[new BaseDropVO(11002,1,1000),new BaseDropVO(11006,1,1000),new BaseDropVO(13133,1,65),new BaseDropVO(13134,1,65),new BaseDropVO(13135,1,65),new BaseDropVO(16810,1,50),new BaseDropVO(16811,1,50),new BaseDropVO(16812,1,50)]));
         this._dropVec.push(new EnemyDropVO(169,1000,null,[new ListDropVO([new BaseDropVO(13133,1,333),new BaseDropVO(13134,1,333),new BaseDropVO(13135,1,334)]),new BaseDropVO(12008,1,100),new BaseDropVO(13133,1,150),new BaseDropVO(13134,1,150),new BaseDropVO(13135,1,150),new BaseDropVO(10000,30000,1000),new ListDropVO([new BaseDropVO(16810,1,333),new BaseDropVO(16811,1,333),new BaseDropVO(16812,1,334)]),new BaseDropVO(16810,1,100),new BaseDropVO(16811,1,100),new BaseDropVO(16812,1,100)]));
         this._dropVec.push(new EnemyDropVO(170,30,null,[new BaseDropVO(11002,1,50),new BaseDropVO(11006,1,50)]));
         this._dropVec.push(new EnemyDropVO(171,500,null,[new BaseDropVO(11002,1,1000),new BaseDropVO(11006,1,1000),new BaseDropVO(13136,1,50),new BaseDropVO(13137,1,50),new BaseDropVO(13138,1,50),new BaseDropVO(13139,1,50)]));
         this._dropVec.push(new EnemyDropVO(172,1000,null,[new ListDropVO([new BaseDropVO(13136,1,250),new BaseDropVO(13137,1,250),new BaseDropVO(13138,1,250),new BaseDropVO(13139,1,250)]),new BaseDropVO(13136,1,100),new BaseDropVO(13137,1,100),new BaseDropVO(13138,1,100),new BaseDropVO(13139,1,100),new BaseDropVO(10000,35000,1000)]));
         this._dropVec.push(new EnemyDropVO(137,6666,null,[new BaseDropVO(14000,1,300),new BaseDropVO(70951,1,110),new BaseDropVO(16121,1,1000),new BaseDropVO(16757,1,900),new BaseDropVO(10000,42,1000),new BaseDropVO(10000,100,1000),new BaseDropVO(10000,200,1000),new BaseDropVO(10000,300,1000),new BaseDropVO(10000,400,1000)]));
         this._dropVec.push(new EnemyDropVO(138,6666,null,[new BaseDropVO(14000,1,300),new BaseDropVO(70956,1,110),new BaseDropVO(16122,1,1000),new BaseDropVO(16755,1,600),new BaseDropVO(10000,42,1000),new BaseDropVO(10000,200,1000),new BaseDropVO(10000,300,1000),new BaseDropVO(10000,400,1000),new BaseDropVO(10000,500,1000)]));
         this._dropVec.push(new EnemyDropVO(139,6666,null,[new BaseDropVO(14000,1,300),new BaseDropVO(70961,1,110),new BaseDropVO(16123,1,1000),new BaseDropVO(16756,1,600),new BaseDropVO(10000,42,1000),new BaseDropVO(10000,300,1000),new BaseDropVO(10000,400,1000),new BaseDropVO(10000,500,1000),new BaseDropVO(10000,600,1000)]));
         this._dropVec.push(new EnemyDropVO(140,6666,null,[new BaseDropVO(14000,1,300),new BaseDropVO(70966,1,110),new BaseDropVO(16124,1,1000),new BaseDropVO(16759,1,800),new BaseDropVO(10000,42,1000),new BaseDropVO(10000,400,1000),new BaseDropVO(10000,500,1000),new BaseDropVO(10000,600,1000),new BaseDropVO(10000,700,1000)]));
         this._dropVec.push(new EnemyDropVO(141,6666,null,[new BaseDropVO(14000,1,300),new BaseDropVO(70971,1,110),new BaseDropVO(16125,1,1000),new BaseDropVO(16760,1,500),new BaseDropVO(10000,42,1000),new BaseDropVO(10000,500,1000),new BaseDropVO(10000,600,1000),new BaseDropVO(10000,700,1000),new BaseDropVO(10000,800,1000)]));
         this._dropVec.push(new EnemyDropVO(142,6666,null,[new BaseDropVO(14000,1,300),new BaseDropVO(70976,1,110),new BaseDropVO(16126,1,1000),new BaseDropVO(16762,1,900),new BaseDropVO(10000,42,1000),new BaseDropVO(10000,600,1000),new BaseDropVO(10000,700,1000),new BaseDropVO(10000,800,1000),new BaseDropVO(10000,900,1000)]));
         this._dropVec.push(new EnemyDropVO(143,6666,null,[new BaseDropVO(14000,1,300),new BaseDropVO(70981,1,110),new BaseDropVO(16127,1,1000),new BaseDropVO(16752,1,700),new BaseDropVO(10000,42,1000),new BaseDropVO(10000,700,1000),new BaseDropVO(10000,800,1000),new BaseDropVO(10000,900,1000),new BaseDropVO(10000,1000,1000)]));
         this._dropVec.push(new EnemyDropVO(201,8888,null,[new ListDropVO([new BaseDropVO(16801,1,333),new BaseDropVO(16802,1,333),new BaseDropVO(16803,1,334)]),new BaseDropVO(16801,1,60),new BaseDropVO(16802,1,60),new BaseDropVO(16803,1,60),new BaseDropVO(10000,3500,1000)]));
         this._dropVec.push(new EnemyDropVO(202,8888,null,[new ListDropVO([new BaseDropVO(16816,1,333),new BaseDropVO(16817,1,333),new BaseDropVO(16818,1,334)]),new BaseDropVO(16816,1,60),new BaseDropVO(16817,1,60),new BaseDropVO(16818,1,60),new BaseDropVO(10000,3600,1000)]));
         this._dropVec.push(new EnemyDropVO(203,8888,null,[new ListDropVO([new BaseDropVO(13141,1,500),new BaseDropVO(13142,1,500)]),new BaseDropVO(13141,1,60),new BaseDropVO(13142,1,60),new BaseDropVO(10000,3700,1000)]));
         this._dropVec.push(new EnemyDropVO(204,8888,null,[new ListDropVO([new BaseDropVO(13144,1,500),new BaseDropVO(13145,1,500)]),new BaseDropVO(13145,1,60),new BaseDropVO(13144,1,60),new BaseDropVO(10000,3800,1000)]));
         this._dropVec.push(new EnemyDropVO(205,8888,null,[new ListDropVO([new BaseDropVO(13550,1,500),new BaseDropVO(13556,1,500)]),new ListDropVO([new BaseDropVO(13553,1,333),new BaseDropVO(13554,1,334),new BaseDropVO(13555,1,333)]),new BaseDropVO(13550,1,30),new BaseDropVO(13553,1,30),new BaseDropVO(13554,1,30),new BaseDropVO(13555,1,30),new BaseDropVO(13556,1,30),new BaseDropVO(10000,3900,1000)]));
         this._dropVec.push(new EnemyDropVO(206,8888,null,[new ListDropVO([new BaseDropVO(13551,1,500),new BaseDropVO(13552,1,500)]),new BaseDropVO(13551,1,30),new BaseDropVO(13552,1,30),new BaseDropVO(10000,4000,1000)]));
         this._dropVec.push(new EnemyDropVO(207,8888,null,[new ListDropVO([new BaseDropVO(13557,1,500),new BaseDropVO(13558,1,500)]),new ListDropVO([new BaseDropVO(13559,1,500),new BaseDropVO(13560,1,500)]),new BaseDropVO(13557,1,30),new BaseDropVO(13558,1,30),new BaseDropVO(13559,1,30),new BaseDropVO(13560,1,30),new BaseDropVO(10000,4100,1000)]));
         this._dropVec.push(new EnemyDropVO(208,8888,null,[new ListDropVO([new BaseDropVO(13561,1,333),new BaseDropVO(13562,1,333),new BaseDropVO(13563,1,334)]),new ListDropVO([new BaseDropVO(13561,1,333),new BaseDropVO(13562,1,333),new BaseDropVO(13563,1,334)]),new BaseDropVO(13561,1,30),new BaseDropVO(13562,1,30),new BaseDropVO(13563,1,30),new BaseDropVO(10000,4200,1000)]));
         this._dropVec.push(new EnemyDropVO(209,8888,null,[new EquipDropVO(22124,4,30),new EquipDropVO(22125,4,30),new BaseDropVO(10000,4300,1000)]));
         this._dropVec.push(new EnemyDropVO(210,8888,null,[new EquipDropVO(22122,4,30),new EquipDropVO(22123,4,30),new BaseDropVO(10000,4400,1000)]));
         this._dropVec.push(new EnemyDropVO(211,8888,null,[new EquipDropVO(22121,4,50),new BaseDropVO(10000,4500,1000)]));
         this._dropVec.push(new EnemyDropVO(212,8888,null,[new BaseDropVO(10000,4500,1000),new BaseDropVO(13146,1,200)]));
         this._dropVec.push(new EnemyDropVO(251,6666,null,[new BaseDropVO(13327,1,700),new BaseDropVO(13327,1,200),new BaseDropVO(10000,2500,1000)]));
         this._dropVec.push(new EnemyDropVO(252,6666,null,[new BaseDropVO(13328,1,700),new BaseDropVO(13328,1,200),new BaseDropVO(10000,2500,1000)]));
         this._dropVec.push(new EnemyDropVO(253,6666,null,[new BaseDropVO(13329,1,700),new BaseDropVO(13329,1,200),new BaseDropVO(10000,2500,1000)]));
         this._dropVec.push(new EnemyDropVO(254,6666,null,[new BaseDropVO(13330,1,700),new BaseDropVO(13330,1,200),new BaseDropVO(10000,2500,1000)]));
         // 天宫关卡最终boss奖励 - 如来佛祖
         this._dropVec.push(new EnemyDropVO(30011,10000,null,[new BaseDropVO(70200,1,100),new BaseDropVO(10000,5000,1000),new BaseDropVO(14001,5,1000),new BaseDropVO(14002,3,1000),new BaseDropVO(13301,3,1000),new EquipDropVO(20025,4,800),new EquipDropVO(21025,4,800),new EquipDropVO(22025,4,800)]));
      }
      
      public function findEnemyDrop(param1:int) : EnemyDropVO
      {
         var _loc2_:EnemyDropVO = null;
         for each(_loc2_ in this._dropVec)
         {
            if(_loc2_.dropID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

