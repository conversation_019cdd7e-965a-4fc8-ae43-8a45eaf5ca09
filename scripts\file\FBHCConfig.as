package file
{
   import mogames.gameData.fabao.FBHCVO;
   import mogames.gameData.forge.NeedVO;
   
   public class FBHCConfig
   {
      private static var _instance:FBHCConfig;
      
      private var _hcVec:Vector.<FBHCVO>;
      
      public function FBHCConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : FBHCConfig
      {
         if(!_instance)
         {
            _instance = new FBHCConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._hcVec = new Vector.<FBHCVO>();
         this._hcVec.push(new FBHCVO(12001,23001,3000,[new NeedVO(13102,5),new NeedVO(13103,5),new NeedVO(13101,5)]));
         this._hcVec.push(new FBHCVO(12002,23002,1000,[new NeedVO(13104,5),new NeedVO(13101,5)]));
         this._hcVec.push(new FBHCVO(12003,23003,5000,[new NeedVO(13105,10),new NeedVO(13106,10)]));
         this._hcVec.push(new FBHCVO(12004,23004,10000,[new NeedVO(13105,5),new NeedVO(13107,5),new NeedVO(13101,5)]));
         this._hcVec.push(new FBHCVO(12005,23005,50000,[new NeedVO(13109,5),new NeedVO(13108,5)]));
         this._hcVec.push(new FBHCVO(12006,23008,5000,[new NeedVO(13103,5),new NeedVO(13104,5),new NeedVO(13105,3)]));
         this._hcVec.push(new FBHCVO(12007,23009,100000,[new NeedVO(13111,8),new NeedVO(13112,8),new NeedVO(13113,8)]));
         this._hcVec.push(new FBHCVO(12008,23010,200000,[new NeedVO(13133,30),new NeedVO(13134,30),new NeedVO(13135,30)]));
         this._hcVec.push(new FBHCVO(12009,23012,200000,[new NeedVO(14130,20),new NeedVO(14131,20),new NeedVO(14132,20)]));
         this._hcVec.push(new FBHCVO(12010,23013,300000,[new NeedVO(13140,20)]));
      }
      
      public function fincHCVO(param1:int) : FBHCVO
      {
         var _loc2_:FBHCVO = null;
         for each(_loc2_ in this._hcVec)
         {
            if(_loc2_.paperID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

