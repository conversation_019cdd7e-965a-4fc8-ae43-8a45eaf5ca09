package mogames.gameData.rank
{
   import mogames.gameData.role.HeroGameVO;
   
   public class RankMDEFHandler extends RankBRHandler
   {
      public function RankMDEFHandler()
      {
         super();
         rankIDs = [1722,1723,1724,1725,1726];
         _msg = "初始化魔防排行榜";
      }
      
      override protected function newScore(param1:HeroGameVO) : int
      {
         return param1.ownMDEF;
      }
   }
}

