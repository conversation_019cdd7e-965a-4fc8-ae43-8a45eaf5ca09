package mogames.gameMission.mission.npc.wuzhuangguan
{
   import citrus.objects.CitrusSprite;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.yaoyuan.vo.HerbGroudVO;
   import mogames.gameData.yaoyuan.vo.HerbGrowVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameRole.HeroManager;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.FilterFactory;
   
   public class GroundSprite extends CitrusSprite
   {
      private var _item:GroundItem;
      
      public function GroundSprite(param1:int, param2:HerbGroudVO)
      {
         super("ground",{
            "width":74,
            "height":66,
            "group":param1
         });
         touchable = true;
         this.createView(param2);
      }
      
      private function createView(param1:HerbGroudVO) : void
      {
         this._item = new GroundItem(param1);
         this._item.buttonMode = true;
         this._item.addEventListener(MouseEvent.MOUSE_OVER,this.onOver,false,0,true);
         this._item.addEventListener(MouseEvent.MOUSE_OUT,this.onOut,false,0,true);
         this._item.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.view = this._item;
      }
      
      public function initHerbVO(param1:HerbGrowVO) : void
      {
         this._item.initHerbVO(param1);
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         var _loc2_:Sprite = param1.target as Sprite;
         _loc2_.filters = [FilterFactory.getLightFilter()];
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         var _loc2_:Sprite = param1.target as Sprite;
         _loc2_.filters = [];
      }
      
      private function onDown(param1:MouseEvent) : void
      {
         if(Point.distance(new Point(HeroManager.onePlayer.x,HeroManager.onePlayer.y),new Point(x,y)) > 200)
         {
            MiniMsgMediator.instance().showAutoMsg("与药田距离太远！",480,200);
            EffectManager.instance().playAudio("ERROR");
            return;
         }
         EventManager.dispatchEvent(UIEvent.HERB_EVENT,{
            "type":"clickHerb",
            "data":this._item
         });
      }
      
      override public function destroy() : void
      {
         this._item.destroy();
         this._item = null;
         super.destroy();
      }
   }
}

