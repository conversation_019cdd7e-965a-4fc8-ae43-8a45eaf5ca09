package file
{
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.mall.vo.MallMoneyVO;
   import mogames.gameData.mall.vo.MallWeekVO;
   
   public class MallWeekConfig
   {
      private static var _instance:MallWeekConfig;
      
      public var list:Array;
      
      public function MallWeekConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : MallWeekConfig
      {
         if(!_instance)
         {
            _instance = new MallWeekConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.list = [];
         this.list[0] = new MallWeekVO(212,3,1140,new MallMoneyVO(3247,new BaseRewardVO(14062),328));
         this.list[1] = new MallWeekVO(213,5,210,new MallMoneyVO(3003,new BaseRewardVO(14061),104));
         this.list[2] = new MallWeekVO(214,5,792,new MallMoneyVO(3554,new BaseRewardVO(14108),380));
      }
   }
}

