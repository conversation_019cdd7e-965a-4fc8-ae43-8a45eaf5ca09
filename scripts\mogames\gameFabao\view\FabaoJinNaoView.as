package mogames.gameFabao.view
{
   import mogames.gameData.ConstRole;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoJinNao;
   
   public class FabaoJinNaoView extends FabaoBaseView
   {
      private var _fabao:FabaoJinNao;
      
      public function FabaoJinNaoView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         this.addSound(param1);
         super.updateCurrentFrame(param1);
         if(!mcATK)
         {
            return;
         }
         if(curStatus == ConstRole.SKILL_ONE)
         {
            this._fabao.dispatchSkillOne();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as FabaoJinNao;
      }
      
      override protected function addSound(param1:int) : void
      {
      }
   }
}

