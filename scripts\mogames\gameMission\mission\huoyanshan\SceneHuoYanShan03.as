package mogames.gameMission.mission.huoyanshan
{
   import flash.geom.Point;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.huoyundong.TrapHuoZhu;
   import mogames.gameMission.mission.huoyundong.TrapTieLian;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameSystem.CitrusTimer;
   
   public class SceneHuoYanShan03 extends BaseScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _arr0:Array = [121,398,661];
      
      private var _arr1:Array = [130,413,678];
      
      private var _huozhus:Array;
      
      private var _curs:Array;
      
      private var _huozhuTimer0:CitrusTimer;
      
      private var _huozhuTimer1:CitrusTimer;
      
      private var _tielianArg:Object = {
         "hurt":new Sint(180),
         "keepTime":new Snum(5)
      };
      
      private var _huozhuArg:Object = {
         "hurt":new Sint(250),
         "keepTime":new Snum(2),
         "interval":new Snum(10)
      };
      
      public function SceneHuoYanShan03(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.initEnemies();
         this.initHuoZhu();
      }
      
      private function initHuoZhu() : void
      {
         var _loc2_:TrapTieLian = null;
         var _loc3_:TrapHuoZhu = null;
         this._huozhus = [];
         this._curs = [];
         var _loc1_:int = 0;
         while(_loc1_ < 3)
         {
            _loc2_ = new TrapTieLian();
            _loc2_.x = this._arr0[_loc1_];
            _loc2_.y = 500;
            _loc2_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",2,5);
            _loc2_.createHitHurt(this._tielianArg.hurt.v,0,false);
            _loc3_ = new TrapHuoZhu();
            _loc3_.initArg(this._arr1[_loc1_],540,354);
            _loc3_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",5,5);
            _loc3_.createHitHurt(this._huozhuArg.hurt.v,0,false);
            _loc3_.relateTrap = _loc2_;
            add(_loc2_);
            add(_loc3_);
            this._huozhus[_loc1_] = _loc3_;
            _loc1_++;
         }
         this._huozhuTimer0 = new CitrusTimer();
         this._huozhuTimer1 = new CitrusTimer();
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v,this.showHuoZhu);
      }
      
      private function initEnemies() : void
      {
         if(_mission.hasMark("enemy17031"))
         {
            return;
         }
         this._eTrigger0 = new EnemyTrigger(_mission,[[270,280,150,100],[516,280,150,100]],17031,this.handlerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(783,465),-1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger0.start();
      }
      
      private function handlerEnd() : void
      {
         _mission.setMark("enemy17031");
      }
      
      private function showHuoZhu() : void
      {
         var _loc3_:int = 0;
         var _loc5_:TrapHuoZhu = null;
         this._curs = [];
         var _loc1_:int = Math.random() * 2 + 1;
         var _loc2_:Array = this._huozhus.slice();
         var _loc4_:int = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = Math.random() * _loc2_.length;
            this._curs[_loc4_] = _loc2_[_loc3_];
            _loc2_.splice(_loc3_,1);
            _loc4_++;
         }
         for each(_loc5_ in this._curs)
         {
            _loc5_.startMove();
         }
         this._huozhuTimer1.setTimeOut(this._huozhuArg.keepTime.v,this.resumeHuoZhu);
      }
      
      private function resumeHuoZhu() : void
      {
         var _loc1_:TrapHuoZhu = null;
         for each(_loc1_ in this._curs)
         {
            _loc1_.resume();
            _loc1_.relateTrap.showRed(this._tielianArg.keepTime.v);
         }
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v + this._tielianArg.keepTime.v,this.showHuoZhu);
      }
      
      override public function destroy() : void
      {
         this._curs = null;
         this._huozhus = null;
         this._huozhuTimer0.destroy();
         this._huozhuTimer1.destroy();
         this._huozhuTimer0 = null;
         this._huozhuTimer1 = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         this._eTrigger0 = null;
         this._pTrigger0 = null;
         super.destroy();
      }
   }
}

