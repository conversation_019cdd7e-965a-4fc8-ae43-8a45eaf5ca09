package mogames.gameFabao.fabao
{
   import flash.geom.Point;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.view.FabaoWeiBJSView;
   import mogames.gameObj.bullet.LocationBullet;
   import mogames.gameRole.hero.BaseHero;
   
   public class FabaoWeiBJS extends BaseFabao
   {
      public function FabaoWeiBJS(param1:GameFabaoVO)
      {
         super(param1,28,62);
         this.createFabaoData();
         createView(new FabaoWeiBJSView());
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(30),
            "hurt":new Sint(750)
         },{
            "cdTime":new Snum(30),
            "hurt":new Sint(845)
         },{
            "cdTime":new Snum(30),
            "hurt":new Sint(1098)
         },{
            "cdTime":new Snum(30),
            "hurt":new Sint(1428)
         },{
            "cdTime":new Snum(30),
            "hurt":new Sint(1856)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      public function addXuanFeng() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         var _loc1_:Point = new Point(x + dirX * 900,y);
         var _loc2_:LocationBullet = new LocationBullet(5,_loc1_,true,{
            "x":x,
            "y":y,
            "width":86,
            "height":86,
            "group":3
         });
         _loc2_.createView("EFFECT_WBJS_WIND_CLIP",86,86,0,0);
         _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",10,2);
         _loc2_.createHitHurt(_fabaoData.hurt.v,1,false,1,_fabaoVO.constFabao.wuxing.v);
         _loc2_.isFriend = owner is BaseHero;
         _loc2_.isHurtDead = false;
         Layers.addCEChild(_loc2_);
         EffectManager.instance().playAudio("XUAN_FENG");
      }
   }
}

