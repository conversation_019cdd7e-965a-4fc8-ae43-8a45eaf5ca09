package mogames.gameMission.mission.huangquanlu
{
   import file.EnemyDropConfig;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.drop.vo.BaseDropVO;
   import mogames.gameData.drop.vo.EnemyDropVO;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameObj.DropObject;
   import mogames.gameSystem.SysRender;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.MathUtil;
   
   public class SceneHuangQuanLu extends BaseScene
   {
      private var _trigger:HQLEnemyTrigger;
      
      private var _tangseng:MoveTangSeng;
      
      private var _isWin:Boolean;
      
      private var _args:Object = {
         "speed":0.5,
         "hp":200,
         "dis":250
      };
      
      public function SceneHuangQuanLu(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_BATTLE1");
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.layoutTangSeng();
         this.initTrigger();
         this._tangseng.start();
         SysRender.instance().add(this.checkWin);
         SysRender.instance().add(this.checkLose);
      }
      
      private function layoutTangSeng() : void
      {
         this._tangseng = new MoveTangSeng();
         Layers.addCEChild(this._tangseng);
         this._tangseng.x = 800;
         this._tangseng.y = 422;
         this._tangseng.owner = _mission.curTarget;
         this._tangseng.speed = this._args.speed;
         this._tangseng.maxCount = this._args.hp;
         this._tangseng.dis = this._args.dis;
      }
      
      private function initTrigger() : void
      {
         this.destroyTrigger();
         this._trigger = new HQLEnemyTrigger(_mission,[[252,212,1896,100]],this.waveID);
         this._trigger.showTip = false;
         this._trigger.start();
      }
      
      private function checkLose() : void
      {
         if(!this._tangseng.isDead && this._tangseng.x > 160)
         {
            return;
         }
         setRoleEnable(false);
         MissionManager.instance().confirmLose();
         this.destroyTrigger();
         this._tangseng.kill = true;
         SysRender.instance().remove(this.checkLose);
      }
      
      private function checkWin() : void
      {
         if(this._tangseng.isDead)
         {
            return;
         }
         if(this._tangseng.x < 2280)
         {
            return;
         }
         this.destroyTrigger();
         this.handlerWin();
         SysRender.instance().remove(this.checkWin);
      }
      
      private function handlerWin() : void
      {
         var _loc3_:Boolean = false;
         var _loc4_:BaseDropVO = null;
         var _loc5_:int = 0;
         var _loc6_:int = 0;
         EffectManager.instance().stopAllSound();
         MiniMsgMediator.instance().showAutoMsg("唐僧成功到达终点！",480,300,"SUCCESS1");
         this._tangseng.kill = true;
         var _loc1_:Rectangle = new Rectangle(1720,206,554,100);
         var _loc2_:EnemyDropVO = EnemyDropConfig.instance().findEnemyDrop(119);
         for each(_loc4_ in _loc2_.dropList)
         {
            if(_loc4_ && _loc4_.isDrop)
            {
               _loc3_ = true;
               _loc5_ = _loc1_.x + Math.random() * _loc1_.width;
               _loc6_ = _loc1_.y + Math.random() * _loc1_.height;
               Layers.addCEChild(new DropObject(_loc4_.newGood(),_loc5_,_loc6_,30));
               EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,_loc5_,_loc6_);
            }
         }
         addLeaveDoor(2000,400);
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         Layers.setKeyEnable(false);
         MissionManager.instance().handlerWin(false,true);
      }
      
      private function get waveID() : int
      {
         var _loc1_:int = (_mission.curTarget.roleVO as HeroGameVO).level.v;
         if(MathUtil.checkInRate(_loc1_,1,5))
         {
            return 610;
         }
         if(MathUtil.checkInRate(_loc1_,6,10))
         {
            return 611;
         }
         if(MathUtil.checkInRate(_loc1_,11,15))
         {
            return 612;
         }
         if(MathUtil.checkInRate(_loc1_,16,20))
         {
            return 613;
         }
         if(MathUtil.checkInRate(_loc1_,21,25))
         {
            return 614;
         }
         if(MathUtil.checkInRate(_loc1_,26,30))
         {
            return 615;
         }
         if(MathUtil.checkInRate(_loc1_,31,35))
         {
            return 616;
         }
         if(MathUtil.checkInRate(_loc1_,36,40))
         {
            return 617;
         }
         if(MathUtil.checkInRate(_loc1_,41,45))
         {
            return 618;
         }
         if(MathUtil.checkInRate(_loc1_,46,50))
         {
            return 619;
         }
         return 619;
      }
      
      private function destroyTrigger() : void
      {
         if(!this._trigger)
         {
            return;
         }
         this._trigger.removeListener();
         this._trigger.destroy();
         this._trigger = null;
      }
      
      override public function destroy() : void
      {
         SysRender.instance().remove(this.checkWin);
         SysRender.instance().remove(this.checkLose);
         this.destroyTrigger();
         super.destroy();
      }
   }
}

