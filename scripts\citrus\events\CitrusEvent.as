package citrus.events
{
   public class CitrusEvent
   {
      public static var CAPTURE_PHASE:int = 0;
      
      public static var AT_TARGET:int = 1;
      
      public static var BUBBLE_PHASE:int = 2;
      
      internal var _type:String;
      
      internal var _phase:int = CAPTURE_PHASE;
      
      internal var _bubbles:<PERSON>olean = true;
      
      internal var _cancelable:<PERSON>olean = false;
      
      internal var _target:CitrusEventDispatcher;
      
      internal var _currentTarget:CitrusEventDispatcher;
      
      internal var _currentListener:Function;
      
      public function CitrusEvent(param1:String, param2:Boolean = true, param3:<PERSON>olean = false)
      {
         super();
         this._type = param1;
         this._bubbles = param2;
         this._cancelable = param3;
      }
      
      protected function setTarget(param1:*) : void
      {
         this._target = param1;
      }
      
      public function clone() : CitrusEvent
      {
         var _loc1_:CitrusEvent = new CitrusEvent(this._type,this._bubbles,this._cancelable);
         _loc1_._target = _loc1_._currentTarget = this._currentTarget;
         return _loc1_;
      }
      
      public function get type() : String
      {
         return this._type;
      }
      
      public function get phase() : int
      {
         return this._phase;
      }
      
      public function get bubbles() : <PERSON><PERSON><PERSON>
      {
         return this._bubbles;
      }
      
      public function get cancelable() : Boolean
      {
         return this._cancelable;
      }
      
      public function get target() : CitrusEventDispatcher
      {
         return this._target;
      }
      
      public function get currentTarget() : CitrusEventDispatcher
      {
         return this._currentTarget;
      }
      
      public function get currentListener() : Function
      {
         return this._currentListener;
      }
      
      public function toString() : String
      {
         return "[CitrusEvent type:" + this._type + " target:" + Object(this._target).constructor + " currentTarget:" + Object(this._currentTarget).constructor + " phase:" + this._phase + " bubbles:" + this._bubbles + " cancelable:" + this._cancelable + " ]";
      }
   }
}

