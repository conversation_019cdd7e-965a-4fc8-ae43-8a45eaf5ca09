package mogames.gameData.petSkill.passive
{
   import mogames.gameData.petSkill.PetSkillVO;
   import mogames.gameData.role.HeroGameVO;
   import utils.TxtUtil;
   
   public class PetSkillRenXingVO extends PetSkillVO
   {
      private var _qualityArg:Array = [4,8,12,16,20];
      
      public function PetSkillRenXingVO(param1:int, param2:HeroGameVO)
      {
         super(param1,param2);
      }
      
      public function get addMDEF() : int
      {
         return level.v * this._qualityArg[curQuality.v];
      }
      
      override public function get tipInfor() : String
      {
         return constVO.infor + TxtUtil.setColor("<br>魔法防御+" + this.addMDEF,"99ff00");
      }
   }
}

