package mogames.gameMission.mission.luanshishan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.TrapClock;
   import mogames.gameObj.trap.TrapHeYe;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BOSSJiuTouChong;
   import mogames.gameRole.enemy.BOSSZhenJiuTou;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.dialog.StoryDialogModule;
   import utils.MathUtil;
   
   public class SceneLuanShiShan extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _pTrigger5:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _eTrigger4:EnemyTrigger;
      
      private var _fakeBOSS:BOSSJiuTouChong;
      
      private var _zhenBOSS:BOSSZhenJiuTou;
      
      private var _traps:Array;
      
      private var _heyes:Array;
      
      private var _heyeTimer:CitrusTimer;
      
      public function SceneLuanShiShan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER3");
         setWinPosition(new Rectangle(3108,170,480,140),new Point(3214,406));
         this._heyeTimer = new CitrusTimer(true);
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2040,1);
            TaskProxy.instance().addTask(11023);
         };
         _mission.cleanLoadUI();
         this.initTrap();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2040))
         {
            showDialog("STORY0066",func);
         }
      }
      
      private function initTrap() : void
      {
         var _loc2_:TrapClock = null;
         this._traps = [];
         var _loc1_:int = 0;
         while(_loc1_ < 7)
         {
            _loc2_ = new TrapClock();
            _loc2_.x = 197 + _loc1_ * 404;
            _loc2_.y = 180;
            _loc2_.initTimer(MathUtil.randomNum(7,10));
            _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",2,2);
            _loc2_.createHitHurt(1,0.5,false,0);
            _loc2_.createOtherBuff([new BuffVO(1004,5)]);
            Layers.addCEChild(_loc2_);
            this._traps[_loc1_] = _loc2_;
            _loc1_++;
         }
      }
      
      private function stopTrap() : void
      {
         var _loc1_:TrapClock = null;
         for each(_loc1_ in this._traps)
         {
            _loc1_.stop();
         }
      }
      
      private function initHeYe() : void
      {
         var _loc2_:TrapHeYe = null;
         this._heyes = [];
         var _loc1_:int = 0;
         while(_loc1_ < 8)
         {
            _loc2_ = new TrapHeYe(1,7);
            _loc2_.setLocation(2942 + _loc1_ * 122,546);
            Layers.addCEChild(_loc2_);
            this._heyes[_loc1_] = _loc2_;
            _loc1_++;
         }
      }
      
      private function activeHeYe() : void
      {
         this._heyeTimer.setInterval(10,0,this.updateHeYe,null,false);
         this._heyeTimer.startNone();
      }
      
      private function updateHeYe() : void
      {
         var _loc3_:int = 0;
         var _loc1_:Array = this._heyes.slice(1);
         var _loc4_:int = 0;
         while(_loc4_ < 2)
         {
            _loc3_ = int(Math.random() * _loc1_.length);
            _loc1_[_loc3_].startDisappear();
            _loc1_.splice(_loc3_,1);
            _loc4_++;
         }
      }
      
      private function stopHeYe() : void
      {
         var _loc1_:TrapHeYe = null;
         this._heyeTimer.pause();
         for each(_loc1_ in this._heyes)
         {
            _loc1_.stop();
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[282,306,150,100],[493,306,150,100]],18011,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[727,306,150,100],[1000,306,150,100]],18012,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1226,306,150,100],[1556,306,150,100]],18013,this.triggerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[1837,306,150,100],[2167,306,150,100]],18014,this.triggerEnd);
         this._eTrigger4 = new EnemyTrigger(_mission,[[1837,306,150,100],[2167,306,150,100]],18015,this.triggerEnd4);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(220,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(955,450),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1475,450),1,new Rectangle(440,0,1380,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2088,450),1,new Rectangle(1040,0,1380,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(2459,450),1,new Rectangle(1500,0,1380,600));
         this._pTrigger5 = new LocalTriggerX(_mission.onePlayer,new Point(3380,450),1,new Rectangle(2880,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,true);
         this._pTrigger5.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger4.trigger = this._eTrigger4;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this._pTrigger5.start;
         this._pTrigger5.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function startBattle() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2041,1);
            activeFailBOSS();
         };
         if(!FlagProxy.instance().isComplete(2041))
         {
            showDialog("STORY0067",func);
         }
         else
         {
            this.activeFailBOSS();
         }
      }
      
      private function triggerEnd4() : void
      {
         this.triggerEnd();
         this.addFakeBOSS();
         this.stopTrap();
         this.initHeYe();
      }
      
      private function triggerEnd0() : void
      {
         this.triggerEnd();
         CitrusLock.instance().lock(new Rectangle(0,0,1380,600),false,false,false,true);
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function addFakeBOSS() : void
      {
         this._fakeBOSS = new BOSSJiuTouChong();
         this._fakeBOSS.x = 3680;
         this._fakeBOSS.y = 285;
         this._fakeBOSS.initData(30321,30321);
         this._fakeBOSS.target = _mission.onePlayer;
         add(this._fakeBOSS);
         this._fakeBOSS.onRole.add(this.listenDead);
      }
      
      override protected function addBOSS() : void
      {
         this._zhenBOSS = new BOSSZhenJiuTou(new Rectangle(2880,0,960,600));
         this._zhenBOSS.setLocation(3356,200);
         this._zhenBOSS.x = 3356;
         this._zhenBOSS.y = 158;
         this._zhenBOSS.initData(30331,30331,85);
         this._zhenBOSS.target = _mission.onePlayer;
         add(this._zhenBOSS);
         setHeroEnable(true);
         this._zhenBOSS.activeEnemy(false);
         this._zhenBOSS.onRole.add(listenBOSS);
         this.activeHeYe();
      }
      
      private function activeFailBOSS() : void
      {
         setHeroEnable(true);
         this._fakeBOSS.activeEnemy(false);
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      private function listenDead(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(680,87),"野和尚有点能耐，我要认真了！","BODY_JIU_TOU_CHONG",this.addBOSS,2,false);
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11023);
         this.stopHeYe();
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._zhenBOSS,
            "rect":_dropRect,
            "hero":this._zhenBOSS.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         this._zhenBOSS = null;
         this._fakeBOSS = null;
         this._heyes = null;
         this._traps = null;
         this._heyeTimer.destroy();
         this._heyeTimer = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._eTrigger4)
         {
            this._eTrigger4.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         if(this._pTrigger5)
         {
            this._pTrigger5.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._eTrigger4 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._pTrigger5 = null;
         super.destroy();
      }
   }
}

