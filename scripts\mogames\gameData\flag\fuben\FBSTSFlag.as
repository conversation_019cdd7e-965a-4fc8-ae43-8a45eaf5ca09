package mogames.gameData.flag.fuben
{
   public class FBSTSFlag extends FBBaseFlag
   {
      public function FBSTSFlag(param1:int, param2:int)
      {
         super(param1,param2);
      }
      
      override public function get totalFree() : int
      {
         return _totalFree.v;
      }
      
      override public function get status() : String
      {
         return leftFree + "/" + this.totalFree;
      }
   }
}

