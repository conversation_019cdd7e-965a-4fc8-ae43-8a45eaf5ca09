package file
{
   import mogames.gameData.mall.vo.MallMoneyVO;
   
   public class BuyConfig
   {
      private static var _instance:BuyConfig;
      
      public var list:Vector.<MallMoneyVO>;
      
      public var bets:Vector.<MallMoneyVO>;
      
      public function BuyConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : BuyConfig
      {
         if(!_instance)
         {
            _instance = new BuyConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.list = new Vector.<MallMoneyVO>();
         this.list.push(new MallMoneyVO(2792,null,5));
         this.list.push(new MallMoneyVO(2812,null,5));
         this.list.push(new MallMoneyVO(2814,null,10));
         this.list.push(new MallMoneyVO(2813,null,3));
         this.list.push(new MallMoneyVO(2819,null,10));
         this.list.push(new MallMoneyVO(2793,null,10));
         this.list.push(new MallMoneyVO(2815,null,3));
         this.list.push(new MallMoneyVO(2832,null,6));
         this.list.push(new MallMoneyVO(2832,null,6));
         this.list.push(new MallMoneyVO(3098,null,5));
         this.list.push(new MallMoneyVO(3177,null,20));
         this.list.push(new MallMoneyVO(3182,null,15));
         this.list.push(new MallMoneyVO(3183,null,149));
         this.list.push(new MallMoneyVO(3271,null,20));
         this.list.push(new MallMoneyVO(3098,null,5));
         this.list.push(new MallMoneyVO(3271,null,20));
         this.bets = new Vector.<MallMoneyVO>();
         this.bets[this.bets.length] = new MallMoneyVO(3263,null,38);
         this.bets[this.bets.length] = new MallMoneyVO(3264,null,88);
         this.bets[this.bets.length] = new MallMoneyVO(3265,null,138);
         this.bets[this.bets.length] = new MallMoneyVO(3266,null,188);
         this.bets[this.bets.length] = new MallMoneyVO(3267,null,238);
         this.bets[this.bets.length] = new MallMoneyVO(3268,null,288);
         this.bets[this.bets.length] = new MallMoneyVO(3269,null,666);
         this.bets[this.bets.length] = new MallMoneyVO(3270,null,888);
      }
   }
}

