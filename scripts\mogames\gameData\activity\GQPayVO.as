package mogames.gameData.activity
{
   import file.GQPayConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   
   public class GQPayVO
   {
      private var _flagID:Sint;
      
      private var _need:Sint;
      
      private var _rewards:Array;
      
      public function GQPayVO(param1:int, param2:int, param3:Array)
      {
         super();
         this._flagID = new Sint(param1);
         this._need = new Sint(param2);
         this._rewards = param3;
      }
      
      public function setGet() : void
      {
         FlagProxy.instance().setValue(this.flagID,1);
      }
      
      public function get canGet() : Boolean
      {
         return GQPayConfig.instance().total >= this.need;
      }
      
      public function get hasGet() : Boolean
      {
         return FlagProxy.instance().isComplete(this.flagID);
      }
      
      public function get need() : int
      {
         return this._need.v;
      }
      
      public function get rewards() : Array
      {
         return this._rewards;
      }
      
      private function get flagID() : int
      {
         return this._flagID.v;
      }
   }
}

