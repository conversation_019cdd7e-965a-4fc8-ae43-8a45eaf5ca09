package mogames.gameMission.mission.pindingshan.scene
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameSystem.CitrusLock;
   
   public class ScenePingDingShan01 extends BaseScene
   {
      private var _eTrigger0:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _pTrigger1:LocalTriggerX;
      
      public function ScenePingDingShan01(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER3");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2012,1);
         };
         _mission.cleanLoadUI();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2012))
         {
            showDialog("STORY0023",func);
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,1200,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[85,300,150,100],[675,300,150,100]],9011,this.triggerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1107,300,150,100],[1613,300,150,100]],9012,this.triggerEnd1);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(470,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1335,450),1,new Rectangle(735,0,1200,600));
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd1() : void
      {
         this.triggerEnd();
         this.checkTask();
      }
      
      private function checkTask() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2018,1);
            TaskProxy.instance().addTask(11010);
         };
         if(FlagProxy.instance().isComplete(2018))
         {
            return;
         }
         showDialog("STORY0039",func);
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         super.destroy();
      }
   }
}

