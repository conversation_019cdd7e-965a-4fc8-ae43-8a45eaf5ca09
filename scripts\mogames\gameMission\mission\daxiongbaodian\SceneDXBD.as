package mogames.gameMission.mission.daxiongbaodian
{
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameObj.box2d.npc.BaseNPC;
   import mogames.gameUI.leiyinsi.ZBSJModule;
   import mogames.gameUI.mall.MallModule;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.MethodUtil;
   
   public class SceneDXBD extends BaseScene
   {
      private var _npc113:BaseNPC;
      
      private var _npc114:BaseNPC;
      
      private var _npc115:BaseNPC;
      
      private var _btnMall:SimpleButton;
      
      public function SceneDXBD(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DXBD");
         EventManager.addEventListener(UIEvent.NPC_EVENT,this.onNPC,false,0,true);
      }
      
      override protected function handlerInit() : void
      {
         this.initNPC();
         super.handlerInit();
         setHeroLock(true);
         this.initMallBtn();
      }
      
      private function initMallBtn() : void
      {
         this._btnMall = AssetManager.newButtonRes("BTN_MALL_CLIP",887,28);
         Layers.frameLayer.addChild(this._btnMall);
         this._btnMall.addEventListener(MouseEvent.CLICK,this.onMall,false,0,true);
      }
      
      private function removeMallBtn() : void
      {
         this._btnMall.removeEventListener(MouseEvent.CLICK,this.onMall);
         MethodUtil.removeMe(this._btnMall);
         this._btnMall = null;
      }
      
      private function onMall(param1:MouseEvent) : void
      {
         MallModule.instance().init();
      }
      
      private function initNPC() : void
      {
         this._npc113 = new BaseNPC(113,"MC_NPC_CLIP113",200,120);
         this._npc113.x = 638;
         this._npc113.y = 444;
         add(this._npc113);
         this._npc114 = new BaseNPC(114,"MC_NPC_CLIP114",200,120);
         this._npc114.x = 848;
         this._npc114.y = 420;
         add(this._npc114);
         this._npc115 = new BaseNPC(115,"MC_NPC_CLIP115",200,120);
         this._npc115.x = 1056;
         this._npc115.y = 444;
         add(this._npc115);
      }
      
      private function onNPC(param1:UIEvent) : void
      {
         if(param1.data.type != "onNPC")
         {
            return;
         }
         setHeroEnable(false);
         switch(param1.data.data)
         {
            case 113:
               showDialog("STORY_PU_XIAN");
               break;
            case 114:
               this.handlerFoZu();
               break;
            case 115:
               ZBSJModule.instance().init(enabledKey);
         }
      }
      
      private function handlerFoZu() : void
      {
         var func:Function = null;
         func = function():void
         {
            _mission.missionVO.setComplete();
            EffectManager.instance().addScreenEffect(16777215,1);
            PromptMediator.instance().showPrompt("妖岛已经开启！",null,null,true,"NEW_FUNCTION");
         };
         if(_mission.missionVO.isComplete())
         {
            showDialog("STORY_RU_LAI1");
         }
         else
         {
            showDialog("STORY_RU_LAI0",func);
         }
      }
      
      override public function destroy() : void
      {
         this.removeMallBtn();
         EventManager.removeEventListener(UIEvent.NPC_EVENT,this.onNPC);
         this._npc113 = null;
         this._npc114 = null;
         this._npc115 = null;
         super.destroy();
      }
   }
}

