package mogames.gameData.yaoyuan
{
   import mogames.gameData.yaoyuan.vo.HerbGrowVO;
   
   public class Herb<PERSON>roxy
   {
      private static var _instance:HerbProxy;
      
      public var list:Vector.<HerbGrowVO>;
      
      public function HerbProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : HerbProxy
      {
         if(!_instance)
         {
            _instance = new HerbProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.list = new Vector.<HerbGrowVO>();
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:* = null;
         var _loc4_:HerbGrowVO = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc3_ in _loc2_)
         {
            _loc4_ = new HerbGrowVO();
            _loc4_.loadData = _loc3_;
            if(_loc4_.index.v >= 1)
            {
               this.addGrow(_loc4_);
            }
         }
      }
      
      public function get saveData() : String
      {
         var _loc2_:HerbGrowVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.list)
         {
            _loc1_.push(_loc2_.saveData);
         }
         return _loc1_.join("T");
      }
      
      public function dailyRefresh() : void
      {
         var _loc1_:HerbGrowVO = null;
         for each(_loc1_ in this.list)
         {
            _loc1_.cuishu();
         }
      }
      
      public function addGrow(param1:HerbGrowVO) : void
      {
         this.list.push(param1);
      }
      
      public function handlerPick(param1:HerbGrowVO) : void
      {
         var _loc2_:int = int(this.list.indexOf(param1));
         if(_loc2_ != -1)
         {
            this.list.splice(_loc2_,1);
         }
      }
   }
}

