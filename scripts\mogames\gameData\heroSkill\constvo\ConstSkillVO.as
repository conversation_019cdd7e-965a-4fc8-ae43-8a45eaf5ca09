package mogames.gameData.heroSkill.constvo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.IconVO;
   import mogames.gameData.heroSkill.SkillLevelVO;
   
   public class ConstSkillVO extends IconVO
   {
      public var id:Sint;
      
      public var mcLabel:String;
      
      protected var _lvVec:Vector.<SkillLevelVO>;
      
      public function ConstSkillVO(param1:int, param2:String, param3:String, param4:String, param5:String)
      {
         super();
         this.id = new Sint(param1);
         name = param2;
         iname = param3;
         this.mcLabel = param4;
         infor = param5;
         this.initCondition();
      }
      
      protected function initCondition() : void
      {
         this._lvVec = new Vector.<SkillLevelVO>();
      }
      
      public function findlvVO(param1:int) : SkillLevelVO
      {
         var _loc2_:SkillLevelVO = null;
         for each(_loc2_ in this._lvVec)
         {
            if(_loc2_.level.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function countTotalGold(param1:int) : int
      {
         return 0;
      }
   }
}

