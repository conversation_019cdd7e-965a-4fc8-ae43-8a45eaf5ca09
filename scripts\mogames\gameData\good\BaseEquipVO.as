package mogames.gameData.good
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class BaseEquipVO extends GameGoodVO
   {
      public var baseHP:Sint;
      
      public var baseMP:Sint;
      
      public var baseATK:Sint;
      
      public var basePDEF:Sint;
      
      public var baseMDEF:Sint;
      
      public var baseCRIT:Sint;
      
      public var baseMISS:Sint;
      
      public var baseLUCK:Sint;
      
      public function BaseEquipVO(param1:ConstGoodVO)
      {
         super(param1);
         this.baseHP = new Sint();
         this.baseMP = new Sint();
         this.baseATK = new Sint();
         this.basePDEF = new Sint();
         this.baseMDEF = new Sint();
         this.baseCRIT = new Sint();
         this.baseMISS = new Sint();
         this.baseLUCK = new Sint();
      }
   }
}

