package mogames.gameMission.mission.liushahe
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.dialog.DialogVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameRole.enemy.BOSSShaSeng;
   import mogames.gameUI.dialog.DialogMediator;
   import mogames.gameUI.fuben.TZJS.TZJSEnterModule;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.MathUtil;
   
   public class SceneLiuShaHe extends BossScene
   {
      private var _bossTrigger:LocalTriggerX;
      
      private var loseWords:Array = ["出家人四大皆空。","一加一等于几？","我呸！"];
      
      public function SceneLiuShaHe(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER1");
         setWinPosition(new Rectangle(530,223,614,156),new Point(700,345));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2038,1);
         };
         _mission.cleanLoadUI();
         this._bossTrigger = new LocalTriggerX(_mission.onePlayer,new Point(780,432),1);
         this._bossTrigger.okFunc = this.showStory;
         this._bossTrigger.start();
         this.addBOSS();
         if(!FlagProxy.instance().isComplete(2038))
         {
            showDialog("STORY0064",func);
         }
      }
      
      private function showStory() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2039,1);
            startBattle();
         };
         if(TZJSEnterModule.TZJS_DIFF.v == 0 && !FlagProxy.instance().isComplete(2039))
         {
            showDialog("STORY0065",func);
         }
         else
         {
            this.startBattle();
         }
      }
      
      private function startBattle() : void
      {
         _boss.target = _mission.curTarget;
         startBossBattle("BGM_BATTLE0");
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSShaSeng();
         _boss.x = 1180;
         _boss.y = 290;
         this.checkDiff();
         add(_boss);
      }
      
      private function checkDiff() : void
      {
         var _loc1_:int = TZJSEnterModule.TZJS_DIFF.v;
         if(_loc1_ == 0)
         {
            _boss.initData(30311,30311);
         }
         else if(_loc1_ == 1)
         {
            _boss.initData(30312,30312,81);
         }
         else if(_loc1_ == 2)
         {
            _boss.initData(30313,30313,82);
         }
         else if(_loc1_ == 3)
         {
            _boss.initData(30314,30314,83);
         }
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         MissionManager.instance().handlerWin(false);
         _mission.missionVO.setComplete();
      }
      
      override protected function handlerWin() : void
      {
         Layers.lockGame = false;
         if(HeroProxy.instance().bonzeVO == null && TZJSEnterModule.TZJS_DIFF.v == 0)
         {
            this.startZhaoXiang();
         }
         else
         {
            this.showWinDrop();
         }
      }
      
      private function startZhaoXiang() : void
      {
         var okFunc:Function = null;
         okFunc = function():void
         {
            var _loc1_:Vector.<DialogVO> = new Vector.<DialogVO>();
            if(MathUtil.checkOdds(400))
            {
               _loc1_.push(new DialogVO(1,"沙僧","BODY_SHA_SENG0",loseWords[int(Math.random() * loseWords.length)]));
               DialogMediator.instance().init(_loc1_,loseDialogEnd);
            }
            else
            {
               _loc1_.push(new DialogVO(1,"沙僧","BODY_SHA_SENG4","啊，原来是取经高僧，有眼不识泰山，我在此等候多时了！"));
               DialogMediator.instance().init(_loc1_,winDialogEnd);
               HeroProxy.isNewHero = true;
            }
         };
         PromptMediator.instance().showPrompt("沙僧被打成虚弱状态，是否降服？\n（成功后成为队友）",okFunc,this.showWinDrop);
      }
      
      private function winDialogEnd() : void
      {
         HeroProxy.instance().unlockHero(1004);
         HeroProxy.instance().bonzeVO.setLevel(5);
         FlagProxy.instance().setValue(100400,5);
         _boss.visible = false;
         EffectManager.instance().addScreenEffect(16777215);
         MiniMsgMediator.instance().showAutoMsg("【沙僧】已加入队伍！",480,200,"NEW_FUNCTION");
         this.showWinDrop();
      }
      
      private function loseDialogEnd() : void
      {
         MiniMsgMediator.instance().showAutoMsg("降服失败！",480,300,"ZHAOMU_FAIL");
         this.showWinDrop();
      }
      
      private function showWinDrop() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         this._bossTrigger.destroy();
         this._bossTrigger = null;
         super.destroy();
      }
   }
}

