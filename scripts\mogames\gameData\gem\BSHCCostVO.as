package mogames.gameData.gem
{
   import file.GoodConfig;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.constvo.ConstGemVO;
   import mogames.gameData.shop.vo.BaseCostVO;
   
   public class BSHCCostVO extends BaseCostVO
   {
      public var price:Sint;
      
      public var fenchen:Sint;
      
      public var per:Sint;
      
      public var newID:Sint;
      
      public function BSHCCostVO(param1:int, param2:int, param3:int, param4:int, param5:int)
      {
         super(param1);
         this.newID = new Sint(param2);
         this.price = new Sint(param3);
         this.fenchen = new Sint(param4);
         this.per = new Sint(param5);
      }
      
      override public function useStuff() : void
      {
         MasterProxy.instance().changeGold(-this.price.v);
         BagProxy.instance().useItems(BagProxy.GOOD_FEN_CHEN,this.fenchen.v);
      }
      
      override public function get isLack() : LackVO
      {
         if(BagProxy.instance().getGoodNum(id.v) < 5)
         {
            return new LackVO("五个宝石方可合成一个新宝石！");
         }
         if(MasterProxy.instance().gameGold.v < this.price.v)
         {
            return new LackVO("铜钱不足！");
         }
         if(BagProxy.instance().getGoodNum(BagProxy.GOOD_FEN_CHEN) < this.fenchen.v)
         {
            return new LackVO("粉尘不足！");
         }
         return null;
      }
      
      public function get newConst() : ConstGemVO
      {
         return GoodConfig.instance().findConstGood(this.newID.v) as ConstGemVO;
      }
      
      public function get needPrice() : String
      {
         return this.price.v + "";
      }
      
      public function get needFenchen() : String
      {
         return this.fenchen.v + "";
      }
      
      public function get successRate() : String
      {
         return int(this.per.v * 0.1) + "%";
      }
   }
}

