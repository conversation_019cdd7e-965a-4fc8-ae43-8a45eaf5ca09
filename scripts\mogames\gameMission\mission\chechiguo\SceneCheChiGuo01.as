package mogames.gameMission.mission.chechiguo
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameObj.box2d.Door;
   import mogames.gameRole.enemy.BOSSHuDaXian;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class SceneCheChiGuo01 extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _door:CitrusMCSprite;
      
      private var _keyID:int = 10015;
      
      public function SceneCheChiGuo01(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_SUNSHINE");
         _dropRect = new Rectangle(3255,228,396,128);
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2027,1);
         };
         this.initDoor();
         _mission.cleanLoadUI();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2027))
         {
            showDialog("STORY0051",func);
         }
      }
      
      private function initDoor() : void
      {
         this._door = new CitrusMCSprite("MC_SQD_DOOR_CLIP",{
            "width":174,
            "height":184,
            "x":1918,
            "y":320,
            "group":2
         });
         this._door.addMouseListener(this.handlerOpenDoor);
         this._door.changeAnimation("idle");
         add(this._door);
      }
      
      private function handlerOpenDoor() : void
      {
         if(!BagProxy.instance().hasItem(this._keyID))
         {
            MiniMsgMediator.instance().showAutoMsg("需要【三清殿钥匙】方可打开！");
            return;
         }
         BagProxy.instance().delItemByID(this._keyID);
         (getObjectByName("door1302") as Door).setActive();
         this._door.changeAnimation("open",false);
         this._door.touchable = false;
         EffectManager.instance().playAudio("OPEN_LOCK");
         MiniMsgMediator.instance().showAutoMsg("失去了【三清殿钥匙】");
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[52,345,150,100],[448,345,150,100]],13011,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[692,345,150,100],[1110,345,150,100]],13012,this.handlerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1317,294,150,100],[1547,294,150,100]],13013,this.triggerEnd2);
         this._eTrigger3 = new EnemyTrigger(_mission,[[2257,294,150,100],[2485,294,150,100]],13014,this.triggerEnd3);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(300,480),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1004,480),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1695,480),1,new Rectangle(960,0,1400,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2477,480),1);
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(3030,480),1,new Rectangle(2400,0,1440,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function handlerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd0() : void
      {
         this.handlerEnd();
         CitrusLock.instance().lock(new Rectangle(0,0,1440,600),false,false,false,true);
      }
      
      private function triggerEnd2() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2028,1);
            TaskProxy.instance().addTask(11016);
         };
         this.handlerEnd();
         CitrusLock.instance().lock(new Rectangle(960,0,1920,600),false,false,false,true);
         if(!FlagProxy.instance().isComplete(2028))
         {
            showDialog("STORY0052",func);
         }
      }
      
      private function triggerEnd3() : void
      {
         this.handlerEnd();
         this.addBOSS();
      }
      
      private function startBattle() : void
      {
         startBossBattle("BGM_BATTLE0");
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHuDaXian();
         _boss.x = 3710;
         _boss.y = 250;
         _boss.initData(30191,30191,62);
         _boss.target = _mission.onePlayer;
         add(_boss);
      }
      
      override protected function handlerWin() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2029,1);
            showDrop();
            TaskProxy.instance().addTask(11017);
         };
         TaskProxy.instance().setComplete(11016);
         BagProxy.instance().addPileItem(10015);
         MiniMsgMediator.instance().showAutoMsg("获得三清殿钥匙！",480,200,"KEY_DROP");
         if(!FlagProxy.instance().isComplete(2029))
         {
            showDialog("STORY0053",func);
         }
         else
         {
            this.showDrop();
         }
      }
      
      private function showDrop() : void
      {
         unlock();
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         super.destroy();
      }
   }
}

