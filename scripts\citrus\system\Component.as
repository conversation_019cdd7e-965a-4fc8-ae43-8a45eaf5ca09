package citrus.system
{
   import citrus.core.CitrusObject;
   
   public class Component extends CitrusObject
   {
      public var entity:Entity;
      
      public function Component(param1:String, param2:Object = null)
      {
         if(param2 == null)
         {
            param2 = {"type":"component"};
         }
         else
         {
            param2["type"] = "component";
         }
         super(param1,param2);
      }
      
      override public function initialize(param1:Object = null) : void
      {
         super.initialize();
      }
      
      override public function destroy() : void
      {
         super.destroy();
      }
      
      override public function update(param1:Number) : void
      {
         super.update(param1);
      }
   }
}

