package mogames.gameData.shop.vo
{
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   
   public class BaseCostVO
   {
      public var id:Sint;
      
      public function BaseCostVO(param1:int)
      {
         super();
         this.id = new Sint(param1);
      }
      
      public function useStuff() : void
      {
      }
      
      public function get isLack() : LackVO
      {
         return null;
      }
      
      public function get needStr() : String
      {
         return "";
      }
   }
}

