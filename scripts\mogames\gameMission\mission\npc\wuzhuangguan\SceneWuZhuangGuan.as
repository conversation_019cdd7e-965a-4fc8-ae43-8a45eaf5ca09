package mogames.gameMission.mission.npc.wuzhuangguan
{
   import citrus.objects.CitrusSprite;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameObj.CitrusBSprite;
   import mogames.gameObj.box2d.npc.BaseNPC;
   import mogames.gameTask.Task11005;
   import mogames.gameUI.mall.MallModule;
   import mogames.gameUI.shop.ShopModule;
   import mogames.gameUI.tip.HelpModule;
   import utils.MethodUtil;
   
   public class SceneWuZhuangGuan extends BaseScene
   {
      private var _breakTree:CitrusSprite;
      
      private var _okTree:CitrusSprite;
      
      private var _task:Task11005;
      
      private var _npc105:BaseNPC;
      
      private var _yaoyuan:YaoYuanHandler;
      
      private var _help:CitrusBSprite;
      
      private var _btnMall:SimpleButton;
      
      public function SceneWuZhuangGuan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EventManager.addEventListener(UIEvent.NPC_EVENT,this.onNPC,false,0,true);
         EffectManager.instance().playBGM("BGM_WUZHUANGGUAN");
      }
      
      override protected function handlerInit() : void
      {
         this.layoutTree();
         this.layoutNPC();
         this.layoutYaoyuan();
         this.initTask();
         super.handlerInit();
         view.camera.center = new Point(0.5,0.5);
         setHeroLock(true);
         this.initMallBtn();
      }
      
      private function initMallBtn() : void
      {
         this._btnMall = AssetManager.newButtonRes("BTN_MALL_CLIP",887,28);
         Layers.frameLayer.addChild(this._btnMall);
         this._btnMall.addEventListener(MouseEvent.CLICK,this.onMall,false,0,true);
      }
      
      private function removeMallBtn() : void
      {
         this._btnMall.removeEventListener(MouseEvent.CLICK,this.onMall);
         MethodUtil.removeMe(this._btnMall);
         this._btnMall = null;
      }
      
      private function onMall(param1:MouseEvent) : void
      {
         MallModule.instance().init();
      }
      
      private function initTask() : void
      {
         if(this.isUnlock)
         {
            return;
         }
         this._task = new Task11005(this._npc105,this.updateTree,this.layoutYaoyuan);
      }
      
      private function layoutNPC() : void
      {
         this._npc105 = new BaseNPC(105,"MC_NPC_CLIP105",200,120);
         this._npc105.x = 380;
         this._npc105.y = 728;
         add(this._npc105);
         if(!this.isUnlock)
         {
            this._npc105.changeAnimation("angry",true);
         }
         this.updateNPC();
      }
      
      private function layoutTree() : void
      {
         this._breakTree = getObjectByName("breakTree") as CitrusSprite;
         this._okTree = getObjectByName("okTree") as CitrusSprite;
         this.updateTree();
      }
      
      private function layoutYaoyuan() : void
      {
         if(!this.isUnlock)
         {
            return;
         }
         if(!this._yaoyuan)
         {
            this._yaoyuan = new YaoYuanHandler();
         }
         this._yaoyuan.initYaoYuan();
         if(!this._help)
         {
            this._help = new CitrusBSprite("PIC_YAO_TIAN_JIE_SHAO",{
               "width":115,
               "height":103,
               "group":2
            });
         }
         this._help.x = 875;
         this._help.y = 618;
         add(this._help);
         this._help.addMouseListener(this.handlerHelp);
      }
      
      private function handlerHelp() : void
      {
         HelpModule.instance().showHelp("PIC_YAO_TIAN_INFOR",false);
      }
      
      private function updateTree() : void
      {
         this._okTree.visible = this.isUnlock;
         this._breakTree.visible = !this._okTree.visible;
         this.updateNPC();
      }
      
      private function updateNPC() : void
      {
         if(this.isUnlock && Boolean(this._npc105))
         {
            this._npc105.nearSound = "NEAR105";
         }
      }
      
      private function onNPC(param1:UIEvent) : void
      {
         if(param1.data.type != "onNPC")
         {
            return;
         }
         if(param1.data.data == 105)
         {
            if(!this.isUnlock)
            {
               this._task.handlerTask();
            }
            else
            {
               setHeroEnable(false);
               ShopModule.instance().init(100,enabledKey,"CLOSE105");
            }
         }
      }
      
      private function get isUnlock() : Boolean
      {
         return FlagProxy.instance().isComplete(103);
      }
      
      override public function destroy() : void
      {
         EventManager.removeEventListener(UIEvent.NPC_EVENT,this.onNPC);
         if(this._yaoyuan)
         {
            this._yaoyuan.destroy();
         }
         this._yaoyuan = null;
         this._task = null;
         this._breakTree = null;
         this._okTree = null;
         this._help = null;
         this.removeMallBtn();
         super.destroy();
      }
   }
}

