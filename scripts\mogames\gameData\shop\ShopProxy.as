package mogames.gameData.shop
{
   import mogames.gameData.base.LackVO;
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.shop.vo.BaseShopVO;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class ShopProxy
   {
      private static var _instance:ShopProxy;
      
      public function ShopProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : ShopProxy
      {
         if(!_instance)
         {
            _instance = new ShopProxy();
         }
         return _instance;
      }
      
      public function handlerBuy(param1:BaseShopVO, param2:int, param3:Function, param4:Boolean = true) : void
      {
         var _loc5_:int = param2 * param1.price.v;
         if(this.checkLack(param2,param1))
         {
            return;
         }
         param1.usePrice(param2);
         var _loc6_:GameGoodVO = param1.newGood(param2);
         if(_loc6_)
         {
            RewardHandler.instance().onReward([_loc6_]);
         }
         param3();
      }
      
      private function checkLack(param1:int, param2:BaseShopVO) : Boolean
      {
         var _loc3_:LackVO = param2.checkLack(param1);
         if(_loc3_ != null)
         {
            MiniMsgMediator.instance().showAutoMsg(_loc3_.str);
            return true;
         }
         return false;
      }
   }
}

