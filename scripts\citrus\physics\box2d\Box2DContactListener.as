package citrus.physics.box2d
{
   import Box2D.Collision.b2Manifold;
   import Box2D.Collision.b2WorldManifold;
   import Box2D.Dynamics.Contacts.b2Contact;
   import Box2D.Dynamics.b2ContactImpulse;
   import Box2D.Dynamics.b2ContactListener;
   
   public class Box2DContactListener extends b2ContactListener
   {
      private var _worldManifold:b2WorldManifold;
      
      public function Box2DContactListener()
      {
         super();
         this._worldManifold = new b2WorldManifold();
      }
      
      override public function BeginContact(param1:b2Contact) : void
      {
         var _loc2_:IBox2DPhysicsObject = param1.GetFixtureA().GetBody().GetUserData();
         var _loc3_:IBox2DPhysicsObject = param1.GetFixtureB().GetBody().GetUserData();
         if(!_loc2_ || !_loc3_)
         {
            return;
         }
         this._contactGetWorldManifoldValues(param1);
         if(_loc2_.beginContactCallEnabled)
         {
            _loc2_.handleBeginContact(param1);
         }
         if(_loc3_.beginContactCallEnabled)
         {
            _loc3_.handleBeginContact(param1);
         }
      }
      
      override public function EndContact(param1:b2Contact) : void
      {
         var _loc2_:IBox2DPhysicsObject = param1.GetFixtureA().GetBody().GetUserData();
         var _loc3_:IBox2DPhysicsObject = param1.GetFixtureB().GetBody().GetUserData();
         if(!_loc2_ || !_loc3_)
         {
            return;
         }
         this._contactGetWorldManifoldValues(param1);
         if(_loc2_.endContactCallEnabled)
         {
            _loc2_.handleEndContact(param1);
         }
         if(_loc3_.endContactCallEnabled)
         {
            _loc3_.handleEndContact(param1);
         }
      }
      
      override public function PreSolve(param1:b2Contact, param2:b2Manifold) : void
      {
         var _loc3_:IBox2DPhysicsObject = param1.GetFixtureA().GetBody().GetUserData();
         var _loc4_:IBox2DPhysicsObject = param1.GetFixtureB().GetBody().GetUserData();
         if(!_loc3_ || !_loc4_)
         {
            return;
         }
         this._contactGetWorldManifoldValues(param1);
         if(_loc3_.preContactCallEnabled)
         {
            _loc3_.handlePreSolve(param1,param2);
         }
         if(_loc4_.preContactCallEnabled)
         {
            _loc4_.handlePreSolve(param1,param2);
         }
      }
      
      override public function PostSolve(param1:b2Contact, param2:b2ContactImpulse) : void
      {
         var _loc3_:IBox2DPhysicsObject = param1.GetFixtureA().GetBody().GetUserData();
         var _loc4_:IBox2DPhysicsObject = param1.GetFixtureB().GetBody().GetUserData();
         if(!_loc3_ || !_loc4_)
         {
            return;
         }
         this._contactGetWorldManifoldValues(param1);
         if(_loc3_.postContactCallEnabled)
         {
            _loc3_.handlePostSolve(param1,param2);
         }
         if(_loc4_.postContactCallEnabled)
         {
            _loc4_.handlePostSolve(param1,param2);
         }
      }
      
      private function _contactGetWorldManifoldValues(param1:b2Contact) : void
      {
         param1.GetWorldManifold(this._worldManifold);
         param1.normal = this._worldManifold.m_normal;
         param1.contactPoints = this._worldManifold.m_points;
      }
   }
}

