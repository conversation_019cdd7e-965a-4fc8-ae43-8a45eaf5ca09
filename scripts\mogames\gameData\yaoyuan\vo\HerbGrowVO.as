package mogames.gameData.yaoyuan.vo
{
   import file.GoodConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.constvo.ConstSeedVO;
   
   public class HerbGrowVO
   {
      private static const GROW_GOLD_ARG:Sint = new Sint(200);
      
      public var index:Sint;
      
      private var _status:Sint;
      
      private var _zzID:Sint;
      
      public function HerbGrowVO(param1:int = 0)
      {
         super();
         this.index = new Sint();
         this._zzID = new Sint(param1);
         this._status = new Sint(1);
      }
      
      public function cuishu() : void
      {
         if(this.isChengShu)
         {
            return;
         }
         this._status.v += ConstData.DATA_NUM1.v;
      }
      
      public function get status() : int
      {
         return this._status.v;
      }
      
      public function get isYouMiao() : Boolean
      {
         return this._status.v == ConstData.DATA_NUM1.v;
      }
      
      public function get isChengZhang() : Boolean
      {
         return this._status.v == ConstData.DATA_NUM2.v;
      }
      
      public function get isChengShu() : Boolean
      {
         return this._status.v == ConstData.DATA_NUM3.v;
      }
      
      public function get growGold() : int
      {
         return (this.constSeed.quality.v + ConstData.DATA_NUM1.v) * GROW_GOLD_ARG.v;
      }
      
      public function get constSeed() : ConstSeedVO
      {
         return GoodConfig.instance().findConstGood(this._zzID.v) as ConstSeedVO;
      }
      
      public function get saveData() : String
      {
         return [this.index.v,this._zzID.v,this._status.v].join("H");
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc2_:Array = param1.split("H");
         this.index.v = int(_loc2_[0]);
         this._zzID.v = int(_loc2_[1]);
         this._status.v = int(_loc2_[2]);
      }
   }
}

