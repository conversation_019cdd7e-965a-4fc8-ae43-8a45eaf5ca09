package mogames.gameData.good.constvo
{
   import mogames.gameData.base.Sint;
   
   public class ConstSeedVO extends ConstGoodVO
   {
      public var herbID:Sint;
      
      public var skin:String;
      
      public function ConstSeedVO(param1:int, param2:int, param3:String, param4:String, param5:String, param6:int, param7:int, param8:int, param9:String)
      {
         super(param1,param3,param4,param6,param7,param8,param9);
         this.herbID = new Sint(param2);
         this.skin = param5;
      }
   }
}

