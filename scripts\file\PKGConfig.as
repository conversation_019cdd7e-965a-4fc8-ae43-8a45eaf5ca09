package file
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.drop.vo.EquipRewardVO;
   
   public class PKGConfig
   {
      private static var _instance:PKGConfig;
      
      private var _pkgs:Array;
      
      public function PKGConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : PKGConfig
      {
         if(!_instance)
         {
            _instance = new PKGConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._pkgs = [];
         this._pkgs.push([new Sint(14050),[new BaseRewardVO(16711,2),new BaseRewardVO(14150,10),new BaseRewardVO(14009,20)]]);
         this._pkgs.push([new Sint(14051),[new EquipRewardVO(35004,0),new BaseRewardVO(14150,10),new BaseRewardVO(14009,10),new BaseRewardVO(14101,5),new BaseRewardVO(16711,2)]]);
         this._pkgs.push([new Sint(14052),[new EquipRewardVO(34104,0),new BaseRewardVO(14150,20),new BaseRewardVO(13325,10),new BaseRewardVO(16305,6),new BaseRewardVO(16315,6),new BaseRewardVO(16325,6)]]);
         this._pkgs.push([new Sint(14053),[new BaseRewardVO(14101,20)]]);
         this._pkgs.push([new Sint(14054),[new BaseRewardVO(16411,3),new BaseRewardVO(16412,3),new BaseRewardVO(16413,3),new BaseRewardVO(16414,3),new BaseRewardVO(16415,3)]]);
         this._pkgs.push([new Sint(14055),[new BaseRewardVO(10007,1600)]]);
         this._pkgs.push([new Sint(14056),[new BaseRewardVO(16421,3),new BaseRewardVO(16422,3),new BaseRewardVO(16423,3),new BaseRewardVO(16424,3),new BaseRewardVO(16425,3)]]);
         this._pkgs.push([new Sint(14057),[new BaseRewardVO(14202,1),new BaseRewardVO(14212,1),new BaseRewardVO(14222,1)]]);
         this._pkgs.push([new Sint(14058),[new BaseRewardVO(14203,1),new BaseRewardVO(14213,1),new BaseRewardVO(14223,1)]]);
         this._pkgs.push([new Sint(14059),[new BaseRewardVO(14204,1),new BaseRewardVO(14214,1),new BaseRewardVO(14224,1)]]);
         this._pkgs.push([new Sint(14060),[new BaseRewardVO(14021,20),new BaseRewardVO(14039,20),new BaseRewardVO(14101,5)]]);
         this._pkgs.push([new Sint(14061),[new BaseRewardVO(16751,20),new BaseRewardVO(16753,5),new BaseRewardVO(16754,5)]]);
         this._pkgs.push([new Sint(14062),[new BaseRewardVO(16752,20),new BaseRewardVO(16760,10),new BaseRewardVO(16755,5),new BaseRewardVO(16756,5)]]);
         this._pkgs.push([new Sint(14063),[new BaseRewardVO(16757,30)]]);
         this._pkgs.push([new Sint(14064),[new BaseRewardVO(14205,1),new BaseRewardVO(14215,1),new BaseRewardVO(14225,1)]]);
         this._pkgs.push([new Sint(14065),[new EquipRewardVO(34111,3),new EquipRewardVO(35011,3)]]);
         this._pkgs.push([new Sint(14066),[new EquipRewardVO(34112,3),new EquipRewardVO(35012,3)]]);
         this._pkgs.push([new Sint(14067),[new EquipRewardVO(34113,3),new EquipRewardVO(35013,3)]]);
         this._pkgs.push([new Sint(14068),[new EquipRewardVO(34114,3),new EquipRewardVO(35014,3)]]);
         this._pkgs.push([new Sint(14112),[new EquipRewardVO(23014,4)]]);
         this._pkgs.push([new Sint(14176),[new EquipRewardVO(34115,3),new EquipRewardVO(35015,3)]]);
         this._pkgs.push([new Sint(14069),[new BaseRewardVO(16751,99)]]);
         this._pkgs.push([new Sint(14070),[new BaseRewardVO(14001,30),new BaseRewardVO(14002,20),new BaseRewardVO(14101,5)]]);
         this._pkgs.push([new Sint(14071),[new BaseRewardVO(16711,99)]]);
         this._pkgs.push([new Sint(14072),[new BaseRewardVO(16757,50)]]);
         this._pkgs.push([new Sint(14073),[new BaseRewardVO(14107,10)]]);
         this._pkgs.push([new Sint(14074),[new BaseRewardVO(14205,1),new BaseRewardVO(14215,1),new BaseRewardVO(14225,1)]]);
         this._pkgs.push([new Sint(14077),[new BaseRewardVO(16721,20),new BaseRewardVO(16722,10)]]);
         this._pkgs.push([new Sint(14078),[new EquipRewardVO(34111,3),new EquipRewardVO(35011,3),new EquipRewardVO(34114,3),new EquipRewardVO(35014,3)]]);
         this._pkgs.push([new Sint(14079),[new EquipRewardVO(34112,3),new EquipRewardVO(35012,3),new EquipRewardVO(34113,3),new EquipRewardVO(35013,3)]]);
         this._pkgs.push([new Sint(14080),[new BaseRewardVO(16501,5),new BaseRewardVO(16502,5),new BaseRewardVO(16503,5),new BaseRewardVO(16504,5),new BaseRewardVO(16505,5)]]);
         this._pkgs.push([new Sint(14081),[new EquipRewardVO(35001,0),new BaseRewardVO(14150,10),new BaseRewardVO(14009,10),new BaseRewardVO(14101,5),new BaseRewardVO(16711,2)]]);
         this._pkgs.push([new Sint(14082),[new EquipRewardVO(34101,0),new BaseRewardVO(14150,20),new BaseRewardVO(13325,10),new BaseRewardVO(16301,6),new BaseRewardVO(16311,6),new BaseRewardVO(16321,6)]]);
         this._pkgs.push([new Sint(14083),[new EquipRewardVO(35002,0),new BaseRewardVO(14150,10),new BaseRewardVO(14009,10),new BaseRewardVO(14101,5),new BaseRewardVO(16711,2)]]);
         this._pkgs.push([new Sint(14084),[new EquipRewardVO(34102,0),new BaseRewardVO(14150,20),new BaseRewardVO(13325,10),new BaseRewardVO(16302,6),new BaseRewardVO(16312,6),new BaseRewardVO(16322,6)]]);
         this._pkgs.push([new Sint(14085),[new EquipRewardVO(35003,0),new BaseRewardVO(14150,10),new BaseRewardVO(14009,10),new BaseRewardVO(14101,5),new BaseRewardVO(16711,2)]]);
         this._pkgs.push([new Sint(14086),[new EquipRewardVO(34103,0),new BaseRewardVO(14150,20),new BaseRewardVO(13325,10),new BaseRewardVO(16303,6),new BaseRewardVO(16313,6),new BaseRewardVO(16323,6)]]);
         this._pkgs.push([new Sint(14090),[new EquipRewardVO(46001,3),new EquipRewardVO(47001,3),new EquipRewardVO(48001,3),new EquipRewardVO(49001,3)]]);
         this._pkgs.push([new Sint(14091),[new EquipRewardVO(46002,3),new EquipRewardVO(47002,3),new EquipRewardVO(48002,3),new EquipRewardVO(49002,3)]]);
         this._pkgs.push([new Sint(14092),[new EquipRewardVO(46003,3),new EquipRewardVO(47003,3),new EquipRewardVO(48003,3),new EquipRewardVO(49003,3)]]);
         this._pkgs.push([new Sint(14093),[new EquipRewardVO(46004,3),new EquipRewardVO(47004,3),new EquipRewardVO(48004,3),new EquipRewardVO(49004,3)]]);
         this._pkgs.push([new Sint(14095),[new EquipRewardVO(46051,3),new EquipRewardVO(47051,3),new EquipRewardVO(48051,3),new EquipRewardVO(49051,3)]]);
         this._pkgs.push([new Sint(14096),[new BaseRewardVO(16204,1),new BaseRewardVO(16214,1),new BaseRewardVO(16224,1)]]);
         this._pkgs.push([new Sint(14097),[new BaseRewardVO(16304,1),new BaseRewardVO(16314,1),new BaseRewardVO(16324,1)]]);
         this._pkgs.push([new Sint(14098),[new BaseRewardVO(16404,1),new BaseRewardVO(16414,1),new BaseRewardVO(16424,1)]]);
         this._pkgs.push([new Sint(14099),[new BaseRewardVO(16504,1),new BaseRewardVO(16514,1),new BaseRewardVO(16524,1)]]);
         this._pkgs.push([new Sint(14100),[new BaseRewardVO(16604,1),new BaseRewardVO(16614,1),new BaseRewardVO(16624,1)]]);
         this._pkgs.push([new Sint(14108),[new BaseRewardVO(14150,99)]]);
         this._pkgs.push([new Sint(14109),[new EquipRewardVO(46052,3),new EquipRewardVO(47052,3),new EquipRewardVO(48052,3),new EquipRewardVO(49052,3)]]);
         this._pkgs.push([new Sint(14111),[new EquipRewardVO(46053,3),new EquipRewardVO(47053,3),new EquipRewardVO(48053,3),new EquipRewardVO(49053,3)]]);
         this._pkgs.push([new Sint(14124),[new BaseRewardVO(14120,20),new BaseRewardVO(14121,10),new BaseRewardVO(14125,5)]]);
         this._pkgs.push([new Sint(14165),[new EquipRewardVO(34121,3),new EquipRewardVO(35021,3)]]);
         this._pkgs.push([new Sint(14166),[new EquipRewardVO(34122,3),new EquipRewardVO(35022,3)]]);
         this._pkgs.push([new Sint(14167),[new EquipRewardVO(34123,3),new EquipRewardVO(35023,3)]]);
         this._pkgs.push([new Sint(14168),[new EquipRewardVO(34124,3),new EquipRewardVO(35024,3)]]);
         this._pkgs.push([new Sint(14169),[new EquipRewardVO(34125,3),new EquipRewardVO(35025,3)]]);
         this._pkgs.push([new Sint(14171),[new EquipRewardVO(34131,4),new EquipRewardVO(35031,4)]]);
         this._pkgs.push([new Sint(14172),[new EquipRewardVO(34132,4),new EquipRewardVO(35032,4)]]);
         this._pkgs.push([new Sint(14173),[new EquipRewardVO(34133,4),new EquipRewardVO(35033,4)]]);
         this._pkgs.push([new Sint(14174),[new EquipRewardVO(34134,4),new EquipRewardVO(35034,4)]]);
         this._pkgs.push([new Sint(14175),[new EquipRewardVO(34135,4),new EquipRewardVO(35035,4)]]);
         this._pkgs.push([new Sint(15100),[new BaseRewardVO(15295,1),new BaseRewardVO(15296,1),new BaseRewardVO(15297,1),new BaseRewardVO(15298,1)]]);
         this._pkgs.push([new Sint(15101),[new BaseRewardVO(15201,1),new BaseRewardVO(15202,1),new BaseRewardVO(15203,1),new BaseRewardVO(15204,1)]]);
         this._pkgs.push([new Sint(15102),[new BaseRewardVO(15206,1),new BaseRewardVO(15207,1),new BaseRewardVO(15208,1),new BaseRewardVO(15209,1)]]);
         this._pkgs.push([new Sint(15103),[new BaseRewardVO(15211,1),new BaseRewardVO(15212,1),new BaseRewardVO(15213,1),new BaseRewardVO(15214,1)]]);
         this._pkgs.push([new Sint(15104),[new BaseRewardVO(15216,1),new BaseRewardVO(15217,1),new BaseRewardVO(15218,1),new BaseRewardVO(15219,1)]]);
         this._pkgs.push([new Sint(15105),[new BaseRewardVO(15221,1),new BaseRewardVO(15222,1),new BaseRewardVO(15223,1),new BaseRewardVO(15224,1)]]);
         this._pkgs.push([new Sint(15106),[new BaseRewardVO(15226,1),new BaseRewardVO(15227,1),new BaseRewardVO(15228,1),new BaseRewardVO(15229,1)]]);
         this._pkgs.push([new Sint(15201),[new EquipRewardVO(34801,4),new EquipRewardVO(35901,4)]]);
         this._pkgs.push([new Sint(15202),[new EquipRewardVO(34802,4),new EquipRewardVO(35902,4)]]);
         this._pkgs.push([new Sint(15203),[new EquipRewardVO(34803,4),new EquipRewardVO(35903,4)]]);
         this._pkgs.push([new Sint(15204),[new EquipRewardVO(34804,4),new EquipRewardVO(35904,4)]]);
         this._pkgs.push([new Sint(15206),[new EquipRewardVO(34806,4),new EquipRewardVO(35906,4)]]);
         this._pkgs.push([new Sint(15207),[new EquipRewardVO(34807,4),new EquipRewardVO(35907,4)]]);
         this._pkgs.push([new Sint(15208),[new EquipRewardVO(34808,4),new EquipRewardVO(35908,4)]]);
         this._pkgs.push([new Sint(15209),[new EquipRewardVO(34809,4),new EquipRewardVO(35909,4)]]);
         this._pkgs.push([new Sint(15211),[new EquipRewardVO(34811,4),new EquipRewardVO(35911,4)]]);
         this._pkgs.push([new Sint(15212),[new EquipRewardVO(34812,4),new EquipRewardVO(35912,4)]]);
         this._pkgs.push([new Sint(15213),[new EquipRewardVO(34813,4),new EquipRewardVO(35913,4)]]);
         this._pkgs.push([new Sint(15214),[new EquipRewardVO(34814,4),new EquipRewardVO(35914,4)]]);
         this._pkgs.push([new Sint(15216),[new EquipRewardVO(34816,4),new EquipRewardVO(35916,4)]]);
         this._pkgs.push([new Sint(15217),[new EquipRewardVO(34817,4),new EquipRewardVO(35917,4)]]);
         this._pkgs.push([new Sint(15218),[new EquipRewardVO(34818,4),new EquipRewardVO(35918,4)]]);
         this._pkgs.push([new Sint(15219),[new EquipRewardVO(34819,4),new EquipRewardVO(35919,4)]]);
         this._pkgs.push([new Sint(15221),[new EquipRewardVO(34821,4),new EquipRewardVO(35921,4)]]);
         this._pkgs.push([new Sint(15222),[new EquipRewardVO(34822,4),new EquipRewardVO(35922,4)]]);
         this._pkgs.push([new Sint(15223),[new EquipRewardVO(34823,4),new EquipRewardVO(35923,4)]]);
         this._pkgs.push([new Sint(15224),[new EquipRewardVO(34824,4),new EquipRewardVO(35924,4)]]);
         this._pkgs.push([new Sint(15226),[new EquipRewardVO(34826,4),new EquipRewardVO(35926,4)]]);
         this._pkgs.push([new Sint(15227),[new EquipRewardVO(34827,4),new EquipRewardVO(35927,4)]]);
         this._pkgs.push([new Sint(15228),[new EquipRewardVO(34828,4),new EquipRewardVO(35928,4)]]);
         this._pkgs.push([new Sint(15229),[new EquipRewardVO(34829,4),new EquipRewardVO(35929,4)]]);
         this._pkgs.push([new Sint(15295),[new EquipRewardVO(34831,4),new EquipRewardVO(35931,4)]]);
         this._pkgs.push([new Sint(15296),[new EquipRewardVO(34832,4),new EquipRewardVO(35932,4)]]);
         this._pkgs.push([new Sint(15297),[new EquipRewardVO(34833,4),new EquipRewardVO(35933,4)]]);
         this._pkgs.push([new Sint(15298),[new EquipRewardVO(34834,4),new EquipRewardVO(35934,4)]]);
      }
      
      public function findReward(param1:int) : Array
      {
         var _loc2_:Array = null;
         for each(_loc2_ in this._pkgs)
         {
            if(_loc2_[0].v == param1)
            {
               return _loc2_[1];
            }
         }
         return [];
      }
   }
}

