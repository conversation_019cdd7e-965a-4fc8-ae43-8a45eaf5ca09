package mogames.gameMission.mission.pansidong
{
   import citrus.objects.CitrusSprite;
   import mogames.Layers;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameObj.trap.TrapSpiderDuWu;
   
   public class TrapPurpleSpider extends CitrusSprite
   {
      private var _mc:CitrusMC;
      
      private var _endFunc:Function;
      
      public function TrapPurpleSpider(param1:Function)
      {
         this._endFunc = param1;
         super("TrapPurpleSpider",{
            "width":88,
            "height":84,
            "group":2
         });
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("TRAP_PURPLE_SPIDER_CLIP"),this.onComplete,this.updateFrame);
         this._mc.changeAnimation("attack",false);
         this.view = this._mc;
      }
      
      private function updateFrame(param1:int) : void
      {
         if(param1 != 25)
         {
            return;
         }
         var _loc2_:TrapSpiderDuWu = new TrapSpiderDuWu();
         _loc2_.x = x;
         _loc2_.y = y + 330;
         _loc2_.initTimer(8);
         _loc2_.createHitEffect("","GUNHURT",2,2);
         _loc2_.createHitHurt(500,1,false,1);
         Layers.addCEChild(_loc2_);
      }
      
      private function onComplete(param1:String) : void
      {
         if(param1 == "attack")
         {
            this._endFunc();
            kill = true;
         }
      }
      
      override public function destroy() : void
      {
         this._endFunc = null;
         this._mc.destroy();
         this._mc = null;
         super.destroy();
      }
   }
}

