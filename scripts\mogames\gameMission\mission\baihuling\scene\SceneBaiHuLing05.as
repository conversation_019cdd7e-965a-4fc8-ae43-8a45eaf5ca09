package mogames.gameMission.mission.baihuling.scene
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.baihuling.BaiHuLingMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameRole.enemy.BOSSBaiGuJingBody;
   import mogames.gameRole.enemy.BOSSBaiGuJingFoot;
   import mogames.gameRole.enemy.BOSSBaiGuJingHead;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.TxtUtil;
   
   public class SceneBaiHuLing05 extends BossScene
   {
      protected var _eTrigger0:EnemyTrigger;
      
      protected var _eTrigger1:EnemyTrigger;
      
      protected var _pTrigger0:LocalTriggerX;
      
      protected var _pTrigger1:LocalTriggerX;
      
      protected var _bossTrigger:LocalTriggerX;
      
      private var _foot:BOSSBaiGuJingFoot;
      
      private var _body:BOSSBaiGuJingBody;
      
      private var _head:BOSSBaiGuJingHead;
      
      private var _waveList:Array;
      
      private var _npc0:CitrusMCSprite;
      
      private var _npc1:CitrusMCSprite;
      
      private var _npc2:CitrusMCSprite;
      
      public function SceneBaiHuLing05(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         this._waveList = [{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         }];
         setWinPosition(new Rectangle(2200,303,370,140),new Point(2390,406));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2015,1);
            TaskProxy.instance().addTask(11007);
         };
         this.createNPC();
         _mission.cleanLoadUI();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2015))
         {
            showDialog("STORY0028",func);
         }
      }
      
      private function createNPC() : void
      {
         this._npc0 = new CitrusMCSprite("MC_STONE_LAO_TOU_CLIP",{
            "width":83,
            "height":4,
            "x":2179,
            "y":349,
            "group":2
         });
         this._npc0.changeAnimation("stand",true);
         add(this._npc0);
         this._npc1 = new CitrusMCSprite("MC_STONE_LAO_TAI_CLIP",{
            "width":76,
            "height":110,
            "x":2051,
            "y":352,
            "group":2
         });
         this._npc1.changeAnimation("stand",true);
         add(this._npc1);
         this._npc2 = new CitrusMCSprite("MC_STONE_NV_HAI_CLIP",{
            "width":73,
            "height":98,
            "x":2330,
            "y":375,
            "group":2
         });
         this._npc2.changeAnimation("stand",true);
         add(this._npc2);
         if(_mission.hasMark("laotouDead"))
         {
            this._npc0.changeAnimation("break",true);
         }
         if(_mission.hasMark("laotaiDead"))
         {
            this._npc1.changeAnimation("break",true);
         }
         if(_mission.hasMark("nvhaiDead"))
         {
            this._npc2.changeAnimation("break",true);
         }
      }
      
      protected function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[415,330,152,100],[575,172,152,100],[780,330,152,100]],7051,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1500,114,150,100],[1600,330,150,100]],7052,this.triggerEnd1);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(287,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1330,450),1);
         this._bossTrigger = new LocalTriggerX(_mission.onePlayer,new Point(2350,450),1,new Rectangle(1620,0,1260,600));
         this._bossTrigger.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._bossTrigger.start;
         this._bossTrigger.okFunc = this.handlerStart;
         this._pTrigger0.start();
         this._bossTrigger.start();
      }
      
      private function triggerEnd0() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
         CitrusLock.instance().lock(new Rectangle(0,0,1920,600),false,false,false,true);
      }
      
      private function triggerEnd1() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
         this.addFoot();
      }
      
      private function addFoot() : void
      {
         this._foot = new BOSSBaiGuJingFoot();
         this._foot.x = 2700;
         this._foot.y = 218;
         this.countFootATT();
         this._foot.target = _mission.curTarget;
         add(this._foot);
         this._foot.onRole.addOnce(this.listenFootDead);
      }
      
      private function countFootATT() : void
      {
         var _loc1_:int = (_mission as BaiHuLingMission).destroyNum.v;
         if(_loc1_ == 0)
         {
            this._foot.initData(30071,30071);
         }
         else if(_loc1_ == 1)
         {
            this._foot.initData(30072,30071);
         }
         else if(_loc1_ == 2)
         {
            this._foot.initData(30073,30071);
         }
         else if(_loc1_ == 3)
         {
            this._foot.initData(30074,30071);
         }
      }
      
      private function addBody() : void
      {
         this._body = new BOSSBaiGuJingBody();
         this._body.x = this._foot.x;
         this._body.y = 244;
         this.countBodyATT();
         this._body.target = _mission.curTarget;
         add(this._body);
         this._body.activeEnemy(false);
         this._body.onRole.addOnce(this.listenBodyDead);
         this._body.isShowup = true;
         this._body.boneList = this._waveList;
      }
      
      private function countBodyATT() : void
      {
         var _loc1_:int = (_mission as BaiHuLingMission).destroyNum.v;
         if(_loc1_ == 0)
         {
            this._body.initData(30081,30081);
         }
         else if(_loc1_ == 1)
         {
            this._body.initData(30082,30081);
         }
         else if(_loc1_ == 2)
         {
            this._body.initData(30083,30081);
         }
         else if(_loc1_ == 3)
         {
            this._body.initData(30084,30081);
         }
      }
      
      private function addHead() : void
      {
         this._head = new BOSSBaiGuJingHead(new Rectangle(1977,53,834,420));
         this._head.x = this._body.x;
         this._head.y = this._body.y;
         this.countHeadATT();
         this._head.target = _mission.curTarget;
         add(this._head);
         this._head.activeEnemy();
         this._head.onRole.add(listenBOSS);
         this._head.isShowup = true;
      }
      
      private function countHeadATT() : void
      {
         var _loc1_:int = (_mission as BaiHuLingMission).destroyNum.v;
         if(_loc1_ == 0)
         {
            this._head.initData(30091,30091,15);
         }
         else if(_loc1_ == 1)
         {
            this._head.initData(30092,30091,15);
         }
         else if(_loc1_ == 2)
         {
            this._head.initData(30093,30091,15);
         }
         else if(_loc1_ == 3)
         {
            this._head.initData(30094,30091,15);
         }
      }
      
      private function listenFootDead(param1:String) : void
      {
         if(param1 != "onDead")
         {
            return;
         }
         this.addBody();
      }
      
      private function listenBodyDead(param1:String) : void
      {
         if(param1 != "onDead")
         {
            return;
         }
         this.addHead();
      }
      
      private function handlerStart() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2016,1);
            startBattle();
         };
         if(!FlagProxy.instance().isComplete(2016))
         {
            showDialog("STORY0029",func);
         }
         else
         {
            this.startBattle();
         }
      }
      
      private function startBattle() : void
      {
         setHeroEnable(true);
         this._foot.activeEnemy(false);
         this._foot.aiEnabled = true;
         EffectManager.instance().playBGM("BGM_BATTLE2");
         EffectManager.instance().playAudio("BAI_GU_JING_LAUGH");
         if(!_mission.missionVO.isComplete())
         {
            MiniMsgMediator.instance().showMsg("五行相克：" + TxtUtil.setColor("【木】","99ff00") + "克" + TxtUtil.setColor("【土】","ff7f00"),480,30);
         }
      }
      
      override protected function handlerWin() : void
      {
         MiniMsgMediator.clean();
         TaskProxy.instance().setComplete(11007);
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._head,
            "rect":_dropRect,
            "hero":this._head.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         MiniMsgMediator.clean();
         this._npc0 = null;
         this._npc1 = null;
         this._npc2 = null;
         this._foot = null;
         this._body = null;
         this._head = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._bossTrigger)
         {
            this._bossTrigger.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._bossTrigger = null;
         this._waveList = null;
         super.destroy();
      }
   }
}

