package mogames.gameData.role.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   
   public class RoleAIVO
   {
      public var id:Sint;
      
      public var hurtCount:Sint;
      
      public var stoicTime:Snum;
      
      public var lieTime:Snum;
      
      public var hurtX:Number;
      
      public var hurtY:Number;
      
      public var trickVec:Array;
      
      public var trickSeq:Array;
      
      public var isPatrol:Boolean;
      
      public var isReset:Boolean;
      
      public var isNext:Boolean;
      
      public function RoleAIVO(param1:int, param2:int, param3:Number, param4:Number, param5:Number, param6:Number, param7:Array, param8:Array, param9:Boolean, param10:Boolean = false, param11:Boolean = false)
      {
         super();
         this.id = new Sint(param1);
         this.hurtCount = new Sint(param2);
         this.stoicTime = new Snum(param3);
         this.lieTime = new Snum(param4);
         this.hurtX = param5;
         this.hurtY = param6;
         this.trickVec = param8;
         this.trickSeq = param7;
         this.isPatrol = param9;
         this.isReset = param10;
      }
      
      public function findTrick(param1:int) : RoleTrickVO
      {
         var _loc2_:RoleTrickVO = null;
         for each(_loc2_ in this.trickVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

