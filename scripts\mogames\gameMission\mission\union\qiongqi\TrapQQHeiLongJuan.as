package mogames.gameMission.mission.union.qiongqi
{
   import citrus.objects.CitrusSprite;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   import mogames.gameSystem.CitrusTimer;
   
   public class TrapQQHeiLongJuan extends CitrusSprite implements IRole
   {
      private var _mc:CitrusMC;
      
      private var _hitTargets:Array = [];
      
      private var _hitVO:BaseHitVO;
      
      private var _timer:CitrusTimer;
      
      private var _interval:Number;
      
      public function TrapQQHeiLongJuan()
      {
         super("TrapQQHeiLongJuan",{
            "width":200,
            "height":600,
            "group":2
         });
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("TRP_QIONG_QI_LONG_JUAN"),null,this.updateFrame);
         this._mc.changeAnimation("attack",true);
         this.view = this._mc;
         this._timer = new CitrusTimer();
      }
      
      public function initTimer(param1:Number) : void
      {
         var func:Function = null;
         var interval:Number = param1;
         func = function():void
         {
            kill = true;
         };
         this._interval = interval;
         this._timer.setTimeOut(this._interval,func);
      }
      
      public function createHitEffect(param1:String, param2:String, param3:int, param4:int) : void
      {
         this._hitVO.hurtSkin = param1;
         this._hitVO.hurtSound = param2;
         this._hitVO.hurtX = param3;
         this._hitVO.hurtY = param4;
      }
      
      public function createHitHurt(param1:int, param2:Number, param3:Boolean, param4:int = 1, param5:int = 0) : void
      {
         this._hitVO.hurt.v = param1;
         this._hitVO.hurtTime.v = param2;
         this._hitVO.atkType.v = param4;
         this._hitVO.WU_XING.v = param5;
      }
      
      private function updateFrame(param1:int) : void
      {
         if(!this._mc.mc.mcAreaATK)
         {
            this.cleanTargets();
            return;
         }
         this._hitVO.range = this._mc.mc.mcAreaATK;
         BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return this._hitTargets.indexOf(param1) != -1;
      }
      
      public function addTargets(param1:IRole) : void
      {
         this._hitTargets[this._hitTargets.length] = param1;
      }
      
      public function cleanTargets() : void
      {
         this._hitTargets.length = 0;
      }
      
      public function hitBack() : void
      {
      }
      
      override public function destroy() : void
      {
         this._timer.destroy();
         this._timer = null;
         this._mc.destroy();
         this._mc = null;
         this._hitVO = null;
         super.destroy();
      }
   }
}

