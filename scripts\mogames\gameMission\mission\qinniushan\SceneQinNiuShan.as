package mogames.gameMission.mission.qinniushan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.WinEffect;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.huoyundong.TrapHuoZhu;
   import mogames.gameMission.mission.huoyundong.TrapTieLian;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameRole.enemy.BOSSBiChenDaWang;
   import mogames.gameRole.enemy.BOSSBiHanDaWang;
   import mogames.gameRole.enemy.BOSSBiShuDaWang;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   
   public class SceneQinNiuShan extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _arr0:Array = [2032,2314,2582];
      
      private var _arr1:Array = [2053,2325,2597];
      
      private var _huozhus:Array;
      
      private var _curs:Array;
      
      private var _tielianArg:Object = {
         "hurt":new Sint(1000),
         "keepTime":new Snum(5)
      };
      
      private var _huozhuArg:Object = {
         "hurt":new Sint(1800),
         "keepTime":new Snum(2),
         "interval":new Snum(10)
      };
      
      private var _huozhuTimer0:CitrusTimer;
      
      private var _huozhuTimer1:CitrusTimer;
      
      private var _one:BOSSBiShuDaWang;
      
      private var _two:BOSSBiHanDaWang;
      
      private var _three:BOSSBiChenDaWang;
      
      public function SceneQinNiuShan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(3108,170,480,140),new Point(3214,390));
         EffectManager.instance().playBGM("BGM_DANGER0");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2057,1);
            TaskProxy.instance().addTask(11038);
         };
         super.handlerInit();
         this.initEnemies();
         this.initHuoZhu();
         if(!FlagProxy.instance().isComplete(2057))
         {
            showDialog("STORY0084",func);
         }
      }
      
      private function initHuoZhu() : void
      {
         var _loc1_:int = 0;
         var _loc2_:TrapTieLian = null;
         var _loc3_:TrapHuoZhu = null;
         this._huozhus = [];
         this._curs = [];
         _loc1_ = 0;
         while(_loc1_ < 3)
         {
            _loc2_ = new TrapTieLian();
            _loc2_.x = this._arr0[_loc1_];
            _loc2_.y = 500;
            _loc2_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",2,5);
            _loc2_.createHitHurt(this._tielianArg.hurt.v,0,false);
            _loc3_ = new TrapHuoZhu();
            _loc3_.initArg(this._arr1[_loc1_],570,400);
            _loc3_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",5,5);
            _loc3_.createHitHurt(this._huozhuArg.hurt.v,0,false);
            _loc3_.relateTrap = _loc2_;
            add(_loc2_);
            add(_loc3_);
            this._huozhus[_loc1_] = _loc3_;
            _loc1_++;
         }
         this._huozhuTimer0 = new CitrusTimer();
         this._huozhuTimer1 = new CitrusTimer();
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v,this.showHuoZhu);
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[90,284,150,100],[320,284,150,100]],30011,this.triggerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[680,284,150,100],[1112,288,150,100]],30012,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1424,284,150,100],[1746,284,150,100]],30013,this.triggerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[1990,284,150,100],[2638,284,150,100]],30014,null);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(568,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1026,450),1,new Rectangle(0,0,1440,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1700,450),1,new Rectangle(0,0,1920,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2296,450),1,new Rectangle(1920,0,960,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(3400,450),1,new Rectangle(2880,0,960,600));
         this._pTrigger1.setBlock(false,false,true,true);
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this.addBossOne;
         this._pTrigger4.okFunc = this.addBossLeft;
         this._pTrigger0.start();
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function showHuoZhu() : void
      {
         var _loc3_:int = 0;
         var _loc5_:TrapHuoZhu = null;
         this._curs = [];
         var _loc1_:int = Math.random() * 3 + 1;
         var _loc2_:Array = this._huozhus.slice();
         var _loc4_:int = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = Math.random() * _loc2_.length;
            this._curs[_loc4_] = _loc2_[_loc3_];
            _loc2_.splice(_loc3_,1);
            _loc4_++;
         }
         for each(_loc5_ in this._curs)
         {
            _loc5_.startMove();
         }
         this._huozhuTimer1.setTimeOut(this._huozhuArg.keepTime.v,this.resumeHuoZhu);
      }
      
      private function resumeHuoZhu() : void
      {
         var _loc1_:TrapHuoZhu = null;
         for each(_loc1_ in this._curs)
         {
            _loc1_.resume();
            _loc1_.relateTrap.showRed(this._tielianArg.keepTime.v);
         }
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v + this._tielianArg.keepTime.v,this.showHuoZhu);
      }
      
      private function addBossOne() : void
      {
         this._one = new BOSSBiShuDaWang();
         this._one.x = 2714;
         this._one.y = 242;
         this._one.initData(30481,30481);
         this._one.target = _mission.curTarget;
         add(this._one);
         this._one.activeEnemy(false);
         this._one.aiEnabled = true;
         this._one.onRole.add(this.listenOne);
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      private function listenOne(param1:String) : void
      {
         if(param1 != "onDead")
         {
            return;
         }
         this.triggerEnd();
         this._pTrigger4.start();
      }
      
      private function addBossLeft() : void
      {
         this._two = new BOSSBiHanDaWang();
         this._two.x = 3645;
         this._two.y = 236;
         this._two.initData(30491,30491);
         this._two.target = _mission.curTarget;
         add(this._two);
         this._two.activeEnemy(false);
         this._two.aiEnabled = true;
         this._two.onRole.add(this.listenWin);
         this._three = new BOSSBiChenDaWang();
         this._three.x = 3545;
         this._three.y = 236;
         this._three.initData(30501,30501,159);
         this._three.target = _mission.curTarget;
         add(this._three);
         this._three.activeEnemy(false);
         this._three.aiEnabled = true;
         this._three.onRole.add(this.listenWin);
         _boss = this._three;
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      private function listenWin(param1:String) : void
      {
         if(param1 != "onDead")
         {
            return;
         }
         if(!this._two.isDead || !this._three.isDead)
         {
            return;
         }
         Layers.lockGame = true;
         EffectManager.instance().stopAllSound();
         var _loc2_:WinEffect = new WinEffect();
         _loc2_.start();
         _winTimer.setTimeOut(3,this.handlerWin);
         _isWin = true;
      }
      
      override protected function handlerWin() : void
      {
         this.destroyHuoZhu();
         TaskProxy.instance().setComplete(11038);
         super.handlerWin();
      }
      
      private function destroyHuoZhu() : void
      {
         var _loc1_:TrapHuoZhu = null;
         this._huozhuTimer0.pause();
         this._huozhuTimer1.pause();
         for each(_loc1_ in this._huozhus)
         {
            _loc1_.resume();
            _loc1_.relateTrap.resume();
         }
      }
      
      override public function destroy() : void
      {
         this._curs = null;
         this._huozhus = null;
         this._huozhuTimer0.destroy();
         this._huozhuTimer1.destroy();
         this._huozhuTimer0 = null;
         this._huozhuTimer1 = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._one = null;
         this._two = null;
         this._three = null;
         super.destroy();
      }
   }
}

