package mogames.gameMission.mission.npc.wuzhuangguan
{
   import flash.display.MovieClip;
   import mogames.gameData.yaoyuan.vo.HerbGroudVO;
   import mogames.gameData.yaoyuan.vo.HerbGrowVO;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameUI.item.BaseTipItem;
   
   public class GroundItem extends BaseTipItem
   {
      private var ui:MovieClip;
      
      private var _herbView:CitrusMC;
      
      public var groundVO:HerbGroudVO;
      
      public var herbVO:HerbGrowVO;
      
      public function GroundItem(param1:HerbGroudVO)
      {
         super();
         this.groundVO = param1;
         this.ui = GameInLoader.instance().findAsset("UI_GROUND_ITEM") as MovieClip;
         addChild(this.ui);
         this.ui.mcNum.gotoAndStop(this.groundVO.index.v);
         this._herbView = new CitrusMC();
         this.ui.mcHerb.addChild(this._herbView);
         this.updateGround();
         this.initHerbVO(null);
         this.mouseChildren = false;
         tipID = 4000;
      }
      
      public function initHerbVO(param1:HerbGrowVO) : void
      {
         this.herbVO = param1;
         oneTipVO = param1;
         this.cleanItem();
         if(!param1)
         {
            return;
         }
         this.herbVO.index.v = this.groundVO.index.v;
         this._herbView.setupMC(GameInLoader.instance().findAsset(param1.constSeed.skin));
         this.updateHerb();
         this.ui.mcTip.visible = false;
      }
      
      public function handlerCuiShu() : void
      {
         if(!this.herbVO)
         {
            return;
         }
         this.herbVO.cuishu();
         this.updateHerb();
      }
      
      public function updateGround() : void
      {
         this.ui.mcBG.gotoAndStop(1);
         if(!this.groundVO)
         {
            return;
         }
         if(this.groundVO.isOpen)
         {
            this.ui.mcBG.gotoAndStop(2);
         }
         else
         {
            this.ui.mcBG.gotoAndStop(1);
         }
         this.updateTip();
      }
      
      private function updateHerb() : void
      {
         if(!this.herbVO)
         {
            return;
         }
         this.setPick(false);
         if(this.herbVO.isYouMiao)
         {
            this._herbView.changeAnimation("youmiao",true);
         }
         else if(this.herbVO.isChengZhang)
         {
            this._herbView.changeAnimation("chengzhang",true);
         }
         else if(this.herbVO.isChengShu)
         {
            this._herbView.changeAnimation("chengshu",true);
            this.setPick(true);
         }
      }
      
      private function updateTip() : void
      {
         this.ui.mcTip.visible = false;
         this.ui.mcTip.stop();
         if(Boolean(this.groundVO) && !this.groundVO.isOpen)
         {
            this.showTip(1);
         }
         else if(!this.herbVO)
         {
            this.showTip(2);
         }
      }
      
      private function cleanItem() : void
      {
         this._herbView.init();
         this.setPick(false);
         this.updateTip();
      }
      
      private function showTip(param1:int) : void
      {
         this.ui.mcTip.gotoAndStop(param1);
         this.ui.mcTip.visible = true;
      }
      
      private function setPick(param1:Boolean) : void
      {
         this.ui.mcHand.visible = param1;
         if(param1)
         {
            this.ui.mcHand.play();
         }
         else
         {
            this.ui.mcHand.stop();
         }
      }
      
      public function get isPlant() : Boolean
      {
         return this.herbVO != null;
      }
      
      override public function destroy() : void
      {
         this._herbView.destroy();
         this._herbView = null;
         super.destroy();
      }
   }
}

