package mogames.gameData.role
{
   import mogames.gameData.heroSkill.BaseSkillVO;
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.heroSkill.sunwukong.SkillHYJJVO;
   import mogames.gameData.heroSkill.sunwukong.SkillQJSLVO;
   
   public class HeroMonkeyVO extends HeroGameVO
   {
      public function HeroMonkeyVO()
      {
         allSkills = [];
         allSkills.push(new HurtSkillVO(1001,this,0));
         allSkills.push(new SkillQJSLVO(1002,this,1));
         allSkills.push(new SkillHYJJVO(1003,this,2));
         allSkills.push(new HurtSkillVO(1004,this,3));
         allSkills.push(new HurtSkillVO(1005,this,4));
         updateKeySkills();
         teamSequence = 1;
         super(1001);
      }
      
      public function setFreshman() : void
      {
         var _loc1_:BaseSkillVO = null;
         setLevel(99);
         for each(_loc1_ in allSkills)
         {
            _loc1_.setLevel(5);
         }
      }
      
      public function resumeFreahman() : void
      {
         var _loc1_:BaseSkillVO = null;
         setLevel(1);
         for each(_loc1_ in allSkills)
         {
            _loc1_.setLevel(0);
         }
      }
   }
}

