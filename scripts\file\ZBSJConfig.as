package file
{
   import mogames.gameData.forge.NeedVO;
   import mogames.gameData.leiyinsi.ZBSJVO;
   
   public class ZBSJConfig
   {
      private static var _instance:ZBSJConfig;
      
      private var _list:Array;
      
      public function ZBSJConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : ZBSJConfig
      {
         if(!_instance)
         {
            _instance = new ZBSJConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new ZBSJVO(20021,20121,200000,[new NeedVO(13550,10),new NeedVO(13556,10),new NeedVO(13551,10)]);
         this._list[this._list.length] = new ZBSJVO(20022,20122,200000,[new NeedVO(13550,10),new NeedVO(13556,10),new NeedVO(13552,10)]);
         this._list[this._list.length] = new ZBSJVO(20023,20123,200000,[new NeedVO(13550,10),new NeedVO(13556,10),new NeedVO(13553,10)]);
         this._list[this._list.length] = new ZBSJVO(20024,20124,200000,[new NeedVO(13550,10),new NeedVO(13556,10),new NeedVO(13554,10)]);
         this._list[this._list.length] = new ZBSJVO(20025,20125,200000,[new NeedVO(13550,10),new NeedVO(13556,10),new NeedVO(13555,10)]);
         this._list[this._list.length] = new ZBSJVO(21021,21121,300000,[new NeedVO(13557,10),new NeedVO(13558,10),new NeedVO(13561,10)]);
         this._list[this._list.length] = new ZBSJVO(21022,21122,300000,[new NeedVO(13557,10),new NeedVO(13558,10),new NeedVO(13559,10)]);
         this._list[this._list.length] = new ZBSJVO(21023,21123,300000,[new NeedVO(13557,10),new NeedVO(13558,10),new NeedVO(13562,10)]);
         this._list[this._list.length] = new ZBSJVO(21024,21124,300000,[new NeedVO(13557,10),new NeedVO(13558,10),new NeedVO(13563,10)]);
         this._list[this._list.length] = new ZBSJVO(21025,21125,300000,[new NeedVO(13557,10),new NeedVO(13558,10),new NeedVO(13560,10)]);
      }
      
      public function findVO(param1:int) : ZBSJVO
      {
         var _loc2_:ZBSJVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.oldID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

