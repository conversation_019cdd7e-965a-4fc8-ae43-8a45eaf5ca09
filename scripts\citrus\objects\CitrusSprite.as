package citrus.objects
{
   import citrus.core.CitrusObject;
   import citrus.math.MathVector;
   import citrus.view.ICitrusArt;
   import citrus.view.ISpriteView;
   import citrus.view.spriteview.SpriteDebugArt;
   import flash.display.Bitmap;
   import flash.utils.Dictionary;
   import mogames.AssetManager;
   import org.osflash.signals.Signal;
   
   public class CitrusSprite extends CitrusObject implements ISpriteView
   {
      public var collisions:Dictionary = new Dictionary();
      
      public var onCollide:Signal = new Signal(CitrusSprite,CitrusSprite,MathVector,Number);
      
      public var onPersist:Signal = new Signal(CitrusSprite,CitrusSprite,MathVector);
      
      public var onSeparate:Signal = new Signal(CitrusSprite,CitrusSprite);
      
      protected var _x:Number = 0;
      
      protected var _y:Number = 0;
      
      protected var _width:Number = 30;
      
      protected var _height:Number = 30;
      
      protected var _velocity:MathVector = new MathVector();
      
      protected var _parallaxX:Number = 1;
      
      protected var _parallaxY:Number = 1;
      
      protected var _rotation:Number = 0;
      
      protected var _group:uint = 0;
      
      protected var _visible:Boolean = true;
      
      protected var _touchable:Boolean = false;
      
      protected var _view:* = SpriteDebugArt;
      
      protected var _art:ICitrusArt;
      
      protected var _inverted:Boolean = false;
      
      protected var _animation:String = "";
      
      protected var _offsetX:Number = 0;
      
      protected var _offsetY:Number = 0;
      
      protected var _registration:String = "topLeft";
      
      public function CitrusSprite(param1:String, param2:Object = null)
      {
         super(param1,param2);
      }
      
      public function handleArtReady(param1:ICitrusArt) : void
      {
         this._art = param1;
      }
      
      public function handleArtChanged(param1:ICitrusArt) : void
      {
      }
      
      override public function destroy() : void
      {
         if(this._view is Bitmap)
         {
            this.disposeBitmap(this._view as Bitmap);
         }
         this.onCollide.removeAll();
         this.onPersist.removeAll();
         this.onSeparate.removeAll();
         this.collisions = null;
         this._art = null;
         super.destroy();
      }
      
      private function disposeBitmap(param1:Bitmap) : void
      {
         if(!AssetManager.noCache(param1.bitmapData))
         {
            return;
         }
         param1.bitmapData.dispose();
      }
      
      public function getBody() : *
      {
         return null;
      }
      
      public function get x() : Number
      {
         return this._x;
      }
      
      public function set x(param1:Number) : void
      {
         this._x = param1;
      }
      
      public function get y() : Number
      {
         return this._y;
      }
      
      public function set y(param1:Number) : void
      {
         this._y = param1;
      }
      
      public function get z() : Number
      {
         return 0;
      }
      
      public function get width() : Number
      {
         return this._width;
      }
      
      public function set width(param1:Number) : void
      {
         this._width = param1;
      }
      
      public function get height() : Number
      {
         return this._height;
      }
      
      public function set height(param1:Number) : void
      {
         this._height = param1;
      }
      
      public function get depth() : Number
      {
         return 0;
      }
      
      public function get velocity() : Array
      {
         return [this._velocity.x,this._velocity.y,0];
      }
      
      public function set velocity(param1:Array) : void
      {
         this._velocity.x = param1[0];
         this._velocity.y = param1[1];
      }
      
      public function get parallaxX() : Number
      {
         return this._parallaxX;
      }
      
      public function set parallaxX(param1:Number) : void
      {
         this._parallaxX = param1;
      }
      
      public function get parallaxY() : Number
      {
         return this._parallaxY;
      }
      
      public function set parallaxY(param1:Number) : void
      {
         this._parallaxY = param1;
      }
      
      public function get rotation() : Number
      {
         return this._rotation;
      }
      
      public function set rotation(param1:Number) : void
      {
         this._rotation = param1;
      }
      
      public function get group() : uint
      {
         return this._group;
      }
      
      public function set group(param1:uint) : void
      {
         this._group = param1;
      }
      
      public function get visible() : Boolean
      {
         return this._visible;
      }
      
      public function set visible(param1:Boolean) : void
      {
         this._visible = param1;
      }
      
      public function get touchable() : Boolean
      {
         return this._touchable;
      }
      
      public function set touchable(param1:Boolean) : void
      {
         this._touchable = param1;
      }
      
      public function get view() : *
      {
         return this._view;
      }
      
      public function set view(param1:*) : void
      {
         this._view = param1;
      }
      
      public function get art() : ICitrusArt
      {
         return this._art;
      }
      
      public function get inverted() : Boolean
      {
         return this._inverted;
      }
      
      public function set inverted(param1:Boolean) : void
      {
         this._inverted = param1;
      }
      
      public function get animation() : String
      {
         return this._animation;
      }
      
      public function set animation(param1:String) : void
      {
         this._animation = param1;
      }
      
      public function get offsetX() : Number
      {
         return this._offsetX;
      }
      
      public function set offsetX(param1:Number) : void
      {
         this._offsetX = param1;
      }
      
      public function get offsetY() : Number
      {
         return this._offsetY;
      }
      
      public function set offsetY(param1:Number) : void
      {
         this._offsetY = param1;
      }
      
      public function get registration() : String
      {
         return this._registration;
      }
      
      public function set registration(param1:String) : void
      {
         this._registration = param1;
      }
      
      override public function update(param1:Number) : void
      {
         super.update(param1);
         this.x += this._velocity.x * param1;
         this.y += this._velocity.y * param1;
      }
   }
}

