package file
{
   import mogames.gameData.role.vo.RoleAssetVO;
   import mogames.gameData.role.vo.RoleConstVO;
   
   public class EnemyConfig
   {
      private static var _instance:EnemyConfig;
      
      private var _constVec:Vector.<RoleConstVO>;
      
      private var _assetVec:Vector.<RoleAssetVO>;
      
      public function EnemyConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : EnemyConfig
      {
         if(!_instance)
         {
            _instance = new EnemyConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._constVec = new Vector.<RoleConstVO>();
         this._assetVec = new Vector.<RoleAssetVO>();
         this._constVec.push(new RoleConstVO(2001,1,18,0,34,106));
         this._constVec.push(new RoleConstVO(2002,2,18,0,88,104));
         this._constVec.push(new RoleConstVO(2003,3,18,0,24,108));
         this._constVec.push(new RoleConstVO(2004,4,18,0,68,136));
         this._constVec.push(new RoleConstVO(2005,4,18,0,70,130));
         this._constVec.push(new RoleConstVO(2006,3,18,0,24,108));
         this._constVec.push(new RoleConstVO(2007,5,18,0,40,128));
         this._constVec.push(new RoleConstVO(2008,1,18,0,40,128));
         this._constVec.push(new RoleConstVO(2009,2,18,0,46,158));
         this._constVec.push(new RoleConstVO(2010,1,18,0,48,134));
         this._constVec.push(new RoleConstVO(2011,5,18,0,40,112));
         this._constVec.push(new RoleConstVO(2012,2,18,0,28,100));
         this._constVec.push(new RoleConstVO(2013,5,18,0,36,78));
         this._constVec.push(new RoleConstVO(2014,5,18,0,42,92));
         this._constVec.push(new RoleConstVO(2015,3,18,0,24,92));
         this._constVec.push(new RoleConstVO(2016,3,18,0,50,68));
         this._constVec.push(new RoleConstVO(2017,1,18,0,60,106));
         this._constVec.push(new RoleConstVO(2018,5,18,0,36,84));
         this._constVec.push(new RoleConstVO(2019,4,18,0,34,82));
         this._constVec.push(new RoleConstVO(2020,3,18,0,24,94));
         this._constVec.push(new RoleConstVO(2021,3,18,0,56,82));
         this._constVec.push(new RoleConstVO(2022,1,18,0,53,95));
         this._constVec.push(new RoleConstVO(2023,1,18,0,53,95));
         this._constVec.push(new RoleConstVO(2024,1,18,0,30,116));
         this._constVec.push(new RoleConstVO(2025,5,18,0,36,110));
         this._constVec.push(new RoleConstVO(2026,4,0,0,22,64));
         this._constVec.push(new RoleConstVO(2027,4,20,0,88,88));
         this._constVec.push(new RoleConstVO(2028,5,20,0,44,80));
         this._constVec.push(new RoleConstVO(2029,4,20,0,44,80));
         this._constVec.push(new RoleConstVO(2030,2,0,0,30,52));
         this._constVec.push(new RoleConstVO(2031,1,0,0,44,80));
         this._constVec.push(new RoleConstVO(2032,3,0,0,80,48));
         this._constVec.push(new RoleConstVO(2033,5,0,0,80,48));
         this._constVec.push(new RoleConstVO(2034,5,0,0,50,154));
         this._constVec.push(new RoleConstVO(2035,4,0,0,50,140));
         this._constVec.push(new RoleConstVO(2036,1,18,0,32,120));
         this._constVec.push(new RoleConstVO(2037,3,18,0,32,120));
         this._constVec.push(new RoleConstVO(2038,1,18,0,66,132));
         this._constVec.push(new RoleConstVO(2039,1,18,0,22,50));
         this._constVec.push(new RoleConstVO(2040,1,18,0,22,46));
         this._constVec.push(new RoleConstVO(2041,1,18,0,40,104));
         this._constVec.push(new RoleConstVO(2042,4,18,0,30,86));
         this._constVec.push(new RoleConstVO(2043,3,18,0,45,55));
         this._constVec.push(new RoleConstVO(2044,3,18,0,66,202));
         this._constVec.push(new RoleConstVO(2045,2,0,0,80,146));
         this._constVec.push(new RoleConstVO(2046,4,0,0,44,78));
         this._constVec.push(new RoleConstVO(2047,1,0,0,35,86));
         this._constVec.push(new RoleConstVO(2048,1,0,0,28,86));
         this._constVec.push(new RoleConstVO(2049,3,0,0,14,40));
         this._constVec.push(new RoleConstVO(2050,5,0,0,20,68));
         this._constVec.push(new RoleConstVO(2051,5,0,0,18,44));
         this._constVec.push(new RoleConstVO(2052,5,0,0,24,90));
         this._constVec.push(new RoleConstVO(2053,1,0,0,140,158));
         this._constVec.push(new RoleConstVO(3001,1,18,0,150,244));
         this._constVec.push(new RoleConstVO(3002,2,18,0,108,176));
         this._constVec.push(new RoleConstVO(3003,4,18,0,150,226));
         this._constVec.push(new RoleConstVO(3004,5,18,0,150,226));
         this._constVec.push(new RoleConstVO(3005,5,18,0,112,178));
         this._constVec.push(new RoleConstVO(3006,1,18,0,110,208));
         this._constVec.push(new RoleConstVO(3007,5,18,0,348,590));
         this._constVec.push(new RoleConstVO(3008,5,18,0,376,536));
         this._constVec.push(new RoleConstVO(3009,5,18,0,160,164));
         this._constVec.push(new RoleConstVO(3010,4,18,0,100,172));
         this._constVec.push(new RoleConstVO(3011,2,0,0,400,492));
         this._constVec.push(new RoleConstVO(3012,3,0,0,26,110));
         this._constVec.push(new RoleConstVO(3013,5,0,0,120,190));
         this._constVec.push(new RoleConstVO(3014,5,0,0,162,144));
         this._constVec.push(new RoleConstVO(3015,4,0,0,44,116));
         this._constVec.push(new RoleConstVO(3016,2,0,0,28,108));
         this._constVec.push(new RoleConstVO(3017,3,0,0,100,160));
         this._constVec.push(new RoleConstVO(3018,3,0,0,222,194));
         this._constVec.push(new RoleConstVO(3019,1,0,0,86,140));
         this._constVec.push(new RoleConstVO(3020,2,0,0,62,130));
         this._constVec.push(new RoleConstVO(3021,4,0,0,70,132));
         this._constVec.push(new RoleConstVO(3022,5,0,0,340,446));
         this._constVec.push(new RoleConstVO(3023,5,0,0,82,140));
         this._constVec.push(new RoleConstVO(3024,1,0,0,80,144));
         this._constVec.push(new RoleConstVO(3025,1,0,0,34,68));
         this._constVec.push(new RoleConstVO(3026,5,0,0,38,126));
         this._constVec.push(new RoleConstVO(3027,5,0,0,344,400));
         this._constVec.push(new RoleConstVO(3028,4,0,0,40,110));
         this._constVec.push(new RoleConstVO(3029,1,0,0,90,180));
         this._constVec.push(new RoleConstVO(3030,1,0,0,304,226));
         this._constVec.push(new RoleConstVO(3031,5,0,0,28,107));
         this._constVec.push(new RoleConstVO(3032,1,0,0,68,150));
         this._constVec.push(new RoleConstVO(3033,1,0,0,324,340));
         this._constVec.push(new RoleConstVO(3034,1,0,0,68,166));
         this._constVec.push(new RoleConstVO(3035,3,0,0,68,148));
         this._constVec.push(new RoleConstVO(3036,1,0,0,84,180));
         this._constVec.push(new RoleConstVO(3037,3,18,0,30,120));
         this._constVec.push(new RoleConstVO(3038,5,18,0,66,132));
         this._constVec.push(new RoleConstVO(3039,4,18,0,84,156));
         this._constVec.push(new RoleConstVO(3040,5,18,0,84,176));
         this._constVec.push(new RoleConstVO(3041,3,18,0,68,172));
         this._constVec.push(new RoleConstVO(3042,3,18,0,50,100));
         this._constVec.push(new RoleConstVO(3043,3,10,0,28,108));
         this._constVec.push(new RoleConstVO(3044,1,10,0,47,127));
         this._constVec.push(new RoleConstVO(3045,5,10,0,58,165));
         this._constVec.push(new RoleConstVO(3046,1,10,0,42,114));
         this._constVec.push(new RoleConstVO(3047,1,10,0,162,130));
         this._constVec.push(new RoleConstVO(3048,4,10,0,60,186));
         this._constVec.push(new RoleConstVO(3049,3,10,0,60,186));
         this._constVec.push(new RoleConstVO(3050,5,10,0,60,186));
         this._constVec.push(new RoleConstVO(3051,3,10,0,18,102));
         this._constVec.push(new RoleConstVO(3052,3,10,0,56,84));
         this._constVec.push(new RoleConstVO(3053,1,10,0,20,118));
         this._constVec.push(new RoleConstVO(3054,5,10,0,22,100));
         this._constVec.push(new RoleConstVO(3055,5,0,0,140,158));
         this._constVec.push(new RoleConstVO(3056,4,0,0,160,158));
         this._constVec.push(new RoleConstVO(3057,3,0,0,138,178));
         this._constVec.push(new RoleConstVO(3058,3,0,0,140,162));
         this._constVec.push(new RoleConstVO(3059,5,0,0,164,108));
         this._constVec.push(new RoleConstVO(3060,3,0,0,104,100));
         this._constVec.push(new RoleConstVO(3061,1,0,0,196,212));
         this._constVec.push(new RoleConstVO(3062,4,0,0,182,186));
         this._constVec.push(new RoleConstVO(3063,3,0,0,160,164));
         this._constVec.push(new RoleConstVO(3064,4,0,0,158,158));
         this._constVec.push(new RoleConstVO(3065,5,0,0,245,188));
         this._constVec.push(new RoleConstVO(3066,5,0,0,110,214));
         this._constVec.push(new RoleConstVO(4001,3,0,0,176,154));
         this._constVec.push(new RoleConstVO(4002,3,0,0,160,146));
         this._constVec.push(new RoleConstVO(4003,3,0,0,160,146));
         this._constVec.push(new RoleConstVO(4004,3,0,0,152,154));
         this._constVec.push(new RoleConstVO(4005,5,0,0,232,278));
         this._constVec.push(new RoleConstVO(4006,5,0,0,202,156));
         this._constVec.push(new RoleConstVO(4007,5,0,0,164,305));
         this._constVec.push(new RoleConstVO(4008,5,0,0,102,274));
         this._constVec.push(new RoleConstVO(4009,1,0,0,210,166));
         this._constVec.push(new RoleConstVO(4010,1,10,0,112,225));
         this._constVec.push(new RoleConstVO(4011,3,10,0,128,255));
         this._constVec.push(new RoleConstVO(4012,1,10,0,66,202));
         this._constVec.push(new RoleConstVO(4013,2,10,0,70,206));
         this._constVec.push(new RoleConstVO(4014,4,10,0,80,146));
         this._constVec.push(new RoleConstVO(4015,5,10,0,68,155));
         this._constVec.push(new RoleConstVO(4016,1,10,0,50,112));
         this._constVec.push(new RoleConstVO(4017,3,10,0,64,86));
         this._constVec.push(new RoleConstVO(4018,4,10,0,18,54));
         this._constVec.push(new RoleConstVO(4019,5,10,0,144,178));
         this._constVec.push(new RoleConstVO(4020,1,10,0,158,194));
         this._constVec.push(new RoleConstVO(4021,5,10,0,64,86));
         this._constVec.push(new RoleConstVO(4022,3,10,0,74,106));
         this._constVec.push(new RoleConstVO(4023,3,10,0,158,150));
         this._constVec.push(new RoleConstVO(4024,3,10,0,198,84));
         this._constVec.push(new RoleConstVO(4025,3,10,0,318,124));
         this._assetVec.push(new RoleAssetVO(2001,"天兵","","EFFECT_HU_GUAI_ATK","HIT_HU_GUAI","ROLE_TIAN_BING"));
         this._assetVec.push(new RoleAssetVO(2002,"虎怪","","EFFECT_HU_GUAI_ATK","HIT_HU_GUAI","ROLE_HU_GUAI"));
         this._assetVec.push(new RoleAssetVO(2003,"蝙蝠怪","","EFFECT_HU_GUAI_ATK","HIT_HU_GUAI","ROLE_BIAN_FU_GUAI"));
         this._assetVec.push(new RoleAssetVO(2004,"熊怪","","EFFECT_XIONG_GUAI_ATK","XIONG_GUAI_HIT","ROLE_XIONG_GUAI"));
         this._assetVec.push(new RoleAssetVO(2005,"虎法师","","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","ROLE_HU_FA_SHI"));
         this._assetVec.push(new RoleAssetVO(2006,"蝙蝠弓箭手","","EFFECT_HU_GUAI_ATK","HIT_HU_GUAI","ROLE_ARROW_BAT"));
         this._assetVec.push(new RoleAssetVO(2007,"骷髅兵","","EFFECT_HU_GUAI_ATK","BONE_FOOT_HIT","ROLE_KU_LOU_BU_BING"));
         this._assetVec.push(new RoleAssetVO(2008,"骷髅弓","","EFFECT_HU_GUAI_ATK","BONE_FOOT_HIT","ROLE_KU_LOU_GONG_BING"));
         this._assetVec.push(new RoleAssetVO(2009,"骷髅法师","","EFFECT_BONE_BALL_BOOM_CLIP","BONE_BALL_HIT","ROLE_KU_LOU_FA_SHI"));
         this._assetVec.push(new RoleAssetVO(2010,"金角怪","","EFFECT_GUN_ATK","GUNHURT","ROLE_JIN_JIAO_GUAI"));
         this._assetVec.push(new RoleAssetVO(2011,"银角怪","","EFFECT_GUN_ATK","GUNHURT","ROLE_YIN_JIAO_GUAI"));
         this._assetVec.push(new RoleAssetVO(2012,"玉狐","","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","ROLE_YU_HU"));
         this._assetVec.push(new RoleAssetVO(2013,"持枪小妖","","EFFECT_GUN_ATK","HIT_HU_GUAI","ROLE_XIAO_YAO_ONE"));
         this._assetVec.push(new RoleAssetVO(2014,"持刀小妖","","EFFECT_GUN_ATK","GUNHURT","ROLE_XIAO_YAO_TWO"));
         this._assetVec.push(new RoleAssetVO(2015,"虾兵甲","","EFFECT_GUN_ATK","HIT_HU_GUAI","ROLE_XIA_BING"));
         this._assetVec.push(new RoleAssetVO(2016,"蟹将甲","","EFFECT_WATER_BOOM0","GUNHURT","ROLE_XIE_JIANG"));
         this._assetVec.push(new RoleAssetVO(2017,"虎妖","","EFFECT_HU_GUAI_ATK","GUNHURT","ROLE_HU_YAO"));
         this._assetVec.push(new RoleAssetVO(2018,"鹿妖","","EFFECT_LU_YAO_BALL_BOOM_CLIP","GUNHURT","ROLE_LU_YAO"));
         this._assetVec.push(new RoleAssetVO(2019,"羊妖","","EFFECT_FIRE_BOOM_CLIP","BOOM0","ROLE_YANG_YAO"));
         this._assetVec.push(new RoleAssetVO(2020,"虾兵乙","","EFFECT_GUN_ATK","HIT_HU_GUAI","ROLE_DA_XIA_BING"));
         this._assetVec.push(new RoleAssetVO(2021,"蟹将乙","","EFFECT_WATER_BOOM0","GUNHURT","ROLE_DA_XIE_JIANG"));
         this._assetVec.push(new RoleAssetVO(2022,"牛头小妖","","EFFECT_HU_GUAI_ATK","GUNHURT","ROLE_NIU_XIAO_GUAI"));
         this._assetVec.push(new RoleAssetVO(2023,"牛头老妖","","EFFECT_WATER_BOOM0","GUNHURT","ROLE_NIU_LAO_GUAI"));
         this._assetVec.push(new RoleAssetVO(2024,"邪武僧","","EFFECT_GUN_ATK","SWK_ATK_HURT","ROLE_XIE_WU_SENG"));
         this._assetVec.push(new RoleAssetVO(2025,"邪法僧","","EFFECT_WATER_BOOM0","FIRE_EXPLORE","ROLE_XIE_FA_SENG"));
         this._assetVec.push(new RoleAssetVO(2026,"魔炎幼体","","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","ROLE_MO_YAN"));
         this._assetVec.push(new RoleAssetVO(2027,"宝箱怪","","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","ROLE_BAO_XIANG_GUAI"));
         this._assetVec.push(new RoleAssetVO(2028,"绿蛇","","EFFECT_HU_GUAI_ATK","HIT_HU_GUAI","ROLE_GREEN_SNAKE"));
         this._assetVec.push(new RoleAssetVO(2029,"红蛇","","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","ROLE_RED_SNAKE"));
         this._assetVec.push(new RoleAssetVO(2030,"蛇灵幼体","","EFFECT_PUNCH_HURT_CLIP","BONE_BALL_HIT","ROLE_SHE_LING"));
         this._assetVec.push(new RoleAssetVO(2031,"假面怪","","EFFECT_GUN_ATK","GUNHURT","ROLE_JIA_MIAN_GUAI"));
         this._assetVec.push(new RoleAssetVO(2032,"绿蜘蛛","","EFFECT_PUNCH_HURT_CLIP","GUNHURT","ROLE_GREEN_SPIDER"));
         this._assetVec.push(new RoleAssetVO(2033,"紫蜘蛛","","EFFECT_SHE_BOOM_CLIP","GUNHURT","ROLE_PURPLE_SPIDER"));
         this._assetVec.push(new RoleAssetVO(2034,"牛头","","EFFECT_PUNCH_HURT_CLIP","GUNHURT","ROLE_NIU_TOU"));
         this._assetVec.push(new RoleAssetVO(2035,"马面","","EFFECT_HU_GUAI_ATK","GUNHURT","ROLE_MA_MIAN"));
         this._assetVec.push(new RoleAssetVO(2036,"黄蜈蚣","","EFFECT_HU_GUAI_ATK","HIT_YIN_JIANG_JUN","ROLE_HUANG_WU_GONG"));
         this._assetVec.push(new RoleAssetVO(2037,"蓝蜈蚣","","EFFECT_WATER_BOOM0","HIT_YIN_JIANG_JUN","ROLE_LAN_WU_GONG"));
         this._assetVec.push(new RoleAssetVO(2038,"蜈蚣精分身","","EFFECT_LING_GAN_CHUI_BOOM","GUNHURT","ROLE_WU_GONG_JING"));
         this._assetVec.push(new RoleAssetVO(2039,"金灵幼体","","EFFECT_GUN_ATK","GUNHURT","ROLE_YOU_JIN_LING"));
         this._assetVec.push(new RoleAssetVO(2040,"金灵成熟体","","EFFECT_GUN_ATK","GUNHURT","ROLE_SHU_JIN_LING"));
         this._assetVec.push(new RoleAssetVO(2041,"白象小妖","","EFFECT_GUN_ATK","GUNHURT","ROLE_BAI_XIANG_XIAO_YAO"));
         this._assetVec.push(new RoleAssetVO(2042,"狮子小妖","","EFFECT_GUN_ATK","GUNHURT","ROLE_SHI_XIAO_YAO"));
         this._assetVec.push(new RoleAssetVO(2043,"白面狐狸","","EFFECT_HU_GUAI_ATK","HIT_YIN_JIANG_JUN","ROLE_BAI_MIAN_FOX"));
         this._assetVec.push(new RoleAssetVO(2044,"分身·鹏魔王","","EFFECT_GUN_ATK","GUNHURT","ROLE_QS_PENG_MO_WANG"));
         this._assetVec.push(new RoleAssetVO(2045,"分身·猕猴王","","EFFECT_GUN_ATK","GUNHURT","ROLE_QS_MI_HOU_WANG"));
         this._assetVec.push(new RoleAssetVO(2046,"鹏小妖","","EFFECT_HU_GUAI_ATK","HIT_HU_GUAI","ROLE_PENG_XIAO_YAO"));
         this._assetVec.push(new RoleAssetVO(2047,"苍狼小妖","","EFFECT_HU_GUAI_ATK","HIT_HU_GUAI","ROLE_CANG_LANG_GUAI"));
         this._assetVec.push(new RoleAssetVO(2048,"豹精小妖","","EFFECT_GUN_ATK","GUNHURT","ROLE_BAO_XIAO_YAO"));
         this._assetVec.push(new RoleAssetVO(2049,"水灵幼体","","EFFECT_BAI_QIU_BOOM_CLIP","GUNHURT","ROLE_SHUI_LING"));
         this._assetVec.push(new RoleAssetVO(2050,"犀牛小妖","","EFFECT_GUN_ATK","GUNHURT","ROLE_XI_NIU_YAO"));
         this._assetVec.push(new RoleAssetVO(2051,"兔妖","","EFFECT_GUN_ATK","GUNHURT","ROLE_TU_YAO"));
         this._assetVec.push(new RoleAssetVO(2052,"和尚","","EFFECT_GUN_ATK","ZBJ_ATK_HURT","ROLE_HE_SHANG"));
         this._assetVec.push(new RoleAssetVO(2053,"天狗分身","","EFFECT_GUN_ATK","XIONG_GUAI_HIT","ROLE_TIAN_GOU"));
         this._assetVec.push(new RoleAssetVO(3001,"佛手","ICON_FO_SHOU","EFFECT_GUN_ATK","GUNHURT","ROLE_FO_SHOU"));
         this._assetVec.push(new RoleAssetVO(3002,"寅将军","ICON_YIN_JIANG_JUN","EFFECT_HU_GUAI_ATK","HIT_YIN_JIANG_JUN","ROLE_YIN_JIANG_JUN"));
         this._assetVec.push(new RoleAssetVO(3003,"黑熊精","ICON_HEI_XIONG_JING","EFFECT_XIONG_GUAI_ATK","XIONG_GUAI_HIT","ROLE_HEI_XIONG_JING"));
         this._assetVec.push(new RoleAssetVO(3004,"黄风怪","ICON_HUANG_FENG_GUAI","EFFECT_HUANG_FENG_GUAI_ATK","HUANG_FENG_GUAI_HIT","ROLE_HUANG_FENG_GUAI"));
         this._assetVec.push(new RoleAssetVO(3005,"银角大王","ICON_YIN_JIAO","EFFECT_GUN_ATK","HIT_YIN_JIANG_JUN","ROLE_YIN_JIAO_DA_WANG"));
         this._assetVec.push(new RoleAssetVO(3006,"金角大王","ICON_JIN_JIAO","EFFECT_GUN_ATK","HIT_YIN_JIANG_JUN","ROLE_JIN_JIAO_DA_WANG"));
         this._assetVec.push(new RoleAssetVO(3007,"白骨精","ICON_BAI_GU_JING_FOOT","EFFECT_GUN_ATK","GUNHURT","ROLE_BAIGUJING_FOOT"));
         this._assetVec.push(new RoleAssetVO(3008,"白骨精","ICON_BAI_GU_JING_BODY","EFFECT_GUN_ATK","GUNHURT","ROLE_BAIGUJING_BODY"));
         this._assetVec.push(new RoleAssetVO(3009,"白骨精","ICON_BAI_GU_JING_HEAD","EFFECT_GUN_ATK","GUNHURT","ROLE_BAIGUJING_HEAD"));
         this._assetVec.push(new RoleAssetVO(3010,"黄袍怪","ICON_HUANG_PAO_GUAI","EFFECT_HUANG_PAO_GUAI_ATK_CLIP","HUANG_PAO_GUAI_SWORD","ROLE_HUANG_PAO_GUAI"));
         this._assetVec.push(new RoleAssetVO(3011,"九尾狐","ICON_JIU_WEI_HU","EFFECT_HUANG_PAO_GUAI_ATK_CLIP","HUANG_PAO_GUAI_SWORD","ROLE_JIU_WEI_HU"));
         this._assetVec.push(new RoleAssetVO(3012,"小白龙","ICON_LONG_MA","EFFECT_SWORD_HIT_CLIP","XBL_ATK_HURT","ROLE_XIAO_BAI_LONG"));
         this._assetVec.push(new RoleAssetVO(3013,"狮俐王","ICON_SHI_LI_WANG","EFFECT_HUANG_PAO_GUAI_ATK_CLIP","HUANG_PAO_GUAI_SWORD","ROLE_SHI_LI_WANG"));
         this._assetVec.push(new RoleAssetVO(3014,"真·狮俐王","ICON_ZHEN_SHI_LI_WANG","EFFECT_HUANG_PAO_GUAI_ATK_CLIP","XIONG_GUAI_HIT","ROLE_ZHEN_SHI_LI_WANG"));
         this._assetVec.push(new RoleAssetVO(3015,"红孩儿","ICON_HONG_HAI_ER","EFFECT_HUANG_PAO_GUAI_ATK_CLIP","HIT_YIN_JIANG_JUN","ROLE_HONG_HAI_ER"));
         this._assetVec.push(new RoleAssetVO(3016,"猪八戒","ICON_BOSS_PIG","EFFECT_PUNCH_HURT_CLIP","ZBJ_ATK_HURT","ROLE_ZHU_BA_JIE"));
         this._assetVec.push(new RoleAssetVO(3017,"鼍龙","ICON_TUO_LONG","EFFECT_WATER_BOOM0","GUNHURT","ROLE_TUO_LONG"));
         this._assetVec.push(new RoleAssetVO(3018,"真·鼍龙","ICON_ZHEN_TUO_LONG","EFFECT_WATER_BOOM0","GUNHURT","ROLE_ZHEN_TUO_LONG"));
         this._assetVec.push(new RoleAssetVO(3019,"虎力大仙","ICON_HU_DA_XIAN","EFFECT_GUN_ATK","GUNHURT","ROLE_HU_DA_XIAN"));
         this._assetVec.push(new RoleAssetVO(3020,"鹿力大仙","ICON_LU_DA_XIAN","EFFECT_LU_YAO_BALL_BOOM_CLIP","GUNHURT","ROLE_LU_DA_XIAN"));
         this._assetVec.push(new RoleAssetVO(3021,"羊力大仙","ICON_YANG_DA_XIAN","EFFECT_HU_GUAI_ATK","HIT_HU_GUAI","ROLE_YANG_DA_XIAN"));
         this._assetVec.push(new RoleAssetVO(3022,"妖魂王","ICON_SAN_YAO","EFFECT_WATER_BOOM0","GUNHURT","ROLE_SAN_YAO"));
         this._assetVec.push(new RoleAssetVO(3023,"灵感大王","ICON_LING_GAN_WANG","EFFECT_WATER_BOOM0","GUNHURT","ROLE_LING_GAN_WANG"));
         this._assetVec.push(new RoleAssetVO(3024,"独角兕","ICON_DU_JIAO","EFFECT_GUN_ATK","GUNHURT","ROLE_DU_JIAO"));
         this._assetVec.push(new RoleAssetVO(3025,"真·独角兕","ICON_ZHEN_DU_JIAO","EFFECT_GUN_ATK","GUNHURT","ROLE_ZHEN_DU_JIAO"));
         this._assetVec.push(new RoleAssetVO(3026,"蝎子精","ICON_XIE_ZI_JING","EFFECT_GUN_ATK","HIT_HU_GUAI","ROLE_XIE_ZI_JING"));
         this._assetVec.push(new RoleAssetVO(3027,"真·蝎子精","ICON_ZHEN_XIE_ZI","EFFECT_GUN_ATK","HIT_HU_GUAI","ROLE_ZHEN_XIE_ZI"));
         this._assetVec.push(new RoleAssetVO(3028,"铁扇公主","ICON_TIE_SHAN","EFFECT_HUANG_PAO_GUAI_ATK_CLIP","GUNHURT","ROLE_TIE_SHAN"));
         this._assetVec.push(new RoleAssetVO(3029,"牛魔王","ICON_NIU_MO_WANG","EFFECT_GUN_ATK","GUNHURT","ROLE_NIU_MO_WANG"));
         this._assetVec.push(new RoleAssetVO(3030,"真·牛魔王","ICON_ZHEN_NIU_MO","EFFECT_GUN_ATK","GUNHURT","ROLE_ZHEN_NIU_MO_WANG"));
         this._assetVec.push(new RoleAssetVO(3031,"沙僧","ICON_BOSS_SS","EFFECT_GUN_ATK","SS_ATK_HURT","ROLE_SHA_SENG"));
         this._assetVec.push(new RoleAssetVO(3032,"九头虫","ICON_JIU_TOU_CHONG","EFFECT_GUN_ATK","GUNHURT","ROLE_JIU_TOU_CHONG"));
         this._assetVec.push(new RoleAssetVO(3033,"真·九头虫","ICON_ZHEN_JIU_TOU","EFFECT_GUN_ATK","GUNHURT","ROLE_ZHEN_JIU_TOU"));
         this._assetVec.push(new RoleAssetVO(3034,"黄眉怪","ICON_HUANG_MEI","EFFECT_GUN_ATK","GUNHURT","ROLE_HUANG_MEI"));
         this._assetVec.push(new RoleAssetVO(3035,"蟒蛇精","ICON_MANG_SHE_JING","EFFECT_SHE_BOOM_CLIP","GUNHURT","ROLE_MANG_SHE_JING"));
         this._assetVec.push(new RoleAssetVO(3036,"赛太岁","ICON_JIN_MAO_HOU","EFFECT_GUN_ATK","GUNHURT","ROLE_JIN_MAO_HOU"));
         this._assetVec.push(new RoleAssetVO(3037,"蜘蛛精","ICON_ZHI_ZHU_JING","EFFECT_LING_GAN_CHUI_BOOM","GUNHURT","ROLE_ZHI_ZHU_JING"));
         this._assetVec.push(new RoleAssetVO(3038,"蜈蚣精","ICON_WU_GONG_JING","EFFECT_LING_GAN_CHUI_BOOM","GUNHURT","ROLE_WU_GONG_JING"));
         this._assetVec.push(new RoleAssetVO(3039,"狮子精","ICON_SHI_ZI_JING","EFFECT_GUN_ATK","GUNHURT","ROLE_SHI_ZI_JING"));
         this._assetVec.push(new RoleAssetVO(3040,"白象精","ICON_BAI_XIANG_JING","EFFECT_GUN_ATK","GUNHURT","ROLE_BAI_XIANG_JING"));
         this._assetVec.push(new RoleAssetVO(3041,"大鹏精","ICON_DA_PENG_JING","EFFECT_GUN_ATK","GUNHURT","ROLE_DA_PENG_JING"));
         this._assetVec.push(new RoleAssetVO(3042,"白鹿精","ICON_BAI_LU_JING","EFFECT_WATER_BOOM0","GUNHURT","ROLE_BAI_LU_JING"));
         this._assetVec.push(new RoleAssetVO(3043,"地涌夫人","ICON_DI_YONG","EFFECT_HUANG_PAO_GUAI_ATK_CLIP","GUNHURT","ROLE_DI_YONG_FU_REN"));
         this._assetVec.push(new RoleAssetVO(3044,"豹子精","ICON_BAO_ZI_JING","EFFECT_GUN_ATK","GUNHURT","ROLE_BAO_ZI_JING"));
         this._assetVec.push(new RoleAssetVO(3045,"黄狮精","ICON_HUANG_SHI_JING","EFFECT_GUN_ATK","GUNHURT","ROLE_HUANG_SHI_JING"));
         this._assetVec.push(new RoleAssetVO(3046,"九灵元圣","ICON_YUAN_SHENG","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","ROLE_YUAN_SHENG"));
         this._assetVec.push(new RoleAssetVO(3047,"真·九灵元圣","ICON_ZHEN_YUAN_SHENG","EFFECT_FIRE_BOOM_CLIP","GUNHURT","ROLE_ZHEN_YUAN_SHENG"));
         this._assetVec.push(new RoleAssetVO(3048,"辟暑大王","ICON_BI_SHU_DA_WANG","EFFECT_XIONG_GUAI_ATK","XIONG_GUAI_HIT","ROLE_BI_SHU_DA_WANG"));
         this._assetVec.push(new RoleAssetVO(3049,"辟寒大王","ICON_BI_HAN_DA_WANG","EFFECT_SWORD_HIT_CLIP","XBL_ATK_HURT","ROLE_BI_HAN_DA_WANG"));
         this._assetVec.push(new RoleAssetVO(3050,"辟尘大王","ICON_BI_CHEN_DA_WANG","EFFECT_GUN_ATK","GUNHURT","ROLE_BI_CHEN_DA_WANG"));
         this._assetVec.push(new RoleAssetVO(3051,"玉兔精","ICON_YU_TU","EFFECT_GUN_ATK","GUNHURT","ROLE_YU_TU_JING"));
         this._assetVec.push(new RoleAssetVO(3052,"真·玉兔精","ICON_ZHEN_YU_TU","EFFECT_GUN_ATK","GUNHURT","ROLE_ZHEN_YU_TU"));
         this._assetVec.push(new RoleAssetVO(3053,"伽叶","ICON_JIA_YE","EFFECT_GUN_ATK","GUNHURT","ROLE_JIA_YE"));
         this._assetVec.push(new RoleAssetVO(3054,"阿傩","ICON_A_NAN","EFFECT_GUN_ATK","GUNHURT","ROLE_A_NAN"));
         this._assetVec.push(new RoleAssetVO(3055,"天狗","ICON_TIAN_GOU","EFFECT_GUN_ATK","XIONG_GUAI_HIT","ROLE_TIAN_GOU"));
         this._assetVec.push(new RoleAssetVO(3056,"狰","ICON_ZHENG","EFFECT_GUN_ATK","GUNHURT","ROLE_ZHEN_ZHEN"));
         this._assetVec.push(new RoleAssetVO(3057,"肥遗","ICON_FEI_YI","EFFECT_BONE_BALL_BOOM_CLIP","BONE_BALL_HIT","ROLE_FEI_YI"));
         this._assetVec.push(new RoleAssetVO(3058,"旋龟","ICON_XUAN_GUI","EFFECT_WATER_BOOM0","GUNHURT","ROLE_XUAN_GUI"));
         this._assetVec.push(new RoleAssetVO(3059,"应龙","ICON_YING_LONG","SEQ_DU_TE_BOOM_CLIP","GUNHURT","ROLE_YING_LONG"));
         this._assetVec.push(new RoleAssetVO(3060,"青鸟","ICON_QING_NIAO","EFFECT_WATER_BOOM0","GUNHURT","ROLE_QING_NIAO"));
         this._assetVec.push(new RoleAssetVO(3061,"烛龙","ICON_ZHU_LONG","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","ROLE_ZHU_LONG"));
         this._assetVec.push(new RoleAssetVO(3062,"赤眼猪妖","ICON_CHI_YAN_ZHU_YAO","EFFECT_FIRE_BOOM_CLIP","GUNHURT","ROLE_CYZY"));
         this._assetVec.push(new RoleAssetVO(3063,"陆吾","ICON_LU_WU","EFFECT_WATER_BOOM0","GUNHURT","ROLE_LU_WU"));
         this._assetVec.push(new RoleAssetVO(3064,"祸斗","ICON_HUO_DOU","SEQ_HUO_DOU_BALL_BOOM_CLIP","FIRE_EXPLORE","ROLE_HUO_DOU"));
         this._assetVec.push(new RoleAssetVO(3065,"诸怀","ICON_ZHU_HUAI","EFFECT_GUN_ATK","GUNHURT","ROLE_ZHU_HUAI"));
         this._assetVec.push(new RoleAssetVO(3066,"凿齿","ICON_ZAO_CHI","EFFECT_GUN_ATK","GUNHURT","ROLE_ZAO_CHI"));
         this._assetVec.push(new RoleAssetVO(4001,"变异蟾蜍","ICON_CHAN_CHU","EFFECT_GUN_ATK","GUNHURT","ROLE_CHAN_CHU"));
         this._assetVec.push(new RoleAssetVO(4002,"变异灯鱼","ICON_DENG_YU_GUAI","EFFECT_BONE_BALL_BOOM_CLIP","GUNHURT","ROLE_DENG_YU_GUAI"));
         this._assetVec.push(new RoleAssetVO(4003,"变异寄居蟹","ICON_JI_JU_XIE","EFFECT_GUN_ATK","GUNHURT","ROLE_JI_JU_XIE"));
         this._assetVec.push(new RoleAssetVO(4004,"变异蚌精","ICON_BANG_JING","EFFECT_WATER_BOOM0","GUNHURT","ROLE_BANG_JING"));
         this._assetVec.push(new RoleAssetVO(4005,"泥浆精","ICON_NI_BA_GUAI","EFFECT_HUANG_PAO_GUAI_ATK_CLIP","GUNHURT","ROLE_NI_BA_GUAI"));
         this._assetVec.push(new RoleAssetVO(4006,"岩鼠精","ICON_YAN_SHU_JING","EFFECT_XIONG_GUAI_ATK","XIONG_GUAI_HIT","ROLE_YAN_SHU_JING"));
         this._assetVec.push(new RoleAssetVO(4007,"巨岩精","ICON_JU_YAN_JING","EFFECT_GUN_ATK","GUNHURT","ROLE_JU_YAN_JING"));
         this._assetVec.push(new RoleAssetVO(4008,"岩石将军","ICON_YAN_SHI_JIANG","EFFECT_LUO_XUAN_QIU_BOOM_CLIP","GUNHURT","ROLE_YAN_SHI_JIANG_JUN"));
         this._assetVec.push(new RoleAssetVO(4009,"真·赛太岁","ICON_JIN_MAO_HOU","EFFECT_SAI_TAI_SUI_BOOM_CLIP","GUNHURT","ROLE_SAI_TAI_SUI"));
         this._assetVec.push(new RoleAssetVO(4010,"七圣·牛魔王","ICON_QS_NIU_MO_WANG","EFFECT_GUN_ATK","GUNHURT","ROLE_QS_NIU_MO_WANG"));
         this._assetVec.push(new RoleAssetVO(4011,"七圣·蛟魔王","ICON_QS_JIAO_MO_WANG","EFFECT_SAI_TAI_SUI_BOOM_CLIP","GUNHURT","ROLE_QS_JIAO_MO_WANG"));
         this._assetVec.push(new RoleAssetVO(4012,"七圣·鹏魔王","ICON_QS_PENG_MO_WANG","EFFECT_GUN_ATK","GUNHURT","ROLE_QS_PENG_MO_WANG"));
         this._assetVec.push(new RoleAssetVO(4013,"七圣·狮驼王","ICON_QS_SHI_TUO_WANG","EFFECT_GUN_ATK","GUNHURT","ROLE_QS_SHI_TUO_WANG"));
         this._assetVec.push(new RoleAssetVO(4014,"七圣·猕猴王","ICON_QS_MI_HOU_WANG","EFFECT_GUN_ATK","GUNHURT","ROLE_QS_MI_HOU_WANG"));
         this._assetVec.push(new RoleAssetVO(4015,"七圣·禺狨王","ICON_QS_YU_RONG_WANG","EFFECT_GUN_ATK","GUNHURT","ROLE_QS_YU_RONG_WANG"));
         this._assetVec.push(new RoleAssetVO(4016,"七圣·美猴王","ICON_QS_MEI_HOU_WANG","EFFECT_GUN_ATK","GUNHURT","ROLE_QS_MEI_HOU_WANG"));
         this._assetVec.push(new RoleAssetVO(4017,"冰麒麟","ICON_QI_LIN2","EFFECT_BAI_QIU_BOOM_CLIP","GUNHURT","ROLE_SHUI_QI_LIN"));
         this._assetVec.push(new RoleAssetVO(4018,"火凤凰","ICON_FENG_HUANG2","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","ROLE_FENG_HUANG"));
         this._assetVec.push(new RoleAssetVO(4019,"饕餮","ICON_TAO_TIE","EFFECT_GUN_ATK","GUNHURT","ROLE_TAO_TIE"));
         this._assetVec.push(new RoleAssetVO(4020,"穷奇","ICON_QIONG_QI","EFFECT_GUN_ATK","GUNHURT","ROLE_QIONG_QI"));
         this._assetVec.push(new RoleAssetVO(4021,"玄武","ICON_XUAN_WU2","EFFECT_GUN_ATK","GUNHURT","ROLE_XUAN_WU"));
         this._assetVec.push(new RoleAssetVO(4022,"龟丞相","ICON_GUI_CHENG_XIANG","EFFECT_GUN_ATK","GUNHURT","ROLE_GUI_CHENG_XIANG"));
         this._assetVec.push(new RoleAssetVO(4023,"虎蛟","ICON_HU_JIAO","EFFECT_GUN_ATK","GUNHURT","ROLE_HU_JIAO"));
         this._assetVec.push(new RoleAssetVO(4024,"冉遗鱼","ICON_RAN_YI_YU","EFFECT_GUN_ATK","GUNHURT","ROLE_RAN_YI_YU"));
         this._assetVec.push(new RoleAssetVO(4025,"赢鱼","ICON_YING_YU","EFFECT_GUN_ATK","GUNHURT","ROLE_YING_YU"));
      }
      
      public function findConstRole(param1:int) : RoleConstVO
      {
         var _loc2_:RoleConstVO = null;
         for each(_loc2_ in this._constVec)
         {
            if(_loc2_.roleID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findRoleAsset(param1:int) : RoleAssetVO
      {
         var _loc2_:RoleAssetVO = null;
         for each(_loc2_ in this._assetVec)
         {
            if(_loc2_.roleID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

