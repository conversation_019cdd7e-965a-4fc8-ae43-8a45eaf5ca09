package mogames.gameMission.mission.huanghuaguan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.WinEffect;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.box2d.AISensor;
   import mogames.gameRole.enemy.BOSSWuGongJing;
   import mogames.gameRole.enemy.BOSSZhiZhuJing;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.SysRender;
   import mogames.gameUI.mission.ReviveModule;
   
   public class SceneHuangHuaGuan extends BossScene
   {
      private var _trapArgs:Array = [{
         "posX":75,
         "posY":30,
         "inverted":false,
         "num":3,
         "hurt":500,
         "interval":6
      },{
         "posX":885,
         "posY":30,
         "inverted":true,
         "num":3,
         "hurt":500,
         "interval":6
      },{
         "posX":1035,
         "posY":30,
         "inverted":false,
         "num":3,
         "hurt":500,
         "interval":3
      },{
         "posX":1845,
         "posY":30,
         "inverted":true,
         "num":3,
         "hurt":500,
         "interval":5
      },{
         "posX":1995,
         "posY":30,
         "inverted":false,
         "num":3,
         "hurt":500,
         "interval":5
      },{
         "posX":2805,
         "posY":30,
         "inverted":true,
         "num":3,
         "hurt":500,
         "interval":3
      },{
         "posX":2945,
         "posY":30,
         "inverted":false,
         "num":3,
         "hurt":500,
         "interval":6
      },{
         "posX":3755,
         "posY":30,
         "inverted":true,
         "num":3,
         "hurt":500,
         "interval":6
      },{
         "posX":3915,
         "posY":30,
         "inverted":false,
         "num":3,
         "hurt":500,
         "interval":12
      },{
         "posX":4725,
         "posY":30,
         "inverted":true,
         "num":3,
         "hurt":500,
         "interval":17
      }];
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _bossSpider:BOSSZhiZhuJing;
      
      private var _traps:Array;
      
      public function SceneHuangHuaGuan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         var _loc3_:Array = [AISensor];
         EffectManager.instance().playBGM("BGM_DANGER2");
         setWinPosition(new Rectangle(4130,134,512,118),new Point(4390,416));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2049,1);
            TaskProxy.instance().addTask(11031);
         };
         super.handlerInit();
         this.layoutEnemies();
         this.layoutTraps();
         if(!FlagProxy.instance().isComplete(2049))
         {
            showDialog("STORY0076",func);
         }
      }
      
      private function layoutTraps() : void
      {
         var _loc2_:TrapGongNu = null;
         var _loc1_:int = 0;
         while(_loc1_ < 10)
         {
            _loc2_ = new TrapGongNu();
            Layers.addCEChild(_loc2_);
            _loc2_.x = this._trapArgs[_loc1_].posX;
            _loc2_.y = this._trapArgs[_loc1_].posY;
            _loc2_.initData(this._trapArgs[_loc1_]);
            _loc1_++;
         }
      }
      
      private function layoutEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[206,260,150,100],[624,260,150,100]],23011,this.handlerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1144,260,150,100],[1562,260,150,100]],23012,this.handlerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[2122,260,150,100],[2537,260,150,100]],23013,this.handlerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[3050,260,150,100],[3470,216,150,100]],23014,this.triggerEnd3);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,430),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1440,480),1,new Rectangle(0,0,1920,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2400,480),1,new Rectangle(0,0,2880,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(3360,480),1,new Rectangle(0,0,3840,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(4238,480),1,new Rectangle(3840,0,960,600));
         this._pTrigger1.setBlock(false,false,false,true);
         this._pTrigger2.setBlock(false,false,false,true);
         this._pTrigger3.setBlock(false,false,false,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function handlerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd3() : void
      {
         this.handlerEnd();
         this.addBOSS();
      }
      
      private function startBattle() : void
      {
         ReviveModule.bornPos.setTo(4238,300);
         this.startBossBattle("BGM_BATTLE2");
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSWuGongJing();
         _boss.x = 4440;
         _boss.y = 280;
         _boss.initData(30381,30381,124);
         _boss.target = _mission.curTarget;
         add(_boss);
         (_boss as BOSSWuGongJing).rect = new Rectangle(3840,100,960,400);
         this._bossSpider = new BOSSZhiZhuJing();
         this._bossSpider.x = 4640;
         this._bossSpider.y = 280;
         this._bossSpider.initData(30372,30372);
         this._bossSpider.target = _mission.curTarget;
         add(this._bossSpider);
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         setHeroEnable(true);
         _boss.activeEnemy(false);
         _boss.aiEnabled = true;
         this._bossSpider.activeEnemy(false);
         this._bossSpider.aiEnabled = true;
         EffectManager.instance().playBGM(param1);
         SysRender.instance().add(this.checkWin);
      }
      
      private function checkWin() : void
      {
         if(!_boss.isDead || !this._bossSpider.isDead)
         {
            return;
         }
         SysRender.instance().remove(this.checkWin);
         Layers.lockGame = true;
         EffectManager.instance().stopAllSound();
         var _loc1_:WinEffect = new WinEffect();
         _loc1_.start();
         _winTimer.setTimeOut(3,this.handlerWin);
         _isWin = true;
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11031);
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         ReviveModule.bornPos.setTo(0,0);
         SysRender.instance().remove(this.checkWin);
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         super.destroy();
      }
   }
}

