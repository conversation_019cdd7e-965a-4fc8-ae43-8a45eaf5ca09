package mogames.gameData.fabao
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.UseHandler;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.TxtUtil;
   
   public class BaseUseVO
   {
      public var needGold:Sint;
      
      public var needList:Array;
      
      public function BaseUseVO(param1:int, param2:Array = null)
      {
         super();
         this.needGold = new Sint(param1);
         this.needList = param2;
      }
      
      public function useStuff() : void
      {
         if(this.needGold.v != 0)
         {
            MasterProxy.instance().changeGold(-this.needGold.v);
         }
         UseHandler.instance().useStuff(this.needList);
      }
      
      public function get isLack() : Boolean
      {
         var _loc1_:LackVO = this.checkLack();
         if(_loc1_)
         {
            MiniMsgMediator.instance().showAutoMsg(_loc1_.str);
            return true;
         }
         return false;
      }
      
      private function checkLack() : LackVO
      {
         var _loc1_:LackVO = null;
         if(this.needGold.v != 0)
         {
            _loc1_ = MasterProxy.instance().checkLack(this.needGold.v,0);
            if(_loc1_ != null)
            {
               return _loc1_;
            }
         }
         return UseHandler.instance().checkLack(this.needList);
      }
      
      public function get goldStr() : String
      {
         if(MasterProxy.instance().gameGold.v < this.needGold.v)
         {
            return TxtUtil.setColor(this.needGold.v + "铜钱","ff0000");
         }
         return TxtUtil.setColor(this.needGold.v + "铜钱","99ff00");
      }
   }
}

