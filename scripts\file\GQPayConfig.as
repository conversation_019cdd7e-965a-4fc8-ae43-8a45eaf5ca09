package file
{
   import mogames.gameData.activity.GQPayVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.vo.BaseRewardVO;
   
   public class GQPayConfig
   {
      private static var _instance:GQPayConfig;
      
      private var _list:Array;
      
      private var _total:Sint;
      
      public var infor:String = "活动时间：2021.2.24-2021.3.28;累计充值可领奖";
      
      public function GQPayConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : GQPayConfig
      {
         if(!_instance)
         {
            _instance = new GQPayConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new GQPayVO(520,500,[new BaseRewardVO(14009,25),new BaseRewardVO(14101,8),new BaseRewardVO(14077,3)]);
         this._list[this._list.length] = new GQPayVO(521,1000,[new BaseRewardVO(14071,66),new BaseRewardVO(50008,6),new BaseRewardVO(70907,3)]);
         this._list[this._list.length] = new GQPayVO(522,2000,[new BaseRewardVO(14009,66),new BaseRewardVO(70160,30),new BaseRewardVO(70918,3)]);
         this._list[this._list.length] = new GQPayVO(523,4000,[new BaseRewardVO(70195,40),new BaseRewardVO(10094,1),new BaseRewardVO(70924,3)]);
         this._list[this._list.length] = new GQPayVO(524,6000,[new BaseRewardVO(70200,40),new BaseRewardVO(70915,3),new BaseRewardVO(70935,3)]);
         this._total = new Sint();
      }
      
      public function get list() : Array
      {
         return this._list;
      }
      
      public function get total() : int
      {
         return this._total.v;
      }
      
      public function set total(param1:int) : void
      {
         this._total.v = param1;
      }
      
      public function get timeStamp() : Object
      {
         var _loc1_:Object = new Object();
         _loc1_.sDate = "2021-02-24|00:00:00";
         _loc1_.eDate = "2021-03-28|23:59:59";
         return _loc1_;
      }
   }
}

