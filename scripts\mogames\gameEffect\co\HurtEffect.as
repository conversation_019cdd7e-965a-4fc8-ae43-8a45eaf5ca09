package mogames.gameEffect.co
{
   import flash.display.DisplayObject;
   import mogames.gameEffect.EffectManager;
   import mogames.gameSystem.CitrusRender;
   
   public class HurtEffect
   {
      private var _count:int;
      
      private var _target:DisplayObject;
      
      private var _time:int;
      
      public function HurtEffect()
      {
         super();
      }
      
      public function setupTarget(param1:DisplayObject, param2:Number = 0.15) : void
      {
         this._time = param2 * CitrusRender.FPS;
         this._target = param1;
         this._count = 0;
         CitrusRender.instance().add(this.update);
      }
      
      public function update() : void
      {
         if(!this._target)
         {
            this.destroy();
         }
         EffectManager.instance().addColor(this._target,255,0,0);
         ++this._count;
         if(this._count >= this._time)
         {
            this.destroy();
         }
      }
      
      public function destroy() : void
      {
         if(this._target)
         {
            EffectManager.instance().removeColor(this._target);
         }
         CitrusRender.instance().remove(this.update);
         this._target = null;
      }
   }
}

