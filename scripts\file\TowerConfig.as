package file
{
   import mogames.gameData.tower.BossNeedVO;
   import mogames.gameData.tower.TowerFloorVO;
   
   public class TowerConfig
   {
      private static var _instance:TowerConfig;
      
      public function TowerConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : TowerConfig
      {
         if(!_instance)
         {
            _instance = new TowerConfig();
         }
         return _instance;
      }
      
      public function newTowers() : Vector.<TowerFloorVO>
      {
         var _loc1_:Vector.<TowerFloorVO> = new Vector.<TowerFloorVO>();
         _loc1_.push(new TowerFloorVO(0,0,[new Boss<PERSON><PERSON><PERSON>(3002),new BossNeedVO(3003),new BossNeed<PERSON>(3004),new BossNeed<PERSON>(3010),new BossNeedVO(3005),new BossNeed<PERSON>(3006),new BossNeed<PERSON>(3013),new Boss<PERSON><PERSON><PERSON>(3014)],[10000,14021,14101,14002,16011,13310,13322,14011,13319]));
         _loc1_.push(new TowerFloorVO(0,1,[new <PERSON><PERSON><PERSON><PERSON>(4001),new Boss<PERSON><PERSON><PERSON>(4002),new Boss<PERSON>eed<PERSON>(4003),new Boss<PERSON><PERSON><PERSON>(4004),new Boss<PERSON><PERSON><PERSON>(3015),new Boss<PERSON>eedVO(3017),new <PERSON>NeedVO(3018),new BossNeedVO(3019)],[10000,14201,14211,14221,13313,13325,14025,14029,14031]));
         _loc1_.push(new TowerFloorVO(0,2,[new BossNeedVO(3020),new BossNeedVO(3021),new <PERSON>NeedVO(3023),new BossNeedVO(3024),new BossNeedVO(3025),new BossNeedVO(4005),new BossNeedVO(3026),new BossNeedVO(3027)],[10000,13319,13320,14027,14035,14037,13401,13402,13403]));
         _loc1_.push(new TowerFloorVO(1,0,[new BossNeedVO(4006),new BossNeedVO(4007),new BossNeedVO(4008),new BossNeedVO(3028),new BossNeedVO(3029),new BossNeedVO(3032)],[10000,13404,13405,13406,16763,16807,16808,16809]));
         _loc1_.push(new TowerFloorVO(1,1,[new BossNeedVO(3034),new BossNeedVO(3035),new BossNeedVO(3037),new BossNeedVO(3038),new BossNeedVO(3042),new BossNeedVO(3043)],[10000,12008,13133,13134,13135,16810,16811,16812]));
         _loc1_.push(new TowerFloorVO(1,2,[new BossNeedVO(3039),new BossNeedVO(3040),new BossNeedVO(3044),new BossNeedVO(3045),new BossNeedVO(3046),new BossNeedVO(3047)],[10000,13136,13137,13138,13139]));
         return _loc1_;
      }
   }
}

