package citrus.physics
{
   public class PhysicsCollisionCategories
   {
      private static var _allCategories:uint = 0;
      
      private static var _numCategories:uint = 0;
      
      private static var _categoryIndexes:Array = [1,2,4,8,16,32,64,128,256,512,1024,2048,4096,8192,16384];
      
      private static var _categoryNames:Object = {};
      
      public function PhysicsCollisionCategories()
      {
         super();
      }
      
      public static function Has(param1:uint, param2:uint) : Boolean
      {
         return Boolean(param1 & param2);
      }
      
      public static function Add(param1:String) : void
      {
         if(_categoryNames[param1])
         {
            return;
         }
         if(_numCategories == 15)
         {
            throw new Error("You can only have 15 categories.");
         }
         _categoryNames[param1] = _categoryIndexes[_numCategories];
         _allCategories |= _categoryIndexes[_numCategories];
         ++_numCategories;
      }
      
      public static function Get(... rest) : uint
      {
         var _loc3_:* = null;
         var _loc4_:* = 0;
         var _loc2_:* = 0;
         for each(_loc3_ in rest)
         {
            _loc4_ = uint(_categoryNames[_loc3_]);
            if(_loc4_ != 0)
            {
               _loc2_ |= _categoryNames[_loc3_];
            }
         }
         return _loc2_;
      }
      
      public static function GetAll() : uint
      {
         return _allCategories;
      }
      
      public static function GetAllExcept(... rest) : uint
      {
         var _loc3_:* = null;
         var _loc4_:* = 0;
         var _loc2_:uint = _allCategories;
         for each(_loc3_ in rest)
         {
            _loc4_ = uint(_categoryNames[_loc3_]);
            if(_loc4_ != 0)
            {
               _loc2_ &= ~_categoryNames[_loc3_];
            }
         }
         return _loc2_;
      }
      
      public static function GetNone() : uint
      {
         return 0;
      }
   }
}

