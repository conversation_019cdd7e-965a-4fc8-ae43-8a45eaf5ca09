package mogames.gameData.maze
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.role.HeroProxy;
   
   public class MazeNeedVO
   {
      public var index:Sint;
      
      public var min:Sint;
      
      public var max:Sint;
      
      public function MazeNeedVO(param1:int, param2:int, param3:int)
      {
         super();
         this.index = new Sint(param1);
         this.min = new Sint(param2);
         this.max = new Sint(param3);
      }
      
      public function get isOpen() : Boolean
      {
         if(!HeroProxy.instance().drakanVO)
         {
            return false;
         }
         return HeroProxy.instance().drakanVO.level.v >= this.min.v;
      }
   }
}

