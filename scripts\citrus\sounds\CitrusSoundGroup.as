package citrus.sounds
{
   import citrus.core.citrus_internal;
   import citrus.events.CitrusEventDispatcher;
   import citrus.events.CitrusSoundEvent;
   import citrus.math.MathUtils;
   
   use namespace citrus_internal;
   
   public class CitrusSoundGroup extends CitrusEventDispatcher
   {
      public static const BGM:String = "BGM";
      
      public static const SFX:String = "SFX";
      
      public static const UI:String = "UI";
      
      protected var _groupID:String;
      
      internal var _volume:Number = 1;
      
      internal var _mute:Boolean = false;
      
      protected var _sounds:Vector.<CitrusSound>;
      
      public var polyphonic:Boolean = true;
      
      public function CitrusSoundGroup()
      {
         super();
         this._sounds = new Vector.<CitrusSound>();
      }
      
      protected function applyChanges() : void
      {
         var _loc1_:CitrusSound = null;
         for each(_loc1_ in this._sounds)
         {
            _loc1_.resetSoundTransform(true);
         }
      }
      
      internal function addSound(param1:CitrusSound) : void
      {
         if(param1.group && Boolean(param1.group.isadded(param1)))
         {
            (param1.group as CitrusSoundGroup).removeSound(param1);
         }
         param1.setGroup(this);
         this._sounds.push(param1);
         param1.addEventListener(CitrusSoundEvent.SOUND_LOADED,this.handleSoundLoaded);
      }
      
      internal function isadded(param1:CitrusSound) : Boolean
      {
         var _loc2_:CitrusSound = null;
         for each(_loc2_ in this._sounds)
         {
            if(param1 == _loc2_)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getAllSounds() : Vector.<CitrusSound>
      {
         return this._sounds.slice();
      }
      
      public function preloadSounds() : void
      {
         var _loc1_:CitrusSound = null;
         for each(_loc1_ in this._sounds)
         {
            if(!_loc1_.loaded)
            {
               _loc1_.load();
            }
         }
      }
      
      public function stopAllSounds() : void
      {
         var _loc1_:CitrusSound = null;
         for each(_loc1_ in this._sounds)
         {
            _loc1_.stop();
         }
      }
      
      internal function removeSound(param1:CitrusSound) : void
      {
         var _loc2_:String = null;
         var _loc3_:CitrusSound = null;
         for(_loc2_ in this._sounds)
         {
            if(this._sounds[_loc2_] == param1)
            {
               _loc3_ = this._sounds[_loc2_];
               _loc3_.setGroup(null);
               _loc3_.resetSoundTransform(true);
               _loc3_.removeEventListener(CitrusSoundEvent.SOUND_LOADED,this.handleSoundLoaded);
               this._sounds.splice(uint(_loc2_),1);
               break;
            }
         }
      }
      
      public function getSound(param1:String) : CitrusSound
      {
         var _loc2_:CitrusSound = null;
         for each(_loc2_ in this._sounds)
         {
            if(_loc2_.name == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function getRandomSound() : CitrusSound
      {
         var _loc1_:uint = uint(MathUtils.randomInt(0,this._sounds.length - 1));
         return this._sounds[_loc1_];
      }
      
      protected function handleSoundLoaded(param1:CitrusSoundEvent) : void
      {
         var _loc2_:CitrusSound = null;
         for each(_loc2_ in this._sounds)
         {
            if(!_loc2_.loaded)
            {
               return;
            }
         }
         dispatchEvent(new CitrusSoundEvent(CitrusSoundEvent.ALL_SOUNDS_LOADED,param1.sound,null));
      }
      
      public function set mute(param1:Boolean) : void
      {
         this._mute = param1;
         this.applyChanges();
      }
      
      public function get mute() : Boolean
      {
         return this._mute;
      }
      
      public function set volume(param1:Number) : void
      {
         this._volume = param1;
         this.applyChanges();
      }
      
      public function get volume() : Number
      {
         return this._volume;
      }
      
      public function get isPlaying() : Boolean
      {
         var _loc1_:CitrusSound = null;
         for each(_loc1_ in this._sounds)
         {
            if(_loc1_.isPlaying)
            {
               return true;
            }
         }
         return false;
      }
      
      citrus_internal function setGroupID(param1:String) : void
      {
         this._groupID = param1;
      }
      
      public function get groupID() : String
      {
         return this._groupID;
      }
      
      internal function destroy() : void
      {
         var _loc1_:CitrusSound = null;
         for each(_loc1_ in this._sounds)
         {
            this.removeSound(_loc1_);
         }
         this._sounds.length = 0;
         removeEventListeners();
      }
   }
}

