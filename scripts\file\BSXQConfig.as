package file
{
   import mogames.gameData.forge.NeedVO;
   import mogames.gameData.gem.XQGemCostVO;
   import mogames.gameData.shop.vo.GameCostVO;
   
   public class BSXQConfig
   {
      private static var _instance:BSXQConfig;
      
      private var _list:Array;
      
      private var _costVec:Vector.<XQGemCostVO>;
      
      public function BSXQConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : BSXQConfig
      {
         if(!_instance)
         {
            _instance = new BSXQConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._costVec = new Vector.<XQGemCostVO>();
         this._costVec.push(new XQGemCostVO(0,new GameCostVO(0,1000,[new NeedVO(14010,1)]),new GameCostVO(0,2000,[new NeedVO(14020,1)]),new GameCostVO(0,1000,[])));
         this._costVec.push(new XQGemCostVO(1,new GameCostVO(0,2500,[new NeedVO(14010,1)]),new GameCostVO(0,5000,[new NeedVO(14020,1)]),new GameCostVO(0,2500,[])));
         this._costVec.push(new XQGemCostVO(2,new GameCostVO(0,5000,[new NeedVO(14010,2)]),new GameCostVO(0,10000,[new NeedVO(14020,2)]),new GameCostVO(0,5000,[])));
         this._costVec.push(new XQGemCostVO(3,new GameCostVO(0,7500,[new NeedVO(14010,2)]),new GameCostVO(0,15000,[new NeedVO(14020,2)]),new GameCostVO(0,7500,[])));
         this._costVec.push(new XQGemCostVO(4,new GameCostVO(0,10000,[new NeedVO(14010,3)]),new GameCostVO(0,20000,[new NeedVO(14020,3)]),new GameCostVO(0,10000,[])));
      }
      
      public function findCostVO(param1:int) : XQGemCostVO
      {
         var _loc2_:XQGemCostVO = null;
         for each(_loc2_ in this._costVec)
         {
            if(_loc2_.index.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

