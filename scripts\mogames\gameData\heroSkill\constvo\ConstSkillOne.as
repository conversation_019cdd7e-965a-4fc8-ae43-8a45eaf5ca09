package mogames.gameData.heroSkill.constvo
{
   import mogames.gameData.heroSkill.SkillLevelVO;
   
   public class ConstSkillOne extends ConstSkillVO
   {
      public function ConstSkillOne(param1:int, param2:String, param3:String, param4:String, param5:String)
      {
         super(param1,param2,param3,param4,param5);
      }
      
      override protected function initCondition() : void
      {
         super.initCondition();
         _lvVec.push(new SkillLevelVO(0,1,200));
         _lvVec.push(new SkillLevelVO(1,6,400));
         _lvVec.push(new SkillLevelVO(2,11,600));
         _lvVec.push(new SkillLevelVO(3,16,800));
         _lvVec.push(new SkillLevelVO(4,21,1000));
         _lvVec.push(new SkillLevelVO(5,26,1200));
         _lvVec.push(new SkillLevelVO(6,31,1400));
         _lvVec.push(new SkillLevelVO(7,36,1600));
         _lvVec.push(new SkillLevelVO(8,41,1800));
         _lvVec.push(new SkillLevelVO(9,46,2000));
      }
   }
}

