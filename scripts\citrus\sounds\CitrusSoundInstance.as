package citrus.sounds
{
   import citrus.core.CitrusEngine;
   import citrus.core.citrus_internal;
   import citrus.events.CitrusEvent;
   import citrus.events.CitrusEventDispatcher;
   import citrus.events.CitrusSoundEvent;
   import citrus.utils.SoundChannelUtil;
   import flash.events.Event;
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundTransform;
   
   use namespace citrus_internal;
   
   public class CitrusSoundInstance extends CitrusEventDispatcher
   {
      protected static var last_id:uint = 0;
      
      protected static var _list:Vector.<CitrusSoundInstance> = new Vector.<CitrusSoundInstance>();
      
      protected static var _nonPermanent:Vector.<CitrusSoundInstance> = new Vector.<CitrusSoundInstance>();
      
      public static var onNewChannelsUnavailable:String = REMOVE_FIRST_PLAYED;
      
      public static var startPositionOffset:Number = 0;
      
      public static var eventVerbose:Boolean = false;
      
      protected static var _maxChannels:uint = SoundChannelUtil.maxAvailableChannels();
      
      public static const REMOVE_LAST_PLAYED:String = "REMOVE_LAST_PLAYED";
      
      public static const REMOVE_FIRST_PLAYED:String = "REMOVE_FIRST_PLAYED";
      
      public static const DONT_PLAY:String = "DONT_PLAY";
      
      if(maxChannels < 32)
      {
      }
      
      public var data:Object = {};
      
      protected var _ID:uint = 0;
      
      protected var _name:String;
      
      protected var _parentsound:CitrusSound;
      
      protected var _soundTransform:SoundTransform;
      
      protected var _permanent:Boolean = false;
      
      protected var _volume:Number = 1;
      
      protected var _panning:Number = 0;
      
      protected var _soundChannel:SoundChannel;
      
      protected var _isPlaying:Boolean = false;
      
      protected var _isPaused:Boolean = false;
      
      protected var _isActive:Boolean = false;
      
      protected var _loops:int = 0;
      
      protected var _loopCount:int = 0;
      
      protected var _last_position:Number = 0;
      
      protected var _destroyed:Boolean = false;
      
      protected var _ce:CitrusEngine;
      
      protected var _autodestroy:Boolean;
      
      public function CitrusSoundInstance(param1:CitrusSound, param2:Boolean = true, param3:Boolean = true)
      {
         super();
         this._parentsound = param1;
         this._permanent = this._parentsound.permanent;
         this._soundTransform = this._parentsound.resetSoundTransform();
         this._ID = last_id++;
         this._parentsound.citrus_internal::addDispatchChild(this);
         this._ce = CitrusEngine.getInstance();
         this._name = this._parentsound.name;
         this._loops = this._parentsound.loops;
         this._autodestroy = param3;
         if(param2)
         {
            this.play();
         }
      }
      
      public static function get maxChannels() : uint
      {
         return _maxChannels;
      }
      
      public static function get activeSoundInstances() : Vector.<CitrusSoundInstance>
      {
         return _list.slice();
      }
      
      public function play() : void
      {
         if(this._destroyed)
         {
            return;
         }
         if(!this._isPaused || !this._isPlaying)
         {
            try
            {
               this.playAt(startPositionOffset);
            }
            catch(e:Error)
            {
            }
         }
      }
      
      public function playAt(param1:Number) : void
      {
         var _loc2_:CitrusSoundInstance = null;
         var _loc4_:int = 0;
         if(this._destroyed)
         {
            return;
         }
         if(this._parentsound.group && !this._parentsound.group.polyphonic)
         {
            CitrusSoundGroup(this._parentsound.group).stopAllSounds();
         }
         if(this._permanent)
         {
            for each(_loc2_ in _list)
            {
               if(_loc2_._name == this._name)
               {
                  this.dispatcher(CitrusSoundEvent.NO_CHANNEL_AVAILABLE);
                  this.stop(true);
                  return;
               }
            }
         }
         if(_list.length >= maxChannels)
         {
            loop3:
            switch(onNewChannelsUnavailable)
            {
               case REMOVE_FIRST_PLAYED:
                  _loc4_ = 0;
                  while(true)
                  {
                     if(_loc4_ >= _nonPermanent.length - 1)
                     {
                        break loop3;
                     }
                     _loc2_ = _nonPermanent[_loc4_];
                     if(Boolean(_loc2_) && !_loc2_.isPaused)
                     {
                        _loc2_.stop(true);
                     }
                     if(_list.length + 1 <= _maxChannels)
                     {
                        break loop3;
                     }
                     _loc4_ = 0;
                     _loc4_++;
                  }
                  break;
               case REMOVE_LAST_PLAYED:
                  _loc4_ = int(_nonPermanent.length - 1);
                  while(true)
                  {
                     if(_loc4_ <= -1)
                     {
                        break loop3;
                     }
                     _loc2_ = _nonPermanent[_loc4_];
                     if(Boolean(_loc2_) && !_loc2_.isPaused)
                     {
                        _loc2_.stop(true);
                     }
                     if(_list.length + 1 <= _maxChannels)
                     {
                        break loop3;
                     }
                     _loc4_ = int(_nonPermanent.length - 1);
                     _loc4_--;
                  }
                  break;
               case DONT_PLAY:
                  this.dispatcher(CitrusSoundEvent.NO_CHANNEL_AVAILABLE);
                  this.stop(true);
                  return;
            }
         }
         if(!this._parentsound.ready)
         {
            this.dispatcher(CitrusSoundEvent.SOUND_NOT_READY);
            this._parentsound.load();
         }
         if(_list.length >= _maxChannels)
         {
            this.dispatcher(CitrusSoundEvent.NO_CHANNEL_AVAILABLE);
            this.stop(true);
            return;
         }
         this._isActive = true;
         if(Boolean(this._parentsound.sound as Sound) && !this._parentsound.ioerror)
         {
            this.soundChannel = (this._parentsound.sound as Sound).play(param1,this._loops < 0 ? int.MAX_VALUE : 0);
         }
         this._isPlaying = true;
         this._isPaused = false;
         this.resetSoundTransform();
         _list.unshift(this);
         if(!this._permanent)
         {
            _nonPermanent.unshift(this);
         }
         this._parentsound.soundInstances.unshift(this);
         if((param1 == 0 || param1 == startPositionOffset) && this._loopCount == 0)
         {
            this.dispatcher(CitrusSoundEvent.SOUND_START);
         }
      }
      
      public function pause() : void
      {
         if(!this._isActive)
         {
            return;
         }
         if(this._soundChannel)
         {
            this._last_position = this._soundChannel.position;
            this._soundChannel.stop();
         }
         this.soundChannel = this._parentsound.sound.play(0,int.MAX_VALUE);
         this._isPlaying = false;
         this._isPaused = true;
         this.resetSoundTransform();
         this.dispatcher(CitrusSoundEvent.SOUND_PAUSE);
      }
      
      public function resume() : void
      {
         if(!this._isActive)
         {
            return;
         }
         this._soundChannel.stop();
         this.soundChannel = this._parentsound.sound.play(this._last_position,0);
         this._isPlaying = true;
         this._isPaused = false;
         this.resetSoundTransform();
         this.dispatcher(CitrusSoundEvent.SOUND_RESUME);
      }
      
      public function stop(param1:Boolean = false) : void
      {
         if(this._destroyed)
         {
            return;
         }
         if(this._soundChannel)
         {
            this._soundChannel.stop();
         }
         this.soundChannel = null;
         this._isPlaying = false;
         this._isPaused = false;
         this._isActive = false;
         this._loopCount = 0;
         this.removeSelfFromVector(_list);
         this.removeSelfFromVector(_nonPermanent);
         this.removeSelfFromVector(this._parentsound.soundInstances);
         if(param1)
         {
            this.dispatcher(CitrusSoundEvent.FORCE_STOP);
         }
         this.dispatcher(CitrusSoundEvent.SOUND_END);
         if(this._autodestroy)
         {
            this.destroy();
         }
      }
      
      public function destroy(param1:Boolean = false) : void
      {
         this._parentsound.citrus_internal::removeDispatchChild(this);
         this._parentsound = null;
         this._soundTransform = null;
         this.data = null;
         this.soundChannel = null;
         removeEventListeners();
         this._destroyed = true;
      }
      
      protected function onComplete(param1:Event) : void
      {
         if(this._isPaused)
         {
            this.soundChannel = this._parentsound.sound.play(0,int.MAX_VALUE);
            return;
         }
         ++this._loopCount;
         if(this._loops < 0)
         {
            this._soundChannel.stop();
            this.soundChannel = (this._parentsound.sound as Sound).play(startPositionOffset,int.MAX_VALUE);
            this.resetSoundTransform();
         }
         else if(this._loopCount > this._loops)
         {
            this.stop();
         }
         else
         {
            this._soundChannel.stop();
            this.soundChannel = (this._parentsound.sound as Sound).play(startPositionOffset,0);
            this.resetSoundTransform();
            this.dispatcher(CitrusSoundEvent.SOUND_LOOP);
         }
      }
      
      public function set volume(param1:Number) : void
      {
         this._volume = param1;
         this.resetSoundTransform();
      }
      
      public function get volume() : Number
      {
         return this._volume;
      }
      
      public function set panning(param1:Number) : void
      {
         this._panning = param1;
         this.resetSoundTransform();
      }
      
      public function get panning() : Number
      {
         return this._panning;
      }
      
      public function setVolumePanning(param1:Number = 1, param2:Number = 0) : CitrusSoundInstance
      {
         this._volume = param1;
         this._panning = param2;
         this.resetSoundTransform();
         return this;
      }
      
      public function removeSelfFromVector(param1:Vector.<CitrusSoundInstance>) : void
      {
         var _loc2_:String = null;
         for(_loc2_ in param1)
         {
            if(param1[_loc2_] == this)
            {
               param1[_loc2_] = null;
               param1.splice(int(_loc2_),1);
               return;
            }
         }
      }
      
      internal function set soundChannel(param1:SoundChannel) : void
      {
         if(this._soundChannel)
         {
            this._soundChannel.removeEventListener(Event.SOUND_COMPLETE,this.onComplete,true);
         }
         if(param1)
         {
            param1.addEventListener(Event.SOUND_COMPLETE,this.onComplete);
         }
         this._soundChannel = param1;
      }
      
      public function getSoundChannel() : SoundChannel
      {
         return this._soundChannel;
      }
      
      internal function get soundChannel() : SoundChannel
      {
         return this._soundChannel;
      }
      
      public function get leftPeak() : Number
      {
         if(this._soundChannel)
         {
            return this._soundChannel.leftPeak;
         }
         return 0;
      }
      
      public function get rightPeak() : Number
      {
         if(this._soundChannel)
         {
            return this._soundChannel.rightPeak;
         }
         return 0;
      }
      
      public function get parentsound() : CitrusSound
      {
         return this._parentsound;
      }
      
      public function get ID() : uint
      {
         return this._ID;
      }
      
      public function get isPlaying() : Boolean
      {
         return this._isPlaying;
      }
      
      public function get isPaused() : Boolean
      {
         return this._isPaused;
      }
      
      internal function get isActive() : Boolean
      {
         return this._isActive;
      }
      
      public function get loopCount() : uint
      {
         return this._loopCount;
      }
      
      public function get loops() : int
      {
         return this._loops;
      }
      
      internal function dispatcher(param1:String) : void
      {
         var _loc2_:CitrusEvent = new CitrusSoundEvent(param1,this._parentsound,this,this.ID) as CitrusEvent;
         dispatchEvent(_loc2_);
         if(eventVerbose)
         {
         }
      }
      
      internal function get destroyed() : Boolean
      {
         return this._destroyed;
      }
      
      internal function resetSoundTransform(param1:Boolean = true) : SoundTransform
      {
         this._soundTransform = param1 ? this._parentsound.resetSoundTransform() : this._parentsound.soundTransform;
         this._soundTransform.volume *= this._volume;
         this._soundTransform.pan = this._panning;
         if(this._soundChannel)
         {
            if(this._isPaused)
            {
               return this._soundChannel.soundTransform = SoundChannelUtil.silentST;
            }
            return this._soundChannel.soundTransform = this._soundTransform;
         }
         return this._soundTransform;
      }
      
      public function toString() : String
      {
         return "CitrusSoundInstance name:" + this._name + " id:" + this._ID + " playing:" + this._isPlaying + " paused:" + this._isPaused + "\n";
      }
   }
}

