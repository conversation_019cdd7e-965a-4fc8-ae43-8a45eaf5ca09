package mogames.gameMission.mission.pansidong
{
   import citrus.objects.CitrusSprite;
   import flash.display.MovieClip;
   import mogames.Layers;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.co.HurtEffect;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   import mogames.gameSystem.CitrusTimer;
   
   public class TrapSpiderEgg extends CitrusSprite implements IRole
   {
      private var _mc:CitrusMC;
      
      private var _timer:CitrusTimer;
      
      private var _interval:int;
      
      private var _duwuFunc:Function;
      
      private var _hurtCount:int;
      
      private var _hurtEffect:HurtEffect;
      
      private var _active:Boolean;
      
      public var maxCount:int;
      
      public function TrapSpiderEgg(param1:int, param2:Function)
      {
         this._interval = param1;
         this._duwuFunc = param2;
         super("TrapSpiderEgg",{
            "width":190,
            "height":112,
            "group":3
         });
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("MC_ZHI_ZHU_BALL_CLIP") as MovieClip,this.onComplete,this.updateFrame);
         this._mc.changeAnimation("idle",true);
         this.view = this._mc;
         this._mc.mc.mcNum.gotoAndStop(1);
         this._mc.mc.mcNum.visible = false;
         this._timer = new CitrusTimer();
         this._hurtEffect = new HurtEffect();
      }
      
      private function listenHurt(param1:BaseHitVO) : void
      {
         if(!this._active || kill)
         {
            return;
         }
         if(Boolean(param1.source) && param1.source.hasTargets(this))
         {
            return;
         }
         if(param1.atkType.v == 1 || !this._mc.mc.mcAreaHurt || !this._mc.mc.mcAreaHurt.hitTestObject(param1.range))
         {
            return;
         }
         if(param1.source)
         {
            param1.source.addTargets(this);
         }
         ++this._hurtCount;
         if(this._hurtCount >= this.maxCount)
         {
            kill = true;
         }
         else
         {
            this._hurtEffect.setupTarget(this._mc);
         }
         EffectManager.instance().addBMCEffect(param1.hurtSkin,Layers.ceEffectLayer,x,y);
         EffectManager.instance().playAudio(param1.hurtSound);
      }
      
      public function startTimer() : void
      {
         var func:Function = null;
         var onTime:Function = null;
         func = function():void
         {
            _mc.changeAnimation("attack",false);
         };
         onTime = function():void
         {
            _mc.mc.mcNum.gotoAndStop(_timer.leftTime + 1);
         };
         this._active = true;
         this._timer.setInterval(1,this._interval,onTime,func);
         this._mc.mc.mcNum.visible = true;
         BattleMediator.instance().onHeroATK.add(this.listenHurt);
      }
      
      private function onComplete(param1:String) : void
      {
         if(param1 == "attack")
         {
            kill = true;
         }
      }
      
      private function updateFrame(param1:int) : void
      {
         if(param1 != 11)
         {
            return;
         }
         if(this._duwuFunc != null)
         {
            this._duwuFunc(x,y);
         }
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return false;
      }
      
      public function addTargets(param1:IRole) : void
      {
      }
      
      public function cleanTargets() : void
      {
      }
      
      public function hitBack() : void
      {
      }
      
      override public function destroy() : void
      {
         BattleMediator.instance().onHeroATK.remove(this.listenHurt);
         this._timer.destroy();
         this._timer = null;
         this._mc.destroy();
         this._mc = null;
         this._duwuFunc = null;
         super.destroy();
      }
   }
}

