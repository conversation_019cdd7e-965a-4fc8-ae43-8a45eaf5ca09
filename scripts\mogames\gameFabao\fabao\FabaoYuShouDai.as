package mogames.gameFabao.fabao
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameData.pet.PetProxy;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.view.FabaoYuShouDaiView;
   import mogames.gameMission.BattleMediator;
   import mogames.gameRole.PetFactory;
   import mogames.gameRole.enemy.BasePetEnemy;
   import mogames.gameRole.hero.BaseHero;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import org.osflash.signals.Signal;
   import utils.MathUtil;
   
   public class FabaoYuShouDai extends BaseFabao
   {
      private var _hitVO:BaseHitVO;
      
      public var onZhuaBu:Signal;
      
      public function FabaoYuShouDai(param1:GameFabaoVO)
      {
         super(param1,40,48);
         this.createFabaoData();
         this.createVO();
         createView(new FabaoYuShouDaiView());
         this.onZhuaBu = new Signal(BasePetEnemy);
         this.onZhuaBu.add(this.handlerZhuaBu);
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(15),
            "hurt":new Sint(1),
            "rate":new Sint(250)
         },{
            "cdTime":new Snum(14),
            "hurt":new Sint(1),
            "rate":new Sint(300)
         },{
            "cdTime":new Snum(13),
            "hurt":new Sint(1),
            "rate":new Sint(350)
         },{
            "cdTime":new Snum(12),
            "hurt":new Sint(1),
            "rate":new Sint(400)
         },{
            "cdTime":new Snum(11),
            "hurt":new Sint(1),
            "rate":new Sint(450)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      private function createVO() : void
      {
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._hitVO.HURT_X = 0;
         this._hitVO.HURT_Y = 0;
         this._hitVO.HURT_SKIN = "EFFECT_WATER_BOOM0";
         this._hitVO.HURT_SOUND = "GUNHURT";
         this._hitVO.WU_XING.v = _fabaoVO.constFabao.wuxing.v;
      }
      
      override public function followTarget() : void
      {
         if(_fabaoView.isSkill)
         {
            return;
         }
         super.followTarget();
      }
      
      override public function handlerSkill() : void
      {
         this.x = owner.x + owner.width * owner.dirX;
         super.handlerSkill();
      }
      
      public function dispatchSkillOne() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         this._hitVO.init(_fabaoView.mcATK,_fabaoData.hurt.v,1,false);
         this._hitVO.isMiss = false;
         if(owner is BaseHero)
         {
            BattleMediator.instance().onHeroATK.dispatch(this._hitVO);
         }
         else
         {
            BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
         }
      }
      
      private function handlerZhuaBu(param1:BasePetEnemy) : void
      {
         if(PetProxy.instance().isFullPet)
         {
            MiniMsgMediator.instance().showAutoMsg("宠物栏已满，无法捕捉！");
            return;
         }
         if(MathUtil.checkOdds(_fabaoData.rate.v))
         {
            PetProxy.instance().addNewPet(PetFactory.instance().newPetVO(param1.petID),true);
            param1.setSysDead();
            MiniMsgMediator.instance().showAutoMsg("获得了新的宠物！",480,300,"SUCCESS1");
            TaskProxy.instance().setComplete(11027);
         }
         else
         {
            EffectManager.instance().addHeadWord("捕捉失败！",x,y);
         }
      }
      
      override public function destroy() : void
      {
         this.onZhuaBu.removeAll();
         this.onZhuaBu = null;
         super.destroy();
      }
   }
}

