package mogames.gameMission.mission.shuangchaling
{
   import citrus.objects.CitrusSprite;
   import flash.events.MouseEvent;
   import mogames.Layers;
   import mogames.event.UIEvent;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameObj.display.CitrusMC;
   import utils.FilterFactory;
   import utils.MethodUtil;
   
   public class SaveNPC extends CitrusSprite
   {
      public var mcNPC:CitrusMC;
      
      public var openFunc:Function;
      
      public var taskID:int;
      
      public function SaveNPC(param1:String, param2:int, param3:int)
      {
         super("SaveNPC",{
            "width":param2,
            "height":param3,
            "group":2
         });
         touchable = true;
         this.createView(param1);
      }
      
      public function createView(param1:String) : void
      {
         this.mcNPC = new CitrusMC();
         this.mcNPC.setupMC(GameInLoader.instance().findAsset(param1),this.onAniEnd);
         this.mcNPC.changeAnimation("lock",true);
         this.mcNPC.buttonMode = true;
         this.mcNPC.addEventListener(MouseEvent.MOUSE_OVER,this.onOver,false,0,true);
         this.mcNPC.addEventListener(MouseEvent.MOUSE_OUT,this.onOut,false,0,true);
         this.mcNPC.addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
         this.view = this.mcNPC;
      }
      
      public function changeAnimation(param1:String, param2:Boolean = true) : void
      {
         this.mcNPC.changeAnimation(param1,param2);
         if(param1 == "open")
         {
            EffectManager.instance().playAudio("OPEN_LOCK");
         }
      }
      
      private function onAniEnd(param1:String) : void
      {
         if(param1 == "open")
         {
            MethodUtil.setMousable(this.mcNPC,false);
            if(this.openFunc != null)
            {
               this.openFunc();
            }
            this.changeAnimation("stand",true);
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         var _loc2_:CitrusMC = param1.target as CitrusMC;
         _loc2_.filters = [FilterFactory.getLightFilter()];
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         var _loc2_:CitrusMC = param1.target as CitrusMC;
         _loc2_.filters = [];
      }
      
      private function onDown(param1:MouseEvent) : void
      {
         this.mcNPC.dispatchEvent(new UIEvent(UIEvent.NPC_EVENT,{"type":"onClick"}));
      }
      
      public function disappear() : void
      {
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,x + width * 0.5,y + height * 0.5);
         this.kill = true;
      }
      
      override public function destroy() : void
      {
         this.mcNPC.removeEventListener(MouseEvent.MOUSE_OVER,this.onOver);
         this.mcNPC.removeEventListener(MouseEvent.MOUSE_OUT,this.onOut);
         this.mcNPC.removeEventListener(MouseEvent.CLICK,this.onDown);
         this.mcNPC.destroy();
         super.destroy();
      }
   }
}

