package mogames.gameMission.mission.qilinshan
{
   import Box2D.Dynamics.Contacts.b2Contact;
   import citrus.physics.box2d.Box2DUtils;
   import citrus.physics.box2d.IBox2DPhysicsObject;
   import flash.geom.Point;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameObj.box2d.BlockTile;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.hero.BaseHero;
   import mogames.gameSystem.CitrusTimer;
   
   public class BaseCloud extends BlockTile
   {
      private var _mc:CitrusMC;
      
      private var _idleTime:Number;
      
      private var _showTime:Number;
      
      private var _timer:CitrusTimer;
      
      private var _location:Point;
      
      public var isPause:Boolean = false;
      
      public function BaseCloud(param1:String, param2:Object = null)
      {
         _beginContactCallEnabled = true;
         super("BaseCloud",param2);
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset(param1),this.onComplete);
         this._mc.changeAnimation("idle",true);
         this.view = this._mc;
         this._timer = new CitrusTimer();
         this._location = new Point();
      }
      
      public function setLocation(param1:int, param2:int) : void
      {
         this._location = new Point(param1,param2);
         x = param1;
         y = param2;
      }
      
      public function setTime(param1:Number, param2:Number) : void
      {
         this._idleTime = param1;
         this._showTime = param2;
      }
      
      public function stopCloud() : void
      {
         x = this._location.x;
         y = this._location.y;
         this._mc.visible = true;
         this._mc.changeAnimation("idle",true);
         _beginContactCallEnabled = false;
         this.isPause = true;
      }
      
      private function onComplete(param1:String) : void
      {
         if(param1 == "show")
         {
            this._mc.changeAnimation("idle",true);
         }
         else if(param1 == "start")
         {
            this.hideCloud();
         }
      }
      
      override public function handleBeginContact(param1:b2Contact) : void
      {
         var collider:IBox2DPhysicsObject;
         var func:Function = null;
         var contact:b2Contact = param1;
         func = function():void
         {
            _mc.changeAnimation("start",false);
         };
         if(this.isPause)
         {
            return;
         }
         collider = Box2DUtils.CollisionGetOther(this,contact);
         if(!(collider is BaseHero) || (collider as BaseHero).isDead)
         {
            return;
         }
         if(this._mc.curLabel == "idle" && !this._timer.running)
         {
            this._timer.setTimeOut(this._idleTime,func);
         }
      }
      
      private function hideCloud() : void
      {
         this._mc.changeAnimation("hide",true);
         this._mc.visible = false;
         y = -1000;
         this._timer.setTimeOut(this._showTime,this.showCloud);
      }
      
      private function showCloud() : void
      {
         x = this._location.x;
         y = this._location.y;
         this._mc.visible = true;
         this._mc.changeAnimation("show",false);
      }
      
      override public function destroy() : void
      {
         this._timer.destroy();
         this._timer = null;
         this._location = null;
         this._mc.destroy();
         this._mc = null;
         super.destroy();
      }
   }
}

