package mogames.gameData.pet.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.forge.NeedVO;
   
   public class PetGrowCostVO extends BaseUseVO
   {
      public var min:Sint;
      
      public var max:Sint;
      
      public var needDan:NeedVO;
      
      public function PetGrowCostVO(param1:int, param2:int, param3:int, param4:NeedVO)
      {
         super(param3,[param4]);
         this.min = new Sint(param1);
         this.max = new Sint(param2);
         this.needDan = param4;
      }
   }
}

