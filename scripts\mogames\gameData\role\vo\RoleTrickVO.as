package mogames.gameData.role.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   
   public class RoleTrickVO
   {
      public static const MIN_HURT_TIME:Snum = new Snum(0.3);
      
      public var id:Sint;
      
      public var thinkTime:Snum;
      
      public var hurtTime:Snum;
      
      public var range:int;
      
      public function RoleTrickVO(param1:int, param2:Number, param3:Number, param4:int)
      {
         super();
         this.id = new Sint(param1);
         this.thinkTime = new Snum(param2);
         this.hurtTime = new Snum(param3);
         this.range = param4;
         if(this.hurtTime.v < MIN_HURT_TIME.v)
         {
            this.hurtTime.v = MIN_HURT_TIME.v;
         }
      }
   }
}

