package mogames.gameData.pk.enemy
{
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.heroSkill.zhubajie.SkillDDYPVO;
   import mogames.gameData.heroSkill.zhubajie.SkillKFSJVO;
   
   public class PKEnemyPigVO extends PKEnemyVO
   {
      public function PKEnemyPigVO(param1:int)
      {
         allSkills = [];
         allSkills.push(new HurtSkillVO(1011,this,0));
         allSkills.push(new HurtSkillVO(1012,this,1));
         allSkills.push(new SkillKFSJVO(1013,this,2));
         allSkills.push(new SkillDDYPVO(1014,this,3));
         allSkills.push(new HurtSkillVO(1015,this,4));
         super(1003,param1);
      }
   }
}

