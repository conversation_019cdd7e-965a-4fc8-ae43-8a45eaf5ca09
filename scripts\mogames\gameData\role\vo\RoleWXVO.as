package mogames.gameData.role.vo
{
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   
   public class RoleWXVO
   {
      public var mine:Sint;
      
      public var zero:Sint;
      
      public var one:Sint;
      
      public var two:Sint;
      
      public var three:Sint;
      
      public var four:Sint;
      
      public var five:Sint;
      
      public function RoleWXVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:int, param7:int)
      {
         super();
         this.mine = new Sint(param1);
         this.zero = new Sint(param2);
         this.one = new Sint(param3);
         this.two = new Sint(param4);
         this.three = new Sint(param5);
         this.four = new Sint(param6);
         this.five = new Sint(param7);
      }
      
      public function findPer(param1:int) : Sint
      {
         switch(param1)
         {
            case ConstData.WX_ZERO.v:
               return this.zero;
            case ConstData.WX_ONE.v:
               return this.one;
            case ConstData.WX_TWO.v:
               return this.two;
            case ConstData.WX_THREE.v:
               return this.three;
            case ConstData.WX_FOUR.v:
               return this.four;
            case ConstData.WX_FIVE.v:
               return this.five;
            default:
               return this.zero;
         }
      }
   }
}

