package citrus.view.spriteview
{
   import citrus.core.CitrusEngine;
   import flash.display.FrameLabel;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.utils.Dictionary;
   import org.osflash.signals.Signal;
   
   public class AnimationSequence extends Sprite
   {
      protected var _ce:CitrusEngine;
      
      protected var _mc:MovieClip;
      
      protected var anims:Dictionary;
      
      protected var time:int = 0;
      
      protected var _currentAnim:AnimationSequenceData;
      
      protected var _currentFrame:int = 0;
      
      protected var _looping:Boolean = false;
      
      protected var _playing:Boolean = false;
      
      protected var _paused:Boolean = false;
      
      protected var _animChanged:Boolean = false;
      
      public var onAnimationComplete:Signal;
      
      public var fpsRatio:Number = 0.5;
      
      public function AnimationSequence(param1:MovieClip)
      {
         super();
         this._ce = CitrusEngine.getInstance();
         this._mc = param1;
         this.anims = new Dictionary();
         if(this._mc.totalFrames != 1)
         {
            this.onAnimationComplete = new Signal(String);
            this.setupMCActions();
            this._mc.gotoAndStop(0);
            this._ce.stage.addEventListener(Event.ENTER_FRAME,this.handleEnterFrame);
         }
         addChild(this._mc);
      }
      
      protected function handleEnterFrame(param1:Event) : void
      {
         if(this._paused)
         {
            return;
         }
         if(this._playing)
         {
            if(this.fpsRatio == 1 || this.time % (1 / this.fpsRatio << 0) == 0)
            {
               this._mc.nextFrame();
            }
            ++this.time;
            if(this._mc.currentFrame == this._currentAnim.endFrame)
            {
               this.handleAnimationComplete();
            }
         }
      }
      
      protected function handleAnimationComplete() : void
      {
         this.onAnimationComplete.dispatch(this._currentAnim.name);
         if(this._looping && this._playing)
         {
            this.changeAnimation(this._currentAnim.name,this._looping);
         }
         else
         {
            this._playing = false;
         }
      }
      
      protected function setupMCActions() : void
      {
         var _loc1_:String = null;
         var _loc2_:int = 0;
         var _loc3_:FrameLabel = null;
         var _loc4_:String = null;
         for each(_loc3_ in this._mc.currentLabels)
         {
            _loc1_ = _loc3_.name;
            _loc2_ = _loc3_.frame;
            if(!(_loc1_ in this.anims))
            {
               this.anims[_loc1_] = new AnimationSequenceData(_loc1_,_loc2_);
               if(!this._currentAnim)
               {
                  this._currentAnim = this.anims[_loc1_];
               }
            }
         }
         for each(_loc3_ in this._mc.currentLabels)
         {
            if(_loc4_)
            {
               AnimationSequenceData(this.anims[_loc4_]).endFrame = _loc3_.frame - 1;
            }
            _loc4_ = _loc3_.name;
         }
         AnimationSequenceData(this.anims[_loc4_]).endFrame = this._mc.totalFrames - 1;
      }
      
      public function pause() : void
      {
         this._paused = true;
         this._ce.stage.removeEventListener(Event.ENTER_FRAME,this.handleEnterFrame);
      }
      
      public function resume() : void
      {
         this._paused = false;
         this._ce.stage.addEventListener(Event.ENTER_FRAME,this.handleEnterFrame);
      }
      
      public function changeAnimation(param1:String, param2:Boolean = false) : void
      {
         this._looping = param2;
         if(param1 in this.anims)
         {
            this._currentAnim = this.anims[param1];
            this._mc.gotoAndStop(this._currentAnim.startFrame);
            this._playing = true;
         }
      }
      
      public function hasAnimation(param1:String) : Boolean
      {
         return this.anims[param1];
      }
      
      public function destroy() : void
      {
         this._ce.stage.removeEventListener(Event.ENTER_FRAME,this.handleEnterFrame);
         this.onAnimationComplete.removeAll();
         this.anims = null;
         removeChild(this._mc);
      }
      
      public function get mc() : MovieClip
      {
         return this._mc;
      }
   }
}

class AnimationSequenceData
{
   internal var startFrame:int;
   
   internal var endFrame:int;
   
   internal var name:String;
   
   public function AnimationSequenceData(param1:String, param2:int = -1, param3:int = -1)
   {
      super();
      this.name = param1;
      this.startFrame = param2;
      this.endFrame = param3;
   }
}
