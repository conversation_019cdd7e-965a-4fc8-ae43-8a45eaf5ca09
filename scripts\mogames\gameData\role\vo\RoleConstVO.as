package mogames.gameData.role.vo
{
   import mogames.gameData.base.Sint;
   
   public class RoleConstVO
   {
      public var roleID:Sint;
      
      public var wuxing:Sint;
      
      public var firJump:Sint;
      
      public var secJump:Sint;
      
      public var width:int;
      
      public var height:int;
      
      public function RoleConstVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:int)
      {
         super();
         this.roleID = new Sint(param1);
         this.wuxing = new Sint(param2);
         this.firJump = new Sint(param3);
         this.secJump = new Sint(param4);
         this.width = param5;
         this.height = param6;
      }
   }
}

