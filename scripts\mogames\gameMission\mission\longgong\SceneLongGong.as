package mogames.gameMission.mission.longgong
{
   import file.StoryConfig;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameUI.dialog.DialogMediator;
   import mogames.gameUI.pet.MiniPetModule;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.MethodUtil;
   
   public class SceneLongGong extends BaseScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _btnPet:SimpleButton;
      
      private var _npc:CitrusMCSprite;
      
      public function SceneLongGong(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         sceneType = 1;
         EffectManager.instance().playBGM("BGM_DANGER3");
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this.layoutPetBtn();
         this.layoutNPC();
         this.initEnemies();
      }
      
      private function layoutNPC() : void
      {
         if(HeroProxy.instance().drakanVO != null)
         {
            return;
         }
         this._npc = new CitrusMCSprite("SEQ_XIAO_LONG_NV_STAND_CLIP",{
            "width":28,
            "height":106,
            "x":353,
            "y":368,
            "group":2
         });
         this._npc.changeAnimation("stand",true);
         this._npc.addMouseListener(this.addXLN);
         add(this._npc);
      }
      
      private function layoutPetBtn() : void
      {
         this._btnPet = AssetManager.newButtonRes("BTN_PET_CLIP",887,16);
         Layers.frameLayer.addChild(this._btnPet);
         this._btnPet.addEventListener(MouseEvent.CLICK,this.onPet,false,0,true);
      }
      
      private function onPet(param1:MouseEvent) : void
      {
         MiniPetModule.instance().init();
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,1920,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[1150,300,150,100],[1388,300,150,100]],1301,this.triggerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[2075,306,150,100],[2645,306,150,100]],1302,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[2983,306,150,100],[3587,306,150,100]],1303,this.handlerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(1650,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(2366,450),1,new Rectangle(960,0,1920,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(3348,450),1,new Rectangle(2880,0,960,600));
         this._pTrigger1.setBlock(false,false,true,true);
         this._pTrigger2.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function handlerEnd() : void
      {
         addLeaveDoor(3350,438);
      }
      
      private function addXLN() : void
      {
         var func:Function = null;
         func = function():void
         {
            HeroProxy.instance().unlockHero(1005);
            HeroProxy.instance().drakanVO.setLevel(10);
            FlagProxy.instance().setValue(100500,10);
            _npc.visible = false;
            EffectManager.instance().addScreenEffect(16777215);
            MiniMsgMediator.instance().showAutoMsg("【小龙女】加入队伍！",480,200,"NEW_FUNCTION");
         };
         DialogMediator.instance().init(StoryConfig.instance().findDialog("STORY_XLN"),func);
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._btnPet.removeEventListener(MouseEvent.CLICK,this.onPet);
         MethodUtil.removeMe(this._btnPet);
         this._btnPet = null;
         super.destroy();
         this._npc = null;
      }
   }
}

