package mogames.gameData.petSkill.active
{
   import mogames.gameData.heroSkill.constvo.ConstSkillVO;
   import mogames.gameData.petSkill.PetSkillLevelVO;
   
   public class PetConstSkillTwo extends ConstSkillVO
   {
      public function PetConstSkillTwo(param1:int, param2:String, param3:String, param4:String, param5:String)
      {
         super(param1,param2,param3,param4,param5);
      }
      
      override protected function initCondition() : void
      {
         super.initCondition();
         _lvVec.push(new PetSkillLevelVO(0,1,5000));
         _lvVec.push(new PetSkillLevelVO(1,3,5000));
         _lvVec.push(new PetSkillLevelVO(2,8,10000));
         _lvVec.push(new PetSkillLevelVO(3,13,15000));
         _lvVec.push(new PetSkillLevelVO(4,18,20000));
         _lvVec.push(new PetSkillLevelVO(5,23,25000));
         _lvVec.push(new PetSkillLevelVO(6,28,30000));
         _lvVec.push(new PetSkillLevelVO(7,33,35000));
         _lvVec.push(new PetSkillLevelVO(8,38,40000));
         _lvVec.push(new PetSkillLevelVO(9,43,45000));
      }
   }
}

