package file
{
   import mogames.gameData.chenghao.BaseChenghaoVO;
   import mogames.gameData.chenghao.ch.ChenghaoDTYS;
   import mogames.gameData.chenghao.ch.ChenghaoMYXR;
   import mogames.gameData.chenghao.ch.ChenghaoMYZZ;
   import mogames.gameData.chenghao.ch.ChenghaoRWDR;
   import mogames.gameData.chenghao.ch.ChenghaoTBYS;
   import mogames.gameData.chenghao.ch.ChenghaoTHao;
   import mogames.gameData.chenghao.ch.ChenghaoXYYS;
   import mogames.gameData.chenghao.ch.ChenghaoYLXX;
   import mogames.gameData.chenghao.ch.ChenghaoZQKL;
   
   public class ChenghaoConfig
   {
      private static var _instance:ChenghaoConfig;
      
      public function ChenghaoConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : ChenghaoConfig
      {
         if(!_instance)
         {
            _instance = new ChenghaoConfig();
         }
         return _instance;
      }
      
      public function newChenghaos() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new ChenghaoMYXR(65001,0,"通关双叉岭"));
         _loc1_.push(new ChenghaoYLXX(65002,0,"降服小白龙、猪八戒、沙僧"));
         _loc1_.push(new ChenghaoDTYS(65003,0,"通关东土地图所有场景"));
         _loc1_.push(new ChenghaoTBYS(65004,0,"通关吐蕃地图所有场景"));
         _loc1_.push(new ChenghaoXYYS(65010,0,"通关西域地图所有场景"));
         _loc1_.push(new ChenghaoMYZZ(65011,0,"通关天竺地图所有场景"));
         _loc1_.push(new ChenghaoRWDR(65006,1,"完成100次每日任务"));
         _loc1_.push(new BaseChenghaoVO(65005,1,"背包中铜钱达到100W",true));
         _loc1_.push(new ChenghaoTHao(65008,1,"累计金锭充值达到5000"));
         _loc1_.push(new BaseChenghaoVO(65015,1,"国庆期间内登录游戏自动获得<br>",true));
         _loc1_.push(new ChenghaoZQKL(65009,1,"登录游戏自动获得",true));
         _loc1_.push(new BaseChenghaoVO(65007,1,"暑假期间内登录游戏自动获得<br>",true));
         _loc1_.push(new BaseChenghaoVO(65016,1,"登录游戏自动获得",true));
         _loc1_.push(new BaseChenghaoVO(65017,1,"活动期间登录游戏自动获得<br>（活动已结束）",true));
         _loc1_.push(new BaseChenghaoVO(65100,2,"竞技场周赛领奖时排名第1获得",true));
         _loc1_.push(new BaseChenghaoVO(65101,2,"竞技场周赛领奖时排名第2获得",true));
         _loc1_.push(new BaseChenghaoVO(65102,2,"竞技场周赛领奖时排名第3获得",true));
         _loc1_.push(new BaseChenghaoVO(65103,2,"竞技场周赛领奖时排名第4-10获得",true));
         _loc1_.push(new BaseChenghaoVO(65104,2,"竞技场周赛领奖时排名第11-50获得",true));
         return _loc1_;
      }
   }
}

