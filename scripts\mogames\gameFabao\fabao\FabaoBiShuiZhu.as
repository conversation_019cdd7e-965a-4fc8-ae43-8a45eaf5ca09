package mogames.gameFabao.fabao
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameFabao.view.FabaoBiShuiZhuView;
   
   public class FabaoBi<PERSON><PERSON><PERSON>hu extends BaseFabao
   {
      public function FabaoBiShuiZhu(param1:GameFabaoVO)
      {
         super(param1,40,44);
         this.createFabaoData();
         createView(new FabaoBiShuiZhuView());
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(30),
            "keepTime":new Snum(10),
            "per":new Sint(10)
         },{
            "cdTime":new Snum(30),
            "keepTime":new Snum(10),
            "per":new Sint(14)
         },{
            "cdTime":new Snum(30),
            "keepTime":new Snum(10),
            "per":new Sint(18)
         },{
            "cdTime":new Snum(30),
            "keepTime":new Snum(10),
            "per":new Sint(22)
         },{
            "cdTime":new Snum(30),
            "keepTime":new Snum(10),
            "per":new Sint(26)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      public function addBuff() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         owner.addBuff(new BuffVO(1009,_fabaoData.keepTime.v,{"per":_fabaoData.per.v}));
         owner.addBuff(new BuffVO(1010,_fabaoData.keepTime.v,{
            "per":_fabaoData.per.v,
            "skin":"EFFECT_BUFF_BI_SHUI_ZHU_CLIP"
         }));
      }
   }
}

