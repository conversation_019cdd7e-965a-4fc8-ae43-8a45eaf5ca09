package citrus.input.controllers
{
   import citrus.input.InputController;
   import flash.events.KeyboardEvent;
   import flash.utils.Dictionary;
   import flash.utils.describeType;
   import org.osflash.signals.Signal;
   
   public class Keyboard extends InputController
   {
      public static const NUMBER_0:uint = 48;
      
      public static const NUMBER_1:uint = 49;
      
      public static const NUMBER_2:uint = 50;
      
      public static const NUMBER_3:uint = 51;
      
      public static const NUMBER_4:uint = 52;
      
      public static const NUMBER_5:uint = 53;
      
      public static const NUMBER_6:uint = 54;
      
      public static const NUMBER_7:uint = 55;
      
      public static const NUMBER_8:uint = 56;
      
      public static const NUMBER_9:uint = 57;
      
      public static const A:uint = 65;
      
      public static const B:uint = 66;
      
      public static const C:uint = 67;
      
      public static const D:uint = 68;
      
      public static const E:uint = 69;
      
      public static const F:uint = 70;
      
      public static const G:uint = 71;
      
      public static const H:uint = 72;
      
      public static const I:uint = 73;
      
      public static const J:uint = 74;
      
      public static const K:uint = 75;
      
      public static const L:uint = 76;
      
      public static const M:uint = 77;
      
      public static const N:uint = 78;
      
      public static const O:uint = 79;
      
      public static const P:uint = 80;
      
      public static const Q:uint = 81;
      
      public static const R:uint = 82;
      
      public static const S:uint = 83;
      
      public static const T:uint = 84;
      
      public static const U:uint = 85;
      
      public static const V:uint = 86;
      
      public static const W:uint = 87;
      
      public static const X:uint = 88;
      
      public static const Y:uint = 89;
      
      public static const Z:uint = 90;
      
      public static const BACKSPACE:uint = 8;
      
      public static const TAB:uint = 9;
      
      public static const ENTER:uint = 13;
      
      public static const SHIFT:uint = 16;
      
      public static const CTRL:uint = 17;
      
      public static const CAPS_LOCK:uint = 20;
      
      public static const ESCAPE:uint = 27;
      
      public static const SPACE:uint = 32;
      
      public static const PAGE_UP:uint = 33;
      
      public static const PAGE_DOWN:uint = 34;
      
      public static const END:uint = 35;
      
      public static const HOME:uint = 36;
      
      public static const LEFT:uint = 37;
      
      public static const UP:uint = 38;
      
      public static const RIGHT:uint = 39;
      
      public static const DOWN:uint = 40;
      
      public static const INSERT:uint = 45;
      
      public static const DELETE:uint = 46;
      
      public static const BREAK:uint = 19;
      
      public static const NUM_LOCK:uint = 144;
      
      public static const SCROLL_LOCK:uint = 145;
      
      public static const NUMPAD_0:uint = 96;
      
      public static const NUMPAD_1:uint = 97;
      
      public static const NUMPAD_2:uint = 98;
      
      public static const NUMPAD_3:uint = 99;
      
      public static const NUMPAD_4:uint = 100;
      
      public static const NUMPAD_5:uint = 101;
      
      public static const NUMPAD_6:uint = 102;
      
      public static const NUMPAD_7:uint = 103;
      
      public static const NUMPAD_8:uint = 104;
      
      public static const NUMPAD_9:uint = 105;
      
      public static const NUMPAD_MULTIPLY:uint = 105;
      
      public static const NUMPAD_ADD:uint = 107;
      
      public static const NUMPAD_ENTER:uint = 13;
      
      public static const NUMPAD_SUBTRACT:uint = 109;
      
      public static const NUMPAD_DECIMAL:uint = 110;
      
      public static const NUMPAD_DIVIDE:uint = 111;
      
      public static const F1:uint = 112;
      
      public static const F2:uint = 113;
      
      public static const F3:uint = 114;
      
      public static const F4:uint = 115;
      
      public static const F5:uint = 116;
      
      public static const F6:uint = 117;
      
      public static const F7:uint = 118;
      
      public static const F8:uint = 119;
      
      public static const F9:uint = 120;
      
      public static const F10:uint = 121;
      
      public static const F11:uint = 122;
      
      public static const F12:uint = 123;
      
      public static const F13:uint = 124;
      
      public static const F14:uint = 125;
      
      public static const F15:uint = 126;
      
      public static const COMMAND:uint = 15;
      
      public static const ALTERNATE:uint = 18;
      
      public static const BACKQUOTE:uint = 192;
      
      public static const QUOTE:uint = 222;
      
      public static const COMMA:uint = 188;
      
      public static const PERIOD:uint = 190;
      
      public static const SEMICOLON:uint = 186;
      
      public static const BACKSLASH:uint = 220;
      
      public static const SLASH:uint = 191;
      
      public static const EQUAL:uint = 187;
      
      public static const MINUS:uint = 189;
      
      public static const LEFT_BRACKET:uint = 219;
      
      public static const RIGHT_BRACKET:uint = 221;
      
      public static const AUDIO:uint = 16777239;
      
      public static const BACK:uint = 16777238;
      
      public static const MENU:uint = 16777234;
      
      public static const SEARCH:uint = 16777247;
      
      public static const AZERTY_SQUARE:uint = 222;
      
      public static const AZERTY_RIGHT_PARENTHESIS:uint = 219;
      
      public static const AZERTY_CIRCUMFLEX:uint = 221;
      
      public static const AZERTY_DOLLAR_SIGN:uint = 186;
      
      public static const AZERTY_U_GRAVE:uint = 192;
      
      public static const AZERTY_MULTIPLY:uint = 220;
      
      public static const AZERTY_EXCLAMATION_MARK:uint = 223;
      
      protected var _keyActions:Dictionary;
      
      public var onKeyUp:Signal;
      
      public var onKeyDown:Signal;
      
      public var keyNames:Dictionary;
      
      public function Keyboard(param1:String, param2:Object = null)
      {
         var _loc4_:String = null;
         var _loc5_:* = 0;
         var _loc6_:XML = null;
         super(param1,param2);
         this._keyActions = new Dictionary();
         this.addKeyAction("left",LEFT);
         this.addKeyAction("up",UP);
         this.addKeyAction("right",RIGHT);
         this.addKeyAction("down",DOWN);
         this.addKeyAction("jump",SPACE);
         _ce.stage.addEventListener(KeyboardEvent.KEY_DOWN,this.handleKeyDown);
         _ce.stage.addEventListener(KeyboardEvent.KEY_UP,this.handleKeyUp);
         this.onKeyUp = new Signal(uint,int,Object);
         this.onKeyDown = new Signal(uint,int,Object);
         this.keyNames = new Dictionary();
         var _loc3_:XMLList = describeType(Keyboard).child("constant");
         for each(_loc6_ in _loc3_)
         {
            _loc4_ = _loc6_.attribute("name");
            _loc5_ = uint(Keyboard[_loc4_]);
            if(_loc4_.substr(0,7) != "AZERTY_")
            {
               if(_loc5_ is uint)
               {
                  this.keyNames[_loc5_] = _loc4_;
               }
            }
         }
      }
      
      private function handleKeyDown(param1:KeyboardEvent) : void
      {
         var _loc2_:Object = null;
         var _loc3_:Object = null;
         if(this.onKeyDown.numListeners > 0)
         {
            _loc2_ = {
               "prevent":false,
               "stop":false
            };
            this.onKeyDown.dispatch(param1.keyCode,param1.keyLocation,_loc2_);
            if(_loc2_.prevent)
            {
               param1.preventDefault();
            }
            if(_loc2_.stop)
            {
               param1.stopImmediatePropagation();
               return;
            }
         }
         if(this._keyActions[param1.keyCode])
         {
            for each(_loc3_ in this._keyActions[param1.keyCode])
            {
               triggerON(_loc3_.name,1,null,_loc3_.channel < 0 ? int(defaultChannel) : int(_loc3_.channel));
            }
         }
      }
      
      private function handleKeyUp(param1:KeyboardEvent) : void
      {
         var _loc2_:Object = null;
         var _loc3_:Object = null;
         if(this.onKeyUp.numListeners > 0)
         {
            _loc2_ = {
               "prevent":false,
               "stop":false
            };
            this.onKeyUp.dispatch(param1.keyCode,param1.keyLocation,_loc2_);
            if(_loc2_.prevent)
            {
               param1.preventDefault();
            }
            if(_loc2_.stop)
            {
               param1.stopImmediatePropagation();
               return;
            }
         }
         if(this._keyActions[param1.keyCode])
         {
            for each(_loc3_ in this._keyActions[param1.keyCode])
            {
               triggerOFF(_loc3_.name,0,null,_loc3_.channel < 0 ? int(defaultChannel) : int(_loc3_.channel));
            }
         }
      }
      
      public function addKeyAction(param1:String, param2:uint, param3:int = -1) : void
      {
         var _loc4_:Object = null;
         if(!this._keyActions[param2])
         {
            this._keyActions[param2] = new Vector.<Object>();
         }
         else
         {
            for each(_loc4_ in this._keyActions[param2])
            {
               if(_loc4_.name == param1 && _loc4_.channel == param3)
               {
                  return;
               }
            }
         }
         this._keyActions[param2].push({
            "name":param1,
            "channel":param3
         });
      }
      
      public function removeActionFromKey(param1:String, param2:uint) : void
      {
         var _loc3_:Vector.<Object> = null;
         var _loc4_:String = null;
         if(this._keyActions[param2])
         {
            _loc3_ = this._keyActions[param2];
            for(_loc4_ in _loc3_)
            {
               if(_loc3_[_loc4_].name == param1)
               {
                  triggerOFF(param1);
                  _loc3_.splice(uint(_loc4_),1);
                  return;
               }
            }
         }
      }
      
      public function removeAction(param1:String) : void
      {
         var _loc2_:Vector.<Object> = null;
         var _loc3_:String = null;
         for each(_loc2_ in this._keyActions)
         {
            for(_loc3_ in _loc2_)
            {
               if(_loc2_[uint(_loc3_)].name == param1)
               {
                  triggerOFF(param1);
                  _loc2_.splice(uint(_loc3_),1);
               }
            }
         }
      }
      
      public function resetAllKeyActions() : void
      {
         this._keyActions = new Dictionary();
         _ce.input.stopActionsOf(this);
      }
      
      public function changeKeyAction(param1:uint, param2:uint) : void
      {
         var _loc3_:Vector.<Object> = this.getActionsByKey(param1);
         this.setKeyActions(param2,_loc3_);
         this.removeKeyActions(param1);
      }
      
      private function setKeyActions(param1:uint, param2:Vector.<Object>) : void
      {
         if(!this._keyActions[param1])
         {
            this._keyActions[param1] = param2;
         }
         _ce.input.stopActionsOf(this);
      }
      
      public function removeKeyActions(param1:uint) : void
      {
         delete this._keyActions[param1];
         _ce.input.stopActionsOf(this);
      }
      
      public function getActionsByKey(param1:uint) : Vector.<Object>
      {
         if(this._keyActions[param1])
         {
            return this._keyActions[param1];
         }
         return null;
      }
      
      public function getKeysFromAction(param1:String, param2:int = -1) : Array
      {
         var _loc4_:String = null;
         var _loc5_:Object = null;
         var _loc3_:Array = [];
         for(_loc4_ in this._keyActions)
         {
            for each(_loc5_ in this._keyActions[uint(_loc4_)])
            {
               if(_loc5_.name == param1 && (param2 > -1 ? (_loc5_.channel > -1 ? _loc5_.channel == param2 : true) : true))
               {
                  _loc3_.push(this.keyNames[uint(_loc4_)]);
               }
            }
         }
         return _loc3_;
      }
      
      public function getKeyFromAction(param1:String, param2:int = -1) : String
      {
         var _loc3_:Array = this.getKeysFromAction(param1,param2);
         if(Boolean(_loc3_) && _loc3_.length > 0)
         {
            return _loc3_[0];
         }
         return null;
      }
      
      override public function destroy() : void
      {
         this.onKeyUp.removeAll();
         this.onKeyDown.removeAll();
         _ce.stage.removeEventListener(KeyboardEvent.KEY_DOWN,this.handleKeyDown);
         _ce.stage.removeEventListener(KeyboardEvent.KEY_UP,this.handleKeyUp);
         this._keyActions = null;
         super.destroy();
      }
   }
}

