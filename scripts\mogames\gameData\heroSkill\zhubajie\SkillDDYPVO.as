package mogames.gameData.heroSkill.zhubajie
{
   import mogames.gameData.base.Snum;
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.role.HeroGameVO;
   
   public class SkillDDYPVO extends HurtSkillVO
   {
      public var stunTime:Snum = new Snum();
      
      public function SkillDDYPVO(param1:int, param2:HeroGameVO, param3:int = 0)
      {
         super(param1,param2,param3);
      }
      
      override public function setLevel(param1:int, param2:Boolean = false) : void
      {
         super.setLevel(param1,param2);
         this.stunTime.v = countVO.argDic.arg0.v + countVO.argDic.arg1.v * level.v;
      }
   }
}

