package mogames.gameEffect.co
{
   import flash.display.Bitmap;
   import mogames.gameEffect.FallFactory;
   import mogames.gameSystem.SysRender;
   import utils.MethodUtil;
   
   public class FallBitmap extends Bitmap
   {
      private var _aa:Number = 0.5;
      
      private var _vx:Number;
      
      private var _vy:Number;
      
      private var _count:int;
      
      public function FallBitmap()
      {
         super();
      }
      
      public function start(param1:int) : void
      {
         this._count = 0;
         this._vy = -(Math.random() * 10 + 5);
         this._vx = (Math.random() * 5 + 5) * param1;
         SysRender.instance().add(this.updateTarget);
      }
      
      public function updateTarget() : void
      {
         if(this._vy > 0)
         {
            ++this._count;
         }
         this._vy += this._aa;
         y += this._vy;
         x += this._vx;
         if(this._count >= 20)
         {
            this.destroy();
         }
      }
      
      protected function destroy() : void
      {
         bitmapData = null;
         SysRender.instance().remove(this.updateTarget);
         MethodUtil.removeMe(this);
         FallFactory.instance().recyle(this);
      }
   }
}

