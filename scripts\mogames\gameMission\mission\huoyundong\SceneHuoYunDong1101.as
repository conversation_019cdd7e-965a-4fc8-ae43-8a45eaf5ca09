package mogames.gameMission.mission.huoyundong
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneHuoYunDong1101 extends BaseScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      public function SceneHuoYunDong1101(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_SUNSHINE");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            TaskProxy.instance().addTask(11014);
            FlagProxy.instance().setValue(2021,1);
         };
         super.handlerInit();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2021))
         {
            showDialog("STORY0046",func);
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[224,329,150,100],[465,329,150,100]],11011,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[720,329,150,100],[1012,329,150,100]],11012,this.triggerEnd1);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1254,329,150,100],[1508,329,150,100]],11013);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(180,480),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(920,480),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1700,480),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger0.start();
      }
      
      private function triggerEnd0() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
         this._pTrigger1.start();
      }
      
      private function triggerEnd1() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
         this._pTrigger2.start();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         super.destroy();
      }
   }
}

