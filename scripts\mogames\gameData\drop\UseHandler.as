package mogames.gameData.drop
{
   import file.GoodConfig;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.forge.NeedVO;
   import utils.TxtUtil;
   
   public class UseHandler
   {
      private static var _instance:UseHandler;
      
      public function UseHandler()
      {
         super();
      }
      
      public static function instance() : UseHandler
      {
         if(!_instance)
         {
            _instance = new UseHandler();
         }
         return _instance;
      }
      
      public function useStuff(param1:Array) : void
      {
         var _loc2_:NeedVO = null;
         for each(_loc2_ in param1)
         {
            BagProxy.instance().useItems(_loc2_.id.v,_loc2_.num.v);
         }
      }
      
      public function checkLack(param1:Array) : LackVO
      {
         var _loc2_:NeedVO = null;
         for each(_loc2_ in param1)
         {
            if(BagProxy.instance().getGoodNum(_loc2_.id.v) < _loc2_.num.v)
            {
               return new LackVO(GoodConfig.instance().findConstGood(_loc2_.id.v).name + "不足！");
            }
         }
         return null;
      }
      
      public function parseNeed(param1:Array) : String
      {
         var _loc3_:NeedVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in param1)
         {
            if(BagProxy.instance().getGoodNum(_loc3_.id.v) < _loc3_.num.v)
            {
               _loc2_.push(TxtUtil.setColor(_loc3_.needName + "X" + _loc3_.num.v,"ff0000"));
            }
            else
            {
               _loc2_.push(TxtUtil.setColor(_loc3_.needName + "X" + _loc3_.num.v,"99ff00"));
            }
         }
         return _loc2_.join("，");
      }
   }
}

