package mogames.gameFabao.view
{
   import mogames.gameData.ConstRole;
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoLingLongDing;
   
   public class FabaoLingLongDingView extends FabaoBaseView
   {
      private var _fabao:FabaoLingLongDing;
      
      public function FabaoLingLongDingView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         this.addSound(param1);
         super.updateCurrentFrame(param1);
         if(!mcATK)
         {
            return;
         }
         if(curStatus == ConstRole.SKILL_ONE)
         {
            this._fabao.dispatchSkillOne();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as FabaoLingLongDing;
      }
      
      override protected function addSound(param1:int) : void
      {
         if([39,41,43,45].indexOf(param1) != -1)
         {
            EffectManager.instance().playAudio("BOOM0");
         }
      }
   }
}

