package mogames.gameData.task.vo
{
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameData.task.constvo.ConstTask;
   
   public class BaseTask
   {
      protected var _flag:Sint;
      
      protected var _isGet:Sint;
      
      public var constTask:ConstTask;
      
      public function BaseTask(param1:ConstTask)
      {
         super();
         this.constTask = param1;
         this._flag = new Sint();
         this._isGet = new Sint();
      }
      
      public function reset() : void
      {
         this._isGet.v = 0;
      }
      
      public function setComplete() : void
      {
         this._flag.v = ConstData.DATA_NUM1.v;
         EventManager.dispatchEvent(UIEvent.MENU_TIP_EVENT,{"type":"updateTaskBtn"});
         TaskProxy.instance().taskHandler.handlerComplete(this.constTask.id.v);
      }
      
      public function checkComplete() : void
      {
      }
      
      public function handlerGet() : void
      {
         this._isGet.v = ConstData.DATA_NUM1.v;
      }
      
      public function get isComplete() : Boolean
      {
         return this._flag.v == ConstData.DATA_NUM1.v;
      }
      
      public function get isGet() : Boolean
      {
         return this._isGet.v >= ConstData.DATA_NUM1.v;
      }
      
      public function get targetStr() : String
      {
         return "";
      }
      
      public function get saveData() : String
      {
         return "";
      }
      
      public function set loadData(param1:Array) : void
      {
      }
   }
}

