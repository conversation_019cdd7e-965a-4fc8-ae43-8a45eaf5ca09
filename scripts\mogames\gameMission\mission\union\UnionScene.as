package mogames.gameMission.mission.union
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.union.qiongqi.BOSSQiongQi;
   import mogames.gameRole.enemy.BOSSTaoTie;
   import mogames.gameUI.prompt.MsgMediator;
   import unit4399.Open4399Union;
   
   public class UnionScene extends BossScene
   {
      private var _dataVO:UnionFBDataVO;
      
      public function UnionScene(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(270,342,446,110),new Point(480,414));
         this._dataVO = FBUnionProxy.instance().curData;
         if(!this._dataVO)
         {
            this._dataVO = FBUnionProxy.instance().fubens[0];
         }
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.addBOSS();
         startBossBattle("BGM_BATTLE1");
         this._dataVO.handlerNum();
      }
      
      override protected function addBOSS() : void
      {
         switch(this._dataVO.id)
         {
            case 1:
               _boss = new BOSSTaoTie();
               _boss.initData(40191,40191,0);
               break;
            case 2:
               _boss = new BOSSQiongQi();
               _boss.initData(40201,40201,0);
         }
         _boss.x = 816;
         _boss.y = 200;
         _boss.target = _mission.curTarget;
         add(_boss);
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         Layers.setKeyEnable(false);
         this.handlerFinish();
      }
      
      protected function handlerFinish() : void
      {
         var func:Function = null;
         func = function(param1:Boolean):void
         {
            MsgMediator.clean();
            if(!param1)
            {
               handlerFinish();
            }
            else
            {
               MissionManager.instance().handlerWin(false,true);
            }
         };
         MsgMediator.instance().show("正在处理数据，请稍等");
         Open4399Union.changeVar(this._dataVO.hpID,func);
      }
   }
}

