package mogames.gameData.fuben.TZJS
{
   import mogames.gameData.base.Sint;
   
   public class TZJSVO
   {
      public var flagID:Sint;
      
      public var dropList:Array;
      
      public var limitList:Array;
      
      public function TZJSVO(param1:int, param2:Array, param3:Array)
      {
         super();
         this.flagID = new Sint(param1);
         this.limitList = param2;
         this.dropList = param3;
      }
   }
}

