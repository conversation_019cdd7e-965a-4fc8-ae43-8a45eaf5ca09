package mogames.gameData.pet
{
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetHurtSkillVO;
   
   public class PetQiLinVO extends PetGameVO
   {
      public function PetQiLinVO()
      {
         allSkills = this.newActiveSkills();
         super(1106);
      }
      
      override public function newActiveSkills() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new PetHurtSkillVO(11061,this));
         _loc1_.push(new PetHurtSkillVO(11062,this));
         _loc1_.push(new PetHurtSkillVO(11063,this));
         _loc1_.push(new PetHurtSkillVO(11064,this,2));
         _loc1_.push(new PetHurtSkillVO(11065,this));
         return _loc1_;
      }
   }
}

