package file
{
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.sign.SignFlagVO;
   
   public class SignConfig
   {
      private static var _instance:SignConfig;
      
      private var _signFlags:Vector.<SignFlagVO>;
      
      public var signRewards:Array;
      
      public function SignConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : SignConfig
      {
         if(!_instance)
         {
            _instance = new SignConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._signFlags = new Vector.<SignFlagVO>();
         this._signFlags.push(new SignFlagVO(0,300,3,2,[new BaseRewardVO(10000,20000),new BaseRewardVO(14150,2),new BaseRewardVO(14009,5),new BaseRewardVO(14002,10),new BaseRewardVO(14001,10)]));
         this._signFlags.push(new SignFlagVO(1,700,7,2,[new BaseRewardVO(10000,40000),new BaseRewardVO(16751,3),new BaseRewardVO(14009,5),new BaseRewardVO(16761,5),new BaseRewardVO(14002,10),new BaseRewardVO(16757,5)]));
         this._signFlags.push(new SignFlagVO(2,1200,12,2,[new BaseRewardVO(10000,60000),new BaseRewardVO(14101,2),new BaseRewardVO(16755,3),new BaseRewardVO(16756,3),new BaseRewardVO(16757,10),new BaseRewardVO(50000,5)]));
         this._signFlags.push(new SignFlagVO(3,2000,20,2,[new BaseRewardVO(10000,80000),new BaseRewardVO(71103,3),new BaseRewardVO(14021,10),new BaseRewardVO(16752,3),new BaseRewardVO(16760,2),new BaseRewardVO(50005,2)]));
         this._signFlags.push(new SignFlagVO(4,3000,0,2,[new BaseRewardVO(10000,100000),new BaseRewardVO(14204,1),new BaseRewardVO(14214,1),new BaseRewardVO(14224,1),new BaseRewardVO(14039,10),new BaseRewardVO(70956,1)]));
         this.signRewards = [new BaseRewardVO(10000,10000)];
      }
      
      public function findSignFlagVO(param1:int) : SignFlagVO
      {
         var _loc2_:SignFlagVO = null;
         for each(_loc2_ in this._signFlags)
         {
            if(_loc2_.index.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function newSignRewards(param1:Boolean) : Array
      {
         var _loc3_:GameGoodVO = null;
         var _loc2_:Array = RewardHandler.instance().newGiftReward(this.signRewards);
         if(!param1)
         {
            return _loc2_;
         }
         for each(_loc3_ in _loc2_)
         {
            _loc3_.amount.v *= 2;
         }
         return _loc2_;
      }
   }
}

