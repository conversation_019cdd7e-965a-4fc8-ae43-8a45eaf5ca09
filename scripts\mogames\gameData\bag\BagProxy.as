package mogames.gameData.bag
{
   import file.FBDZConfig;
   import file.GoodConfig;
   import file.PEHCConfig;
   import flash.utils.Dictionary;
   import mogames.gameData.ConstData;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.chenghao.ChenghaoProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.good.GameEquipVO;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.good.constvo.ConstGemVO;
   import mogames.gameData.good.constvo.ConstSeedVO;
   import mogames.gameData.task.TaskProxy;
   import utils.MathUtil;
   
   public class BagProxy
   {
      private static var _instance:BagProxy;
      
      public static const BAG_PAGE_NUM:Sint = new Sint(25);
      
      public static const BAG_MAX_COUNT:Sint = new Sint(99);
      
      public static const GOOD_GOLD:Sint = new Sint(10000);
      
      public static const GOOD_TICKET:Sint = new Sint(10007);
      
      public static const GOOD_SKILL_RESET:Sint = new Sint(16710);
      
      public static const GOOD_KAI_KONG:Sint = new Sint(14010);
      
      public static const GOOD_GOLD_CHUI:Sint = new Sint(14020);
      
      public static const GOOD_TIAN_DAN:int = 14150;
      
      public static const GOOD_XIAN_DAN:int = 14160;
      
      public static const GOOD_FEN_CHEN:int = 14000;
      
      public static const GOOD_SHEN_TAN:int = 14030;
      
      public static const GOOD_FU_HUO:int = 14009;
      
      private var _bagDic:Dictionary;
      
      private var wd:Array = [0,10,20,40,60,80,100,150,200,9999];
      
      public function BagProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this._bagDic = new Dictionary();
      }
      
      public static function instance() : BagProxy
      {
         if(!_instance)
         {
            _instance = new BagProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._bagDic[ConstData.GOOD_PROP] = [];
         this._bagDic[ConstData.GOOD_EQUIP] = [];
         this._bagDic[ConstData.GOOD_FASHION] = [];
         this._bagDic[ConstData.GOOD_PET] = [];
         this._bagDic[ConstData.GOOD_CLIP] = [];
         this._bagDic[ConstData.GOOD_CARD] = [];
      }
      
      public function set loadData(param1:Object) : void
      {
         this.startNew();
         if(!param1)
         {
            return;
         }
         this.parseProp(param1.prop,this._bagDic[ConstData.GOOD_PROP]);
         this.parseEquip(param1.equip,this._bagDic[ConstData.GOOD_EQUIP]);
         this.parseEquip(param1.fashion,this._bagDic[ConstData.GOOD_FASHION]);
         if(param1.pet)
         {
            this.parseEquip(param1.pet,this._bagDic[ConstData.GOOD_PET]);
         }
         if(param1.clip)
         {
            this.parseProp(param1.clip,this._bagDic[ConstData.GOOD_CLIP]);
         }
         if(param1.card)
         {
            this.parseProp(param1.card,this._bagDic[ConstData.GOOD_CARD]);
         }
      }
      
      public function get saveData() : Object
      {
         return {
            "prop":this.collectProp(this._bagDic[ConstData.GOOD_PROP]),
            "equip":this.collectEquip(this._bagDic[ConstData.GOOD_EQUIP]),
            "fashion":this.collectEquip(this._bagDic[ConstData.GOOD_FASHION]),
            "pet":this.collectEquip(this._bagDic[ConstData.GOOD_PET]),
            "clip":this.collectProp(this._bagDic[ConstData.GOOD_CLIP]),
            "card":this.collectProp(this._bagDic[ConstData.GOOD_CARD])
         };
      }
      
      private function parseProp(param1:String, param2:Array) : void
      {
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         var _loc8_:GameGoodVO = null;
         if(param1 == "")
         {
            return;
         }
         var _loc3_:Array = param1.split("T");
         var _loc4_:int = 0;
         var _loc5_:int = int(_loc3_.length);
         while(_loc4_ < _loc5_)
         {
            _loc6_ = _loc3_[_loc4_].split("H");
            _loc7_ = int(_loc6_[0]);
            _loc8_ = GoodConfig.instance().newGood(_loc7_);
            if(_loc8_)
            {
               _loc8_.parseLoadData(_loc6_);
               param2.push(_loc8_);
            }
            _loc4_++;
         }
      }
      
      private function collectProp(param1:Array) : String
      {
         var _loc5_:GameGoodVO = null;
         var _loc2_:Array = [];
         var _loc3_:int = 0;
         var _loc4_:int = int(param1.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = param1[_loc3_];
            _loc2_.push(_loc5_.collectSaveData());
            _loc3_++;
         }
         return _loc2_.join("T");
      }
      
      private function parseEquip(param1:String, param2:Array) : void
      {
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         var _loc8_:GameEquipVO = null;
         if(param1 == "")
         {
            return;
         }
         var _loc3_:Array = param1.split("T");
         var _loc4_:int = 0;
         var _loc5_:int = int(_loc3_.length);
         while(_loc4_ < _loc5_)
         {
            _loc6_ = _loc3_[_loc4_].split("B");
            _loc7_ = int(_loc6_[0]);
            _loc8_ = GoodConfig.instance().newGood(_loc7_) as GameEquipVO;
            if(_loc8_)
            {
               _loc8_.parseLoadData(_loc6_);
               param2.push(_loc8_);
            }
            _loc4_++;
         }
      }
      
      private function collectEquip(param1:Array) : String
      {
         var _loc5_:GameEquipVO = null;
         var _loc2_:Array = [];
         var _loc3_:int = 0;
         var _loc4_:int = int(param1.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = param1[_loc3_];
            _loc2_.push(_loc5_.collectSaveData());
            _loc3_++;
         }
         return _loc2_.join("T");
      }
      
      public function useItems(param1:int, param2:int) : void
      {
         var _loc3_:int = 0;
         while(_loc3_ < param2)
         {
            this.useItemByID(param1);
            _loc3_++;
         }
      }
      
      public function useItemByID(param1:int) : void
      {
         var _loc4_:GameGoodVO = null;
         var _loc2_:Array = this._bagDic[GoodConfig.instance().findGoodType(param1)];
         var _loc3_:int = int(_loc2_.length - 1);
         while(_loc3_ >= 0)
         {
            _loc4_ = _loc2_[_loc3_];
            if(_loc4_.constVO.id.v == param1)
            {
               if(_loc4_.amount.v <= 1)
               {
                  _loc2_.splice(_loc3_,1);
               }
               else
               {
                  _loc4_.amount.v -= ConstData.DATA_NUM1.v;
               }
               FlagProxy.instance().changeValue(param1,-1);
               return;
            }
            _loc3_--;
         }
      }
      
      public function delItemByID(param1:int) : void
      {
         var _loc2_:Array = this._bagDic[GoodConfig.instance().findGoodType(param1)];
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_.length)
         {
            if(_loc2_[_loc3_].constVO.id.v == param1)
            {
               this.delItemByVO(_loc2_[_loc3_]);
               _loc3_--;
            }
            _loc3_++;
         }
      }
      
      public function delItemByVO(param1:GameGoodVO) : void
      {
         var _loc2_:Array = this._bagDic[GoodConfig.instance().findGoodType(param1.constVO.id.v)];
         var _loc3_:int = int(_loc2_.indexOf(param1));
         if(_loc3_ != -1)
         {
            _loc2_.splice(_loc3_,1);
            FlagProxy.instance().changeValue(param1.constVO.id.v,-param1.amount.v);
         }
         param1.destroy();
      }
      
      public function removeEquip(param1:GameEquipVO) : void
      {
         var _loc2_:int = 0;
         if(!param1)
         {
            return;
         }
         if(param1.isPetEquip)
         {
            _loc2_ = int(this._bagDic[ConstData.GOOD_PET].indexOf(param1));
            if(_loc2_ != -1)
            {
               this._bagDic[ConstData.GOOD_PET].splice(_loc2_,1);
            }
         }
         else if(param1.isFashion)
         {
            _loc2_ = int(this._bagDic[ConstData.GOOD_FASHION].indexOf(param1));
            if(_loc2_ != -1)
            {
               this._bagDic[ConstData.GOOD_FASHION].splice(_loc2_,1);
            }
         }
         else
         {
            _loc2_ = int(this._bagDic[ConstData.GOOD_EQUIP].indexOf(param1));
            if(_loc2_ != -1)
            {
               this._bagDic[ConstData.GOOD_EQUIP].splice(_loc2_,1);
            }
         }
      }
      
      public function addItems(param1:Array) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         while(_loc2_ < _loc3_)
         {
            this.addMulitItem(param1[_loc2_]);
            _loc2_++;
         }
      }
      
      public function addMulitItem(param1:GameGoodVO) : void
      {
         if(param1.constVO.isPile == 0 && param1.amount.v == 1)
         {
            this.addDirect(param1);
            return;
         }
         var _loc2_:int = param1.amount.v;
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_)
         {
            if(param1.constVO.isPile == 0)
            {
               this.addNoPileItem(param1.constVO.id.v);
            }
            else
            {
               this.addPileItem(param1.constVO.id.v);
            }
            _loc3_++;
         }
         TaskProxy.instance().checkGoodTask();
         ChenghaoProxy.instance().checkGood(param1.constVO.id.v);
      }
      
      public function addPileItem(param1:int) : void
      {
         var _loc6_:GameGoodVO = null;
         var _loc2_:Array = this._bagDic[GoodConfig.instance().findGoodType(param1)];
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            _loc6_ = _loc2_[_loc3_];
            if(_loc6_.constVO.id.v == param1 && _loc6_.amount.v < BAG_MAX_COUNT.v)
            {
               _loc6_.amount.v += ConstData.DATA_NUM1.v;
               FlagProxy.instance().changeValue(param1,1);
               return;
            }
            _loc3_++;
         }
         var _loc5_:GameGoodVO = GoodConfig.instance().newGood(param1);
         _loc2_.push(_loc5_);
         FlagProxy.instance().changeValue(param1,1);
      }
      
      public function addNoPileItem(param1:int) : void
      {
         var _loc2_:Array = this._bagDic[GoodConfig.instance().findGoodType(param1)];
         _loc2_.push(GoodConfig.instance().newGood(param1));
         FlagProxy.instance().changeValue(param1,1);
      }
      
      public function addDirect(param1:GameGoodVO) : void
      {
         var _loc2_:Array = this._bagDic[GoodConfig.instance().findGoodType(param1.constVO.id.v)];
         _loc2_.push(param1);
         FlagProxy.instance().changeValue(param1.constVO.id.v,param1.amount.v);
      }
      
      public function checkLack(param1:Array) : String
      {
         if(param1[0].length > 0 && this.countNum(param1[0]) > this.totalGrid - this._bagDic[ConstData.GOOD_PROP].length)
         {
            return "【道具】栏空间不足！";
         }
         if(param1[1].length > 0 && this.countNum(param1[1]) > this.totalGrid - this._bagDic[ConstData.GOOD_EQUIP].length)
         {
            return "【装备】栏空间不足！";
         }
         if(param1[2].length > 0 && this.countNum(param1[2]) > this.totalGrid - this._bagDic[ConstData.GOOD_FASHION].length)
         {
            return "【时装】栏空间不足！";
         }
         if(param1[3].length > 0 && this.countNum(param1[3]) > this.totalGrid - this._bagDic[ConstData.GOOD_PET].length)
         {
            return "【宠物】栏空间不足！";
         }
         if(param1[4].length > 0 && this.countNum(param1[4]) > this.totalGrid - this._bagDic[ConstData.GOOD_CLIP].length)
         {
            return "【碎片】栏空间不足！";
         }
         return "";
      }
      
      public function countNum(param1:Array) : int
      {
         var _loc3_:GameGoodVO = null;
         var _loc2_:int = 0;
         for each(_loc3_ in param1)
         {
            if(_loc3_.constVO.isPile)
            {
               _loc2_++;
            }
            else
            {
               _loc2_ += _loc3_.amount.v;
            }
         }
         return _loc2_;
      }
      
      public function checkTabFull(param1:int) : Boolean
      {
         return this._bagDic[GoodConfig.instance().findGoodType(param1)].length >= this.totalGrid;
      }
      
      public function checkFull(param1:String) : Boolean
      {
         return this._bagDic[param1].length >= this.totalGrid;
      }
      
      public function findList(param1:String) : Array
      {
         return this._bagDic[param1];
      }
      
      public function cloneFabao() : Array
      {
         var _loc3_:GameEquipVO = null;
         var _loc1_:Array = [];
         var _loc2_:Array = this.findList(ConstData.GOOD_EQUIP);
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.isFabao)
            {
               _loc1_.push(_loc3_);
            }
         }
         return _loc1_;
      }
      
      public function cloneEquips() : Array
      {
         return this._bagDic[ConstData.GOOD_EQUIP].concat(this._bagDic[ConstData.GOOD_FASHION]);
      }
      
      public function get petEquips() : Array
      {
         return this._bagDic[ConstData.GOOD_PET].slice();
      }
      
      public function cloneNoneFashion() : Array
      {
         return this._bagDic[ConstData.GOOD_EQUIP].concat();
      }
      
      public function getGoodNum(param1:int) : int
      {
         var _loc4_:GameGoodVO = null;
         var _loc2_:Array = this._bagDic[GoodConfig.instance().findGoodType(param1)];
         var _loc3_:Sint = new Sint();
         for each(_loc4_ in _loc2_)
         {
            if(_loc4_)
            {
               if(_loc4_.constVO.id.v == param1)
               {
                  _loc3_.v += _loc4_.amount.v;
               }
            }
         }
         return _loc3_.v;
      }
      
      public function getVONum(param1:int) : int
      {
         var _loc3_:int = 0;
         var _loc4_:GameGoodVO = null;
         var _loc2_:Array = this._bagDic[GoodConfig.instance().findGoodType(param1)];
         for each(_loc4_ in _loc2_)
         {
            if(_loc4_)
            {
               if(_loc4_.constVO.id.v == param1)
               {
                  _loc3_++;
               }
            }
         }
         return _loc3_;
      }
      
      public function hasItem(param1:int) : Boolean
      {
         var _loc3_:GameGoodVO = null;
         var _loc2_:Array = this._bagDic[GoodConfig.instance().findGoodType(param1)];
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.constVO.id.v == param1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get petEquipClips() : Array
      {
         var _loc3_:GameGoodVO = null;
         var _loc1_:Array = [];
         var _loc2_:Array = this.findList(ConstData.GOOD_CLIP);
         for each(_loc3_ in _loc2_)
         {
            if(PEHCConfig.instance().fincHCVO(_loc3_.constVO.id.v) != null)
            {
               _loc1_.push(_loc3_);
            }
         }
         return _loc1_;
      }
      
      public function get fabaoPapers() : Array
      {
         var _loc4_:GameGoodVO = null;
         var _loc1_:Array = this.findList(ConstData.GOOD_PROP);
         var _loc3_:Array = [];
         for each(_loc4_ in _loc1_)
         {
            if(String(_loc4_.constVO.id.v).charAt(1) == ConstData.GOOD_FABAO_PAPER)
            {
               _loc3_.push(_loc4_);
            }
         }
         return _loc3_;
      }
      
      public function get dzFabaos() : Array
      {
         var _loc5_:GameEquipVO = null;
         var _loc2_:Array = this.findList(ConstData.GOOD_EQUIP);
         var _loc4_:Array = [];
         for each(_loc5_ in _loc2_)
         {
            if(_loc5_.isFabao)
            {
               if(FBDZConfig.instance().findDZVO(_loc5_.constEquip.id.v) != null)
               {
                  _loc4_.push(_loc5_);
               }
            }
         }
         return _loc4_;
      }
      
      public function get gems() : Array
      {
         var _loc4_:GameGoodVO = null;
         var _loc1_:Array = this.findList(ConstData.GOOD_PROP);
         var _loc3_:Array = [];
         for each(_loc4_ in _loc1_)
         {
            if(_loc4_.constVO is ConstGemVO)
            {
               _loc3_.push(_loc4_);
            }
         }
         return _loc3_;
      }
      
      public function findSeed(param1:int) : Array
      {
         var _loc5_:GameGoodVO = null;
         var _loc2_:Array = this.findList(ConstData.GOOD_PROP);
         var _loc4_:Array = [];
         for each(_loc5_ in _loc2_)
         {
            if(_loc5_.constVO is ConstSeedVO && _loc5_.constVO.quality.v <= param1)
            {
               _loc4_.push(_loc5_);
            }
         }
         return _loc4_;
      }
      
      public function get totalGrid() : int
      {
         return this.bagIndex.v * BAG_PAGE_NUM.v;
      }
      
      public function get bagIndex() : Sint
      {
         if(MasterProxy.instance().gameVIP.v >= 5)
         {
            return new Sint(6);
         }
         if(MasterProxy.instance().gameVIP.v >= 4)
         {
            return new Sint(5);
         }
         if(MasterProxy.instance().gameVIP.v >= 3)
         {
            return new Sint(4);
         }
         return new Sint(3);
      }
      
      public function countStoneWD(param1:int) : int
      {
         return MathUtil.checkWD(this.getGoodNum(14001),this.wd);
      }
   }
}

