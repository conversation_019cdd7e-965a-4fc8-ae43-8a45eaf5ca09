package mogames.gameData.good
{
   import flash.display.BitmapData;
   import mogames.AssetManager;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class GameGoodVO
   {
      public var amount:Sint;
      
      public var constVO:ConstGoodVO;
      
      public function GameGoodVO(param1:ConstGoodVO)
      {
         super();
         this.amount = new Sint(1);
         this.constVO = param1;
      }
      
      public function get quality() : int
      {
         return this.constVO.quality.v;
      }
      
      public function get sortIndex() : int
      {
         return this.constVO.id.v;
      }
      
      public function get sortNum() : int
      {
         return this.amount.v;
      }
      
      public function get icon() : BitmapData
      {
         if(this.constVO.id.v == 10000)
         {
            if(this.amount.v <= 10)
            {
               return AssetManager.findPicRes("TONG_QIAN1");
            }
            if(this.amount.v <= 100)
            {
               return AssetManager.findPicRes("TONG_QIAN2");
            }
            return AssetManager.findPicRes("TONG_QIAN3");
         }
         return AssetManager.findPicRes(this.constVO.iname);
      }
      
      public function get sellInfor() : String
      {
         return this.constVO.sellInfor;
      }
      
      public function destroy() : void
      {
         this.amount = null;
         this.constVO = null;
      }
      
      public function parseLoadData(param1:Array) : void
      {
         this.amount.v = int(param1[1]);
      }
      
      public function collectSaveData() : String
      {
         return this.constVO.id.v + "H" + this.amount.v;
      }
   }
}

