package mogames.gameLoad
{
   import com.hexagonstar.util.debug.Debug;
   import flash.display.Loader;
   import flash.display.LoaderInfo;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.events.ProgressEvent;
   import flash.net.URLRequest;
   import flash.utils.Dictionary;
   import mogames.AssetManager;
   import utils.SWFUtil;
   
   public class GameInLoader
   {
      private static var _instance:GameInLoader;
      
      private static var mcRes:Dictionary;
      
      private var _okFunc:Function;
      
      private var _cache:Boolean;
      
      private var _list:Array;
      
      private var _uiType:int;
      
      private var _loader:Loader;
      
      private var _cur:int;
      
      private var _total:int;
      
      private var _per:int;
      
      public var isAuto:Boolean;
      
      public function GameInLoader()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         mcRes = new Dictionary();
      }
      
      public static function instance() : GameInLoader
      {
         if(!_instance)
         {
            _instance = new GameInLoader();
         }
         return _instance;
      }
      
      public function init(param1:Function, param2:Array, param3:int = 1, param4:Boolean = false) : void
      {
         this.isAuto = true;
         this._list = param2;
         this._okFunc = param1;
         this._cache = param4;
         this._uiType = param3;
         this._total = param2.length;
         this.initLoad();
         this.startLoad();
      }
      
      private function startLoad() : void
      {
         this._cur = 0;
         this._per = 0;
         this.updateUI();
         this._loader.load(new URLRequest(this._list[this._cur]));
      }
      
      private function initLoad() : void
      {
         if(this._loader != null)
         {
            this.destroyLoader();
         }
         this._loader = new Loader();
         this._loader.contentLoaderInfo.addEventListener(ProgressEvent.PROGRESS,this.onProcess);
         this._loader.contentLoaderInfo.addEventListener(Event.COMPLETE,this.onComplete);
         this._loader.contentLoaderInfo.addEventListener(IOErrorEvent.IO_ERROR,this.onError);
      }
      
      public function findAsset(param1:String) : *
      {
         var _loc2_:Class = mcRes[param1] as Class;
         if(!_loc2_)
         {
            throw new Error("未找到加载资源：" + param1);
         }
         return new _loc2_();
      }
      
      private function saveRes(param1:LoaderInfo) : void
      {
         var _loc3_:* = null;
         var _loc2_:Array = SWFUtil.getSWFClassName(param1.bytes);
         for each(_loc3_ in _loc2_)
         {
            if(!(Boolean(mcRes[_loc3_]) || _loc3_.indexOf("Timeline") != -1))
            {
               if(this._cache)
               {
                  AssetManager.saveRes(_loc3_,SWFUtil.parseClass(param1,_loc3_));
               }
               else
               {
                  mcRes[_loc3_] = SWFUtil.parseClass(param1,_loc3_);
               }
            }
         }
      }
      
      private function onError(param1:IOErrorEvent) : void
      {
         Debug.trace("资源加载错误: " + param1.text);
         this.initLoad();
         this._loader.load(new URLRequest(this._list[this._cur]));
      }
      
      private function onProcess(param1:ProgressEvent) : void
      {
         this._per = param1.bytesLoaded / param1.bytesTotal * 100;
         this.updateUI();
      }
      
      private function onComplete(param1:Event) : void
      {
         this.saveRes(this._loader.contentLoaderInfo);
         ++this._cur;
         if(this._cur >= this._total)
         {
            this.handlerLoaded();
         }
         else
         {
            this.updateUI();
            this.initLoad();
            this._loader.load(new URLRequest(this._list[this._cur]));
         }
      }
      
      private function updateUI() : void
      {
         if(this._uiType == 0)
         {
            MiniLoadFrame.instance().update(this._per);
         }
         else if(this._uiType == 1)
         {
            AssetLoadFrame.instance().update(this._cur + 1 + "/" + this._total,this._per);
         }
      }
      
      public function cleanUI() : void
      {
         if(this._uiType == 0)
         {
            MiniLoadFrame.clean();
         }
         else if(this._uiType == 1)
         {
            AssetLoadFrame.clean();
         }
      }
      
      private function handlerLoaded() : void
      {
         this._cur = 0;
         this.destroyLoader();
         if(this._okFunc != null)
         {
            this._okFunc();
         }
         if(this.isAuto)
         {
            this.cleanUI();
         }
         this._okFunc = null;
      }
      
      private function destroyLoader() : void
      {
         if(!this._loader)
         {
            return;
         }
         this._loader.unloadAndStop();
         this._loader.contentLoaderInfo.removeEventListener(ProgressEvent.PROGRESS,this.onProcess);
         this._loader.contentLoaderInfo.removeEventListener(Event.COMPLETE,this.onComplete);
         this._loader.contentLoaderInfo.removeEventListener(IOErrorEvent.IO_ERROR,this.onError);
         this._loader = null;
      }
      
      public function cleanRes(param1:String) : void
      {
         if(!mcRes)
         {
            return;
         }
         if(mcRes[param1])
         {
            delete mcRes[param1];
         }
      }
      
      public function clean() : void
      {
         var _loc1_:String = null;
         this.destroyLoader();
         if(!mcRes)
         {
            return;
         }
         for(_loc1_ in mcRes)
         {
            delete mcRes[_loc1_];
         }
      }
   }
}

