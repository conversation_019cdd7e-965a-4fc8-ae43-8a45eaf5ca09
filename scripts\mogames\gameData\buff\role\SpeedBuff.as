package mogames.gameData.buff.role
{
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class SpeedBuff extends BaseBuff
   {
      private var addSpeed:Number;
      
      public function SpeedBuff()
      {
         super(1020);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         this.addSpeed = _roleVO.totalMOVE.v * _buffVO.argDic.per * 0.01;
         _roleVO.skillMOVE.v += this.addSpeed;
         _roleVO.updateMOVE();
         if(_role.isReady)
         {
            EffectManager.instance().addHeadWord("移动速度提升！",_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override protected function cleanEffect() : void
      {
         _roleVO.skillMOVE.v -= this.addSpeed;
         _roleVO.updateMOVE();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         return null;
      }
   }
}

