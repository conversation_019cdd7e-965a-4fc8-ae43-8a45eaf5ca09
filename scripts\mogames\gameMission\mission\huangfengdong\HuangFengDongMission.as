package mogames.gameMission.mission.huangfengdong
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameMission.mission.BaseMission;
   
   public class HuangFengDongMission extends BaseMission
   {
      private var _randomList:Array;
      
      public var newList:Array;
      
      public function HuangFengDongMission()
      {
         super();
         if(TaskProxy.instance().hasTask(12004))
         {
            this.taskYaoCao();
         }
         else
         {
            this.createYaoCao();
         }
      }
      
      private function taskYaoCao() : void
      {
         var _loc1_:Array = null;
         this._randomList = [];
         this._randomList.push([new Sint(10013),new Sint(10013),new Sint(10005)]);
         this._randomList.push([new Sint(10013),new Sint(10013),new Sint(10006)]);
         this._randomList.push([new Sint(10013),new Sint(10013),new Sint()]);
         this._randomList.push([new Sint(10013),new Sint(10013),new Sint()]);
         this._randomList.push([new Sint(10013),new Sint(10013),new Sint(10005)]);
         this._randomList.push([new Sint(10013),new Sint(10013),new Sint(10006)]);
         this.newList = [];
         for each(_loc1_ in this._randomList)
         {
            this.newList.push(_loc1_[int(Math.random() * _loc1_.length)]);
         }
      }
      
      private function createYaoCao() : void
      {
         var _loc1_:Array = null;
         this._randomList = [];
         this._randomList.push([new Sint(),new Sint(10005)]);
         this._randomList.push([new Sint(),new Sint(10006)]);
         this._randomList.push([new Sint(),new Sint(10005)]);
         this._randomList.push([new Sint(),new Sint(10006)]);
         this._randomList.push([new Sint(),new Sint(10005)]);
         this._randomList.push([new Sint(),new Sint(10006)]);
         this.newList = [];
         for each(_loc1_ in this._randomList)
         {
            this.newList.push(_loc1_[int(Math.random() * _loc1_.length)]);
         }
      }
      
      override public function cleanMission() : void
      {
         this._randomList = null;
         this.newList = [];
         super.cleanMission();
      }
   }
}

