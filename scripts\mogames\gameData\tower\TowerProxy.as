package mogames.gameData.tower
{
   import file.TowerConfig;
   
   public class TowerProxy
   {
      public static var curFloor:TowerFloorVO;
      
      private static var _instance:TowerProxy;
      
      private var _towers:Vector.<TowerFloorVO>;
      
      public function TowerProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : TowerProxy
      {
         if(!_instance)
         {
            _instance = new TowerProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._towers = null;
         this._towers = TowerConfig.instance().newTowers();
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:Array = null;
         var _loc4_:TowerFloorVO = null;
         var _loc5_:* = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc5_ in _loc2_)
         {
            _loc3_ = _loc5_.split("H");
            _loc4_ = this.findFloorVO(int(_loc3_[0]),int(_loc3_[1]));
            if(_loc4_)
            {
               _loc4_.loadData = _loc3_;
            }
         }
      }
      
      public function get saveData() : String
      {
         var _loc2_:TowerFloorVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._towers)
         {
            _loc1_.push(_loc2_.saveData);
         }
         return _loc1_.join("T");
      }
      
      public function activeFloor(param1:int, param2:int) : void
      {
         curFloor = this.findFloorVO(param1,param2);
      }
      
      public function findFloorNum(param1:int) : int
      {
         var _loc2_:int = 0;
         var _loc3_:TowerFloorVO = null;
         for each(_loc3_ in this._towers)
         {
            if(_loc3_.tower.v == param1)
            {
               _loc2_++;
            }
         }
         return _loc2_;
      }
      
      public function findFloorVO(param1:int, param2:int) : TowerFloorVO
      {
         var _loc3_:TowerFloorVO = null;
         for each(_loc3_ in this._towers)
         {
            if(_loc3_.tower.v == param1 && _loc3_.floor.v == param2)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function findTowerBoss(param1:int) : BossNeedVO
      {
         var _loc2_:BossNeedVO = null;
         var _loc3_:TowerFloorVO = null;
         for each(_loc3_ in this._towers)
         {
            _loc2_ = _loc3_.findBossFlag(param1);
            if(_loc2_)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

