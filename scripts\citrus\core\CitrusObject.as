package citrus.core
{
   use namespace citrus_internal;
   
   public class CitrusObject
   {
      citrus_internal static var last_id:uint = 0;
      
      public static var hideParamWarnings:<PERSON>olean = false;
      
      citrus_internal var data:Object = {"ID":0};
      
      public var name:String;
      
      public var kill:Boolean = false;
      
      public var updateCallEnabled:Boolean = false;
      
      public var type:String = "classicObject";
      
      protected var _initialized:Boolean = false;
      
      protected var _ce:CitrusEngine;
      
      protected var _params:Object;
      
      protected var _timeDelta:Number;
      
      public function CitrusObject(param1:String, param2:Object = null)
      {
         super();
         this.name = param1;
         this._ce = CitrusEngine.getInstance();
         this._params = param2;
         if(param2)
         {
            if(this.type == "classicObject" && !param2["type"])
            {
               this.initialize();
            }
         }
         else
         {
            this.initialize();
         }
         citrus_internal::data.ID = citrus_internal::last_id = citrus_internal::last_id + 1;
      }
      
      public function initialize(param1:Object = null) : void
      {
         if(param1)
         {
            this._params = param1;
         }
         if(this._params)
         {
            this.setParams(this,this._params);
         }
         else
         {
            this._initialized = true;
         }
      }
      
      public function destroy() : void
      {
         citrus_internal::data = null;
         this._initialized = false;
         this._params = null;
      }
      
      public function update(param1:Number) : void
      {
         this._timeDelta = param1;
      }
      
      public function setParams(param1:Object, param2:Object) : void
      {
         var param:String = null;
         var object:Object = param1;
         var params:Object = param2;
         for(param in params)
         {
            try
            {
               if(params[param] == "true")
               {
                  object[param] = true;
               }
               else if(params[param] == "false")
               {
                  object[param] = false;
               }
               else
               {
                  object[param] = params[param];
               }
            }
            catch(e:Error)
            {
               if(hideParamWarnings)
               {
               }
            }
         }
         this._initialized = true;
      }
      
      public function get ID() : uint
      {
         return citrus_internal::data.ID;
      }
      
      public function toString() : String
      {
         return String(Object(this).constructor) + " ID:" + (Boolean(this.citrus_internal::data) && Boolean(this.citrus_internal::data["ID"]) ? this.citrus_internal::data.ID : "null") + " name:" + String(this.name) + " type:" + String(this.type);
      }
   }
}

