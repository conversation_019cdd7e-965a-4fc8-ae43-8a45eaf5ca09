package file
{
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.heishi.MallHeiShiVO;
   import mogames.gameData.mall.vo.MallMoneyVO;
   import mogames.gameData.mall.vo.MallTicketVO;
   
   public class MallHeiShiConfig
   {
      private static var _instance:MallHeiShiConfig;
      
      private var _list:Array;
      
      public function MallHeiShiConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : MallHeiShiConfig
      {
         if(!_instance)
         {
            _instance = new MallHeiShiConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = [];
         this._list[this._list.length] = new MallHeiShiVO(101,5,10,new MallMoneyVO(3011,new BaseRewardVO(14002),5));
         this._list[this._list.length] = new MallHeiShiVO(102,6,10,new MallMoneyVO(3012,new BaseRewardVO(14009),6));
         this._list[this._list.length] = new MallHeiShiVO(103,5,16,new MallMoneyVO(3013,new BaseRewardVO(14020),7));
         this._list[this._list.length] = new MallHeiShiVO(104,7,30,new MallMoneyVO(3014,new BaseRewardVO(14030),20));
         this._list[this._list.length] = new MallHeiShiVO(105,3,1000,new MallMoneyVO(2966,new BaseRewardVO(14053),288));
         this._list[this._list.length] = new MallHeiShiVO(106,8,200,new MallMoneyVO(2842,new BaseRewardVO(14055),157));
         this._list[this._list.length] = new MallHeiShiVO(107,7,25,new MallMoneyVO(3015,new BaseRewardVO(14202),17));
         this._list[this._list.length] = new MallHeiShiVO(108,7,25,new MallMoneyVO(3016,new BaseRewardVO(14212),17));
         this._list[this._list.length] = new MallHeiShiVO(109,7,25,new MallMoneyVO(3017,new BaseRewardVO(14222),17));
         this._list[this._list.length] = new MallHeiShiVO(110,7,126,new MallMoneyVO(3018,new BaseRewardVO(14203),87));
         this._list[this._list.length] = new MallHeiShiVO(111,7,126,new MallMoneyVO(3019,new BaseRewardVO(14213),87));
         this._list[this._list.length] = new MallHeiShiVO(112,7,126,new MallMoneyVO(3020,new BaseRewardVO(14223),87));
         this._list[this._list.length] = new MallHeiShiVO(113,7,50,new MallMoneyVO(3021,new BaseRewardVO(16710),34));
         this._list[this._list.length] = new MallHeiShiVO(114,8,20,new MallMoneyVO(3022,new BaseRewardVO(16711),15));
         this._list[this._list.length] = new MallHeiShiVO(115,8,29,new MallMoneyVO(3023,new BaseRewardVO(16755),23));
         this._list[this._list.length] = new MallHeiShiVO(116,8,29,new MallMoneyVO(3024,new BaseRewardVO(16756),23));
         this._list[this._list.length] = new MallHeiShiVO(117,8,48,new MallMoneyVO(3025,new BaseRewardVO(16758),38));
         this._list[this._list.length] = new MallHeiShiVO(118,8,49,new MallMoneyVO(3026,new BaseRewardVO(16760),38));
         this._list[this._list.length] = new MallHeiShiVO(119,6,30,new MallMoneyVO(3027,new BaseRewardVO(16112),17));
         this._list[this._list.length] = new MallHeiShiVO(120,6,30,new MallMoneyVO(3028,new BaseRewardVO(16113),17));
         this._list[this._list.length] = new MallHeiShiVO(121,6,30,new MallMoneyVO(3029,new BaseRewardVO(16114),17));
         this._list[this._list.length] = new MallHeiShiVO(122,4,378,new MallMoneyVO(2978,new BaseRewardVO(14058),146));
         this._list[this._list.length] = new MallHeiShiVO(123,8,18,new MallMoneyVO(3034,new BaseRewardVO(16752),14));
         this._list[this._list.length] = new MallHeiShiVO(124,6,630,new MallMoneyVO(3082,new BaseRewardVO(14204),370));
         this._list[this._list.length] = new MallHeiShiVO(125,6,630,new MallMoneyVO(3083,new BaseRewardVO(14214),370));
         this._list[this._list.length] = new MallHeiShiVO(126,6,630,new MallMoneyVO(3084,new BaseRewardVO(14224),370));
         this._list[this._list.length] = new MallHeiShiVO(127,8,25,new MallMoneyVO(3102,new BaseRewardVO(16721),19));
         this._list[this._list.length] = new MallHeiShiVO(128,8,20,new MallMoneyVO(3103,new BaseRewardVO(16722),15));
         this._list[this._list.length] = new MallHeiShiVO(129,8,4,new MallMoneyVO(3131,new BaseRewardVO(14120),3));
         this._list[this._list.length] = new MallHeiShiVO(130,7,10,new MallMoneyVO(3132,new BaseRewardVO(14121),7));
         this._list[this._list.length] = new MallHeiShiVO(131,8,30,new MallMoneyVO(3133,new BaseRewardVO(14125),20));
         this._list[this._list.length] = new MallHeiShiVO(132,7,128,new MallMoneyVO(3138,new BaseRewardVO(14008),88));
         this._list[this._list.length] = new MallHeiShiVO(133,7,128,new MallMoneyVO(3138,new BaseRewardVO(14008),88));
         this._list[this._list.length] = new MallHeiShiVO(134,8,6,new MallMoneyVO(3186,new BaseRewardVO(14039),5));
         this._list[this._list.length] = new MallHeiShiVO(135,8,9,new MallMoneyVO(3185,new BaseRewardVO(14045),7));
         this._list[this._list.length] = new MallHeiShiVO(136,7,47,new MallMoneyVO(3056,new BaseRewardVO(16421),32));
         this._list[this._list.length] = new MallHeiShiVO(137,7,47,new MallMoneyVO(3057,new BaseRewardVO(16422),32));
         this._list[this._list.length] = new MallHeiShiVO(138,7,47,new MallMoneyVO(3058,new BaseRewardVO(16423),32));
         this._list[this._list.length] = new MallHeiShiVO(139,7,47,new MallMoneyVO(3059,new BaseRewardVO(16425),32));
         this._list[this._list.length] = new MallHeiShiVO(140,7,47,new MallMoneyVO(3048,new BaseRewardVO(16401),32));
         this._list[this._list.length] = new MallHeiShiVO(141,7,47,new MallMoneyVO(3049,new BaseRewardVO(16402),32));
         this._list[this._list.length] = new MallHeiShiVO(142,7,47,new MallMoneyVO(3050,new BaseRewardVO(16403),32));
         this._list[this._list.length] = new MallHeiShiVO(143,7,47,new MallMoneyVO(3051,new BaseRewardVO(16405),32));
         this._list[this._list.length] = new MallHeiShiVO(144,7,47,new MallMoneyVO(3052,new BaseRewardVO(16411),32));
         this._list[this._list.length] = new MallHeiShiVO(145,7,47,new MallMoneyVO(3053,new BaseRewardVO(16412),32));
         this._list[this._list.length] = new MallHeiShiVO(146,7,47,new MallMoneyVO(3054,new BaseRewardVO(16413),32));
         this._list[this._list.length] = new MallHeiShiVO(147,7,47,new MallMoneyVO(3055,new BaseRewardVO(16415),32));
         this._list[this._list.length] = new MallHeiShiVO(148,7,70,new MallMoneyVO(3196,new BaseRewardVO(16501),48));
         this._list[this._list.length] = new MallHeiShiVO(149,7,70,new MallMoneyVO(3197,new BaseRewardVO(16502),48));
         this._list[this._list.length] = new MallHeiShiVO(150,7,70,new MallMoneyVO(3198,new BaseRewardVO(16503),48));
         this._list[this._list.length] = new MallHeiShiVO(151,7,70,new MallMoneyVO(3199,new BaseRewardVO(16505),48));
         this._list[this._list.length] = new MallHeiShiVO(152,7,70,new MallMoneyVO(3200,new BaseRewardVO(16511),48));
         this._list[this._list.length] = new MallHeiShiVO(153,7,70,new MallMoneyVO(3201,new BaseRewardVO(16512),48));
         this._list[this._list.length] = new MallHeiShiVO(154,7,70,new MallMoneyVO(3202,new BaseRewardVO(16513),48));
         this._list[this._list.length] = new MallHeiShiVO(155,7,70,new MallMoneyVO(3203,new BaseRewardVO(16515),48));
         this._list[this._list.length] = new MallHeiShiVO(156,7,70,new MallMoneyVO(3200,new BaseRewardVO(16521),48));
         this._list[this._list.length] = new MallHeiShiVO(157,7,70,new MallMoneyVO(3201,new BaseRewardVO(16522),48));
         this._list[this._list.length] = new MallHeiShiVO(158,7,70,new MallMoneyVO(3202,new BaseRewardVO(16523),48));
         this._list[this._list.length] = new MallHeiShiVO(159,7,70,new MallMoneyVO(3203,new BaseRewardVO(16525),48));
         this._list[this._list.length] = new MallHeiShiVO(160,8,25,new MallMoneyVO(3102,new BaseRewardVO(16721),19));
         this._list[this._list.length] = new MallHeiShiVO(161,5,10,new MallMoneyVO(3011,new BaseRewardVO(14002),5));
         this._list[this._list.length] = new MallHeiShiVO(162,6,10,new MallMoneyVO(3012,new BaseRewardVO(14009),6));
         this._list[this._list.length] = new MallHeiShiVO(163,5,16,new MallMoneyVO(3013,new BaseRewardVO(14020),7));
         this._list[this._list.length] = new MallHeiShiVO(164,7,30,new MallMoneyVO(3014,new BaseRewardVO(14030),20));
         this._list[this._list.length] = new MallHeiShiVO(165,3,1000,new MallMoneyVO(2966,new BaseRewardVO(14053),288));
         this._list[this._list.length] = new MallHeiShiVO(166,8,200,new MallMoneyVO(2842,new BaseRewardVO(14055),157));
         this._list[this._list.length] = new MallHeiShiVO(167,7,25,new MallMoneyVO(3015,new BaseRewardVO(14202),17));
         this._list[this._list.length] = new MallHeiShiVO(168,7,25,new MallMoneyVO(3016,new BaseRewardVO(14212),17));
         this._list[this._list.length] = new MallHeiShiVO(169,7,25,new MallMoneyVO(3017,new BaseRewardVO(14222),17));
         this._list[this._list.length] = new MallHeiShiVO(170,7,126,new MallMoneyVO(3018,new BaseRewardVO(14203),87));
         this._list[this._list.length] = new MallHeiShiVO(171,7,126,new MallMoneyVO(3019,new BaseRewardVO(14213),87));
         this._list[this._list.length] = new MallHeiShiVO(172,7,126,new MallMoneyVO(3020,new BaseRewardVO(14223),87));
         this._list[this._list.length] = new MallHeiShiVO(173,7,50,new MallMoneyVO(3021,new BaseRewardVO(16710),34));
         this._list[this._list.length] = new MallHeiShiVO(174,8,20,new MallMoneyVO(3022,new BaseRewardVO(16711),15));
         this._list[this._list.length] = new MallHeiShiVO(175,8,29,new MallMoneyVO(3023,new BaseRewardVO(16755),23));
         this._list[this._list.length] = new MallHeiShiVO(176,8,29,new MallMoneyVO(3024,new BaseRewardVO(16756),23));
         this._list[this._list.length] = new MallHeiShiVO(177,8,48,new MallMoneyVO(3025,new BaseRewardVO(16758),38));
         this._list[this._list.length] = new MallHeiShiVO(178,8,49,new MallMoneyVO(3026,new BaseRewardVO(16760),38));
         this._list[this._list.length] = new MallHeiShiVO(179,6,30,new MallMoneyVO(3027,new BaseRewardVO(16112),17));
         this._list[this._list.length] = new MallHeiShiVO(180,6,30,new MallMoneyVO(3028,new BaseRewardVO(16113),17));
         this._list[this._list.length] = new MallHeiShiVO(181,6,30,new MallMoneyVO(3029,new BaseRewardVO(16114),17));
         this._list[this._list.length] = new MallHeiShiVO(182,4,378,new MallMoneyVO(2978,new BaseRewardVO(14058),146));
         this._list[this._list.length] = new MallHeiShiVO(183,8,18,new MallMoneyVO(3034,new BaseRewardVO(16752),14));
         this._list[this._list.length] = new MallHeiShiVO(184,6,630,new MallMoneyVO(3082,new BaseRewardVO(14204),370));
         this._list[this._list.length] = new MallHeiShiVO(185,6,630,new MallMoneyVO(3083,new BaseRewardVO(14214),370));
         this._list[this._list.length] = new MallHeiShiVO(186,6,630,new MallMoneyVO(3084,new BaseRewardVO(14224),370));
         this._list[this._list.length] = new MallHeiShiVO(187,8,20,new MallMoneyVO(3103,new BaseRewardVO(16722),15));
         this._list[this._list.length] = new MallHeiShiVO(188,8,4,new MallMoneyVO(3131,new BaseRewardVO(14120),3));
         this._list[this._list.length] = new MallHeiShiVO(189,8,4,new MallMoneyVO(3131,new BaseRewardVO(14120),3));
         this._list[this._list.length] = new MallHeiShiVO(190,7,10,new MallMoneyVO(3132,new BaseRewardVO(14121),7));
         this._list[this._list.length] = new MallHeiShiVO(191,8,30,new MallMoneyVO(3133,new BaseRewardVO(14125),20));
         this._list[this._list.length] = new MallHeiShiVO(192,7,128,new MallMoneyVO(3138,new BaseRewardVO(14008),88));
         this._list[this._list.length] = new MallHeiShiVO(193,7,128,new MallMoneyVO(3138,new BaseRewardVO(14008),88));
         this._list[this._list.length] = new MallHeiShiVO(194,8,6,new MallMoneyVO(3186,new BaseRewardVO(14039),5));
         this._list[this._list.length] = new MallHeiShiVO(195,8,9,new MallMoneyVO(3185,new BaseRewardVO(14045),7));
         this._list[this._list.length] = new MallHeiShiVO(196,6,700,new MallMoneyVO(3099,new BaseRewardVO(14077),411));
         this._list[this._list.length] = new MallHeiShiVO(197,6,700,new MallMoneyVO(3099,new BaseRewardVO(14077),411));
         this._list[this._list.length] = new MallHeiShiVO(198,6,1000,new MallMoneyVO(3151,new BaseRewardVO(14092),588));
         this._list[this._list.length] = new MallHeiShiVO(199,5,210,new MallMoneyVO(3003,new BaseRewardVO(14061),104));
         this._list[this._list.length] = new MallHeiShiVO(200,8,200,new MallMoneyVO(2842,new BaseRewardVO(14055),157));
         this._list[this._list.length] = new MallHeiShiVO(201,3,1000,new MallMoneyVO(2966,new BaseRewardVO(14053),288));
         this._list[this._list.length] = new MallHeiShiVO(202,6,1000,new MallMoneyVO(3061,new BaseRewardVO(14090),588));
         this._list[this._list.length] = new MallHeiShiVO(205,6,700,new MallMoneyVO(3099,new BaseRewardVO(14077),411));
         this._list[this._list.length] = new MallHeiShiVO(206,6,330,new MallMoneyVO(3130,new BaseRewardVO(14124),194));
         this._list[this._list.length] = new MallHeiShiVO(207,3,540,new MallMoneyVO(2839,new BaseRewardVO(14070),155));
         this._list[this._list.length] = new MallHeiShiVO(208,6,700,new MallMoneyVO(3099,new BaseRewardVO(14077),411));
         this._list[this._list.length] = new MallHeiShiVO(209,6,1000,new MallMoneyVO(3062,new BaseRewardVO(14091),588));
         this._list[this._list.length] = new MallHeiShiVO(212,8,200,new MallMoneyVO(2842,new BaseRewardVO(14055),157));
         this._list[this._list.length] = new MallHeiShiVO(213,4,1140,new MallMoneyVO(3004,new BaseRewardVO(14062),453));
         this._list[this._list.length] = new MallHeiShiVO(214,5,255,new MallMoneyVO(2837,new BaseRewardVO(14050),124));
         this._list[this._list.length] = new MallHeiShiVO(215,6,700,new MallMoneyVO(3099,new BaseRewardVO(14077),411));
         this._list[this._list.length] = new MallHeiShiVO(216,3,540,new MallMoneyVO(2839,new BaseRewardVO(14070),155));
         this._list[this._list.length] = new MallHeiShiVO(217,6,700,new MallMoneyVO(3099,new BaseRewardVO(14077),411));
         this._list[this._list.length] = new MallHeiShiVO(218,6,700,new MallMoneyVO(3099,new BaseRewardVO(14077),411));
         this._list[this._list.length] = new MallHeiShiVO(235,5,2000,new MallMoneyVO(3287,new BaseRewardVO(14095),980));
         this._list[this._list.length] = new MallHeiShiVO(236,5,2000,new MallMoneyVO(3287,new BaseRewardVO(14095),980));
         this._list[this._list.length] = new MallHeiShiVO(237,6,1000,new MallMoneyVO(3286,new BaseRewardVO(14093),588));
         this._list[this._list.length] = new MallHeiShiVO(238,6,1000,new MallMoneyVO(3286,new BaseRewardVO(14093),588));
         this._list[this._list.length] = new MallHeiShiVO(239,7,450,new MallMoneyVO(3295,new BaseRewardVO(14072),309));
         this._list[this._list.length] = new MallHeiShiVO(240,7,450,new MallMoneyVO(3295,new BaseRewardVO(14072),309));
         this._list[this._list.length] = new MallHeiShiVO(241,7,450,new MallMoneyVO(3295,new BaseRewardVO(14072),309));
         this._list[this._list.length] = new MallHeiShiVO(242,7,1868,new MallMoneyVO(3289,new BaseRewardVO(14171),1280));
         this._list[this._list.length] = new MallHeiShiVO(243,7,1868,new MallMoneyVO(3296,new BaseRewardVO(14172),1280));
         this._list[this._list.length] = new MallHeiShiVO(244,7,1868,new MallMoneyVO(3297,new BaseRewardVO(14174),1280));
         this._list[this._list.length] = new MallHeiShiVO(245,7,1868,new MallMoneyVO(3301,new BaseRewardVO(14173),1280));
         this._list[this._list.length] = new MallHeiShiVO(256,7,113,new MallMoneyVO(3302,new BaseRewardVO(14107),78));
         this._list[this._list.length] = new MallHeiShiVO(257,7,113,new MallMoneyVO(3302,new BaseRewardVO(14107),78));
         this._list[this._list.length] = new MallHeiShiVO(258,7,113,new MallMoneyVO(3302,new BaseRewardVO(14107),78));
         this._list[this._list.length] = new MallHeiShiVO(259,5,2300,new MallMoneyVO(3581,new BaseRewardVO(14109),1120));
         this._list[this._list.length] = new MallHeiShiVO(260,5,2300,new MallMoneyVO(3581,new BaseRewardVO(14109),1120));
         this._list[this._list.length] = new MallHeiShiVO(261,5,2300,new MallMoneyVO(3581,new BaseRewardVO(14109),1120));
         this._list[this._list.length] = new MallHeiShiVO(262,7,1868,new MallMoneyVO(3301,new BaseRewardVO(14175),1280));
         this._list[this._list.length] = new MallHeiShiVO(263,7,1868,new MallMoneyVO(3301,new BaseRewardVO(14175),1280));
         this._list[this._list.length] = new MallHeiShiVO(401,5,180,new MallTicketVO(0,new BaseRewardVO(16752),88));
         this._list[this._list.length] = new MallHeiShiVO(402,5,180,new MallTicketVO(0,new BaseRewardVO(16757),88));
         this._list[this._list.length] = new MallHeiShiVO(403,2,40,new MallTicketVO(0,new BaseRewardVO(14001),8));
         this._list[this._list.length] = new MallHeiShiVO(404,4,80,new MallTicketVO(0,new BaseRewardVO(14002),31));
         this._list[this._list.length] = new MallHeiShiVO(405,8,50,new MallTicketVO(0,new BaseRewardVO(14020),40));
         this._list[this._list.length] = new MallHeiShiVO(406,4,130,new MallTicketVO(0,new BaseRewardVO(14009),50));
         this._list[this._list.length] = new MallHeiShiVO(407,2,200,new MallTicketVO(0,new BaseRewardVO(14021),38));
         this._list[this._list.length] = new MallHeiShiVO(408,8,10,new MallTicketVO(0,new BaseRewardVO(14010),8));
         this._list[this._list.length] = new MallHeiShiVO(409,8,30,new MallTicketVO(0,new BaseRewardVO(14120),24));
         this._list[this._list.length] = new MallHeiShiVO(410,7,80,new MallTicketVO(0,new BaseRewardVO(14121),56));
         this._list[this._list.length] = new MallHeiShiVO(411,7,80,new MallTicketVO(0,new BaseRewardVO(14121),56));
         this._list[this._list.length] = new MallHeiShiVO(412,2,40,new MallTicketVO(0,new BaseRewardVO(14001),8));
         this._list[this._list.length] = new MallHeiShiVO(413,4,80,new MallTicketVO(0,new BaseRewardVO(14002),31));
         this._list[this._list.length] = new MallHeiShiVO(414,8,50,new MallTicketVO(0,new BaseRewardVO(14020),40));
         this._list[this._list.length] = new MallHeiShiVO(415,4,130,new MallTicketVO(0,new BaseRewardVO(14009),50));
         this._list[this._list.length] = new MallHeiShiVO(416,2,200,new MallTicketVO(0,new BaseRewardVO(14021),38));
         this._list[this._list.length] = new MallHeiShiVO(417,8,10,new MallTicketVO(0,new BaseRewardVO(14010),8));
         this._list[this._list.length] = new MallHeiShiVO(418,8,30,new MallTicketVO(0,new BaseRewardVO(14120),24));
         this._list[this._list.length] = new MallHeiShiVO(419,7,80,new MallTicketVO(0,new BaseRewardVO(14121),56));
         this._list[this._list.length] = new MallHeiShiVO(420,5,180,new MallTicketVO(0,new BaseRewardVO(16752),88));
         this._list[this._list.length] = new MallHeiShiVO(421,5,180,new MallTicketVO(0,new BaseRewardVO(16757),88));
         this._list[this._list.length] = new MallHeiShiVO(422,7,32,new MallTicketVO(0,new BaseRewardVO(14120),21));
         this._list[this._list.length] = new MallHeiShiVO(423,7,80,new MallTicketVO(0,new BaseRewardVO(14121),55));
         this._list[this._list.length] = new MallHeiShiVO(424,7,32,new MallTicketVO(0,new BaseRewardVO(14120),21));
         this._list[this._list.length] = new MallHeiShiVO(425,7,80,new MallTicketVO(0,new BaseRewardVO(14121),55));
         this._list[this._list.length] = new MallHeiShiVO(426,7,150,new MallTicketVO(0,new BaseRewardVO(16017),102));
         this._list[this._list.length] = new MallHeiShiVO(427,7,150,new MallTicketVO(0,new BaseRewardVO(16119),102));
         this._list[this._list.length] = new MallHeiShiVO(428,7,150,new MallTicketVO(0,new BaseRewardVO(16120),102));
         this._list[this._list.length] = new MallHeiShiVO(429,7,150,new MallTicketVO(0,new BaseRewardVO(16017),102));
         this._list[this._list.length] = new MallHeiShiVO(430,7,150,new MallTicketVO(0,new BaseRewardVO(16119),102));
         this._list[this._list.length] = new MallHeiShiVO(431,7,150,new MallTicketVO(0,new BaseRewardVO(16120),102));
      }
      
      public function findVO(param1:int) : MallHeiShiVO
      {
         var _loc2_:MallHeiShiVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function newList() : Array
      {
         var _loc3_:int = 0;
         var _loc1_:Array = this._list.slice();
         var _loc2_:Array = [];
         var _loc4_:int = 0;
         while(_loc4_ < 5)
         {
            _loc3_ = Math.random() * _loc1_.length;
            _loc2_[_loc4_] = _loc1_[_loc3_];
            _loc1_.splice(_loc3_,1);
            _loc4_++;
         }
         return _loc2_;
      }
   }
}

