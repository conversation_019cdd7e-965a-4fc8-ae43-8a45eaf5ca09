package mogames.gameData.buff
{
   import mogames.gameData.base.Sint;
   import mogames.gameSystem.SysTimer;
   import org.osflash.signals.Signal;
   import utils.TxtUtil;
   
   public class BaseTeamBuff
   {
      public var buffID:Sint;
      
      protected var _buffTimer:SysTimer;
      
      protected var _buffVO:BuffVO;
      
      public var onEnd:Signal;
      
      public var isLoop:Boolean;
      
      public function BaseTeamBuff(param1:int, param2:Boolean = false)
      {
         super();
         this.isLoop = param2;
         this.buffID = new Sint(param1);
         this._buffTimer = new SysTimer(param2);
         this.onEnd = new Signal();
      }
      
      public function setBuffVO(param1:BuffVO) : void
      {
         this._buffVO = param1;
         this.startTimeout();
      }
      
      public function get leftTime() : String
      {
         return TxtUtil.initTimeFormat(this._buffTimer.leftTime);
      }
      
      protected function startTimeout() : void
      {
         this._buffTimer.setTimeOut(this._buffVO.sec.v,this.handlerEnd);
      }
      
      protected function handlerEnd() : void
      {
         this.onEnd.dispatch();
      }
      
      public function get tip() : String
      {
         return "";
      }
      
      public function destroy() : void
      {
         this.onEnd.removeAll();
         this.onEnd = null;
         this._buffTimer.destroy();
         this._buffTimer = null;
      }
   }
}

