package citrus.system
{
   import citrus.core.CitrusObject;
   
   public class Entity extends CitrusObject
   {
      protected var _components:Vector.<Component>;
      
      public function Entity(param1:String, param2:Object = null)
      {
         updateCallEnabled = true;
         if(param2 == null)
         {
            param2 = {"type":"entity"};
         }
         else
         {
            param2["type"] = "entity";
         }
         super(param1,param2);
         this._components = new Vector.<Component>();
      }
      
      public function add(param1:Component) : Entity
      {
         this.doAddComponent(param1);
         return this;
      }
      
      protected function doAddComponent(param1:Component) : Boolean
      {
         if(param1.name == "")
         {
         }
         if(this.lookupComponentByName(param1.name))
         {
            throw Error("A component with name \'" + param1.name + "\' already exists on this entity.");
         }
         if(param1.entity)
         {
            if(param1.entity == this)
            {
               this._components.push(param1);
               return true;
            }
            throw Error("The component \'" + param1.name + "\' already has an owner. (\'" + param1.entity.name + "\')");
         }
         param1.entity = this;
         this._components.push(param1);
         return true;
      }
      
      public function remove(param1:Component) : void
      {
         var _loc2_:int = int(this._components.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._components.splice(_loc2_,1)[0].destroy();
         }
      }
      
      public function lookupComponentByType(param1:Class) : Component
      {
         var componentType:Class = param1;
         var component:Component = null;
         var filteredComponents:Vector.<Component> = this._components.filter(function(param1:Component, param2:int, param3:Vector.<Component>):Boolean
         {
            return param1 is componentType;
         });
         if(filteredComponents.length != 0)
         {
            component = filteredComponents[0];
         }
         return component;
      }
      
      public function lookupComponentsByType(param1:Class) : Vector.<Component>
      {
         var componentType:Class = param1;
         var filteredComponents:Vector.<Component> = this._components.filter(function(param1:Component, param2:int, param3:Vector.<Component>):Boolean
         {
            return param1 is componentType;
         });
         return filteredComponents;
      }
      
      public function lookupComponentByName(param1:String) : Component
      {
         var name:String = param1;
         var component:Component = null;
         var filteredComponents:Vector.<Component> = this._components.filter(function(param1:Component, param2:int, param3:Vector.<Component>):Boolean
         {
            return param1.name == name;
         });
         if(filteredComponents.length != 0)
         {
            component = filteredComponents[0];
         }
         return component;
      }
      
      override public function initialize(param1:Object = null) : void
      {
         var poolObjectParams:Object = param1;
         super.initialize();
         this._components.forEach(function(param1:Component, param2:int, param3:Vector.<Component>):void
         {
            param1.initialize();
         });
      }
      
      override public function destroy() : void
      {
         this._components.forEach(function(param1:Component, param2:int, param3:Vector.<Component>):void
         {
            param1.destroy();
         });
         this._components = null;
         super.destroy();
      }
      
      override public function update(param1:Number) : void
      {
         var timeDelta:Number = param1;
         this._components.forEach(function(param1:Component, param2:int, param3:Vector.<Component>):void
         {
            param1.update(timeDelta);
         },this);
      }
      
      public function get components() : Vector.<Component>
      {
         return this._components;
      }
   }
}

