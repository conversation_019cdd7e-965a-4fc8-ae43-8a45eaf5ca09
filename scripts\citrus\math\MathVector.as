package citrus.math
{
   public class MathVector
   {
      public var x:Number;
      
      public var y:Number;
      
      public function MathVector(param1:Number = 0, param2:Number = 0)
      {
         super();
         this.x = param1;
         this.y = param2;
      }
      
      public function copy() : MathVector
      {
         return new MathVector(this.x,this.y);
      }
      
      public function copyFrom(param1:MathVector) : void
      {
         this.x = param1.x;
         this.y = param1.y;
      }
      
      public function setTo(param1:Number = 0, param2:Number = 0) : void
      {
         this.x = param1;
         this.y = param2;
      }
      
      public function rotate(param1:Number) : void
      {
         var _loc2_:Number = param1;
         var _loc3_:Number = Math.cos(_loc2_);
         var _loc4_:Number = Math.sin(_loc2_);
         var _loc5_:Number = this.x;
         var _loc6_:Number = this.y;
         this.x = _loc5_ * _loc3_ - _loc6_ * _loc4_;
         this.y = _loc5_ * _loc4_ + _loc6_ * _loc3_;
      }
      
      public function scaleEquals(param1:Number) : void
      {
         this.x *= param1;
         this.y *= param1;
      }
      
      public function scale(param1:Number, param2:MathVector = null) : MathVector
      {
         if(param2)
         {
            param2.x = this.x * param1;
            param2.y = this.y * param1;
            return param2;
         }
         return new MathVector(this.x * param1,this.y * param1);
      }
      
      public function normalize() : void
      {
         var _loc1_:Number = this.length;
         this.x /= _loc1_;
         this.y /= _loc1_;
      }
      
      public function plusEquals(param1:MathVector) : void
      {
         this.x += param1.x;
         this.y += param1.y;
      }
      
      public function plus(param1:MathVector, param2:MathVector = null) : MathVector
      {
         if(param2)
         {
            param2.x = this.x + param1.x;
            param2.y = this.y + param1.y;
            return param2;
         }
         return new MathVector(this.x + param1.x,this.y + param1.y);
      }
      
      public function minusEquals(param1:MathVector) : void
      {
         this.x -= param1.x;
         this.y -= param1.y;
      }
      
      public function minus(param1:MathVector, param2:MathVector = null) : MathVector
      {
         if(param2)
         {
            param2.x = this.x - param1.x;
            param2.y = this.y - param1.y;
            return param2;
         }
         return new MathVector(this.x - param1.x,this.y - param1.y);
      }
      
      public function dot(param1:MathVector) : Number
      {
         return this.x * param1.x + this.y * param1.y;
      }
      
      public function get angle() : Number
      {
         return Math.atan2(this.y,this.x);
      }
      
      public function set angle(param1:Number) : void
      {
         var _loc2_:Number = this.length;
         var _loc3_:Number = _loc2_ * Math.cos(param1);
         var _loc4_:Number = _loc2_ * Math.sin(param1);
         this.x = _loc3_;
         this.y = _loc4_;
      }
      
      public function get length() : Number
      {
         return Math.sqrt(this.x * this.x + this.y * this.y);
      }
      
      public function set length(param1:Number) : void
      {
         this.scaleEquals(param1 / this.length);
      }
      
      public function get normal() : MathVector
      {
         return new MathVector(-this.y,this.x);
      }
      
      public function toString() : String
      {
         return "[" + this.x + ", " + this.y + "]";
      }
   }
}

