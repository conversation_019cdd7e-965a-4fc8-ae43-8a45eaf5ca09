package mogames.gameMission.mission.pipadong
{
   import mogames.gameData.fuben.zhenbei.ZhenBeiVO;
   import mogames.gameMission.mission.BaseMission;
   
   public class PiPaDongMission extends BaseMission
   {
      public var zhenbeiVO:ZhenBeiVO;
      
      public function PiPaDongMission()
      {
         super();
      }
      
      public function enterZhenBei(param1:ZhenBeiVO) : void
      {
         this.zhenbeiVO = param1;
         if(!param1)
         {
            return;
         }
         handlerTouchDoor(1602,84,434);
      }
      
      public function leaveZhenBei() : void
      {
         this.zhenbeiVO = null;
         handlerTouchDoor(1601,100,390);
      }
   }
}

