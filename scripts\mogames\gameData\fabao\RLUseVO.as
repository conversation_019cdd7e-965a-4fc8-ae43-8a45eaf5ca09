package mogames.gameData.fabao
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.UseHandler;
   
   public class RLUseVO extends BaseUseVO
   {
      public var quality:Sint;
      
      public function RLUseVO(param1:int, param2:int, param3:Array = null)
      {
         this.quality = new Sint(param1);
         super(param2,param3);
      }
      
      override public function useStuff() : void
      {
         if(needGold.v != 0)
         {
            MasterProxy.instance().changeGold(-needGold.v);
         }
         UseHandler.instance().useStuff(needList);
      }
   }
}

