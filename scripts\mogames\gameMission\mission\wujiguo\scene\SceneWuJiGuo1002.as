package mogames.gameMission.mission.wujiguo.scene
{
   import citrus.objects.CitrusSprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.wujiguo.WuJiGuoMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.bullet.LocationBullet;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BOSSShiLiWang;
   import mogames.gameRole.enemy.BOSSZhenShiLiWang;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.dialog.StoryDialogModule;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.MathUtil;
   
   public class SceneWuJiGuo1002 extends BossScene
   {
      private var _fireArgs:Array = [{
         "skin":"EFFECT_WU_JI_GUO_FIRE_CLIP",
         "width":42,
         "height":70,
         "group":2,
         "posX":1151,
         "posY":256
      },{
         "skin":"EFFECT_WU_JI_GUO_FIRE_CLIP",
         "width":42,
         "height":70,
         "group":2,
         "posX":1205,
         "posY":284
      },{
         "skin":"EFFECT_WU_JI_GUO_FIRE_CLIP",
         "width":42,
         "height":70,
         "group":2,
         "posX":1239,
         "posY":228
      },{
         "skin":"EFFECT_WU_JI_GUO_FIRE_CLIP",
         "width":42,
         "height":70,
         "group":2,
         "posX":1665,
         "posY":228
      },{
         "skin":"EFFECT_WU_JI_GUO_FIRE_CLIP",
         "width":42,
         "height":70,
         "group":2,
         "posX":1700,
         "posY":288
      },{
         "skin":"EFFECT_WU_JI_GUO_FIRE_CLIP",
         "width":42,
         "height":70,
         "group":2,
         "posX":1751,
         "posY":256
      }];
      
      private var _windArg:Object = {
         "interval":25,
         "showTime":5,
         "hurt":new Sint(10),
         "hurtTime":new Snum(0.5)
      };
      
      private var _trapPos:Array = [[399,420],[727,420],[1056,420],[1384,420],[1713,420]];
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _fires:Vector.<CitrusMC>;
      
      private var _windTimer:CitrusTimer;
      
      private var _windTrigger:CitrusTimer;
      
      private var _fireTimer:CitrusTimer;
      
      private var _failBoss:BOSSShiLiWang;
      
      private var _failPos:Point;
      
      public function SceneWuJiGuo1002(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(1302,318,352,136),new Point(1300,406));
         this._windTrigger = new CitrusTimer(true);
         this._windTimer = new CitrusTimer();
         this._fireTimer = new CitrusTimer();
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.initEnemies();
         this.initTrap();
         this.addFailBOSS();
      }
      
      private function initTrap() : void
      {
         var _loc1_:WuJiGuoMission = _mission as WuJiGuoMission;
         var _loc2_:int = 0;
         while(_loc2_ < 5)
         {
            _loc1_.createTrap(this._trapPos[_loc2_][0],this._trapPos[_loc2_][1]);
            _loc2_++;
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[202,330,150,100],[765,240,150,100]],10021,this.triggerEnd0);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(643,435),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1010,435),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this.activeFailBOSS;
         this._pTrigger0.start();
      }
      
      private function triggerEnd0() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
         this.addFire();
      }
      
      private function addFailBOSS() : void
      {
         this._failBoss = new BOSSShiLiWang();
         this._failBoss.x = 1555;
         this._failBoss.y = 416;
         this._failBoss.initData(30131,30131,4);
         this._failBoss.target = _mission.onePlayer;
         add(this._failBoss);
         this._failBoss.onRole.add(this.listenDead);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSZhenShiLiWang();
         _boss.x = this._failPos.x;
         _boss.y = this._failPos.y;
         _boss.initData(30141,30141,40);
         _boss.target = _mission.onePlayer;
         add(_boss);
         this._failBoss.kill = true;
         startBossBattle();
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,_boss.x,_boss.y);
      }
      
      private function activeFailBOSS() : void
      {
         CitrusLock.instance().lock(new Rectangle(960,0,960,600),false,false,true,false);
         setHeroEnable(true);
         this._failBoss.activeEnemy(false);
         this._windTrigger.setInterval(this._windArg.interval,0,this.triggerXuanFeng,null,false);
         this._windTrigger.startNone();
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      private function listenDead(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         this._failPos = new Point(this._failBoss.x,this._failBoss.y);
         StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(680,87),"嗷呜！让你看爷爷的真本事！本体现形！","BODY_SHI_LI_WANG",this.addBOSS,2,false);
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      private function triggerXuanFeng() : void
      {
         this.addXuanFeng();
         this._windTimer.setTimeOut(this._windArg.showTime,this.addXuanFeng);
      }
      
      private function addXuanFeng() : void
      {
         var _loc1_:int = MathUtil.checkOdds(500) ? 1 : -1;
         var _loc2_:int = _loc1_ == 1 ? 800 : 2050;
         var _loc3_:int = _loc1_ == 1 ? 2050 : -800;
         var _loc5_:Point = new Point(_loc3_,440);
         var _loc6_:LocationBullet = new LocationBullet(5,_loc5_,true,{
            "x":_loc2_,
            "y":440,
            "width":118,
            "height":143,
            "group":3
         });
         _loc6_.createView("EFFECT_XUAN_FENG_CLIP",118,143,0,0);
         _loc6_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",10,10);
         _loc6_.createHitHurt(this._windArg.hurt.v,this._windArg.hurtTime.v,false,1);
         _loc6_.isAlpha = false;
         _loc6_.isHurtDead = false;
         _loc6_.cleanFrame = 10;
         add(_loc6_);
         EffectManager.instance().playAudio("XUAN_FENG");
         if(_loc1_ == 1)
         {
            this.changeFire("right");
         }
         else
         {
            this.changeFire("left");
         }
         this._fireTimer.setTimeOut(2,this.changeFire);
      }
      
      private function changeFire(param1:String = "idle") : void
      {
         var _loc2_:CitrusMC = null;
         for each(_loc2_ in this._fires)
         {
            _loc2_.changeAnimation(param1,true);
         }
      }
      
      private function addFire() : void
      {
         var _loc1_:Object = null;
         this._fires = new Vector.<CitrusMC>();
         for each(_loc1_ in this._fireArgs)
         {
            this.createFire(_loc1_);
         }
      }
      
      private function createFire(param1:Object) : void
      {
         var _loc2_:CitrusSprite = new CitrusSprite("fire",{
            "width":param1.width,
            "height":param1.height,
            "group":param1.group
         });
         _loc2_.x = param1.posX;
         _loc2_.y = param1.posY;
         var _loc3_:CitrusMC = new CitrusMC();
         _loc3_.setupMC(GameInLoader.instance().findAsset(param1.skin));
         _loc3_.changeAnimation("idle",true);
         _loc2_.view = _loc3_;
         this._fires.push(_loc3_);
         add(_loc2_);
      }
      
      private function destroyFire() : void
      {
         var _loc1_:CitrusMC = null;
         if(!this._fires)
         {
            return;
         }
         for each(_loc1_ in this._fires)
         {
            _loc1_.destroy();
         }
         this._fires.length = 0;
         this._fires = null;
      }
      
      override protected function handlerWin() : void
      {
         var func:Function = null;
         func = function():void
         {
            TaskProxy.instance().setComplete(11011);
            TaskProxy.instance().addTask(11012);
            FlagProxy.instance().setValue(400,1);
            onWin();
         };
         this._windTimer.pause();
         this._windTrigger.pause();
         if(TaskProxy.instance().hasTask(11011) && !TaskProxy.instance().isTaskFinish(11011))
         {
            showDialog("STORY0041",func);
         }
         else
         {
            this.onWin();
         }
      }
      
      private function onWin() : void
      {
         MiniMsgMediator.clean();
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         this.destroyFire();
         this._windTimer.destroy();
         this._windTimer = null;
         this._fireTimer.destroy();
         this._fireTimer = null;
         this._windTrigger.destroy();
         this._windTrigger = null;
         this._eTrigger0.destroy();
         this._pTrigger0.destroy();
         this._pTrigger1.destroy();
         this._eTrigger0 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         super.destroy();
      }
   }
}

