package mogames.gameData.petSkill.passive
{
   import mogames.gameData.petSkill.PetSkillVO;
   import mogames.gameData.role.HeroGameVO;
   import utils.TxtUtil;
   
   public class PetSkillRebornVO extends PetSkillVO
   {
      private var _qualityArg:Array = [1,1.25,1.5,1.75,2];
      
      public function PetSkillRebornVO(param1:int, param2:HeroGameVO)
      {
         super(param1,param2);
      }
      
      public function get odds() : int
      {
         return level.v * this._qualityArg[curQuality.v] * (1 + _pet.curWit.v * 0.1) * 8 + 30;
      }
      
      override public function get tipInfor() : String
      {
         return constVO.infor + TxtUtil.setColor("<br>触发概率" + (this.odds * 0.1).toFixed(1) + "%","99ff00");
      }
   }
}

