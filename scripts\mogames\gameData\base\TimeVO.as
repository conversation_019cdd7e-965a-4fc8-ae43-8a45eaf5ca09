package mogames.gameData.base
{
   public class TimeVO
   {
      public var date:Date;
      
      public var year:Sint;
      
      public var month:Sint;
      
      public var day:Sint;
      
      public var hour:Sint;
      
      public var min:Sint;
      
      public var sec:Sint;
      
      public var week:Sint;
      
      public function TimeVO(param1:String = "2016-04-23 10:50:10")
      {
         super();
         this.year = new Sint();
         this.month = new Sint();
         this.day = new Sint();
         this.hour = new Sint();
         this.min = new Sint();
         this.sec = new Sint();
         this.week = new Sint();
         if(param1 != "")
         {
            this.timeStamp = param1;
         }
      }
      
      public function set timeStamp(param1:String) : void
      {
         var _loc2_:Array = null;
         _loc2_ = param1.split(" ")[0].split("-");
         this.year.v = _loc2_[0];
         this.month.v = _loc2_[1];
         this.day.v = _loc2_[2];
         _loc2_ = param1.split(" ")[1].split(":");
         this.hour.v = _loc2_[0];
         this.min.v = _loc2_[1];
         this.sec.v = _loc2_[2];
         this.date = new Date(this.year.v,this.month.v - 1,this.day.v);
         this.week.v = this.date.getDay();
      }
      
      public function get timeStamp() : String
      {
         return [this.year.v,this.month.v,this.day.v].join("-") + " " + [this.hour.v,this.min.v,this.sec.v].join(":");
      }
      
      public function get hourAndMin() : String
      {
         return this.hour.v + ":" + this.min.v;
      }
      
      public function get monthAndDay() : String
      {
         return this.month.v + "月" + this.day.v + "日";
      }
      
      public function get totalDay() : int
      {
         return this.date.time / 86400000;
      }
      
      public function isInArea(param1:int, param2:int, param3:int, param4:int) : Boolean
      {
         if(this.hour.v == param1)
         {
            return this.min.v >= param2;
         }
         if(this.hour.v == param3)
         {
            return this.min.v <= param4;
         }
         if(this.hour.v > param1 && this.hour.v < param3)
         {
            return true;
         }
         return false;
      }
   }
}

