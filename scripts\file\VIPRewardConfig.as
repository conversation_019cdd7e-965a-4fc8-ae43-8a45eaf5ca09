package file
{
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.drop.vo.CardRewardVO;
   import mogames.gameData.vip.VIPRewardVO;
   
   public class VIPRewardConfig
   {
      private static var _instance:VIPRewardConfig;
      
      private var _list:Vector.<VIPRewardVO>;
      
      public function VIPRewardConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : VIPRewardConfig
      {
         if(!_instance)
         {
            _instance = new VIPRewardConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<VIPRewardVO>();
         this._list.push(new VIPRewardVO(1,507,[new BaseRewardVO(14009,2),new BaseRewardVO(14150,1),new BaseRewardVO(14120,10),new CardRewardVO(71131,2),new BaseRewardVO(50000,3)]));
         this._list.push(new VIPRewardVO(2,508,[new BaseRewardVO(14009,4),new BaseRewardVO(16751,5),new BaseRewardVO(14121,5),new CardRewardVO(71156,3),new BaseRewardVO(50005,1)]));
         this._list.push(new VIPRewardVO(3,509,[new BaseRewardVO(14009,6),new BaseRewardVO(16755,5),new BaseRewardVO(14021,10),new CardRewardVO(70116,1),new BaseRewardVO(50005,2)]));
         this._list.push(new VIPRewardVO(4,510,[new BaseRewardVO(14009,8),new BaseRewardVO(16756,5),new BaseRewardVO(14039,10),new CardRewardVO(70096,1),new BaseRewardVO(50008,1)]));
         this._list.push(new VIPRewardVO(5,511,[new BaseRewardVO(14009,10),new BaseRewardVO(16757,20),new BaseRewardVO(14045,10),new CardRewardVO(70981,1),new BaseRewardVO(50008,2)]));
         this._list.push(new VIPRewardVO(6,512,[new BaseRewardVO(16762,20),new BaseRewardVO(16757,20),new BaseRewardVO(16755,10),new BaseRewardVO(16756,10),new BaseRewardVO(10090,1)]));
         this._list.push(new VIPRewardVO(7,513,[new BaseRewardVO(16762,30),new BaseRewardVO(16757,30),new BaseRewardVO(16755,20),new BaseRewardVO(16756,20),new BaseRewardVO(10092,1)]));
      }
      
      public function findReward(param1:int) : VIPRewardVO
      {
         var _loc2_:VIPRewardVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.vip.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

