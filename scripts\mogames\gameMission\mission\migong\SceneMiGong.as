package mogames.gameMission.mission.migong
{
   import file.MazeConfig;
   import flash.geom.Point;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class SceneMiGong extends BaseScene
   {
      private var _eTrigger0:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _index:int;
      
      public function SceneMiGong(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_BATTLE1");
         this._index = MazeConfig.instance().activeIndex.v;
         MiniMsgMediator.instance().showMsg("迷宫第" + (this._index + 1) + "层",480,50);
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this._eTrigger0 = new EnemyTrigger(_mission,[[190,247,150,90],[739,247,150,90],[1209,247,150,90]],1600 + this._index,this.triggerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(280,480),1);
         this._pTrigger0.okFunc = this._eTrigger0.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd() : void
      {
         addLeaveDoor(480,400);
         MiniMsgMediator.clean();
      }
      
      override public function destroy() : void
      {
         MiniMsgMediator.clean();
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         this._eTrigger0 = null;
         this._pTrigger0 = null;
         super.destroy();
      }
   }
}

