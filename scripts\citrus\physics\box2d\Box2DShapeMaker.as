package citrus.physics.box2d
{
   import Box2D.Collision.Shapes.b2CircleShape;
   import Box2D.Collision.Shapes.b2PolygonShape;
   import Box2D.Common.Math.b2Vec2;
   
   public class Box2DShapeMaker
   {
      public function Box2DShapeMaker()
      {
         super();
      }
      
      public static function BeveledRect(param1:Number, param2:Number, param3:Number) : b2PolygonShape
      {
         var _loc4_:Number = param1 * 0.5;
         var _loc5_:Number = param2 * 0.5;
         var _loc6_:b2PolygonShape = new b2PolygonShape();
         var _loc7_:Array = [];
         _loc7_.push(new b2Vec2(-_loc4_ + param3,-_loc5_));
         _loc7_.push(new b2Vec2(_loc4_ - param3,-_loc5_));
         _loc7_.push(new b2Vec2(_loc4_,-_loc5_ + param3));
         _loc7_.push(new b2Vec2(_loc4_,_loc5_ - param3));
         _loc7_.push(new b2Vec2(_loc4_ - param3,_loc5_));
         _loc7_.push(new b2Vec2(-_loc4_ + param3,_loc5_));
         _loc7_.push(new b2Vec2(-_loc4_,_loc5_ - param3));
         _loc7_.push(new b2Vec2(-_loc4_,-_loc5_ + param3));
         _loc6_.SetAsArray(_loc7_);
         return _loc6_;
      }
      
      public static function Rect(param1:Number, param2:Number) : b2PolygonShape
      {
         var _loc3_:b2PolygonShape = new b2PolygonShape();
         _loc3_.SetAsBox(param1 * 0.5,param2 * 0.5);
         return _loc3_;
      }
      
      public static function Circle(param1:Number, param2:Number) : b2CircleShape
      {
         var _loc3_:Number = (param1 + param2) * 0.25;
         var _loc4_:b2CircleShape = new b2CircleShape();
         _loc4_.SetRadius(_loc3_);
         return _loc4_;
      }
   }
}

