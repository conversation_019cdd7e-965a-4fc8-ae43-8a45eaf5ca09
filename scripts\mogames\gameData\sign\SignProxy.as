package mogames.gameData.sign
{
   import file.SignConfig;
   import mogames.gameData.flag.vo.NumFlag;
   import mogames.gamePKG.GameProxy;
   
   public class SignProxy
   {
      private static var _instance:SignProxy;
      
      private var _signFlags:Vector.<NumFlag>;
      
      public function SignProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : SignProxy
      {
         if(!_instance)
         {
            _instance = new SignProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._signFlags = new Vector.<NumFlag>();
         var _loc1_:int = 0;
         while(_loc1_ < 31)
         {
            this._signFlags[_loc1_] = new NumFlag(_loc1_ + 1);
            _loc1_++;
         }
         this._signFlags.push(new NumFlag(300));
         this._signFlags.push(new NumFlag(700));
         this._signFlags.push(new NumFlag(1200));
         this._signFlags.push(new NumFlag(2000));
         this._signFlags.push(new NumFlag(3000));
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:Array = null;
         var _loc4_:NumFlag = null;
         var _loc5_:* = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc5_ in _loc2_)
         {
            _loc3_ = _loc5_.split("H");
            _loc4_ = this.findFlag(int(_loc3_[0]));
            if(_loc4_)
            {
               _loc4_.setValue(int(_loc3_[1]));
            }
         }
      }
      
      public function get saveData() : String
      {
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         var _loc3_:int = int(this._signFlags.length);
         while(_loc2_ < _loc3_)
         {
            _loc1_[_loc2_] = this._signFlags[_loc2_].saveStr;
            _loc2_++;
         }
         return _loc1_.join("T");
      }
      
      public function monthRefresh() : void
      {
         this.startNew();
      }
      
      public function checkCanBQ(param1:int) : Boolean
      {
         if(param1 <= 1)
         {
            return false;
         }
         var _loc2_:int = 1;
         while(_loc2_ < param1)
         {
            if(!this.findFlag(_loc2_).isComplete())
            {
               return true;
            }
            _loc2_++;
         }
         return false;
      }
      
      public function countMaxBQ(param1:int) : int
      {
         var _loc2_:int = 0;
         var _loc3_:int = 1;
         while(_loc3_ < param1)
         {
            if(!this.findFlag(_loc3_).isComplete())
            {
               _loc2_++;
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function handlerOneBQ() : int
      {
         var _loc2_:NumFlag = null;
         var _loc1_:int = GameProxy.instance().newTS.day.v;
         var _loc3_:int = 1;
         while(_loc3_ < _loc1_)
         {
            _loc2_ = this.findFlag(_loc3_);
            if(!_loc2_.isComplete())
            {
               _loc2_.setValue(1);
               return _loc3_;
            }
            _loc3_++;
         }
         return 0;
      }
      
      public function handlerBuQian(param1:int) : void
      {
         var _loc2_:int = 0;
         while(_loc2_ < param1)
         {
            this.handlerOneBQ();
            _loc2_++;
         }
      }
      
      public function handlerSign(param1:int) : void
      {
         this.findFlag(param1).setValue(1);
      }
      
      public function checkHasSign(param1:int) : Boolean
      {
         return this.findFlag(param1).isComplete();
      }
      
      public function checkHasReward(param1:int) : Boolean
      {
         var _loc2_:SignFlagVO = SignConfig.instance().findSignFlagVO(param1);
         if(param1 == 4)
         {
            return this.signTotal >= GameProxy.instance().curMonthDay;
         }
         return this.signTotal >= _loc2_.need.v;
      }
      
      public function checkGetReward(param1:int) : Boolean
      {
         var _loc2_:SignFlagVO = SignConfig.instance().findSignFlagVO(param1);
         var _loc3_:NumFlag = this.findFlag(_loc2_.flag.v);
         return _loc3_.cur >= 1;
      }
      
      public function setGetReward(param1:int) : void
      {
         var _loc2_:SignFlagVO = SignConfig.instance().findSignFlagVO(param1);
         var _loc3_:NumFlag = this.findFlag(_loc2_.flag.v);
         _loc3_.setValue(1);
      }
      
      public function get canSign() : Boolean
      {
         return !this.checkHasSign(GameProxy.instance().newTS.day.v);
      }
      
      public function get signTotal() : int
      {
         var _loc1_:int = 0;
         var _loc2_:NumFlag = null;
         var _loc3_:int = 0;
         while(_loc3_ < 31)
         {
            _loc2_ = this.findFlag(_loc3_ + 1);
            _loc1_ += _loc2_.cur;
            _loc3_++;
         }
         return _loc1_;
      }
      
      public function findFlag(param1:int) : NumFlag
      {
         var _loc2_:NumFlag = null;
         for each(_loc2_ in this._signFlags)
         {
            if(_loc2_.flagID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

