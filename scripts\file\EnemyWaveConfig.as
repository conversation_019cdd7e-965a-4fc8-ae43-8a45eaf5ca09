package file
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.mission.EnemyWaveVO;
   
   public class EnemyWaveConfig
   {
      private static var _instance:EnemyWaveConfig;
      
      private var _waveVec:Vector.<EnemyWaveVO>;
      
      public function EnemyWaveConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : EnemyWaveConfig
      {
         if(!_instance)
         {
            _instance = new EnemyWaveConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._waveVec = new Vector.<EnemyWaveVO>();
         this._waveVec.push(new EnemyWaveVO(11,5,[{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         },{
            "id":new Sint(2001),
            "attID":new Sint(20011),
            "aiID":new Sint(20011),
            "dropID":new Sint(0)
         }]));
         this._waveVec.push(new EnemyWaveVO(1011,3,[{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20031),
            "aiID":new Sint(20031),
            "dropID":new Sint(94)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20031),
            "aiID":new Sint(20031),
            "dropID":new Sint(94)
         }]));
         this._waveVec.push(new EnemyWaveVO(1012,3,[{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20031),
            "aiID":new Sint(20031),
            "dropID":new Sint(94)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20031),
            "aiID":new Sint(20031),
            "dropID":new Sint(94)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20021),
            "aiID":new Sint(20021),
            "dropID":new Sint(1)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20031),
            "aiID":new Sint(20031),
            "dropID":new Sint(94)
         }]));
         this._waveVec.push(new EnemyWaveVO(3011,4,[{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20032),
            "aiID":new Sint(20032),
            "dropID":new Sint(67)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         }]));
         this._waveVec.push(new EnemyWaveVO(3012,4,[{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20032),
            "aiID":new Sint(20032),
            "dropID":new Sint(67)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20032),
            "aiID":new Sint(20032),
            "dropID":new Sint(67)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20041),
            "aiID":new Sint(20041),
            "dropID":new Sint(3)
         }]));
         this._waveVec.push(new EnemyWaveVO(311,4,[{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(312,4,[{
            "id":new Sint(2004),
            "attID":new Sint(20044),
            "aiID":new Sint(20041),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20044),
            "aiID":new Sint(20041),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20044),
            "aiID":new Sint(20041),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20044),
            "aiID":new Sint(20041),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20044),
            "aiID":new Sint(20041),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20044),
            "aiID":new Sint(20041),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(313,4,[{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20044),
            "aiID":new Sint(20041),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20026),
            "aiID":new Sint(20023),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20044),
            "aiID":new Sint(20041),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2026),
            "attID":new Sint(20261),
            "aiID":new Sint(20261),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20044),
            "aiID":new Sint(20041),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(511,4,[{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(512,4,[{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(513,4,[{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2030),
            "attID":new Sint(20301),
            "aiID":new Sint(20301),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20223),
            "aiID":new Sint(20222),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(610,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20340),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20340),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20340),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20350),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20350),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(611,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20341),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20341),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20341),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20351),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20351),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(612,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20342),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20342),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20342),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20352),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20352),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(613,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20343),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20343),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20343),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20353),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20353),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(614,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20344),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20344),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20344),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20354),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20354),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(615,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20345),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20345),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20345),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20355),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20355),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(616,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20346),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20346),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20346),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20356),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20356),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(617,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20347),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20347),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20347),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20357),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20357),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(618,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20348),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20348),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20348),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20358),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20358),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(619,5,[{
            "id":new Sint(2034),
            "attID":new Sint(20349),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20349),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2034),
            "attID":new Sint(20349),
            "aiID":new Sint(20341),
            "dropID":new Sint(120)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20359),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         },{
            "id":new Sint(2035),
            "attID":new Sint(20359),
            "aiID":new Sint(20351),
            "dropID":new Sint(121)
         }]));
         this._waveVec.push(new EnemyWaveVO(211,4,[{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(212,4,[{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2039),
            "attID":new Sint(20391),
            "aiID":new Sint(20391),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2040),
            "attID":new Sint(20401),
            "aiID":new Sint(20401),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(1301,4,[{
            "id":new Sint(2020),
            "attID":new Sint(20203),
            "aiID":new Sint(20203),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20203),
            "aiID":new Sint(20203),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20203),
            "aiID":new Sint(20203),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20203),
            "aiID":new Sint(20203),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20203),
            "aiID":new Sint(20203),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20203),
            "aiID":new Sint(20203),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(1302,4,[{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(1303,4,[{
            "id":new Sint(2020),
            "attID":new Sint(20203),
            "aiID":new Sint(20203),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20203),
            "aiID":new Sint(20203),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2049),
            "attID":new Sint(20491),
            "aiID":new Sint(20491),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20203),
            "aiID":new Sint(20203),
            "dropID":new Sint(88)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20213),
            "aiID":new Sint(20213),
            "dropID":new Sint(88)
         }]));
         this._waveVec.push(new EnemyWaveVO(1600,4,[{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(184)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(185)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(186)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(184)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(185)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(186)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(184)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(185)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(186)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(184)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(185)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20155),
            "aiID":new Sint(20151),
            "dropID":new Sint(186)
         }]));
         this._waveVec.push(new EnemyWaveVO(1601,4,[{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(187)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(188)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20165),
            "aiID":new Sint(20161),
            "dropID":new Sint(189)
         }]));
         this._waveVec.push(new EnemyWaveVO(1602,4,[{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(173)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(174)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(175)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         }]));
         this._waveVec.push(new EnemyWaveVO(1603,4,[{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(177)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(178)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(176)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20204),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         }]));
         this._waveVec.push(new EnemyWaveVO(1604,4,[{
            "id":new Sint(2015),
            "attID":new Sint(20156),
            "aiID":new Sint(20151),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20166),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20205),
            "aiID":new Sint(20201),
            "dropID":new Sint(182)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20205),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20156),
            "aiID":new Sint(20151),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20166),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20205),
            "aiID":new Sint(20201),
            "dropID":new Sint(180)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20205),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20156),
            "aiID":new Sint(20151),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20166),
            "aiID":new Sint(20161),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20205),
            "aiID":new Sint(20201),
            "dropID":new Sint(183)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20205),
            "aiID":new Sint(20201),
            "dropID":new Sint(181)
         }]));
         this._waveVec.push(new EnemyWaveVO(5011,4,[{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20051),
            "aiID":new Sint(20051),
            "dropID":new Sint(7)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         }]));
         this._waveVec.push(new EnemyWaveVO(5012,4,[{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20051),
            "aiID":new Sint(20051),
            "dropID":new Sint(7)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20051),
            "aiID":new Sint(20051),
            "dropID":new Sint(7)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         }]));
         this._waveVec.push(new EnemyWaveVO(5013,4,[{
            "id":new Sint(2005),
            "attID":new Sint(20051),
            "aiID":new Sint(20051),
            "dropID":new Sint(7)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20051),
            "aiID":new Sint(20051),
            "dropID":new Sint(7)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20051),
            "aiID":new Sint(20051),
            "dropID":new Sint(7)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20022),
            "aiID":new Sint(20022),
            "dropID":new Sint(6)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20051),
            "aiID":new Sint(20051),
            "dropID":new Sint(7)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         }]));
         this._waveVec.push(new EnemyWaveVO(5014,2,[{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(5)
         }]));
         this._waveVec.push(new EnemyWaveVO(7011,4,[{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         }]));
         this._waveVec.push(new EnemyWaveVO(7012,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         }]));
         this._waveVec.push(new EnemyWaveVO(7013,4,[{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         }]));
         this._waveVec.push(new EnemyWaveVO(7014,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         }]));
         this._waveVec.push(new EnemyWaveVO(7021,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         }]));
         this._waveVec.push(new EnemyWaveVO(7022,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(12)
         }]));
         this._waveVec.push(new EnemyWaveVO(7031,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         }]));
         this._waveVec.push(new EnemyWaveVO(7032,3,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(14)
         }]));
         this._waveVec.push(new EnemyWaveVO(7041,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         }]));
         this._waveVec.push(new EnemyWaveVO(7042,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(13)
         }]));
         this._waveVec.push(new EnemyWaveVO(7051,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         }]));
         this._waveVec.push(new EnemyWaveVO(7052,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20081),
            "aiID":new Sint(20081),
            "dropID":new Sint(10)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20071),
            "aiID":new Sint(20071),
            "dropID":new Sint(9)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20091),
            "aiID":new Sint(20091),
            "dropID":new Sint(11)
         }]));
         this._waveVec.push(new EnemyWaveVO(8011,5,[{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         }]));
         this._waveVec.push(new EnemyWaveVO(8021,5,[{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         }]));
         this._waveVec.push(new EnemyWaveVO(8031,4,[{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         }]));
         this._waveVec.push(new EnemyWaveVO(8032,2,[{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         }]));
         this._waveVec.push(new EnemyWaveVO(8041,4,[{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         }]));
         this._waveVec.push(new EnemyWaveVO(8051,4,[{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         }]));
         this._waveVec.push(new EnemyWaveVO(8052,3,[{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         }]));
         this._waveVec.push(new EnemyWaveVO(8061,4,[{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         }]));
         this._waveVec.push(new EnemyWaveVO(8062,5,[{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         }]));
         this._waveVec.push(new EnemyWaveVO(8071,4,[{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         }]));
         this._waveVec.push(new EnemyWaveVO(8072,4,[{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2002),
            "attID":new Sint(20023),
            "aiID":new Sint(20023),
            "dropID":new Sint(23)
         }]));
         this._waveVec.push(new EnemyWaveVO(8081,4,[{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20061),
            "aiID":new Sint(20061),
            "dropID":new Sint(25)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         }]));
         this._waveVec.push(new EnemyWaveVO(8082,2,[{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         },{
            "id":new Sint(2004),
            "attID":new Sint(20042),
            "aiID":new Sint(20042),
            "dropID":new Sint(22)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20052),
            "aiID":new Sint(20052),
            "dropID":new Sint(24)
         }]));
         this._waveVec.push(new EnemyWaveVO(9011,4,[{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20111),
            "aiID":new Sint(20111),
            "dropID":new Sint(32)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20111),
            "aiID":new Sint(20111),
            "dropID":new Sint(32)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20111),
            "aiID":new Sint(20111),
            "dropID":new Sint(32)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20111),
            "aiID":new Sint(20111),
            "dropID":new Sint(32)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         }]));
         this._waveVec.push(new EnemyWaveVO(9012,4,[{
            "id":new Sint(2010),
            "attID":new Sint(20101),
            "aiID":new Sint(20101),
            "dropID":new Sint(33)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20101),
            "aiID":new Sint(20101),
            "dropID":new Sint(33)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20101),
            "aiID":new Sint(20101),
            "dropID":new Sint(33)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20101),
            "aiID":new Sint(20101),
            "dropID":new Sint(33)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20101),
            "aiID":new Sint(20101),
            "dropID":new Sint(33)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20101),
            "aiID":new Sint(20101),
            "dropID":new Sint(33)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20121),
            "aiID":new Sint(20121),
            "dropID":new Sint(34)
         }]));
         this._waveVec.push(new EnemyWaveVO(9021,4,[{
            "id":new Sint(2011),
            "attID":new Sint(20111),
            "aiID":new Sint(20111),
            "dropID":new Sint(32)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20101),
            "aiID":new Sint(20101),
            "dropID":new Sint(33)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20121),
            "aiID":new Sint(20121),
            "dropID":new Sint(34)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20111),
            "aiID":new Sint(20111),
            "dropID":new Sint(32)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20101),
            "aiID":new Sint(20101),
            "dropID":new Sint(33)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20121),
            "aiID":new Sint(20121),
            "dropID":new Sint(34)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         }]));
         this._waveVec.push(new EnemyWaveVO(9022,3,[{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         }]));
         this._waveVec.push(new EnemyWaveVO(9023,4,[{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20033),
            "aiID":new Sint(20033),
            "dropID":new Sint(31)
         }]));
         this._waveVec.push(new EnemyWaveVO(9031,3,[{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20062),
            "aiID":new Sint(20062),
            "dropID":new Sint(31)
         }]));
         this._waveVec.push(new EnemyWaveVO(10011,4,[{
            "id":new Sint(2005),
            "attID":new Sint(20053),
            "aiID":new Sint(20053),
            "dropID":new Sint(36)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20112),
            "aiID":new Sint(20112),
            "dropID":new Sint(39)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20053),
            "aiID":new Sint(20053),
            "dropID":new Sint(36)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20112),
            "aiID":new Sint(20112),
            "dropID":new Sint(39)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20053),
            "aiID":new Sint(20053),
            "dropID":new Sint(36)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20112),
            "aiID":new Sint(20112),
            "dropID":new Sint(39)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20053),
            "aiID":new Sint(20053),
            "dropID":new Sint(36)
         }]));
         this._waveVec.push(new EnemyWaveVO(10012,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20082),
            "aiID":new Sint(20082),
            "dropID":new Sint(37)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20122),
            "aiID":new Sint(20122),
            "dropID":new Sint(38)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20082),
            "aiID":new Sint(20082),
            "dropID":new Sint(37)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20082),
            "aiID":new Sint(20082),
            "dropID":new Sint(37)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20122),
            "aiID":new Sint(20122),
            "dropID":new Sint(38)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20082),
            "aiID":new Sint(20082),
            "dropID":new Sint(37)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20122),
            "aiID":new Sint(20122),
            "dropID":new Sint(38)
         }]));
         this._waveVec.push(new EnemyWaveVO(10013,4,[{
            "id":new Sint(2005),
            "attID":new Sint(20053),
            "aiID":new Sint(20053),
            "dropID":new Sint(36)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20082),
            "aiID":new Sint(20082),
            "dropID":new Sint(37)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20112),
            "aiID":new Sint(20112),
            "dropID":new Sint(39)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20053),
            "aiID":new Sint(20053),
            "dropID":new Sint(36)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20082),
            "aiID":new Sint(20082),
            "dropID":new Sint(37)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20112),
            "aiID":new Sint(20112),
            "dropID":new Sint(39)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20122),
            "aiID":new Sint(20122),
            "dropID":new Sint(38)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20122),
            "aiID":new Sint(20122),
            "dropID":new Sint(38)
         }]));
         this._waveVec.push(new EnemyWaveVO(10021,4,[{
            "id":new Sint(2011),
            "attID":new Sint(20112),
            "aiID":new Sint(20112),
            "dropID":new Sint(39)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20082),
            "aiID":new Sint(20082),
            "dropID":new Sint(37)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20122),
            "aiID":new Sint(20122),
            "dropID":new Sint(38)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20053),
            "aiID":new Sint(20053),
            "dropID":new Sint(36)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20112),
            "aiID":new Sint(20112),
            "dropID":new Sint(39)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20122),
            "aiID":new Sint(20122),
            "dropID":new Sint(38)
         },{
            "id":new Sint(2011),
            "attID":new Sint(20112),
            "aiID":new Sint(20112),
            "dropID":new Sint(39)
         }]));
         this._waveVec.push(new EnemyWaveVO(11011,4,[{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         }]));
         this._waveVec.push(new EnemyWaveVO(11012,4,[{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         }]));
         this._waveVec.push(new EnemyWaveVO(11013,4,[{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         }]));
         this._waveVec.push(new EnemyWaveVO(11021,3,[{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20123),
            "aiID":new Sint(20122),
            "dropID":new Sint(95)
         }]));
         this._waveVec.push(new EnemyWaveVO(11022,4,[{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(47)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(47)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(47)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(47)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(47)
         },{
            "id":new Sint(2010),
            "attID":new Sint(20102),
            "aiID":new Sint(20101),
            "dropID":new Sint(47)
         }]));
         this._waveVec.push(new EnemyWaveVO(11023,4,[{
            "id":new Sint(2009),
            "attID":new Sint(20092),
            "aiID":new Sint(20091),
            "dropID":new Sint(96)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20092),
            "aiID":new Sint(20091),
            "dropID":new Sint(96)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20092),
            "aiID":new Sint(20091),
            "dropID":new Sint(96)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20092),
            "aiID":new Sint(20091),
            "dropID":new Sint(96)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20092),
            "aiID":new Sint(20091),
            "dropID":new Sint(96)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20092),
            "aiID":new Sint(20091),
            "dropID":new Sint(96)
         }]));
         this._waveVec.push(new EnemyWaveVO(11024,4,[{
            "id":new Sint(2003),
            "attID":new Sint(20034),
            "aiID":new Sint(20033),
            "dropID":new Sint(97)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20034),
            "aiID":new Sint(20033),
            "dropID":new Sint(97)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20034),
            "aiID":new Sint(20033),
            "dropID":new Sint(97)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20034),
            "aiID":new Sint(20033),
            "dropID":new Sint(97)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20034),
            "aiID":new Sint(20033),
            "dropID":new Sint(97)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20034),
            "aiID":new Sint(20033),
            "dropID":new Sint(97)
         }]));
         this._waveVec.push(new EnemyWaveVO(11025,4,[{
            "id":new Sint(2005),
            "attID":new Sint(20054),
            "aiID":new Sint(20053),
            "dropID":new Sint(98)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20054),
            "aiID":new Sint(20053),
            "dropID":new Sint(98)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20054),
            "aiID":new Sint(20053),
            "dropID":new Sint(98)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20054),
            "aiID":new Sint(20053),
            "dropID":new Sint(98)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20054),
            "aiID":new Sint(20053),
            "dropID":new Sint(98)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20054),
            "aiID":new Sint(20053),
            "dropID":new Sint(98)
         }]));
         this._waveVec.push(new EnemyWaveVO(11026,4,[{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         }]));
         this._waveVec.push(new EnemyWaveVO(11031,4,[{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20123),
            "aiID":new Sint(20122),
            "dropID":new Sint(95)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20123),
            "aiID":new Sint(20122),
            "dropID":new Sint(95)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         }]));
         this._waveVec.push(new EnemyWaveVO(11032,4,[{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20123),
            "aiID":new Sint(20122),
            "dropID":new Sint(95)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20123),
            "aiID":new Sint(20122),
            "dropID":new Sint(95)
         },{
            "id":new Sint(2013),
            "attID":new Sint(20131),
            "aiID":new Sint(20131),
            "dropID":new Sint(45)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20141),
            "aiID":new Sint(20141),
            "dropID":new Sint(46)
         }]));
         this._waveVec.push(new EnemyWaveVO(12011,4,[{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         }]));
         this._waveVec.push(new EnemyWaveVO(12012,4,[{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         }]));
         this._waveVec.push(new EnemyWaveVO(12013,4,[{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         }]));
         this._waveVec.push(new EnemyWaveVO(12014,4,[{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         }]));
         this._waveVec.push(new EnemyWaveVO(12021,4,[{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         }]));
         this._waveVec.push(new EnemyWaveVO(12022,4,[{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         },{
            "id":new Sint(2015),
            "attID":new Sint(20151),
            "aiID":new Sint(20151),
            "dropID":new Sint(53)
         },{
            "id":new Sint(2016),
            "attID":new Sint(20161),
            "aiID":new Sint(20161),
            "dropID":new Sint(99)
         }]));
         this._waveVec.push(new EnemyWaveVO(13011,4,[{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         }]));
         this._waveVec.push(new EnemyWaveVO(13012,4,[{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         }]));
         this._waveVec.push(new EnemyWaveVO(13013,4,[{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         }]));
         this._waveVec.push(new EnemyWaveVO(13014,4,[{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2017),
            "attID":new Sint(20171),
            "aiID":new Sint(20171),
            "dropID":new Sint(61)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         },{
            "id":new Sint(2018),
            "attID":new Sint(20181),
            "aiID":new Sint(20181),
            "dropID":new Sint(100)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20191),
            "aiID":new Sint(20191),
            "dropID":new Sint(101)
         }]));
         this._waveVec.push(new EnemyWaveVO(14011,4,[{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         }]));
         this._waveVec.push(new EnemyWaveVO(14012,4,[{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         }]));
         this._waveVec.push(new EnemyWaveVO(14013,4,[{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         }]));
         this._waveVec.push(new EnemyWaveVO(14014,4,[{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         }]));
         this._waveVec.push(new EnemyWaveVO(14015,4,[{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2020),
            "attID":new Sint(20201),
            "aiID":new Sint(20201),
            "dropID":new Sint(64)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         },{
            "id":new Sint(2021),
            "attID":new Sint(20211),
            "aiID":new Sint(20211),
            "dropID":new Sint(102)
         }]));
         this._waveVec.push(new EnemyWaveVO(15011,4,[{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         }]));
         this._waveVec.push(new EnemyWaveVO(15012,4,[{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         }]));
         this._waveVec.push(new EnemyWaveVO(15013,4,[{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         }]));
         this._waveVec.push(new EnemyWaveVO(15014,4,[{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20231),
            "aiID":new Sint(20231),
            "dropID":new Sint(103)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20221),
            "aiID":new Sint(20221),
            "dropID":new Sint(68)
         }]));
         this._waveVec.push(new EnemyWaveVO(16011,4,[{
            "id":new Sint(2007),
            "attID":new Sint(20072),
            "aiID":new Sint(20072),
            "dropID":new Sint(74)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20072),
            "aiID":new Sint(20072),
            "dropID":new Sint(74)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20072),
            "aiID":new Sint(20072),
            "dropID":new Sint(74)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20072),
            "aiID":new Sint(20072),
            "dropID":new Sint(74)
         }]));
         this._waveVec.push(new EnemyWaveVO(16012,4,[{
            "id":new Sint(2007),
            "attID":new Sint(20072),
            "aiID":new Sint(20072),
            "dropID":new Sint(74)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20072),
            "aiID":new Sint(20072),
            "dropID":new Sint(74)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20093),
            "aiID":new Sint(20093),
            "dropID":new Sint(105)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20093),
            "aiID":new Sint(20093),
            "dropID":new Sint(105)
         }]));
         this._waveVec.push(new EnemyWaveVO(16013,4,[{
            "id":new Sint(2007),
            "attID":new Sint(20072),
            "aiID":new Sint(20072),
            "dropID":new Sint(74)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20093),
            "aiID":new Sint(20093),
            "dropID":new Sint(105)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20072),
            "aiID":new Sint(20072),
            "dropID":new Sint(74)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20093),
            "aiID":new Sint(20093),
            "dropID":new Sint(105)
         }]));
         this._waveVec.push(new EnemyWaveVO(16014,4,[{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20063),
            "dropID":new Sint(106)
         },{
            "id":new Sint(2007),
            "attID":new Sint(20072),
            "aiID":new Sint(20072),
            "dropID":new Sint(74)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         },{
            "id":new Sint(2009),
            "attID":new Sint(20093),
            "aiID":new Sint(20093),
            "dropID":new Sint(105)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20063),
            "dropID":new Sint(106)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         }]));
         this._waveVec.push(new EnemyWaveVO(16015,4,[{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20063),
            "dropID":new Sint(106)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20063),
            "dropID":new Sint(106)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20063),
            "dropID":new Sint(106)
         },{
            "id":new Sint(2008),
            "attID":new Sint(20083),
            "aiID":new Sint(20083),
            "dropID":new Sint(104)
         }]));
         this._waveVec.push(new EnemyWaveVO(17011,4,[{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20233),
            "aiID":new Sint(20231),
            "dropID":new Sint(107)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20233),
            "aiID":new Sint(20231),
            "dropID":new Sint(107)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20233),
            "aiID":new Sint(20231),
            "dropID":new Sint(107)
         }]));
         this._waveVec.push(new EnemyWaveVO(17012,4,[{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20057),
            "aiID":new Sint(20053),
            "dropID":new Sint(108)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20057),
            "aiID":new Sint(20053),
            "dropID":new Sint(108)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20057),
            "aiID":new Sint(20053),
            "dropID":new Sint(108)
         }]));
         this._waveVec.push(new EnemyWaveVO(17021,4,[{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20125),
            "aiID":new Sint(20123),
            "dropID":new Sint(109)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20233),
            "aiID":new Sint(20231),
            "dropID":new Sint(107)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20125),
            "aiID":new Sint(20123),
            "dropID":new Sint(109)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20233),
            "aiID":new Sint(20231),
            "dropID":new Sint(107)
         }]));
         this._waveVec.push(new EnemyWaveVO(17022,4,[{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20125),
            "aiID":new Sint(20123),
            "dropID":new Sint(109)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20233),
            "aiID":new Sint(20231),
            "dropID":new Sint(107)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20057),
            "aiID":new Sint(20053),
            "dropID":new Sint(108)
         },{
            "id":new Sint(2014),
            "attID":new Sint(20143),
            "aiID":new Sint(20141),
            "dropID":new Sint(76)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20125),
            "aiID":new Sint(20123),
            "dropID":new Sint(109)
         },{
            "id":new Sint(2023),
            "attID":new Sint(20233),
            "aiID":new Sint(20231),
            "dropID":new Sint(107)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20057),
            "aiID":new Sint(20053),
            "dropID":new Sint(108)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20125),
            "aiID":new Sint(20123),
            "dropID":new Sint(109)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20125),
            "aiID":new Sint(20123),
            "dropID":new Sint(76)
         }]));
         this._waveVec.push(new EnemyWaveVO(17031,4,[{
            "id":new Sint(2005),
            "attID":new Sint(20057),
            "aiID":new Sint(20053),
            "dropID":new Sint(108)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20057),
            "aiID":new Sint(20053),
            "dropID":new Sint(108)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20125),
            "aiID":new Sint(20123),
            "dropID":new Sint(109)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20125),
            "aiID":new Sint(20123),
            "dropID":new Sint(109)
         },{
            "id":new Sint(2005),
            "attID":new Sint(20057),
            "aiID":new Sint(20053),
            "dropID":new Sint(108)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20125),
            "aiID":new Sint(20123),
            "dropID":new Sint(109)
         }]));
         this._waveVec.push(new EnemyWaveVO(18011,4,[{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         }]));
         this._waveVec.push(new EnemyWaveVO(18012,4,[{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         }]));
         this._waveVec.push(new EnemyWaveVO(18013,4,[{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20126),
            "aiID":new Sint(20124),
            "dropID":new Sint(111)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20126),
            "aiID":new Sint(20124),
            "dropID":new Sint(84)
         }]));
         this._waveVec.push(new EnemyWaveVO(18014,4,[{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20126),
            "aiID":new Sint(20124),
            "dropID":new Sint(111)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20126),
            "aiID":new Sint(20124),
            "dropID":new Sint(111)
         }]));
         this._waveVec.push(new EnemyWaveVO(18015,4,[{
            "id":new Sint(2012),
            "attID":new Sint(20126),
            "aiID":new Sint(20124),
            "dropID":new Sint(111)
         },{
            "id":new Sint(2022),
            "attID":new Sint(20222),
            "aiID":new Sint(20222),
            "dropID":new Sint(110)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20126),
            "aiID":new Sint(20124),
            "dropID":new Sint(111)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20126),
            "aiID":new Sint(20124),
            "dropID":new Sint(111)
         },{
            "id":new Sint(2019),
            "attID":new Sint(20192),
            "aiID":new Sint(20192),
            "dropID":new Sint(84)
         },{
            "id":new Sint(2012),
            "attID":new Sint(20126),
            "aiID":new Sint(20124),
            "dropID":new Sint(111)
         }]));
         this._waveVec.push(new EnemyWaveVO(19011,4,[{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         }]));
         this._waveVec.push(new EnemyWaveVO(19012,4,[{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         }]));
         this._waveVec.push(new EnemyWaveVO(19013,4,[{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         }]));
         this._waveVec.push(new EnemyWaveVO(19014,4,[{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20241),
            "aiID":new Sint(20241),
            "dropID":new Sint(86)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20251),
            "aiID":new Sint(20251),
            "dropID":new Sint(112)
         }]));
         this._waveVec.push(new EnemyWaveVO(20011,4,[{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         }]));
         this._waveVec.push(new EnemyWaveVO(20012,4,[{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         }]));
         this._waveVec.push(new EnemyWaveVO(20013,4,[{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         }]));
         this._waveVec.push(new EnemyWaveVO(20014,4,[{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         },{
            "id":new Sint(2028),
            "attID":new Sint(20281),
            "aiID":new Sint(20281),
            "dropID":new Sint(90)
         },{
            "id":new Sint(2029),
            "attID":new Sint(20291),
            "aiID":new Sint(20291),
            "dropID":new Sint(113)
         }]));
         this._waveVec.push(new EnemyWaveVO(21011,4,[{
            "id":new Sint(2031),
            "attID":new Sint(20311),
            "aiID":new Sint(20311),
            "dropID":new Sint(92)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20311),
            "aiID":new Sint(20311),
            "dropID":new Sint(92)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20311),
            "aiID":new Sint(20311),
            "dropID":new Sint(92)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20311),
            "aiID":new Sint(20311),
            "dropID":new Sint(92)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20311),
            "aiID":new Sint(20311),
            "dropID":new Sint(92)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20311),
            "aiID":new Sint(20311),
            "dropID":new Sint(92)
         }]));
         this._waveVec.push(new EnemyWaveVO(21012,4,[{
            "id":new Sint(2031),
            "attID":new Sint(20311),
            "aiID":new Sint(20311),
            "dropID":new Sint(92)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20311),
            "aiID":new Sint(20311),
            "dropID":new Sint(92)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20311),
            "aiID":new Sint(20311),
            "dropID":new Sint(92)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20037),
            "aiID":new Sint(20034),
            "dropID":new Sint(114)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20037),
            "aiID":new Sint(20034),
            "dropID":new Sint(114)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20037),
            "aiID":new Sint(20034),
            "dropID":new Sint(114)
         }]));
         this._waveVec.push(new EnemyWaveVO(21013,4,[{
            "id":new Sint(2003),
            "attID":new Sint(20037),
            "aiID":new Sint(20034),
            "dropID":new Sint(114)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20037),
            "aiID":new Sint(20034),
            "dropID":new Sint(114)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20064),
            "dropID":new Sint(115)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20037),
            "aiID":new Sint(20034),
            "dropID":new Sint(114)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20064),
            "dropID":new Sint(115)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20064),
            "dropID":new Sint(115)
         }]));
         this._waveVec.push(new EnemyWaveVO(21014,4,[{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20064),
            "dropID":new Sint(115)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20064),
            "dropID":new Sint(115)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20037),
            "aiID":new Sint(20034),
            "dropID":new Sint(114)
         },{
            "id":new Sint(2003),
            "attID":new Sint(20037),
            "aiID":new Sint(20034),
            "dropID":new Sint(114)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20064),
            "dropID":new Sint(115)
         },{
            "id":new Sint(2006),
            "attID":new Sint(20063),
            "aiID":new Sint(20064),
            "dropID":new Sint(115)
         }]));
         this._waveVec.push(new EnemyWaveVO(22011,4,[{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         }]));
         this._waveVec.push(new EnemyWaveVO(22012,4,[{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         }]));
         this._waveVec.push(new EnemyWaveVO(22013,4,[{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         }]));
         this._waveVec.push(new EnemyWaveVO(22014,4,[{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2032),
            "attID":new Sint(20321),
            "aiID":new Sint(20321),
            "dropID":new Sint(116)
         },{
            "id":new Sint(2033),
            "attID":new Sint(20331),
            "aiID":new Sint(20331),
            "dropID":new Sint(117)
         }]));
         this._waveVec.push(new EnemyWaveVO(23011,4,[{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         }]));
         this._waveVec.push(new EnemyWaveVO(23012,4,[{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         }]));
         this._waveVec.push(new EnemyWaveVO(23013,4,[{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         }]));
         this._waveVec.push(new EnemyWaveVO(23014,4,[{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20371),
            "aiID":new Sint(20371),
            "dropID":new Sint(123)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         },{
            "id":new Sint(2036),
            "attID":new Sint(20361),
            "aiID":new Sint(20361),
            "dropID":new Sint(122)
         }]));
         this._waveVec.push(new EnemyWaveVO(24011,4,[{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20311),
            "dropID":new Sint(127)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20311),
            "dropID":new Sint(127)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20311),
            "dropID":new Sint(127)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20311),
            "dropID":new Sint(127)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20311),
            "dropID":new Sint(127)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20311),
            "dropID":new Sint(127)
         }]));
         this._waveVec.push(new EnemyWaveVO(24012,4,[{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20311),
            "dropID":new Sint(127)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20311),
            "dropID":new Sint(127)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20312),
            "aiID":new Sint(20311),
            "dropID":new Sint(127)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         }]));
         this._waveVec.push(new EnemyWaveVO(24013,4,[{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         }]));
         this._waveVec.push(new EnemyWaveVO(25011,4,[{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         }]));
         this._waveVec.push(new EnemyWaveVO(25012,4,[{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2043),
            "attID":new Sint(20431),
            "aiID":new Sint(20431),
            "dropID":new Sint(135)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2043),
            "attID":new Sint(20431),
            "aiID":new Sint(20431),
            "dropID":new Sint(135)
         }]));
         this._waveVec.push(new EnemyWaveVO(25013,4,[{
            "id":new Sint(2043),
            "attID":new Sint(20431),
            "aiID":new Sint(20431),
            "dropID":new Sint(135)
         },{
            "id":new Sint(2043),
            "attID":new Sint(20431),
            "aiID":new Sint(20431),
            "dropID":new Sint(135)
         },{
            "id":new Sint(2043),
            "attID":new Sint(20431),
            "aiID":new Sint(20431),
            "dropID":new Sint(135)
         },{
            "id":new Sint(2043),
            "attID":new Sint(20431),
            "aiID":new Sint(20431),
            "dropID":new Sint(135)
         },{
            "id":new Sint(2043),
            "attID":new Sint(20431),
            "aiID":new Sint(20431),
            "dropID":new Sint(135)
         },{
            "id":new Sint(2043),
            "attID":new Sint(20431),
            "aiID":new Sint(20431),
            "dropID":new Sint(135)
         }]));
         this._waveVec.push(new EnemyWaveVO(25014,4,[{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20421),
            "aiID":new Sint(20421),
            "dropID":new Sint(134)
         },{
            "id":new Sint(2043),
            "attID":new Sint(20431),
            "aiID":new Sint(20431),
            "dropID":new Sint(135)
         }]));
         this._waveVec.push(new EnemyWaveVO(26011,4,[{
            "id":new Sint(2041),
            "attID":new Sint(20411),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         }]));
         this._waveVec.push(new EnemyWaveVO(26012,4,[{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         }]));
         this._waveVec.push(new EnemyWaveVO(26013,4,[{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         }]));
         this._waveVec.push(new EnemyWaveVO(26014,4,[{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2041),
            "attID":new Sint(20412),
            "aiID":new Sint(20411),
            "dropID":new Sint(126)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20461),
            "aiID":new Sint(20461),
            "dropID":new Sint(144)
         }]));
         this._waveVec.push(new EnemyWaveVO(27011,4,[{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         }]));
         this._waveVec.push(new EnemyWaveVO(27012,4,[{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(148)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(148)
         }]));
         this._waveVec.push(new EnemyWaveVO(27013,4,[{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(148)
         },{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(148)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(148)
         }]));
         this._waveVec.push(new EnemyWaveVO(27014,4,[{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(148)
         },{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(148)
         },{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(148)
         },{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2031),
            "attID":new Sint(20313),
            "aiID":new Sint(20313),
            "dropID":new Sint(147)
         },{
            "id":new Sint(2047),
            "attID":new Sint(20471),
            "aiID":new Sint(20471),
            "dropID":new Sint(148)
         }]));
         this._waveVec.push(new EnemyWaveVO(28011,4,[{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         }]));
         this._waveVec.push(new EnemyWaveVO(28012,4,[{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         }]));
         this._waveVec.push(new EnemyWaveVO(28013,4,[{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         }]));
         this._waveVec.push(new EnemyWaveVO(28014,4,[{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         },{
            "id":new Sint(2042),
            "attID":new Sint(20422),
            "aiID":new Sint(20422),
            "dropID":new Sint(151)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20481),
            "aiID":new Sint(20481),
            "dropID":new Sint(150)
         }]));
         this._waveVec.push(new EnemyWaveVO(29011,4,[{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         }]));
         this._waveVec.push(new EnemyWaveVO(29012,4,[{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         },{
            "id":new Sint(2037),
            "attID":new Sint(20372),
            "aiID":new Sint(20372),
            "dropID":new Sint(154)
         }]));
         this._waveVec.push(new EnemyWaveVO(29013,4,[{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         }]));
         this._waveVec.push(new EnemyWaveVO(29014,4,[{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         }]));
         this._waveVec.push(new EnemyWaveVO(29015,4,[{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         }]));
         this._waveVec.push(new EnemyWaveVO(30011,4,[{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         },{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         }]));
         this._waveVec.push(new EnemyWaveVO(30012,4,[{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         }]));
         this._waveVec.push(new EnemyWaveVO(30013,4,[{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         },{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         }]));
         this._waveVec.push(new EnemyWaveVO(30014,4,[{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         },{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         },{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         },{
            "id":new Sint(2050),
            "attID":new Sint(20501),
            "aiID":new Sint(20501),
            "dropID":new Sint(158)
         },{
            "id":new Sint(2048),
            "attID":new Sint(20482),
            "aiID":new Sint(20482),
            "dropID":new Sint(157)
         }]));
         this._waveVec.push(new EnemyWaveVO(31011,4,[{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         }]));
         this._waveVec.push(new EnemyWaveVO(31012,4,[{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         }]));
         this._waveVec.push(new EnemyWaveVO(31013,4,[{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         },{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         }]));
         this._waveVec.push(new EnemyWaveVO(31014,4,[{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         },{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         },{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         },{
            "id":new Sint(2051),
            "attID":new Sint(20511),
            "aiID":new Sint(20511),
            "dropID":new Sint(160)
         },{
            "id":new Sint(2046),
            "attID":new Sint(20462),
            "aiID":new Sint(20462),
            "dropID":new Sint(155)
         }]));
         this._waveVec.push(new EnemyWaveVO(32011,4,[{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         },{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20243),
            "dropID":new Sint(164)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20243),
            "dropID":new Sint(164)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20243),
            "dropID":new Sint(164)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20243),
            "dropID":new Sint(164)
         }]));
         this._waveVec.push(new EnemyWaveVO(32012,4,[{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20253),
            "aiID":new Sint(20253),
            "dropID":new Sint(165)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20253),
            "aiID":new Sint(20253),
            "dropID":new Sint(165)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20253),
            "aiID":new Sint(20253),
            "dropID":new Sint(165)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20253),
            "aiID":new Sint(20253),
            "dropID":new Sint(165)
         },{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         }]));
         this._waveVec.push(new EnemyWaveVO(32013,4,[{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20243),
            "dropID":new Sint(164)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20243),
            "dropID":new Sint(164)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20253),
            "aiID":new Sint(20253),
            "dropID":new Sint(165)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20253),
            "aiID":new Sint(20253),
            "dropID":new Sint(165)
         },{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         }]));
         this._waveVec.push(new EnemyWaveVO(32014,4,[{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         },{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         },{
            "id":new Sint(2024),
            "attID":new Sint(20243),
            "aiID":new Sint(20243),
            "dropID":new Sint(164)
         },{
            "id":new Sint(2025),
            "attID":new Sint(20253),
            "aiID":new Sint(20253),
            "dropID":new Sint(165)
         },{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         },{
            "id":new Sint(2052),
            "attID":new Sint(20521),
            "aiID":new Sint(20521),
            "dropID":new Sint(163)
         }]));
      }
      
      public function findWave(param1:int) : EnemyWaveVO
      {
         var _loc2_:EnemyWaveVO = null;
         for each(_loc2_ in this._waveVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

