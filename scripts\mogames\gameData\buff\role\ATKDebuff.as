package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class ATKDebuff extends BaseBuff
   {
      private var _reATK:int;
      
      public function ATKDebuff()
      {
         super(1013,true);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         this._reATK = _roleVO.totalATK.v * _buffVO.argDic.per * 0.01;
         _roleVO.skillATK.v -= this._reATK;
         _roleVO.updateATK();
         if(_role.isReady)
         {
            EffectManager.instance().addHeadWord("攻击力降低！",_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override protected function onEnd() : void
      {
         _roleVO.skillATK.v += this._reATK;
         _roleVO.updateATK();
         destroy();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         if(!_buffVO.argDic.skin)
         {
            return null;
         }
         _skin.setupSequence(AssetManager.findAnimation(_buffVO.argDic.skin),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

