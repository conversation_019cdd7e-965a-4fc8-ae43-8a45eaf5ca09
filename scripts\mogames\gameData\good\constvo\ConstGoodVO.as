package mogames.gameData.good.constvo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.IconVO;
   import utils.TxtUtil;
   
   public class ConstGoodVO extends IconVO
   {
      public var id:Sint;
      
      public var gold:Sint;
      
      public var isPile:int;
      
      public var quality:Sint;
      
      public var isUse:Boolean;
      
      public function ConstGoodVO(param1:int, param2:String, param3:String, param4:int, param5:int, param6:int, param7:String, param8:Boolean = false)
      {
         super();
         this.id = new Sint(param1);
         name = param2;
         iname = param3;
         this.isPile = param4;
         this.quality = new Sint(param5);
         this.gold = new Sint(param6);
         infor = param7;
         this.isUse = param8;
      }
      
      public function get sellInfor() : String
      {
         if(this.id.v == 10000 || this.id.v == 10007)
         {
            return "";
         }
         if(this.gold.v == 0)
         {
            return "不可出售";
         }
         return TxtUtil.setColor("售出：" + this.gold.v + "铜钱","ff9900");
      }
   }
}

