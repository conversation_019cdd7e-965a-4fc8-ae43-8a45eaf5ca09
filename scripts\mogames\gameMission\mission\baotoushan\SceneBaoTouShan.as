package mogames.gameMission.mission.bao<PERSON>ushan
{
   import citrus.objects.CitrusSprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.pansidong.TrapPurpleSpider;
   import mogames.gameMission.mission.pansidong.TrapRedSpider;
   import mogames.gameMission.mission.pansidong.TrapWhiteSpider;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameRole.enemy.BOSSHuangShiJing;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   
   public class SceneBaoTouShan extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _index:int;
      
      private var _spiders:Array = [[149,432,715],[715,1028,1327],[1028,1327,1614]];
      
      private var _trapTimer:CitrusTimer;
      
      private var _activeSpider:CitrusSprite;
      
      public function SceneBaoTouShan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER2");
         setWinPosition(new Rectangle(3108,170,480,140),new Point(3214,416));
         this._trapTimer = new CitrusTimer(true);
         this._index = -1;
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2055,1);
            TaskProxy.instance().addTask(11036);
         };
         _mission.cleanLoadUI();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2055))
         {
            showDialog("STORY0082",func);
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[282,306,150,100],[493,306,150,100]],28011,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[727,306,150,100],[1000,306,150,100]],28012,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1226,306,150,100],[1556,306,150,100]],28013,this.triggerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[1837,306,150,100],[2167,306,150,100]],28014,this.triggerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(220,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(955,450),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1475,450),1,new Rectangle(440,0,1380,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2088,450),1,new Rectangle(1040,0,1380,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(3380,450),1,new Rectangle(2880,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this.startTrigger1;
         this._pTrigger1.okFunc = this.startTrigger2;
         this._pTrigger2.okFunc = this.startTrigger3;
         this._pTrigger3.okFunc = this._pTrigger4.start;
         this._pTrigger4.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function startBattle() : void
      {
         this.addBOSS();
         startBossBattle("BGM_BATTLE1");
      }
      
      private function triggerEnd0() : void
      {
         this.triggerEnd();
         CitrusLock.instance().lock(new Rectangle(0,0,1380,600),false,false,false,true);
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
         this._trapTimer.reset(true);
      }
      
      private function startTrigger1() : void
      {
         ++this._index;
         this._pTrigger1.start();
         this._activeSpider = null;
         this._trapTimer.setInterval(3,0,this.addSpider);
      }
      
      private function startTrigger2() : void
      {
         ++this._index;
         this._pTrigger2.start();
         this._activeSpider = null;
         this._trapTimer.setInterval(3,0,this.addSpider);
      }
      
      private function startTrigger3() : void
      {
         ++this._index;
         this._pTrigger3.start();
         this._activeSpider = null;
         this._trapTimer.setInterval(3,0,this.addSpider);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHuangShiJing();
         _boss.x = 3356;
         _boss.y = 450;
         _boss.initData(30451,30451,152);
         _boss.target = _mission.curTarget;
         add(_boss);
      }
      
      private function addSpider() : void
      {
         if(this._activeSpider != null)
         {
            return;
         }
         var _loc1_:int = Math.random() * 3;
         if(_loc1_ == 0)
         {
            this.addWhiteSpider();
         }
         else if(_loc1_ == 1)
         {
            this.addRedSpider();
         }
         else if(_loc1_ == 2)
         {
            this.addPurpleSpider();
         }
      }
      
      private function addWhiteSpider() : void
      {
         var _loc1_:TrapWhiteSpider = new TrapWhiteSpider(this.cleanSpider);
         Layers.addCEChild(_loc1_);
         var _loc2_:int = Math.random() * this._spiders[this._index].length;
         var _loc3_:int = int(this._spiders[this._index][_loc2_]);
         _loc1_.x = _loc3_;
         _loc1_.y = 30;
         _loc1_.createHitHurt(2700,1);
         this._activeSpider = _loc1_;
      }
      
      private function addRedSpider() : void
      {
         var _loc1_:TrapRedSpider = new TrapRedSpider(this.cleanSpider);
         Layers.addCEChild(_loc1_);
         var _loc2_:int = Math.random() * this._spiders[this._index].length;
         var _loc3_:int = int(this._spiders[this._index][_loc2_]);
         _loc1_.x = _loc3_;
         _loc1_.y = 30;
         _loc1_.createHitHurt(2800);
         this._activeSpider = _loc1_;
      }
      
      private function addPurpleSpider() : void
      {
         var _loc1_:TrapPurpleSpider = null;
         _loc1_ = new TrapPurpleSpider(this.cleanSpider);
         Layers.addCEChild(_loc1_);
         var _loc2_:int = Math.random() * this._spiders[this._index].length;
         var _loc3_:int = int(this._spiders[this._index][_loc2_]);
         _loc1_.x = _loc3_;
         _loc1_.y = 30;
         this._activeSpider = _loc1_;
      }
      
      private function cleanSpider() : void
      {
         this._activeSpider = null;
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11036);
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._trapTimer.destroy();
         this._trapTimer = null;
         super.destroy();
      }
   }
}

