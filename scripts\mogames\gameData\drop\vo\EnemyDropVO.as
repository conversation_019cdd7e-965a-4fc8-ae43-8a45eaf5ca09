package mogames.gameData.drop.vo
{
   import mogames.gameData.base.Sint;
   
   public class EnemyDropVO
   {
      public var dropID:Sint;
      
      public var exp:Sint;
      
      public var gold:BaseDropVO;
      
      public var dropList:Array;
      
      public function EnemyDropVO(param1:int, param2:int, param3:BaseDropVO, param4:Array)
      {
         super();
         this.dropID = new Sint(param1);
         this.exp = new Sint(param2);
         this.gold = param3;
         this.dropList = param4;
      }
      
      public function get dropGold() : Boolean
      {
         return Boolean(this.gold) && this.gold.isDrop;
      }
   }
}

