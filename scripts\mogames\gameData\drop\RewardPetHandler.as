package mogames.gameData.drop
{
   import mogames.gameData.pet.NewPetHandler;
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameRole.PetFactory;
   
   public class RewardPetHandler
   {
      private var _handler:NewPetHandler;
      
      public function RewardPetHandler()
      {
         super();
         this._handler = new NewPetHandler();
      }
      
      public function newPet(param1:int) : PetGameVO
      {
         var _loc2_:PetGameVO = null;
         switch(param1)
         {
            case 10090:
               _loc2_ = PetFactory.instance().newPetVO(1106);
               this._handler.countPetVO(_loc2_);
               _loc2_.isMutate.v = 1;
               _loc2_.curGrow.v = 150;
               _loc2_.curQuality.v = 3;
               this._handler.initActiveSkills(_loc2_,true);
               this._handler.initPassiveSkills(_loc2_,true);
               break;
            case 10091:
               _loc2_ = PetFactory.instance().newPetVO(1106);
               this._handler.countPetVO(_loc2_);
               _loc2_.isMutate.v = 0;
               _loc2_.curGrow.v = 130;
               _loc2_.curQuality.v = 3;
               _loc2_.curTalent.v = 10;
               _loc2_.curWit.v = 10;
               _loc2_.curLearn.v = 10;
               this._handler.initActiveSkills(_loc2_,true);
               this._handler.initPassiveSkills(_loc2_,true);
               break;
            case 10092:
               _loc2_ = PetFactory.instance().newPetVO(1107);
               this._handler.countPetVO(_loc2_);
               _loc2_.isMutate.v = 1;
               _loc2_.curGrow.v = 150;
               _loc2_.curQuality.v = 3;
               this._handler.initActiveSkills(_loc2_,true);
               this._handler.initPassiveSkills(_loc2_,true);
               break;
            case 10093:
               _loc2_ = PetFactory.instance().newPetVO(1107);
               this._handler.countPetVO(_loc2_);
               _loc2_.isMutate.v = 0;
               _loc2_.curGrow.v = 130;
               _loc2_.curQuality.v = 3;
               _loc2_.curTalent.v = 10;
               _loc2_.curWit.v = 10;
               _loc2_.curLearn.v = 10;
               this._handler.initActiveSkills(_loc2_,true);
               this._handler.initPassiveSkills(_loc2_,true);
               break;
            case 10094:
               _loc2_ = PetFactory.instance().newPetVO(1108);
               this._handler.countPetVO(_loc2_);
               _loc2_.isMutate.v = 1;
               _loc2_.curGrow.v = 150;
               _loc2_.curQuality.v = 3;
               this._handler.initActiveSkills(_loc2_,true);
               this._handler.initPassiveSkills(_loc2_,true);
               break;
            case 10095:
               _loc2_ = PetFactory.instance().newPetVO(1108);
               this._handler.countPetVO(_loc2_);
               _loc2_.isMutate.v = 0;
               _loc2_.curGrow.v = 130;
               _loc2_.curQuality.v = 3;
               _loc2_.curTalent.v = 10;
               _loc2_.curWit.v = 10;
               _loc2_.curLearn.v = 10;
               this._handler.initActiveSkills(_loc2_,true);
               this._handler.initPassiveSkills(_loc2_,true);
         }
         return _loc2_;
      }
   }
}

