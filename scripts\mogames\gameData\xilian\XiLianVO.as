package mogames.gameData.xilian
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.forge.NeedVO;
   
   public class XiLianVO extends BaseUseVO
   {
      public var id:Sint;
      
      public var need0:NeedVO;
      
      public var need1:NeedVO;
      
      public function XiLianVO(param1:int, param2:int, param3:NeedVO, param4:NeedVO)
      {
         super(param2,[param3,param4]);
         this.id = new Sint(param1);
         this.need0 = param3;
         this.need1 = param4;
      }
   }
}

