package mogames.gameData.task.constvo
{
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   import mogames.gameData.task.TaskProxy;
   
   public class ConstTask
   {
      public var id:Sint;
      
      public var name:String;
      
      public var needList:Array;
      
      public var rewards:Array;
      
      public var target:String;
      
      private var _infor:String;
      
      public function ConstTask(param1:int, param2:String, param3:Array, param4:Array, param5:String, param6:String)
      {
         super();
         this.id = new Sint(param1);
         this.name = param2;
         this.needList = param3;
         this.rewards = param4;
         this.target = param5;
         this._infor = param6;
      }
      
      public function get infor() : String
      {
         if(this.isDaily)
         {
            return "1、完成不同颜色任务，可以获得不同奖励；<br>2、每天0点任务刷新，并有1次免费刷新次数；<br>3、VIP4可每天免费重置1次任务；<br>";
         }
         return this._infor;
      }
      
      public function get isDaily() : Boolean
      {
         return TaskProxy.instance().isDailyTask(this.id.v);
      }
      
      public function get color() : String
      {
         if(this.isDaily)
         {
            return ConstData.GOOD_COLOR1[int(String(this.id.v).charAt(2))];
         }
         return "FFCC00";
      }
   }
}

