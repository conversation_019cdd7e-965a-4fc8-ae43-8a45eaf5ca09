package file
{
   public class AssetConfig
   {
      public var swfURL:Array;
      
      public function AssetConfig()
      {
         super();
         this.swfURL = [];
         this.swfURL.push("common/enemyClip.swf");
         this.swfURL.push("common/effectQS.swf");
         this.swfURL.push("common/font-120.swf");
         this.swfURL.push("common/iconEquip0-930.swf");
         this.swfURL.push("common/iconGood0-1020.swf");
         this.swfURL.push("common/iconGoodPet-900.swf");
         this.swfURL.push("common/iconRole-1020.swf");
         this.swfURL.push("common/iconSkill-720.swf");
         this.swfURL.push("common/petEffect0-900.swf");
         this.swfURL.push("common/petIcon-900.swf");
         this.swfURL.push("common/roleSWK.swf");
         this.swfURL.push("common/roleXBL-110.swf");
         this.swfURL.push("common/roleZBJ-140.swf");
         this.swfURL.push("common/roleShaSeng-260.swf");
         this.swfURL.push("common/roleXLN-720.swf");
         this.swfURL.push("common/small-350.swf");
         this.swfURL.push("common/smallEffect0.swf");
         this.swfURL.push("common/smallEffect1-280.swf");
         this.swfURL.push("common/smallEffect2-540.swf");
         this.swfURL.push("common/smallEffect3-750.swf");
         this.swfURL.push("common/smallEffect4-1020.swf");
         this.swfURL.push("common/tipChenghao-770.swf");
         this.swfURL.push("common/winEffect.swf");
         this.swfURL.push("common/uiActivityPay-900.swf");
         this.swfURL.push("common/uiBattle-360.swf");
         this.swfURL.push("common/uiDialog.swf");
         this.swfURL.push("ui/uiFirstPay-280.swf");
         this.swfURL.push("common/uiGoodSelect-720.swf");
         this.swfURL.push("common/uiLose-280.swf");
         this.swfURL.push("common/uiMapLG-990.swf");
         this.swfURL.push("common/uiMapOne-870.swf");
         this.swfURL.push("common/uiMapTwo-320.swf");
         this.swfURL.push("common/uiMapThree-500.swf");
         this.swfURL.push("common/uiMapFour-770.swf");
         this.swfURL.push("common/uiMapFive-1020.swf");
         this.swfURL.push("common/uiMapWorld-770.swf");
         this.swfURL.push("common/uiPrompt-700.swf");
         this.swfURL.push("common/uiQuick-310.swf");
         this.swfURL.push("common/uiTitle-1120.swf");
         this.swfURL.push("common/uiTip-720.swf");
         this.swfURL.push("common/uiWin.swf");
         this.swfURL.push("common/unionEffect.swf");
      }
   }
}

