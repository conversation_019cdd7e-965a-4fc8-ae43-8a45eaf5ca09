package mogames.gameData.card
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class KPFJVO
   {
      public var id:Sint;
      
      public var gold:Sint;
      
      public var rewards:Array;
      
      public function KPFJVO(param1:int, param2:int, param3:Array)
      {
         super();
         this.id = new Sint(param1);
         this.gold = new Sint(param2);
         this.rewards = param3;
      }
      
      public function useGold(param1:int = 1) : void
      {
         MasterProxy.instance().changeGold(-this.gold.v * param1);
      }
      
      public function checkLack(param1:int = 1) : Boolean
      {
         var _loc2_:LackVO = MasterProxy.instance().checkLack(this.gold.v * param1,0);
         if(_loc2_)
         {
            MiniMsgMediator.instance().showAutoMsg(_loc2_.str);
            return true;
         }
         return false;
      }
   }
}

