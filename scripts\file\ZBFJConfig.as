package file
{
   import mogames.gameData.FJCJ.ZBFJVO;
   
   public class ZBFJConfig
   {
      private static var _instance:ZBFJConfig;
      
      private var _fjVec:Vector.<ZBFJVO>;
      
      public function ZBFJConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : ZBFJConfig
      {
         if(!_instance)
         {
            _instance = new ZBFJConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._fjVec = new Vector.<ZBFJVO>();
         this._fjVec.push(new ZBFJVO(20001,10021,400));
         this._fjVec.push(new ZBFJVO(20002,10021,400));
         this._fjVec.push(new ZBFJVO(20003,10021,400));
         this._fjVec.push(new ZBFJVO(20004,10021,400));
         this._fjVec.push(new ZBFJVO(20005,10021,400));
         this._fjVec.push(new ZBFJVO(21001,10021,400));
         this._fjVec.push(new ZBFJVO(21002,10021,400));
         this._fjVec.push(new ZBFJVO(21003,10021,400));
         this._fjVec.push(new ZBFJVO(21004,10021,400));
         this._fjVec.push(new ZBFJVO(21005,10021,400));
         this._fjVec.push(new ZBFJVO(22001,10021,400));
         this._fjVec.push(new ZBFJVO(22002,10021,400));
         this._fjVec.push(new ZBFJVO(22003,10021,400));
         this._fjVec.push(new ZBFJVO(22004,10021,400));
         this._fjVec.push(new ZBFJVO(22005,10021,400));
         this._fjVec.push(new ZBFJVO(20006,10021,800));
         this._fjVec.push(new ZBFJVO(20007,10021,800));
         this._fjVec.push(new ZBFJVO(20008,10021,800));
         this._fjVec.push(new ZBFJVO(20009,10021,800));
         this._fjVec.push(new ZBFJVO(20010,10021,800));
         this._fjVec.push(new ZBFJVO(21006,10021,800));
         this._fjVec.push(new ZBFJVO(21007,10021,800));
         this._fjVec.push(new ZBFJVO(21008,10021,800));
         this._fjVec.push(new ZBFJVO(21009,10021,800));
         this._fjVec.push(new ZBFJVO(21010,10021,800));
         this._fjVec.push(new ZBFJVO(22006,10021,800));
         this._fjVec.push(new ZBFJVO(22007,10021,800));
         this._fjVec.push(new ZBFJVO(22008,10021,800));
         this._fjVec.push(new ZBFJVO(22009,10021,800));
         this._fjVec.push(new ZBFJVO(22010,10021,800));
         this._fjVec.push(new ZBFJVO(20011,10022,1200));
         this._fjVec.push(new ZBFJVO(20012,10022,1200));
         this._fjVec.push(new ZBFJVO(20013,10022,1200));
         this._fjVec.push(new ZBFJVO(20014,10022,1200));
         this._fjVec.push(new ZBFJVO(20015,10022,1200));
         this._fjVec.push(new ZBFJVO(21011,10022,1200));
         this._fjVec.push(new ZBFJVO(21012,10022,1200));
         this._fjVec.push(new ZBFJVO(21013,10022,1200));
         this._fjVec.push(new ZBFJVO(21014,10022,1200));
         this._fjVec.push(new ZBFJVO(21015,10022,1200));
         this._fjVec.push(new ZBFJVO(22011,10022,1200));
         this._fjVec.push(new ZBFJVO(22012,10022,1200));
         this._fjVec.push(new ZBFJVO(22013,10022,1200));
         this._fjVec.push(new ZBFJVO(22014,10022,1200));
         this._fjVec.push(new ZBFJVO(22015,10022,1200));
         this._fjVec.push(new ZBFJVO(20016,10023,1600));
         this._fjVec.push(new ZBFJVO(20017,10023,1600));
         this._fjVec.push(new ZBFJVO(20018,10023,1600));
         this._fjVec.push(new ZBFJVO(20019,10023,1600));
         this._fjVec.push(new ZBFJVO(20020,10023,1600));
         this._fjVec.push(new ZBFJVO(21016,10023,1600));
         this._fjVec.push(new ZBFJVO(21017,10023,1600));
         this._fjVec.push(new ZBFJVO(21018,10023,1600));
         this._fjVec.push(new ZBFJVO(21019,10023,1600));
         this._fjVec.push(new ZBFJVO(21020,10023,1600));
         this._fjVec.push(new ZBFJVO(22016,10023,1600));
         this._fjVec.push(new ZBFJVO(22017,10023,1600));
         this._fjVec.push(new ZBFJVO(22018,10023,1600));
         this._fjVec.push(new ZBFJVO(22019,10023,1600));
         this._fjVec.push(new ZBFJVO(22020,10023,1600));
         this._fjVec.push(new ZBFJVO(20021,10024,2000));
         this._fjVec.push(new ZBFJVO(20022,10024,2000));
         this._fjVec.push(new ZBFJVO(20023,10024,2000));
         this._fjVec.push(new ZBFJVO(20024,10024,2000));
         this._fjVec.push(new ZBFJVO(20025,10024,2000));
         this._fjVec.push(new ZBFJVO(21021,10024,2000));
         this._fjVec.push(new ZBFJVO(21022,10024,2000));
         this._fjVec.push(new ZBFJVO(21023,10024,2000));
         this._fjVec.push(new ZBFJVO(21024,10024,2000));
         this._fjVec.push(new ZBFJVO(21025,10024,2000));
         this._fjVec.push(new ZBFJVO(22021,10024,2000));
         this._fjVec.push(new ZBFJVO(22022,10024,2000));
         this._fjVec.push(new ZBFJVO(22023,10024,2000));
         this._fjVec.push(new ZBFJVO(22024,10024,2000));
         this._fjVec.push(new ZBFJVO(22025,10024,2000));
      }
      
      public function findVO(param1:int) : ZBFJVO
      {
         var _loc2_:ZBFJVO = null;
         for each(_loc2_ in this._fjVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

