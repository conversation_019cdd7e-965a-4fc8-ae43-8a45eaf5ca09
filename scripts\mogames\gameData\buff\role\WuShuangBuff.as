package mogames.gameData.buff.role
{
   import flash.display.DisplayObject;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.co.BodyShadow;
   import mogames.gameRole.co.BaseRole;
   
   public class Wu<PERSON>huangBuff extends BaseBuff
   {
      private var _addATK:Sint;
      
      private var _addMOVE:Snum;
      
      private var _addRUN:Snum;
      
      private var _shadow:BodyShadow;
      
      public function WuShuangBuff()
      {
         super(2000);
         this._addATK = new Sint();
         this._addMOVE = new Snum();
         this._addRUN = new Snum();
         this._shadow = new BodyShadow();
         this._shadow.isAnimation = true;
         this._shadow.isColor = true;
      }
      
      override public function setTarget(param1:BaseRole) : void
      {
         super.setTarget(param1);
         this._shadow.setTarget(param1.art as DisplayObject);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         this._addATK.v = _roleVO.totalATK.v * _buffVO.argDic.perATK.v;
         this._addMOVE.v = _roleVO.totalMOVE.v * _buffVO.argDic.perSPD.v;
         this._addRUN.v = _roleVO.totalRUN.v * _buffVO.argDic.perSPD.v;
         _roleVO.skillATK.v += this._addATK.v;
         _roleVO.skillMOVE.v += this._addMOVE.v;
         _roleVO.skillRUN.v += this._addRUN.v;
         _roleVO.updateATK();
         _roleVO.updateSPEED();
         this._shadow.start();
      }
      
      override public function set target(param1:BaseRole) : void
      {
         _role = param1;
         if(param1 == null)
         {
            return;
         }
         this._shadow.setTarget(_role.art as DisplayObject);
         this._shadow.start();
      }
      
      override protected function cleanEffect() : void
      {
         _roleVO.skillATK.v -= this._addATK.v;
         _roleVO.skillMOVE.v -= this._addMOVE.v;
         _roleVO.skillRUN.v -= this._addRUN.v;
         _roleVO.updateATK();
         _roleVO.updateSPEED();
         this._shadow.stop();
      }
      
      override public function destroy() : void
      {
         super.destroy();
         this._shadow.destroy();
         this._shadow = null;
      }
   }
}

