package citrus.view
{
   import aze.motion.EazeTween;
   import citrus.core.CitrusEngine;
   import citrus.math.MathUtils;
   import citrus.math.MathVector;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class ACitrusCamera
   {
      public static const BOUNDS_MODE_AABB:String = "BOUNDS_MODE_AABB";
      
      public static const BOUNDS_MODE_OFFSET:String = "BOUNDS_MODE_OFFSET";
      
      public static const BOUNDS_MODE_ADVANCED:String = "BOUNDS_MODE_ADVANCED";
      
      public static const PARALLAX_MODE_TOPLEFT:String = "PARALLAX_MODE_TOPLEFT";
      
      public static const PARALLAX_MODE_DEPTH:String = "PARALLAX_MODE_DEPTH";
      
      protected var _allowZoom:Boolean = false;
      
      protected var _allowRotation:Boolean = false;
      
      protected var _rotation:Number = 0;
      
      protected var _zoom:Number = 1;
      
      public var baseZoom:Number = 1;
      
      protected var _aabbData:Object = {
         "offsetX":0,
         "offsetY":0,
         "rect":new Rectangle()
      };
      
      protected var _ghostTarget:Point = new Point();
      
      protected var _targetPos:Point = new Point();
      
      protected var _camProxy:Object = {
         "x":0,
         "y":0,
         "offset":new Point(),
         "scale":1,
         "rotation":0
      };
      
      protected var _camPos:Point = new Point();
      
      public var zoomEasing:Number = 0.05;
      
      public var rotationEasing:Number = 0.05;
      
      protected var _viewRoot:*;
      
      protected var _target:Object;
      
      protected var _manualPosition:Point;
      
      protected var _callOnUpdateQueue:Vector.<Object> = new Vector.<Object>();
      
      public var enabled:Boolean = false;
      
      public var center:Point = new Point(0.5,0.5);
      
      protected var offset:Point = new Point();
      
      public var easing:Point = new Point(0.25,0.05);
      
      public var bounds:Rectangle;
      
      public var deadZone:Rectangle = new Rectangle();
      
      public var cameraLensWidth:Number;
      
      public var followTarget:Boolean = true;
      
      public var cameraLensHeight:Number;
      
      protected var _m:Matrix = new Matrix();
      
      protected var _p:Point = new Point();
      
      protected var _r:Rectangle = new Rectangle();
      
      protected var _rect:Rectangle = new Rectangle();
      
      protected var _b:Object = {
         "w2":0,
         "h2":0,
         "diag2":0,
         "rotoffset":new Point(),
         "br":0,
         "bl":0,
         "bt":0,
         "bb":0
      };
      
      public var boundsMode:String = "BOUNDS_MODE_AABB";
      
      public var parallaxMode:String = "PARALLAX_MODE_TOPLEFT";
      
      protected var _ce:CitrusEngine;
      
      public function ACitrusCamera(param1:*)
      {
         super();
         this._viewRoot = param1;
         this.initialize();
      }
      
      protected function initialize() : void
      {
         this._ce = CitrusEngine.getInstance();
         this.cameraLensWidth = this._ce.screenWidth;
         this.cameraLensHeight = this._ce.screenHeight;
         this._ce.onStageResize.add(this.onResize);
      }
      
      protected function onResize(param1:Number, param2:Number) : void
      {
         this.cameraLensWidth = this._ce.screenWidth;
         this.cameraLensHeight = this._ce.screenHeight;
      }
      
      public function setUp(param1:Object, param2:Rectangle = null, param3:Point = null, param4:Point = null) : ACitrusCamera
      {
         if(param1)
         {
            this.target = param1;
            this._ghostTarget.x = param1.x;
            this._ghostTarget.y = param1.y;
         }
         if(param3)
         {
            if(param3.x > 1)
            {
               param3.x = 1;
            }
            if(param3.x < 0)
            {
               param3.x = 0;
            }
            if(param3.y > 1)
            {
               param3.y = 1;
            }
            if(param3.y < 0)
            {
               param3.y = 0;
            }
            this.center = param3;
         }
         if(param2)
         {
            this.bounds = param2;
         }
         if(param4)
         {
            this.easing = param4;
         }
         this.enabled = true;
         return this;
      }
      
      public function reset() : void
      {
         var _loc1_:Point = this.easing.clone();
         var _loc2_:Number = this.rotationEasing;
         var _loc3_:Number = this.zoomEasing;
         this.rotationEasing = 1;
         this.zoomEasing = 1;
         this.easing.setTo(1,1);
         this.update();
         this.easing.copyFrom(_loc1_);
         this.rotationEasing = _loc2_;
         this.zoomEasing = _loc3_;
      }
      
      public function switchToTarget(param1:Object, param2:Number = 10, param3:Function = null) : void
      {
         var _loc4_:Point = new Point(this.camPos.x,this.camPos.y);
         var _loc5_:MathVector = new MathVector(0,0);
         var _loc6_:Point = this.easing.clone();
         this.easing.setTo(1,1);
         var _loc7_:Rectangle = this.deadZone.clone();
         this.deadZone.setTo(0,0,0,0);
         this.target = _loc4_;
         this._callOnUpdateQueue.push({
            "func":this.switchToTargetUpdate,
            "args":{
               "newTarget":param1,
               "speed":param2,
               "onComplete":param3,
               "moveTarget":_loc4_,
               "vec":_loc5_,
               "oldEasing":_loc6_,
               "oldDeadZone":_loc7_
            }
         });
      }
      
      protected function switchToTargetUpdate(param1:Object) : Boolean
      {
         param1.vec.setTo(param1.newTarget.x - param1.moveTarget.x,param1.newTarget.y - param1.moveTarget.y);
         if(param1.vec.length > param1.speed)
         {
            param1.vec.length = param1.speed;
         }
         param1.moveTarget.x += param1.vec.x;
         param1.moveTarget.y += param1.vec.y;
         if(MathUtils.DistanceBetweenTwoPoints(param1.newTarget.x,param1.moveTarget.x,param1.newTarget.y,param1.moveTarget.y) <= 0.1)
         {
            this.target = param1.newTarget;
            this.easing = param1.oldEasing;
            this.deadZone = param1.oldDeadZone;
            if(param1.onComplete != null)
            {
               param1.onComplete();
            }
            return true;
         }
         return false;
      }
      
      public function tweenSwitchToTarget(param1:Object, param2:Number = 2, param3:Function = null, param4:Function = null) : EazeTween
      {
         var eaze:EazeTween;
         var oldEasing:Point = null;
         var oldDeadZone:Rectangle = null;
         var newTarget:Object = param1;
         var duration:Number = param2;
         var easingFunction:Function = param3;
         var onComplete:Function = param4;
         var moveTarget:Point = new Point(this.camPos.x,this.camPos.y);
         oldEasing = this.easing.clone();
         this.easing.setTo(1,1);
         oldDeadZone = this.deadZone.clone();
         this.deadZone.setTo(0,0,0,0);
         this.target = moveTarget;
         eaze = new EazeTween(moveTarget,false).to(duration,{
            "x":newTarget.x,
            "y":newTarget.y
         }).onComplete(function():void
         {
            target = newTarget;
            easing = oldEasing;
            deadZone = oldDeadZone;
            if(onComplete != null)
            {
               onComplete();
            }
         });
         if(easingFunction != null)
         {
            eaze.easing(easingFunction);
         }
         eaze.start();
         return eaze;
      }
      
      public function zoom(param1:Number) : void
      {
         throw new Error("Warning: [object ACitrusCamera] cannot zoom.");
      }
      
      public function zoomFit(param1:Number, param2:Number, param3:Boolean = false) : Number
      {
         throw new Error("Warning: [object ACitrusCamera] cannot zoomFit.");
      }
      
      public function rotate(param1:Number) : void
      {
         throw new Error("Warning: [object ACitrusCamera] cannot rotate.");
      }
      
      public function setRotation(param1:Number) : void
      {
         throw new Error("Warning: [object ACitrusCamera] cannot rotate.");
      }
      
      public function setZoom(param1:Number) : void
      {
         throw new Error("Warning: [object ACitrusCamera] cannot zoom.");
      }
      
      public function getZoom() : Number
      {
         throw new Error("Warning: [object ACitrusCamera] cannot zoom.");
      }
      
      public function getRotation() : Number
      {
         throw new Error("Warning: [object ACitrusCamera] cannot rotate.");
      }
      
      public function update() : void
      {
         var _loc1_:String = null;
         if(this._callOnUpdateQueue.length > 0)
         {
            for(_loc1_ in this._callOnUpdateQueue)
            {
               if(this._callOnUpdateQueue[_loc1_].func(this._callOnUpdateQueue[_loc1_].args))
               {
                  this._callOnUpdateQueue.splice(_loc1_ as int,1);
               }
            }
         }
      }
      
      public function destroy() : void
      {
         this._callOnUpdateQueue.length = 0;
         this._ce.onStageResize.remove(this.onResize);
      }
      
      public function set target(param1:Object) : void
      {
         this._manualPosition = null;
         this._target = param1;
      }
      
      public function get target() : Object
      {
         return this._target;
      }
      
      public function get camPos() : Point
      {
         return this._camPos;
      }
      
      public function set manualPosition(param1:Point) : void
      {
         this._target = null;
         this._manualPosition = param1;
      }
      
      public function get manualPosition() : Point
      {
         return this._manualPosition;
      }
      
      public function get callOnUpdateQueue() : Vector.<Object>
      {
         return this._callOnUpdateQueue;
      }
      
      public function set allowRotation(param1:Boolean) : void
      {
         throw new Error("Warning: [object ACitrusCamera] cannot rotate.");
      }
      
      public function set allowZoom(param1:Boolean) : void
      {
         throw new Error("Warning: [object ACitrusCamera] cannot zoom.");
      }
      
      public function get allowZoom() : Boolean
      {
         throw new Error("Warning: [object ACitrusCamera] cannot zoom.");
      }
      
      public function get allowRotation() : Boolean
      {
         throw new Error("Warning: [object ACitrusCamera] cannot rotate.");
      }
      
      public function get camProxy() : Object
      {
         return this._camProxy;
      }
      
      public function get ghostTarget() : Point
      {
         return this._ghostTarget;
      }
      
      protected function get mzoom() : Number
      {
         return this._zoom * this.baseZoom;
      }
      
      protected function set mzoom(param1:Number) : void
      {
         this._zoom = param1 / this.baseZoom;
      }
      
      public function get transformMatrix() : Matrix
      {
         return this._m;
      }
      
      public function contains(param1:Number, param2:Number, param3:Rectangle = null) : Boolean
      {
         this._p.setTo(param1,param2);
         if(!param3)
         {
            this._rect.setTo(0,0,this.cameraLensWidth,this.cameraLensHeight);
         }
         else
         {
            this._rect.copyFrom(param3);
         }
         this._p.copyFrom(this._m.transformPoint(this._p));
         return this._rect.contains(this._p.x,this._p.y);
      }
      
      public function containsRect(param1:Rectangle, param2:Rectangle = null) : Boolean
      {
         this._p.setTo(param1.x + param1.width * 0.5,param1.y + param1.height * 0.5);
         if(!param2)
         {
            this._rect.setTo(0,0,this.cameraLensWidth,this.cameraLensHeight);
         }
         else
         {
            this._rect.copyFrom(param2);
         }
         this._r.setTo(this._p.x - param1.width * 0.5,this._p.y - param1.height * 0.5,param1.width,param1.height);
         return this._rect.containsRect(this._r);
      }
      
      public function intersectsRect(param1:Rectangle, param2:Rectangle = null) : Boolean
      {
         this._p.setTo(param1.x + param1.width * 0.5,param1.y + param1.height * 0.5);
         if(!param2)
         {
            this._rect.setTo(0,0,this.cameraLensWidth,this.cameraLensHeight);
         }
         else
         {
            this._rect.copyFrom(param2);
         }
         this._r.setTo(this._p.x - param1.width * 0.5,this._p.y - param1.height * 0.5,param1.width,param1.height);
         return this._rect.intersects(this._r);
      }
      
      public function getRect() : Rectangle
      {
         return this._aabbData.rect;
      }
   }
}

