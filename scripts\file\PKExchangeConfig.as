package file
{
   import mogames.gameData.pk.vo.PKExchangeVO;
   
   public class PKExchangeConfig
   {
      private static var _instance:PKExchangeConfig;
      
      public function PKExchangeConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : PKExchangeConfig
      {
         if(!_instance)
         {
            _instance = new PKExchangeConfig();
         }
         return _instance;
      }
      
      public function newList() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new PKExchangeVO(116,16756,6));
         _loc1_.push(new PKExchangeVO(113,16755,5));
         _loc1_.push(new PKExchangeVO(114,16711,4));
         _loc1_.push(new PKExchangeVO(100,16751,3));
         _loc1_.push(new PKExchangeVO(101,14150,3));
         _loc1_.push(new PKExchangeVO(115,14121,2));
         _loc1_.push(new PKExchangeVO(102,14102,2));
         _loc1_.push(new PKExchangeVO(103,16757,2));
         _loc1_.push(new PKExchangeVO(104,14002,2));
         _loc1_.push(new PKExchangeVO(105,14020,2));
         _loc1_.push(new PKExchangeVO(106,16761,2));
         _loc1_.push(new PKExchangeVO(107,14009,2));
         _loc1_.push(new PKExchangeVO(108,14001,1));
         _loc1_.push(new PKExchangeVO(109,16753,1));
         _loc1_.push(new PKExchangeVO(110,14120,1));
         _loc1_.push(new PKExchangeVO(111,16754,1));
         _loc1_.push(new PKExchangeVO(112,14010,1));
         return _loc1_;
      }
   }
}

