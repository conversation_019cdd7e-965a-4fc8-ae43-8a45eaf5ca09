package mogames.gameData.petSkill.passive.constvo
{
   import mogames.gameData.heroSkill.constvo.ConstSkillVO;
   import mogames.gameData.petSkill.PetSkillLevelVO;
   
   public class PetConstPSkill3 extends ConstSkillVO
   {
      public function PetConstPSkill3(param1:int, param2:String, param3:String, param4:String)
      {
         super(param1,param2,param3,"",param4);
      }
      
      override protected function initCondition() : void
      {
         super.initCondition();
         _lvVec.push(new PetSkillLevelVO(0,1,1000));
         _lvVec.push(new PetSkillLevelVO(1,5,10000));
         _lvVec.push(new PetSkillLevelVO(2,10,15000));
         _lvVec.push(new PetSkillLevelVO(3,15,20000));
         _lvVec.push(new PetSkillLevelVO(4,20,25000));
         _lvVec.push(new PetSkillLevelVO(5,25,30000));
         _lvVec.push(new PetSkillLevelVO(6,30,35000));
         _lvVec.push(new PetSkillLevelVO(7,35,40000));
         _lvVec.push(new PetSkillLevelVO(8,40,45000));
         _lvVec.push(new PetSkillLevelVO(9,45,50000));
      }
   }
}

