package mogames.gameData.drop.vo
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class BaseRewardVO
   {
      public var id:Sint;
      
      public var num:Sint;
      
      public function BaseRewardVO(param1:int, param2:int = 1)
      {
         super();
         this.id = new Sint(param1);
         this.num = new Sint(param2);
      }
      
      public function newGood() : GameGoodVO
      {
         var _loc1_:GameGoodVO = GoodConfig.instance().newGood(this.id.v);
         _loc1_.amount.v = this.num.v;
         return _loc1_;
      }
      
      public function get constGood() : ConstGoodVO
      {
         return GoodConfig.instance().findConstGood(this.id.v);
      }
   }
}

