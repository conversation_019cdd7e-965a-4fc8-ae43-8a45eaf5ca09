package mogames.gameData.pk.enemy
{
   import mogames.gameData.heroSkill.HurtSkillVO;
   
   public class PKEnemyDrakanVO extends PKEnemyVO
   {
      public function PKEnemyDrakanVO(param1:int)
      {
         allSkills = [];
         allSkills.push(new HurtSkillVO(1021,this,0));
         allSkills.push(new HurtSkillVO(1022,this,1));
         allSkills.push(new HurtSkillVO(1023,this,2));
         allSkills.push(new HurtSkillVO(1024,this,3));
         allSkills.push(new HurtSkillVO(1025,this,4));
         super(1005,param1);
      }
   }
}

