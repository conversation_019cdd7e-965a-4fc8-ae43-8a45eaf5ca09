package citrus.utils.objectmakers.tmx
{
   import flash.utils.ByteArray;
   import flash.utils.Endian;
   
   public class TmxLayer
   {
      private static const BASE64_CHARS:String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
      
      public var map:TmxMap;
      
      public var name:String;
      
      public var x:int;
      
      public var y:int;
      
      public var width:int;
      
      public var height:int;
      
      public var opacity:Number;
      
      public var visible:Boolean;
      
      public var tileGIDs:Array;
      
      public var properties:TmxPropertySet = null;
      
      public function TmxLayer(param1:XML, param2:TmxMap)
      {
         var _loc3_:XML = null;
         var _loc4_:XML = null;
         var _loc5_:String = null;
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:Boolean = false;
         var _loc10_:int = 0;
         super();
         this.map = param2;
         this.name = param1.@name;
         this.x = param1.@x;
         this.y = param1.@y;
         this.width = param1.@width;
         this.height = param1.@height;
         this.visible = !param1.@visible || param1.@visible != 0;
         this.opacity = param1.@opacity;
         for each(_loc3_ in param1.properties)
         {
            this.properties = !!this.properties ? this.properties.extend(_loc3_) : new TmxPropertySet(_loc3_);
         }
         this.tileGIDs = [];
         _loc4_ = param1.data[0];
         if(_loc4_)
         {
            _loc5_ = "";
            if(<EMAIL>() == 0)
            {
               _loc6_ = this.width;
               _loc7_ = -1;
               for each(_loc3_ in _loc4_.tile)
               {
                  if(++_loc6_ >= this.width)
                  {
                     var _loc13_:*;
                     this.tileGIDs[_loc13_ = ++_loc7_] = [];
                     _loc6_ = 0;
                  }
                  _loc8_ = int(_loc3_.@gid);
                  this.tileGIDs[_loc7_].push(_loc8_);
               }
            }
            else if(_loc4_.@encoding == "csv")
            {
               _loc5_ = _loc4_;
               this.tileGIDs = csvToArray(_loc5_,this.width);
            }
            else if(_loc4_.@encoding == "base64")
            {
               _loc5_ = _loc4_;
               _loc9_ = false;
               if(_loc4_.@compression == "zlib")
               {
                  _loc9_ = true;
               }
               else if(<EMAIL>() != 0)
               {
                  throw Error("TmxLayer - data compression type not supported!");
               }
               _loc10_ = 0;
               while(_loc10_ < 100)
               {
                  this.tileGIDs = base64ToArray(_loc5_,this.width,_loc9_);
                  _loc10_++;
               }
            }
         }
      }
      
      public static function csvToArray(param1:String, param2:int) : Array
      {
         var _loc5_:* = null;
         var _loc6_:Array = null;
         var _loc7_:Array = null;
         var _loc8_:* = null;
         var _loc3_:Array = [];
         var _loc4_:Array = param1.split("\n");
         for each(_loc5_ in _loc4_)
         {
            _loc6_ = [];
            _loc7_ = _loc5_.split(",",param2);
            for each(_loc8_ in _loc7_)
            {
               _loc6_.push(uint(_loc8_));
            }
            _loc3_.push(_loc6_);
         }
         return _loc3_;
      }
      
      public static function base64ToArray(param1:String, param2:int, param3:Boolean) : Array
      {
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         var _loc4_:Array = [];
         var _loc5_:ByteArray = base64ToByteArray(param1);
         if(param3)
         {
            _loc5_.uncompress();
         }
         _loc5_.endian = Endian.LITTLE_ENDIAN;
         while(_loc5_.position < _loc5_.length)
         {
            _loc6_ = [];
            _loc7_ = 0;
            while(_loc7_ < param2)
            {
               _loc6_.push(_loc5_.readInt());
               _loc7_++;
            }
            _loc4_.push(_loc6_);
         }
         return _loc4_;
      }
      
      public static function base64ToByteArray(param1:String) : ByteArray
      {
         var _loc6_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         var _loc9_:int = 0;
         var _loc2_:ByteArray = new ByteArray();
         var _loc3_:Array = [];
         var _loc4_:int = 0;
         while(_loc4_ < BASE64_CHARS.length)
         {
            _loc3_[BASE64_CHARS.charCodeAt(_loc4_)] = _loc4_;
            _loc4_++;
         }
         var _loc5_:* = 0;
         while(_loc5_ < param1.length - 3)
         {
            _loc6_ = int(_loc3_[param1.charCodeAt(_loc5_)]);
            _loc7_ = int(_loc3_[param1.charCodeAt(_loc5_ + 1)]);
            _loc8_ = int(_loc3_[param1.charCodeAt(_loc5_ + 2)]);
            _loc9_ = int(_loc3_[param1.charCodeAt(_loc5_ + 3)]);
            if(_loc7_ < 64)
            {
               _loc2_.writeByte((_loc6_ << 2) + ((_loc7_ & 48) >> 4));
            }
            if(_loc8_ < 64)
            {
               _loc2_.writeByte(((_loc7_ & 15) << 4) + ((_loc8_ & 60) >> 2));
            }
            if(_loc9_ < 64)
            {
               _loc2_.writeByte(((_loc8_ & 3) << 6) + _loc9_);
            }
            _loc5_ += 4;
         }
         _loc2_.position = 0;
         return _loc2_;
      }
      
      public function toCsv(param1:TmxTileSet = null) : String
      {
         var _loc5_:Array = null;
         var _loc6_:* = null;
         var _loc7_:int = 0;
         var _loc2_:int = 16777215;
         var _loc3_:int = 0;
         if(param1)
         {
            _loc3_ = param1.firstGID;
            _loc2_ = param1.numTiles - 1;
         }
         var _loc4_:String = "";
         for each(_loc5_ in this.tileGIDs)
         {
            _loc6_ = "";
            _loc7_ = 0;
            for each(_loc7_ in _loc5_)
            {
               _loc7_ -= _loc3_;
               if(_loc7_ < 0 || _loc7_ > _loc2_)
               {
                  _loc7_ = 0;
               }
               _loc4_ += _loc6_;
               _loc6_ = _loc7_ + ",";
            }
            _loc4_ += _loc7_ + "\n";
         }
         return _loc4_;
      }
   }
}

