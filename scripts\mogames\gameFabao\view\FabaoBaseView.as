package mogames.gameFabao.view
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import mogames.AssetManager;
   import mogames.gameData.ConstRole;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameObj.display.CitrusMC;
   import utils.FontUtil;
   import utils.MethodUtil;
   
   public class FabaoBaseView extends Sprite
   {
      protected var _roleBody:CitrusMC;
      
      protected var _mcBody:MovieClip;
      
      protected var _mcTime:MovieClip;
      
      protected var _bitmap:Bitmap;
      
      protected var _last:int;
      
      public var curStatus:String;
      
      public var owner:BaseFabao;
      
      public var isSkill:Boolean;
      
      public function FabaoBaseView()
      {
         super();
         this._roleBody = new CitrusMC();
         addChild(this._roleBody);
         this._bitmap = new Bitmap();
         addChild(this._bitmap);
         cacheAsBitmap = true;
         this._mcTime = AssetManager.newMCRes("EFFECT_CD_TIME_WORD_CLIP");
      }
      
      public function setBody(param1:MovieClip, param2:Boolean = true) : void
      {
         this._mcBody = param1;
         if(param2)
         {
            this._roleBody.setupMC(param1,this.onAnimComplete,this.updateCurrentFrame);
         }
         else
         {
            this._roleBody.setupMC(param1,this.onAnimComplete);
         }
      }
      
      public function showTime(param1:int) : void
      {
         if(this._last == param1)
         {
            return;
         }
         this._last = param1;
         this.cleanTime();
         if(param1 <= 0)
         {
            return;
         }
         FontUtil.setHtml(this._mcTime.txtWord,param1 + "");
         var _loc2_:BitmapData = new BitmapData(this._mcTime.width,this._mcTime.height,true,16777215);
         _loc2_.draw(this._mcTime);
         this._bitmap.bitmapData = _loc2_;
      }
      
      public function cleanTime() : void
      {
         if(this._bitmap.bitmapData)
         {
            this._bitmap.bitmapData.dispose();
         }
         this._bitmap.bitmapData = null;
      }
      
      public function updateTime() : void
      {
         if(this.owner.inverted)
         {
            this._bitmap.x = (this.owner.width + this._bitmap.width) * 0.5;
         }
         else
         {
            this._bitmap.x = (this.owner.width - this._bitmap.width) * 0.5;
         }
         this._bitmap.y = (this.owner.height - this._bitmap.height) * 0.5;
         this._bitmap.scaleX = this.owner.dirX;
      }
      
      protected function updateCurrentFrame(param1:int) : void
      {
         if(!this.mcATK)
         {
            this.owner.cleanTargets();
         }
      }
      
      public function changeStatus(param1:String, param2:Boolean) : void
      {
         if(this.curStatus == param1)
         {
            return;
         }
         this.curStatus = param1;
         this.changeAnimation(this.curStatus,param2);
      }
      
      public function changeAnimation(param1:String, param2:Boolean = false) : void
      {
         if(this._roleBody)
         {
            this._roleBody.changeAnimation(param1,param2);
         }
      }
      
      protected function onAnimComplete(param1:String) : void
      {
         if(param1 == ConstRole.SKILL_ONE)
         {
            this.owner.handlerSkillEnd();
         }
      }
      
      protected function addSound(param1:int) : void
      {
      }
      
      public function setOwner(param1:BaseFabao) : void
      {
         this.owner = param1;
      }
      
      public function get mcATK() : DisplayObject
      {
         return this._mcBody.mcAreaATK;
      }
      
      public function destroy() : void
      {
         if(this._roleBody)
         {
            this._roleBody.destroy();
         }
         if(this._bitmap.bitmapData)
         {
            this._bitmap.bitmapData.dispose();
         }
         MethodUtil.removeAllChildren(this);
         MethodUtil.removeMe(this);
         this._bitmap = null;
         this._roleBody = null;
         this._mcBody = null;
         this._mcTime = null;
      }
   }
}

