package mogames.gameData
{
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.chenghao.ChenghaoProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameRole.HeroManager;
   import mogames.gameUI.map.AreaManager;
   import utils.MathUtil;
   
   public class MasterProxy
   {
      private static var _instance:MasterProxy;
      
      public var serverName:String;
      
      public var serverNick:String;
      
      public var serverUID:String;
      
      public var gameVIP:Sint;
      
      public var gameMoney:Sint;
      
      public var gameGold:Sint;
      
      public var gameTicket:Sint;
      
      public var sysMoney:Sint;
      
      public var leftMoney:int;
      
      public var totalMoney:int;
      
      public var cheatVIP:int;
      
      private var golds:Array = [9999,49999,999999,1999999,2999999,3999999,4999999,9999999,99999999];
      
      private var vips:Array = [0,1,2,3,4,5];
      
      public function MasterProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.gameVIP = new Sint();
         this.gameMoney = new Sint();
         this.gameGold = new Sint();
         this.gameTicket = new Sint();
         this.serverUID = "xiyou";
         this.sysMoney = new Sint();
      }
      
      public static function instance() : MasterProxy
      {
         if(!_instance)
         {
            _instance = new MasterProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.gameGold.v = 0;
         this.gameTicket.v = 0;
         AreaManager.mapID = 1;
         HeroManager.isDouble = false;
      }
      
      public function set loadData(param1:String) : void
      {
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         this.gameGold.v = int(_loc2_[0]);
         AreaManager.mapID = int(_loc2_[1]);
         HeroManager.isDouble = Boolean(int(_loc2_[2]));
         if(_loc2_[3])
         {
            this.gameTicket.v = int(_loc2_[3]);
         }
      }
      
      public function get saveData() : String
      {
         var _loc1_:Array = [this.gameGold.v,AreaManager.mapID,int(HeroManager.isDouble),this.gameTicket.v,this.gameVIP.v];
         return _loc1_.join("T");
      }
      
      public function initPlayer(param1:Object) : void
      {
         this.serverName = param1.name;
         this.serverUID = param1.uid;
         this.serverNick = param1.nickName;
      }
      
      public function changeGold(param1:int) : void
      {
         this.gameGold.v += param1;
         if(this.gameGold.v <= 0)
         {
            this.gameGold.v = 0;
         }
         EventManager.dispatchEvent(UIEvent.REFRESH_GOLD);
         EventManager.dispatchEvent(UIEvent.MENU_TIP_EVENT,{"type":"updateSkillBtn"});
         FlagProxy.instance().setValue(10000,this.gameGold.v);
         if(this.gameGold.v >= 1000000)
         {
            ChenghaoProxy.instance().setGet(65005);
         }
      }
      
      public function changeTicket(param1:int) : void
      {
         this.gameTicket.v += param1;
         if(this.gameTicket.v <= 0)
         {
            this.gameTicket.v = 0;
         }
         EventManager.dispatchEvent(UIEvent.REFRESH_TICKET);
         FlagProxy.instance().changeValue(10007,this.gameTicket.v);
      }
      
      public function changeMoney(param1:int) : void
      {
         this.gameMoney.v += param1;
         EventManager.dispatchEvent(UIEvent.REFRESH_MONEY);
      }
      
      public function checkLack(param1:int, param2:int) : LackVO
      {
         switch(param2)
         {
            case 0:
               if(this.gameGold.v < param1)
               {
                  return new LackVO("铜钱不足！");
               }
               break;
            case 1:
               if(this.gameMoney.v < param1)
               {
                  return new LackVO("金锭不足！");
               }
               break;
            case 2:
               if(this.gameTicket.v < param1)
               {
                  return new LackVO("银锭不足！");
               }
               break;
         }
         return null;
      }
      
      public function get isVIP() : Boolean
      {
         return MasterProxy.instance().gameVIP.v > 0;
      }
      
      public function countGoldWD() : int
      {
         return MathUtil.checkWD(this.gameGold.v,this.golds);
      }
      
      public function countVIPWD() : int
      {
         return MathUtil.checkWD(this.gameVIP.v,this.vips);
      }
   }
}

