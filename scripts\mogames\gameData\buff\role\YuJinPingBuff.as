package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   import utils.MathUtil;
   import utils.TxtUtil;
   
   public class YuJinPingBuff extends BaseBuff
   {
      private var value:int;
      
      public function YuJinPingBuff()
      {
         super(1006,false,true);
      }
      
      override protected function onBuff() : void
      {
         if(_roleVO.isDead())
         {
            return;
         }
         this.value = MathUtil.randomNum(_buffVO.argDic.minPer.v,_buffVO.argDic.maxPer.v) * 0.01 * _roleVO.totalHP.v;
         _roleVO.changeHP(this.value);
         if(_role.isReady && _role.visible)
         {
            EffectManager.instance().addHeadWord(TxtUtil.setColor("生命+" + this.value,"99ff00"),_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override public function get buffSkin() : BMCSprite
      {
         _skin.setupSequence(AssetManager.findAnimation("EFFECT_BUFF_YUJINPING_CLIP"),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

