package mogames.gameData.flag.vo
{
   import mogames.gameData.base.TimeVO;
   import mogames.gamePKG.GameProxy;
   
   public class WeekRefreshFlag extends NumFlag
   {
      public function WeekRefreshFlag(param1:int, param2:int = 1)
      {
         super(param1,param2,false,true);
      }
      
      override public function dailyRefresh() : void
      {
         var _loc1_:TimeVO = GameProxy.instance().newTS;
         if(_loc1_.week.v != 5)
         {
            return;
         }
         if(isReduce)
         {
            _cur.v = total;
         }
         else
         {
            _cur.v = 0;
         }
      }
   }
}

