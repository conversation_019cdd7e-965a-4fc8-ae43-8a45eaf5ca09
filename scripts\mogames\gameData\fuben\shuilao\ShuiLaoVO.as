package mogames.gameData.fuben.shuilao
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   
   public class ShuiLaoVO
   {
      public var bossID:Sint;
      
      public var dropList:Array;
      
      public function ShuiLaoVO(param1:int, param2:Array)
      {
         super();
         this.bossID = new Sint(param1);
         this.dropList = [];
         var _loc3_:int = 0;
         var _loc4_:int = int(param2.length);
         while(_loc3_ < _loc4_)
         {
            this.dropList[_loc3_] = GoodConfig.instance().findConstGood(param2[_loc3_]);
            _loc3_++;
         }
      }
   }
}

