package mogames.gameMission.mission.huangfengdong.scene
{
   import flash.geom.Point;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.flag.vo.NumFlag;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.huangfengdong.BaseYaocao;
   import mogames.gameMission.mission.huangfengdong.HuangFengDongMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class SceneHuangFengDong02 extends BaseScene
   {
      private var _locations:Array = [[45,182],[264,222],[454,144],[614,269],[810,165],[890,346]];
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      public function SceneHuangFengDong02(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         this.createYaocao();
         _mission.cleanLoadUI();
         this.initEnemies();
         this.initDeadTip();
      }
      
      private function initDeadTip() : void
      {
         var _loc1_:NumFlag = FlagProxy.instance().findFlag(311);
         if(_loc1_.cur <= 0)
         {
            return;
         }
         MiniMsgMediator.instance().showMsg("恭喜，你已经在这边挂了" + _loc1_.cur + "次！",480,30);
      }
      
      private function initEnemies() : void
      {
         if(_mission.hasMark("enemy502"))
         {
            return;
         }
         this._eTrigger0 = new EnemyTrigger(_mission,[[152,74,152,100],[196,74,152,100]],5014,this.triggerEnd0);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(296,396),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger0.start();
      }
      
      private function triggerEnd0() : void
      {
         _mission.setMark("enemy502");
      }
      
      private function createYaocao() : void
      {
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:BaseYaocao = null;
         _loc1_ = 0;
         var _loc2_:int = int(this._locations.length);
         while(_loc1_ < _loc2_)
         {
            if(!_mission.hasMark("isPick" + _loc1_))
            {
               _loc3_ = int((_mission as HuangFengDongMission).newList[_loc1_].v);
               if(_loc3_ != 0)
               {
                  _loc4_ = new BaseYaocao(_loc3_,_loc1_,this.handlerPicked);
                  _loc4_.x = this._locations[_loc1_][0];
                  _loc4_.y = this._locations[_loc1_][1];
                  add(_loc4_);
               }
            }
            _loc1_++;
         }
      }
      
      private function handlerPicked(param1:int) : void
      {
         _mission.setMark("isPick" + param1);
      }
      
      override public function destroy() : void
      {
         MiniMsgMediator.clean();
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         this._eTrigger0 = null;
         this._pTrigger0 = null;
         this._locations = null;
         super.destroy();
      }
   }
}

