package mogames.event
{
   import flash.events.Event;
   
   public class UIEvent extends Event
   {
      public static const REFRESH_GOLD:String = "REFRESH_GOLD";
      
      public static const REFRESH_TICKET:String = "REFRESH_TICKET";
      
      public static const REFRESH_MONEY:String = "REFRESH_MONEY";
      
      public static const TITLE_EVENT:String = "TITLE_EVENT";
      
      public static const MSG_EVENT:String = "MSG_EVENT";
      
      public static const DIALOG_EVENT:String = "DIALOG_EVENT";
      
      public static const ENEMY_EVENT:String = "ENEMY_EVENT";
      
      public static const HERO_EVENT:String = "HERO_EVENT";
      
      public static const TEAM_EVENT:String = "TEAM_EVENT";
      
      public static const TEAM_BUFF_EVENT:String = "TEAM_EVENT";
      
      public static const MAP_EVENT:String = "MAP_EVENT";
      
      public static const MISSION_MENU_EVENT:String = "MISSION_MENU_EVENT";
      
      public static const SHOW_HEAD_HP:String = "SHOW_HEAD_HP";
      
      public static const MENU_EVENT:String = "MENU_EVENT";
      
      public static const BAG_EVENT:String = "BAG_EVENT";
      
      public static const SKILL_EVENT:String = "SKILL_EVENT";
      
      public static const TASK_EVENT:String = "TASK_EVENT";
      
      public static const NPC_EVENT:String = "NPC_EVENT";
      
      public static const GOOD_MENU_EVENT:String = "GOOD_MENU_EVENT";
      
      public static const SELECT_EVENT:String = "SELECT_EVENT";
      
      public static const FORGE_EVENT:String = "FORGE_EVENT";
      
      public static const FABAO_EVENT:String = "FABAO_EVENT";
      
      public static const GEM_EVENT:String = "GEM_EVENT";
      
      public static const ZBSJ_EVENT:String = "ZBSJ_EVENT";
      
      public static const GENGU_EVENT:String = "GENGU_EVENT";
      
      public static const TZJS_EVENT:String = "TZJS_EVENT";
      
      public static const TZQS_EVENT:String = "TZQS_EVENT";
      
      public static const RESULT_EVENT:String = "RESULT_EVENT";
      
      public static const MALL_EVENT:String = "MALL_EVENT";
      
      public static const HERB_EVENT:String = "HERB_EVENT";
      
      public static const GOOD_GET_EVENT:String = "GOOD_GET_EVENT";
      
      public static const MENU_TIP_EVENT:String = "MENU_TIP_EVENT";
      
      public static const GUIDE_EVENT:String = "GUIDE_EVENT";
      
      public static const LIAN_DAN_EVENT:String = "LIAN_DAN_EVENT";
      
      public static const SHUI_LAO_EVENT:String = "SHUI_LAO_EVENT";
      
      public static const ZHEN_BEI_EVENT:String = "ZHEN_BEI_EVENT";
      
      public static const MAZE_EVENT:String = "MAZE_EVENT";
      
      public static const SIGN_EVENT:String = "SIGN_EVENT";
      
      public static const BU_QIAN_EVENT:String = "BU_QIAN_EVENT";
      
      public static const RANK_EVENT:String = "RANK_EVENT";
      
      public static const TOWER_EVENT:String = "TOWER_EVENT";
      
      public static const FIRST_PAY_EVENT:String = "FIRST_PAY_EVENT";
      
      public static const VIP_EVENT:String = "VIP_EVENT";
      
      public static const ACTIVITY_EVENT:String = "ACTIVITY_EVENT";
      
      public static const EXP_EVENT:String = "EXP_EVENT";
      
      public static const REVIVE_EVENT:String = "REVIVE_EVENT";
      
      public static const XI_LIAN_EVENT:String = "XI_LIAN_EVENT";
      
      public static const FJCJ_EVENT:String = "FJCJ_EVENT";
      
      public static const TU_JIAN_EVENT:String = "TU_JIAN_EVENT";
      
      public static const PET_EVENT:String = "PET_EVENT";
      
      public static const DAILY_FB_EVENT:String = "DAILY_FB_EVENT";
      
      public static const CHENG_HAO_EVENT:String = "CHENG_HAO_EVENT";
      
      public static const PE_FUNC_EVENT:String = "PE_FUNC_EVENT";
      
      public static const PK_EVENT:String = "PK_EVENT";
      
      public static const CARD_EVENT:String = "CARD_EVENT";
      
      public static const HUO_LI_EVENT:String = "HUO_LI_EVENT";
      
      public static const BAO_DAI_EVENT:String = "BAO_DAI_EVENT";
      
      public static const BET_EVENT:String = "BET_EVENT";
      
      public static const FASHION_EVENT:String = "FASHION_EVENT";
      
      public static const UNION_EVENT:String = "UNION_EVENT";
      
      public static const GQ_PAY_EVENT:String = "GQ_PAY_EVENT";
      
      public var data:Object;
      
      public function UIEvent(param1:String, param2:Object = null, param3:Boolean = false, param4:Boolean = false)
      {
         super(param1,param3,param4);
         this.data = param2;
      }
   }
}

