package mogames.gameMission.mission.pindingshan.scene
{
   import flash.geom.Point;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.flag.vo.NumFlag;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.huangfengdong.BaseYaocao;
   import mogames.gameMission.mission.pindingshan.PinDingShanMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.FallTrap;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class ScenePingDingShan03 extends BaseScene
   {
      private var _locations:Array = [[86,216],[272,295],[432,220],[607,238],[757,295],[913,180]];
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      public function ScenePingDingShan03(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [FallTrap];
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         this.createYaocao();
         _mission.cleanLoadUI();
         this.initEnemies();
         this.initDeadTip();
      }
      
      private function initDeadTip() : void
      {
         var _loc1_:NumFlag = FlagProxy.instance().findFlag(312);
         if(_loc1_.cur <= 0)
         {
            return;
         }
         MiniMsgMediator.instance().showMsg("恭喜，你已经在这边挂了" + _loc1_.cur + "次！",480,30);
      }
      
      private function initEnemies() : void
      {
         if(_mission.hasMark("enemy903"))
         {
            return;
         }
         this._eTrigger0 = new EnemyTrigger(_mission,[[183,93,152,100],[668,163,152,100]],9031,this.triggerEnd0);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(200,446),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger0.start();
      }
      
      private function triggerEnd0() : void
      {
         _mission.setMark("enemy903");
      }
      
      private function createYaocao() : void
      {
         var _loc3_:int = 0;
         var _loc4_:BaseYaocao = null;
         var _loc1_:int = 0;
         var _loc2_:int = int(this._locations.length);
         while(_loc1_ < _loc2_)
         {
            if(!_mission.hasMark("isPick" + _loc1_))
            {
               _loc3_ = int((_mission as PinDingShanMission).newList[_loc1_].v);
               if(_loc3_ != 0)
               {
                  _loc4_ = new BaseYaocao(_loc3_,_loc1_,this.handlerPicked);
                  _loc4_.x = this._locations[_loc1_][0];
                  _loc4_.y = this._locations[_loc1_][1];
                  add(_loc4_);
               }
            }
            _loc1_++;
         }
      }
      
      private function handlerPicked(param1:int) : void
      {
         _mission.setMark("isPick" + param1);
      }
      
      override public function destroy() : void
      {
         MiniMsgMediator.clean();
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         this._eTrigger0 = null;
         this._pTrigger0 = null;
         this._locations = null;
         super.destroy();
      }
   }
}

