package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class ATKBuff extends BaseBuff
   {
      private var addATK:int;
      
      public function ATKBuff()
      {
         super(1003);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         this.addATK = _roleVO.totalATK.v * _buffVO.argDic.per * 0.01;
         _roleVO.skillATK.v += this.addATK;
         _roleVO.updateATK();
         if(_role.isReady)
         {
            EffectManager.instance().addHeadWord("攻击力提升！",_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override protected function cleanEffect() : void
      {
         _roleVO.skillATK.v -= this.addATK;
         _roleVO.updateATK();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         if(!_buffVO.argDic.skin)
         {
            return null;
         }
         _skin.setupSequence(AssetManager.findAnimation(_buffVO.argDic.skin),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

