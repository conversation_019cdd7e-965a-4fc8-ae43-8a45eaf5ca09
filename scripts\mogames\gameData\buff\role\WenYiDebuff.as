package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameObj.display.BMCSprite;
   
   public class WenYiDebuff extends HurtBuff
   {
      public function WenYiDebuff()
      {
         super(1017);
      }
      
      override public function setBuffVO(param1:BuffVO) : void
      {
         super.setBuffVO(param1);
         _hitVO.hurt.v = param1.argDic.hurt.v;
      }
      
      override public function resetBuff(param1:BuffVO) : void
      {
         _buffVO = param1;
         _hitVO.hurt.v += param1.argDic.hurt.v;
         if(isInterval)
         {
            startInterval();
         }
         else
         {
            startTimeout();
         }
      }
      
      override public function get buffSkin() : BMCSprite
      {
         _skin.setupSequence(AssetManager.findAnimation("EFFECT_BUFF_POSION_CLIP"),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height;
         return _skin;
      }
   }
}

