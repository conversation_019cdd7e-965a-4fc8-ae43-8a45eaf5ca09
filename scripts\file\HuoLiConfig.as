package file
{
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.huoli.vo.HuoLiRewardVO;
   import mogames.gameData.huoli.vo.HuoLiVO;
   
   public class HuoLiConfig
   {
      private static var _instance:HuoLiConfig;
      
      public var rewards:Array;
      
      public function HuoLiConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.initRewards();
      }
      
      public static function instance() : HuoLiConfig
      {
         if(!_instance)
         {
            _instance = new HuoLiConfig();
         }
         return _instance;
      }
      
      private function initRewards() : void
      {
         this.rewards = [];
         this.rewards.push(new HuoLiRewardVO(221,20,[new BaseRewardVO(10000,2500),new BaseRewardVO(14001,2)]));
         this.rewards.push(new HuoLiRewardVO(222,40,[new BaseRewardVO(10000,5000),new BaseRewardVO(13303,1),new BaseRewardVO(13313,1)]));
         this.rewards.push(new HuoLiRewardVO(223,60,[new BaseRewardVO(10000,7500),new BaseRewardVO(16753,1),new BaseRewardVO(14002,2),new BaseRewardVO(13317,1)]));
         this.rewards.push(new HuoLiRewardVO(224,80,[new BaseRewardVO(10000,10000),new BaseRewardVO(14001,3),new BaseRewardVO(16754,1),new BaseRewardVO(10007,20)]));
         this.rewards.push(new HuoLiRewardVO(225,100,[new BaseRewardVO(10000,12500),new BaseRewardVO(14002,3),new BaseRewardVO(14009,1),new BaseRewardVO(16757,1)]));
      }
      
      public function newHuoLis() : Array
      {
         var _loc1_:Array = [];
         _loc1_.push(new HuoLiVO(1001,10,1,"每日签到1次"));
         _loc1_.push(new HuoLiVO(1002,20,5,"通关任意关卡5次"));
         _loc1_.push(new HuoLiVO(1003,5,1,"装备强化1次"));
         _loc1_.push(new HuoLiVO(1004,10,1,"装备洗练1次"));
         _loc1_.push(new HuoLiVO(1005,10,1,"完成每日任务1次"));
         _loc1_.push(new HuoLiVO(1006,5,1,"五庄观种植1次药草"));
         _loc1_.push(new HuoLiVO(1007,15,1,"炼制任意品阶丹药1颗 "));
         _loc1_.push(new HuoLiVO(1008,10,1,"挑战试炼场1次"));
         _loc1_.push(new HuoLiVO(1009,10,1,"挑战寻宝副本1次"));
         _loc1_.push(new HuoLiVO(1010,10,1,"挑战降妖塔1次"));
         _loc1_.push(new HuoLiVO(1011,5,1,"提升宠物1级"));
         _loc1_.push(new HuoLiVO(1012,10,1,"培养宠物1次"));
         return _loc1_;
      }
   }
}

