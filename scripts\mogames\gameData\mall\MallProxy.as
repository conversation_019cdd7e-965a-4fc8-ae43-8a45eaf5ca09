package mogames.gameData.mall
{
   import file.MallConfig;
   import mogames.Layers;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.mall.vo.BaseMallVO;
   import mogames.gameData.mall.vo.MallMoneyVO;
   import mogames.gameData.mall.vo.MallTicketVO;
   import mogames.gamePKG.PKGManager;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.MsgMediator;
   import unit4399.Open4399Pay;
   
   public class MallProxy
   {
      private static var _instance:MallProxy;
      
      public function MallProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : MallProxy
      {
         if(!_instance)
         {
            _instance = new MallProxy();
         }
         return _instance;
      }
      
      public function handlerBuy(param1:BaseMallVO, param2:int, param3:Function, param4:Boolean = true) : void
      {
         var finishBuy:Function = null;
         var mallVO:BaseMallVO = param1;
         var num:int = param2;
         var callBack:Function = param3;
         var $isSave:Boolean = param4;
         finishBuy = function():void
         {
            var _loc1_:Array = mallVO.newGood(num);
            if(_loc1_.length > 0)
            {
               RewardHandler.instance().onReward(_loc1_);
            }
            callBack();
         };
         var total:int = num * mallVO.price.v;
         if(mallVO is MallTicketVO)
         {
            MallConfig.instance().checkCheat(mallVO.mallGood.id.v);
            if(this.checkLack(total,2))
            {
               return;
            }
            MasterProxy.instance().changeTicket(-num * mallVO.price.v);
            finishBuy();
         }
         else if(mallVO is MallMoneyVO)
         {
            if(this.checkLack(total,1))
            {
               return;
            }
            if(mallVO.price.v < 0 || num < 0)
            {
               return;
            }
            this.buy4399(mallVO.mallID.v,mallVO.price.v,num,finishBuy,$isSave);
         }
      }
      
      public function buy4399(param1:int, param2:int, param3:int, param4:Function, param5:Boolean) : void
      {
         var func:Function = null;
         var mallID:int = param1;
         var price:int = param2;
         var buyNum:int = param3;
         var okBuyFunc:Function = param4;
         var $isSave:Boolean = param5;
         func = function(param1:Object):void
         {
            if(!param1.propId)
            {
               return;
            }
            if(int(param1.propId) != mallID)
            {
               return;
            }
            MsgMediator.clean();
            MasterProxy.instance().changeMoney(-price * buyNum);
            if(okBuyFunc != null)
            {
               okBuyFunc();
            }
            if($isSave)
            {
               PKGManager.saveNoMulit();
            }
         };
         var temp:Object = new Object();
         temp.propId = String(mallID);
         temp.count = buyNum;
         temp.price = price;
         temp.idx = PKGManager.curIndex.v;
         Open4399Pay.buyProp(temp,func);
      }
      
      private function checkLack(param1:int, param2:int) : Boolean
      {
         var _loc3_:LackVO = MasterProxy.instance().checkLack(param1,param2);
         if(_loc3_ != null)
         {
            Layers.lockGame = false;
            MiniMsgMediator.instance().showAutoMsg(_loc3_.str);
            return true;
         }
         return false;
      }
   }
}

