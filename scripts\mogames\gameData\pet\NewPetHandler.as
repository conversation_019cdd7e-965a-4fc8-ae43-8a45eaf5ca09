package mogames.gameData.pet
{
   import file.PetConfig;
   import file.PetSkillConfig;
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetSkillVO;
   import utils.MathUtil;
   
   public class NewPetHandler
   {
      public function NewPetHandler()
      {
         super();
      }
      
      public function countPetVO(param1:PetGameVO) : void
      {
         param1.skillPoint.v = 1;
         param1.curName = param1.petAsset.name;
         param1.isMutate.v = this.isMutate;
         param1.curEvolve.v = 0;
         param1.curQuality.v = this.quality;
         param1.curGrow.v = PetConfig.instance().findPetGrow(param1.curQuality.v,param1.mutate).randomValue();
         param1.curTalent.v = PetConfig.instance().talent.randomValue();
         param1.curWit.v = PetConfig.instance().wit.randomValue();
         param1.curLearn.v = PetConfig.instance().learn.randomValue();
         param1.curLife.v = 100;
         param1.curLoyal.v = 100;
         param1.talentHP.v = MathUtil.randomNum(param1.countMinTalent(0),param1.countMaxTalent(0));
         param1.talentATK.v = MathUtil.randomNum(param1.countMinTalent(1),param1.countMaxTalent(1));
         param1.talentPDEF.v = MathUtil.randomNum(param1.countMinTalent(2),param1.countMaxTalent(2));
         param1.talentMDEF.v = MathUtil.randomNum(param1.countMinTalent(3),param1.countMaxTalent(3));
         this.initActiveSkills(param1);
         this.initPassiveSkills(param1);
         param1.setLevel(1);
      }
      
      public function initActiveSkills(param1:PetGameVO, param2:Boolean = false) : void
      {
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc8_:PetSkillVO = null;
         param1.activeSkills = new Array(4);
         var _loc4_:int = int(Math.random() * 100 + 1);
         if(_loc4_ <= 40)
         {
            _loc3_ = 1;
         }
         else if(_loc4_ <= 80)
         {
            _loc3_ = 2;
         }
         else if(_loc4_ <= 95)
         {
            _loc3_ = 3;
         }
         else
         {
            _loc3_ = 4;
         }
         if(param2)
         {
            _loc3_ = 4;
         }
         var _loc5_:Array = param1.allSkills.slice();
         var _loc7_:int = 0;
         while(_loc7_ < _loc3_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc8_ = _loc5_[_loc6_];
            _loc8_.curQuality.v = this.countSkillQuality(param1);
            param1.activeSkills[_loc7_] = _loc8_;
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
      }
      
      public function initPassiveSkills(param1:PetGameVO, param2:Boolean = false) : void
      {
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc8_:PetSkillVO = null;
         param1.passiveSkills = new Array(5);
         var _loc4_:int = int(Math.random() * 100 + 1);
         if(_loc4_ <= 20)
         {
            _loc3_ = 1;
         }
         else if(_loc4_ <= 60)
         {
            _loc3_ = 2;
         }
         else if(_loc4_ <= 85)
         {
            _loc3_ = 3;
         }
         else if(_loc4_ <= 95)
         {
            _loc3_ = 4;
         }
         else
         {
            _loc3_ = 5;
         }
         if(param2)
         {
            _loc3_ = 5;
         }
         var _loc5_:Array = PetSkillConfig.instance().newPassiveSkills(param1);
         var _loc7_:int = 0;
         while(_loc7_ < _loc3_)
         {
            _loc6_ = Math.random() * _loc5_.length;
            _loc8_ = _loc5_[_loc6_];
            _loc8_.curQuality.v = this.countSkillQuality(param1);
            param1.passiveSkills[_loc7_] = _loc8_;
            _loc5_.splice(_loc6_,1);
            _loc7_++;
         }
      }
      
      public function countSkillQuality(param1:PetGameVO) : int
      {
         if(param1.mutate)
         {
            return this.mutateSkillQuality(param1);
         }
         return this.normalSkillQuality(param1);
      }
      
      private function normalSkillQuality(param1:PetGameVO) : int
      {
         var _loc2_:int = Math.random() * 1000 + 1;
         if(_loc2_ <= 350 - param1.curLearn.v * 5)
         {
            return 0;
         }
         if(_loc2_ <= 700 - param1.curLearn.v * 10)
         {
            return 1;
         }
         if(_loc2_ <= 950 - param1.curLearn.v * 5)
         {
            return 2;
         }
         return 3;
      }
      
      private function mutateSkillQuality(param1:PetGameVO) : int
      {
         var _loc2_:int = Math.random() * 1000 + 1;
         if(_loc2_ <= 300 - param1.curLearn.v * 5)
         {
            return 0;
         }
         if(_loc2_ <= 650 - param1.curLearn.v * 10)
         {
            return 1;
         }
         if(_loc2_ <= 850 - param1.curLearn.v * 10)
         {
            return 2;
         }
         if(_loc2_ <= 950 - param1.curLearn.v * 5)
         {
            return 3;
         }
         return 4;
      }
      
      public function get quality() : int
      {
         var _loc1_:int = Math.random() * 100 + 1;
         if(_loc1_ <= 25)
         {
            return 0;
         }
         if(_loc1_ <= 60)
         {
            return 1;
         }
         if(_loc1_ <= 85)
         {
            return 2;
         }
         return 3;
      }
      
      public function get isMutate() : int
      {
         if(MathUtil.checkOdds(800))
         {
            return 0;
         }
         return 1;
      }
   }
}

