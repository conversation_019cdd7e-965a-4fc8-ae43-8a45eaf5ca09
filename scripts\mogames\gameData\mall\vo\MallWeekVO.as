package mogames.gameData.mall.vo
{
   import mogames.gameData.flag.FlagProxy;
   
   public class MallWeekVO
   {
      public var flagID:int;
      
      public var zhekou:int;
      
      public var oldPrice:String;
      
      public var newPrice:String;
      
      public var mallVO:MallMoneyVO;
      
      public function MallWeekVO(param1:int, param2:int, param3:int, param4:MallMoneyVO)
      {
         super();
         this.flagID = param1;
         this.zhekou = param2;
         this.oldPrice = "原价：" + param3 + "金锭";
         this.newPrice = "现价：" + param4.price.v + "金锭";
         this.mallVO = param4;
      }
      
      public function handlerBuy() : void
      {
         FlagProxy.instance().setValue(this.flagID,1);
      }
   }
}

