package file
{
   import mogames.gameData.ConstData;
   import mogames.gameData.fabao.RLUseVO;
   import mogames.gameData.fabao.RongLianVO;
   import mogames.gameData.forge.NeedVO;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.TxtUtil;
   
   public class PERLConfig
   {
      private static var _instance:PERLConfig;
      
      private var _rlVec:Vector.<RongLianVO>;
      
      public function PERLConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : PERLConfig
      {
         if(!_instance)
         {
            _instance = new PERLConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._rlVec = new Vector.<RongLianVO>();
         this._rlVec.push(new RongLianVO(46001,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13401,15),new NeedVO(13402,15),new NeedVO(13405,15)])]));
         this._rlVec.push(new RongLianVO(46002,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13401,15),new NeedVO(13402,15),new NeedVO(13405,15)])]));
         this._rlVec.push(new RongLianVO(46003,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13401,15),new NeedVO(13402,15),new NeedVO(13405,15)])]));
         this._rlVec.push(new RongLianVO(46004,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13401,15),new NeedVO(13402,15),new NeedVO(13405,15)])]));
         this._rlVec.push(new RongLianVO(46051,[null,null,null,new RLUseVO(3,100000,[new NeedVO(13407,15),new NeedVO(13408,15),new NeedVO(13409,15)])]));
         this._rlVec.push(new RongLianVO(46052,[null,null,null,new RLUseVO(3,100000,[new NeedVO(13410,15),new NeedVO(13411,15),new NeedVO(13412,15)])]));
         this._rlVec.push(new RongLianVO(46053,[null,null,null,new RLUseVO(3,200000,[new NeedVO(13413,15),new NeedVO(13414,15),new NeedVO(13415,15)])]));
         this._rlVec.push(new RongLianVO(47001,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13401,15),new NeedVO(13402,15),new NeedVO(13403,15)])]));
         this._rlVec.push(new RongLianVO(47002,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13401,15),new NeedVO(13402,15),new NeedVO(13403,15)])]));
         this._rlVec.push(new RongLianVO(47003,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13401,15),new NeedVO(13402,15),new NeedVO(13403,15)])]));
         this._rlVec.push(new RongLianVO(47004,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13401,15),new NeedVO(13402,15),new NeedVO(13403,15)])]));
         this._rlVec.push(new RongLianVO(47051,[null,null,null,new RLUseVO(3,100000,[new NeedVO(13407,15),new NeedVO(13408,15),new NeedVO(13409,15)])]));
         this._rlVec.push(new RongLianVO(47052,[null,null,null,new RLUseVO(3,100000,[new NeedVO(13410,15),new NeedVO(13411,15),new NeedVO(13412,15)])]));
         this._rlVec.push(new RongLianVO(47053,[null,null,null,new RLUseVO(3,200000,[new NeedVO(13413,15),new NeedVO(13414,15),new NeedVO(13415,15)])]));
         this._rlVec.push(new RongLianVO(48001,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13404,15),new NeedVO(13405,15),new NeedVO(13406,15)])]));
         this._rlVec.push(new RongLianVO(48002,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13404,15),new NeedVO(13405,15),new NeedVO(13406,15)])]));
         this._rlVec.push(new RongLianVO(48003,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13404,15),new NeedVO(13405,15),new NeedVO(13406,15)])]));
         this._rlVec.push(new RongLianVO(48004,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13404,15),new NeedVO(13405,15),new NeedVO(13406,15)])]));
         this._rlVec.push(new RongLianVO(48051,[null,null,null,new RLUseVO(3,100000,[new NeedVO(13407,15),new NeedVO(13408,15),new NeedVO(13409,15)])]));
         this._rlVec.push(new RongLianVO(48052,[null,null,null,new RLUseVO(3,100000,[new NeedVO(13410,15),new NeedVO(13411,15),new NeedVO(13412,15)])]));
         this._rlVec.push(new RongLianVO(48053,[null,null,null,new RLUseVO(3,200000,[new NeedVO(13413,15),new NeedVO(13414,15),new NeedVO(13415,15)])]));
         this._rlVec.push(new RongLianVO(49001,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13404,15),new NeedVO(13403,15),new NeedVO(13406,15)])]));
         this._rlVec.push(new RongLianVO(49002,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13404,15),new NeedVO(13403,15),new NeedVO(13406,15)])]));
         this._rlVec.push(new RongLianVO(49003,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13404,15),new NeedVO(13403,15),new NeedVO(13406,15)])]));
         this._rlVec.push(new RongLianVO(49004,[null,null,null,new RLUseVO(3,60000,[new NeedVO(13404,15),new NeedVO(13403,15),new NeedVO(13406,15)])]));
         this._rlVec.push(new RongLianVO(49051,[null,null,null,new RLUseVO(3,100000,[new NeedVO(13407,15),new NeedVO(13408,15),new NeedVO(13409,15)])]));
         this._rlVec.push(new RongLianVO(49052,[null,null,null,new RLUseVO(3,100000,[new NeedVO(13410,15),new NeedVO(13411,15),new NeedVO(13412,15)])]));
         this._rlVec.push(new RongLianVO(49053,[null,null,null,new RLUseVO(3,200000,[new NeedVO(13413,15),new NeedVO(13414,15),new NeedVO(13415,15)])]));
      }
      
      public function checkRL(param1:int, param2:int) : Boolean
      {
         if(!this.hasEquip(param1))
         {
            MiniMsgMediator.instance().showAutoMsg("该装备熔炼暂未开放！");
            return false;
         }
         var _loc3_:int = this.maxQuality(param1);
         if(param2 >= _loc3_)
         {
            MiniMsgMediator.instance().showAutoMsg("该装备当前只可以熔炼至【" + TxtUtil.setColor(ConstData.GOOD_QUALITY[_loc3_],ConstData.GOOD_COLOR1[_loc3_]) + "】品质！");
            return false;
         }
         return true;
      }
      
      public function findUseVO(param1:int, param2:int) : RLUseVO
      {
         var _loc3_:RongLianVO = this.findRLVO(param1);
         if(!_loc3_)
         {
            return null;
         }
         return _loc3_.findVO(param2);
      }
      
      private function maxQuality(param1:int) : int
      {
         var _loc2_:RongLianVO = this.findRLVO(param1);
         return _loc2_.list.length;
      }
      
      private function findRLVO(param1:int) : RongLianVO
      {
         var _loc2_:RongLianVO = null;
         for each(_loc2_ in this._rlVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      private function hasEquip(param1:int) : Boolean
      {
         var _loc2_:RongLianVO = null;
         for each(_loc2_ in this._rlVec)
         {
            if(_loc2_.id.v == param1)
            {
               return true;
            }
         }
         return false;
      }
   }
}

