package file
{
   import mogames.gameData.role.vo.RoleAIVO;
   import mogames.gameData.role.vo.RoleTrickVO;
   
   public class RoleAIConfig
   {
      private static var _instance:RoleAIConfig;
      
      private var _aiVec:Vector.<RoleAIVO>;
      
      public function RoleAIConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : RoleAIConfig
      {
         if(!_instance)
         {
            _instance = new RoleAIConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._aiVec = new Vector.<RoleAIVO>();
         this._aiVec.push(new RoleAIVO(1001,8,2.5,1,1,2,[],[],false));
         this._aiVec.push(new RoleAIVO(1002,8,2.5,1.5,1.5,2,[],[],false));
         this._aiVec.push(new RoleAIVO(1003,8,2.5,1.5,1.5,2,[],[],false));
         this._aiVec.push(new RoleAIVO(1004,8,2.5,1.5,1.5,2,[],[],false));
         this._aiVec.push(new RoleAIVO(1005,5,3,2,1.5,2,[],[],false));
         this._aiVec.push(new RoleAIVO(1101,8,2.5,0.5,0.5,1,[[0,101,102,103,104,105,106]],[new RoleTrickVO(0,0.5,1,200),new RoleTrickVO(101,1.5,1,200),new RoleTrickVO(102,1.5,1,200),new RoleTrickVO(103,2.5,1,200),new RoleTrickVO(104,2.5,1,200),new RoleTrickVO(105,2.5,1,400),new RoleTrickVO(106,2.5,1,400)],false));
         this._aiVec.push(new RoleAIVO(1102,8,2.5,0.5,0.5,1,[[0,101,102,103,104,105,106]],[new RoleTrickVO(0,0.5,1,300),new RoleTrickVO(101,1.5,1,200),new RoleTrickVO(102,1.5,1,230),new RoleTrickVO(103,2.5,1,250),new RoleTrickVO(104,2.5,1,230),new RoleTrickVO(105,2.5,1,300),new RoleTrickVO(106,2.5,1,300)],false));
         this._aiVec.push(new RoleAIVO(1103,8,2.5,0.5,0.5,1,[[0,101,102,103,104,105,106]],[new RoleTrickVO(0,0.5,0,30),new RoleTrickVO(101,1.5,0,350),new RoleTrickVO(102,1.5,0,90),new RoleTrickVO(103,2.5,0,700),new RoleTrickVO(104,2.5,0,450),new RoleTrickVO(105,2.5,0,150),new RoleTrickVO(106,2.5,0,600)],false));
         this._aiVec.push(new RoleAIVO(1104,8,2.5,0.5,0.5,1,[[0,101,102,103,104,105]],[new RoleTrickVO(0,0.5,0,300),new RoleTrickVO(101,1.5,0,280),new RoleTrickVO(102,1.5,0,500),new RoleTrickVO(103,2.5,0,900),new RoleTrickVO(104,2.5,0,130),new RoleTrickVO(105,2.5,0,150),new RoleTrickVO(106,2.5,0,600)],false));
         this._aiVec.push(new RoleAIVO(1106,8,2.5,0.5,0.5,1,[[0,101,102,103,104,105]],[new RoleTrickVO(0,0.5,0,300),new RoleTrickVO(101,1.5,0,130),new RoleTrickVO(102,1.5,0,500),new RoleTrickVO(103,1.5,0,170),new RoleTrickVO(104,1.5,0,200),new RoleTrickVO(105,2.5,0,150),new RoleTrickVO(106,1.5,0,600)],false));
         this._aiVec.push(new RoleAIVO(1107,8,2.5,0.5,0.5,1,[[0,101,102,103,104,105]],[new RoleTrickVO(0,0.5,0,100),new RoleTrickVO(101,1.5,0,600),new RoleTrickVO(102,1.5,0,500),new RoleTrickVO(103,1.5,0,170),new RoleTrickVO(104,1.5,0,150),new RoleTrickVO(105,2.5,0,150),new RoleTrickVO(106,1.5,0,600)],false));
         this._aiVec.push(new RoleAIVO(1108,8,2.5,0.5,0.5,1,[[0,101,102,103,104,105]],[new RoleTrickVO(0,0.5,0,100),new RoleTrickVO(101,1.5,0,600),new RoleTrickVO(102,1.5,0,150),new RoleTrickVO(103,1.5,0,600),new RoleTrickVO(104,1.5,0,150),new RoleTrickVO(105,2.5,0,200),new RoleTrickVO(106,1.5,0,600)],false));
         this._aiVec.push(new RoleAIVO(10010,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.4,0.5,200),new RoleTrickVO(1,0.4,0.5,200),new RoleTrickVO(2,0.4,0.5,200),new RoleTrickVO(101,0.4,0.5,200),new RoleTrickVO(102,0.4,0.4,200),new RoleTrickVO(103,0.4,0.4,200),new RoleTrickVO(104,0.4,0.4,200),new RoleTrickVO(105,0.4,0.4,200),new RoleTrickVO(200,0.4,0.4,200)],false));
         this._aiVec.push(new RoleAIVO(10011,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.3,0.5,200),new RoleTrickVO(1,0.3,0.5,200),new RoleTrickVO(2,0.3,0.5,200),new RoleTrickVO(101,0.3,1,200),new RoleTrickVO(102,0.3,1,200),new RoleTrickVO(103,0.3,1,200),new RoleTrickVO(104,0.3,1,200),new RoleTrickVO(105,0.3,1,400),new RoleTrickVO(200,0.3,1,400)],false));
         this._aiVec.push(new RoleAIVO(10012,8,1.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.2,0.5,200),new RoleTrickVO(1,0.2,0.5,200),new RoleTrickVO(2,0.2,0.5,200),new RoleTrickVO(101,0.2,1,200),new RoleTrickVO(102,0.2,1,200),new RoleTrickVO(103,0.2,1,200),new RoleTrickVO(104,0.2,1,200),new RoleTrickVO(105,0.2,1,400),new RoleTrickVO(200,0.2,1,400)],false));
         this._aiVec.push(new RoleAIVO(10013,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.1,0.5,200),new RoleTrickVO(1,0.1,0.5,200),new RoleTrickVO(2,0.1,0.5,200),new RoleTrickVO(101,0.1,1,200),new RoleTrickVO(102,0.1,1,200),new RoleTrickVO(103,0.1,1,200),new RoleTrickVO(104,0.1,1,200),new RoleTrickVO(105,0.1,1,400),new RoleTrickVO(200,0.1,1,400)],true));
         this._aiVec.push(new RoleAIVO(10014,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0,0.5,200),new RoleTrickVO(1,0,0.5,200),new RoleTrickVO(2,0,0.5,200),new RoleTrickVO(101,0,1,200),new RoleTrickVO(102,0,1,200),new RoleTrickVO(103,0,1,200),new RoleTrickVO(104,0,1,200),new RoleTrickVO(105,0,1,400),new RoleTrickVO(200,0,1,400)],true));
         this._aiVec.push(new RoleAIVO(10020,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.4,0.5,200),new RoleTrickVO(1,0.4,0.5,200),new RoleTrickVO(2,0.4,0.5,200),new RoleTrickVO(101,0.4,0.5,200),new RoleTrickVO(102,0.4,0.4,200),new RoleTrickVO(103,0.4,0.4,200),new RoleTrickVO(104,0.4,0.4,200),new RoleTrickVO(105,0.4,0.4,200),new RoleTrickVO(200,0.4,0.4,200)],false));
         this._aiVec.push(new RoleAIVO(10021,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.3,0.5,200),new RoleTrickVO(1,0.3,0.5,200),new RoleTrickVO(2,0.3,0.5,200),new RoleTrickVO(101,0.3,1,200),new RoleTrickVO(102,0.3,1,200),new RoleTrickVO(103,0.3,1,200),new RoleTrickVO(104,0.3,1,200),new RoleTrickVO(105,0.3,1,400),new RoleTrickVO(200,0.3,1,400)],false));
         this._aiVec.push(new RoleAIVO(10022,8,1.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.2,0.5,200),new RoleTrickVO(1,0.2,0.5,200),new RoleTrickVO(2,0.2,0.5,200),new RoleTrickVO(101,0.2,1,200),new RoleTrickVO(102,0.2,1,200),new RoleTrickVO(103,0.2,1,200),new RoleTrickVO(104,0.2,1,200),new RoleTrickVO(105,0.2,1,400),new RoleTrickVO(200,0.2,1,400)],false));
         this._aiVec.push(new RoleAIVO(10023,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.1,0.5,200),new RoleTrickVO(1,0.1,0.5,200),new RoleTrickVO(2,0.1,0.5,200),new RoleTrickVO(101,0.1,1,200),new RoleTrickVO(102,0.1,1,200),new RoleTrickVO(103,0.1,1,200),new RoleTrickVO(104,0.1,1,200),new RoleTrickVO(105,0.1,1,400),new RoleTrickVO(200,0.1,1,400)],true));
         this._aiVec.push(new RoleAIVO(10024,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0,0.5,200),new RoleTrickVO(1,0,0.5,200),new RoleTrickVO(2,0,0.5,200),new RoleTrickVO(101,0,1,200),new RoleTrickVO(102,0,1,200),new RoleTrickVO(103,0,1,200),new RoleTrickVO(104,0,1,200),new RoleTrickVO(105,0,1,400),new RoleTrickVO(200,0,1,400)],true));
         this._aiVec.push(new RoleAIVO(10030,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.4,0.5,200),new RoleTrickVO(1,0.4,0.5,200),new RoleTrickVO(2,0.4,0.5,200),new RoleTrickVO(101,0.4,0.5,200),new RoleTrickVO(102,0.4,0.4,200),new RoleTrickVO(103,0.4,0.4,200),new RoleTrickVO(104,0.4,0.4,200),new RoleTrickVO(105,0.4,0.4,200),new RoleTrickVO(200,0.4,0.4,200)],false));
         this._aiVec.push(new RoleAIVO(10031,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.3,0.5,200),new RoleTrickVO(1,0.3,0.5,200),new RoleTrickVO(2,0.3,0.5,200),new RoleTrickVO(101,0.3,1,200),new RoleTrickVO(102,0.3,1,200),new RoleTrickVO(103,0.3,1,200),new RoleTrickVO(104,0.3,1,200),new RoleTrickVO(105,0.3,1,400),new RoleTrickVO(200,0.3,1,400)],false));
         this._aiVec.push(new RoleAIVO(10032,8,1.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.2,0.5,200),new RoleTrickVO(1,0.2,0.5,200),new RoleTrickVO(2,0.2,0.5,200),new RoleTrickVO(101,0.2,1,200),new RoleTrickVO(102,0.2,1,200),new RoleTrickVO(103,0.2,1,200),new RoleTrickVO(104,0.2,1,200),new RoleTrickVO(105,0.2,1,400),new RoleTrickVO(200,0.2,1,400)],false));
         this._aiVec.push(new RoleAIVO(10033,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.1,0.5,200),new RoleTrickVO(1,0.1,0.5,200),new RoleTrickVO(2,0.1,0.5,200),new RoleTrickVO(101,0.1,1,200),new RoleTrickVO(102,0.1,1,200),new RoleTrickVO(103,0.1,1,200),new RoleTrickVO(104,0.1,1,200),new RoleTrickVO(105,0.1,1,400),new RoleTrickVO(200,0.1,1,400)],true));
         this._aiVec.push(new RoleAIVO(10034,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0,0.5,200),new RoleTrickVO(1,0,0.5,200),new RoleTrickVO(2,0,0.5,200),new RoleTrickVO(101,0,1,200),new RoleTrickVO(102,0,1,200),new RoleTrickVO(103,0,1,200),new RoleTrickVO(104,0,1,200),new RoleTrickVO(105,0,1,400),new RoleTrickVO(200,0,1,400)],true));
         this._aiVec.push(new RoleAIVO(10040,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.3,0.5,200),new RoleTrickVO(1,0.3,0.5,200),new RoleTrickVO(2,0.3,0.5,200),new RoleTrickVO(101,0.3,0.5,200),new RoleTrickVO(102,0.3,0.4,200),new RoleTrickVO(103,0.3,0.4,200),new RoleTrickVO(104,0.3,0.4,200),new RoleTrickVO(105,0.3,0.4,200),new RoleTrickVO(200,2.3,0.4,200)],false));
         this._aiVec.push(new RoleAIVO(10041,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.1,0.5,200),new RoleTrickVO(1,0.1,0.5,200),new RoleTrickVO(2,0.1,0.5,200),new RoleTrickVO(101,0.1,1,200),new RoleTrickVO(102,0.1,1,200),new RoleTrickVO(103,0.1,1,200),new RoleTrickVO(104,0.1,1,200),new RoleTrickVO(105,0.1,1,400),new RoleTrickVO(200,2.1,1,400)],false));
         this._aiVec.push(new RoleAIVO(10042,8,1.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0,0.5,200),new RoleTrickVO(1,0,0.5,200),new RoleTrickVO(2,0,0.5,200),new RoleTrickVO(101,0,1,200),new RoleTrickVO(102,0,1,200),new RoleTrickVO(103,0,1,200),new RoleTrickVO(104,0,1,200),new RoleTrickVO(105,0,1,400),new RoleTrickVO(200,0.5,1,400)],false));
         this._aiVec.push(new RoleAIVO(10043,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0,0.5,200),new RoleTrickVO(1,0.8,0.5,200),new RoleTrickVO(2,0,0.5,200),new RoleTrickVO(101,0,1,200),new RoleTrickVO(102,0,1,200),new RoleTrickVO(103,0,1,200),new RoleTrickVO(104,0,1,200),new RoleTrickVO(105,0,1,400),new RoleTrickVO(200,0.4,1,400)],true));
         this._aiVec.push(new RoleAIVO(10044,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.2,0.5,200),new RoleTrickVO(1,0.2,0.5,200),new RoleTrickVO(2,0.2,0.5,200),new RoleTrickVO(101,0.2,1,200),new RoleTrickVO(102,0.2,1,200),new RoleTrickVO(103,0.2,1,200),new RoleTrickVO(104,0.2,1,200),new RoleTrickVO(105,0.2,1,400),new RoleTrickVO(200,0.6,1,400)],true));
         this._aiVec.push(new RoleAIVO(10050,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.3,0.5,900),new RoleTrickVO(1,0.3,0.5,900),new RoleTrickVO(2,0.3,0.5,200),new RoleTrickVO(101,0.3,0.5,200),new RoleTrickVO(102,0.3,0.4,200),new RoleTrickVO(103,0.3,0.4,200),new RoleTrickVO(104,0.3,0.4,400),new RoleTrickVO(105,0.3,0.4,300),new RoleTrickVO(200,2.3,0.4,200)],false));
         this._aiVec.push(new RoleAIVO(10051,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.1,0.5,900),new RoleTrickVO(1,0.1,0.5,900),new RoleTrickVO(2,0.1,0.5,200),new RoleTrickVO(101,0.1,1,200),new RoleTrickVO(102,0.1,1,200),new RoleTrickVO(103,0.1,1,200),new RoleTrickVO(104,0.1,1,400),new RoleTrickVO(105,0.1,1,300),new RoleTrickVO(200,2.1,1,400)],false));
         this._aiVec.push(new RoleAIVO(10052,8,1.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0,0.5,900),new RoleTrickVO(1,0,0.5,900),new RoleTrickVO(2,0,0.5,200),new RoleTrickVO(101,0,1,200),new RoleTrickVO(102,0,1,200),new RoleTrickVO(103,0,1,200),new RoleTrickVO(104,0,1,400),new RoleTrickVO(105,0,1,300),new RoleTrickVO(200,0.5,1,400)],false));
         this._aiVec.push(new RoleAIVO(10053,8,2,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0,0.5,900),new RoleTrickVO(1,0.8,0.5,900),new RoleTrickVO(2,0,0.5,200),new RoleTrickVO(101,0,1,200),new RoleTrickVO(102,0,1,200),new RoleTrickVO(103,0,1,200),new RoleTrickVO(104,0,1,400),new RoleTrickVO(105,0,1,300),new RoleTrickVO(200,0.4,1,400)],true));
         this._aiVec.push(new RoleAIVO(10054,8,2.5,1,1,2,[[0,1,2,101,102,103,104,105,200]],[new RoleTrickVO(0,0.2,0.5,900),new RoleTrickVO(1,0.2,0.5,900),new RoleTrickVO(2,0.2,0.5,200),new RoleTrickVO(101,0.2,1,200),new RoleTrickVO(102,0.2,1,200),new RoleTrickVO(103,0.2,1,200),new RoleTrickVO(104,0.2,1,400),new RoleTrickVO(105,0.2,1,300),new RoleTrickVO(200,0.6,1,400)],true));
         this._aiVec.push(new RoleAIVO(20011,20,0,2,2,3,[[0]],[new RoleTrickVO(0,2,0,60)],false));
         this._aiVec.push(new RoleAIVO(20021,50,1,1,1,1,[[0]],[new RoleTrickVO(0,2,0,68)],false));
         this._aiVec.push(new RoleAIVO(20031,50,1,1,1,1,[[101,0,0]],[new RoleTrickVO(0,2,0,100),new RoleTrickVO(101,2,0,400)],true));
         this._aiVec.push(new RoleAIVO(20041,50,1,1,1,1,[[101,0]],[new RoleTrickVO(0,2,0,70),new RoleTrickVO(101,2,0,100)],false));
         this._aiVec.push(new RoleAIVO(20032,50,1,1,1,1,[[101,0,101]],[new RoleTrickVO(0,2,0,100),new RoleTrickVO(101,2,0,400)],false));
         this._aiVec.push(new RoleAIVO(20022,50,1,1,1,1,[[0]],[new RoleTrickVO(0,2,0,70)],false));
         this._aiVec.push(new RoleAIVO(20051,50,1,1,1,1,[[700,300]],[new RoleTrickVO(0,2.5,0,400),new RoleTrickVO(101,2.5,0,900)],false));
         this._aiVec.push(new RoleAIVO(20033,50,1,1,1,1,[[101,101,0]],[new RoleTrickVO(0,2,0,100),new RoleTrickVO(101,3,0,400)],false));
         this._aiVec.push(new RoleAIVO(20071,50,1,1,1,1,[[0]],[new RoleTrickVO(0,2.5,0,100),new RoleTrickVO(101,2.5,0,170)],false));
         this._aiVec.push(new RoleAIVO(20081,50,1,1,1,1,[[0,0,101]],[new RoleTrickVO(0,2.5,0,500),new RoleTrickVO(101,2.5,0,700)],true));
         this._aiVec.push(new RoleAIVO(20091,50,1,1,1,1,[[0,0,0,101,0,101]],[new RoleTrickVO(0,2.5,0,500),new RoleTrickVO(101,2.5,0,900)],true));
         this._aiVec.push(new RoleAIVO(20042,50,1,1,1,1,[[101,0]],[new RoleTrickVO(0,2,0,170),new RoleTrickVO(101,2,0,130)],false));
         this._aiVec.push(new RoleAIVO(20023,50,1,1,1,1,[[0]],[new RoleTrickVO(0,2,0,70)],false));
         this._aiVec.push(new RoleAIVO(20052,50,1,1,1,1,[[500,500]],[new RoleTrickVO(0,2,0,400),new RoleTrickVO(101,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20061,50,1,1,1,1,[[0,0,0,101]],[new RoleTrickVO(0,2,0,250),new RoleTrickVO(101,3,0.5,500)],false));
         this._aiVec.push(new RoleAIVO(20062,50,1,1,1,1,[[0,0,0,101]],[new RoleTrickVO(0,2,0,250),new RoleTrickVO(101,3,0.5,400)],false));
         this._aiVec.push(new RoleAIVO(20101,50,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,2,0,100),new RoleTrickVO(101,2,0,300)],false));
         this._aiVec.push(new RoleAIVO(20111,50,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,2,0,90),new RoleTrickVO(101,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20121,50,1,1,1,1,[[0,0,0,101]],[new RoleTrickVO(0,2,0,480),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20053,20,1,1,1,1,[[600,400]],[new RoleTrickVO(0,2,0,400),new RoleTrickVO(101,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20082,20,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,2,0,500),new RoleTrickVO(101,2,0,700)],true));
         this._aiVec.push(new RoleAIVO(20122,20,1,1,1,1,[[0,0,101]],[new RoleTrickVO(0,2,0,480),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20112,20,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,2,0,90),new RoleTrickVO(101,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20131,18,1,1,1,1,[[0,101]],[new RoleTrickVO(0,2,0,90),new RoleTrickVO(101,2,0,70)],false));
         this._aiVec.push(new RoleAIVO(20141,18,1,1,1,1,[[0,101]],[new RoleTrickVO(0,2,0,500),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20123,18,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,2,0,480),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20151,16,1,1,1,1,[[0,0,101]],[new RoleTrickVO(0,2,0,130),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20161,16,1,1,1,1,[[101,0,0]],[new RoleTrickVO(0,2,0,80),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20171,14,1,1,1,1,[[0]],[new RoleTrickVO(0,2,0,68)],true));
         this._aiVec.push(new RoleAIVO(20181,14,1,1,1,1,[[0]],[new RoleTrickVO(0,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20191,14,1,1,1,1,[[0,0,101]],[new RoleTrickVO(0,1.5,0,700),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20201,12,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,2,0,130),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20211,12,1,1,1,1,[[101,0]],[new RoleTrickVO(0,2,0,80),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20203,8,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,2,0,130),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20213,8,1,1,1,1,[[101,0]],[new RoleTrickVO(0,2,0,80),new RoleTrickVO(101,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20221,10,1,1,2,2,[[101,0,0,0]],[new RoleTrickVO(0,2,0,80),new RoleTrickVO(101,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20231,10,1,1,2,2,[[0,101,0,0]],[new RoleTrickVO(0,2,0,800),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20072,9,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,2,0,100),new RoleTrickVO(101,2,0,170)],true));
         this._aiVec.push(new RoleAIVO(20083,9,1,1,1,1,[[0,0,101]],[new RoleTrickVO(0,2,0,900),new RoleTrickVO(101,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20093,9,1,1,1,1,[[101,0,0,0]],[new RoleTrickVO(0,2,0,500),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20063,9,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,2,0,250),new RoleTrickVO(101,2,0.5,400)],false));
         this._aiVec.push(new RoleAIVO(20124,8,1,1,1,1,[[101,0,0]],[new RoleTrickVO(0,2,0,480),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20222,8,1,1,2,2,[[0,101,0]],[new RoleTrickVO(0,2,0,80),new RoleTrickVO(101,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20192,8,1,1,1,1,[[0,0,0,101]],[new RoleTrickVO(0,1.5,0,700),new RoleTrickVO(101,2,0,900)],true));
         this._aiVec.push(new RoleAIVO(20241,8,1,1,1,1,[[0,0,101]],[new RoleTrickVO(0,2,0,140),new RoleTrickVO(101,3,0,160)],true,true));
         this._aiVec.push(new RoleAIVO(20242,3,3,1,1,1,[[0,0,101]],[new RoleTrickVO(0,1,0,140),new RoleTrickVO(101,1,0,160)],true,true));
         this._aiVec.push(new RoleAIVO(20251,8,1,1,1,1,[[101,102]],[new RoleTrickVO(101,3,0,900),new RoleTrickVO(102,3.5,0,900)],false,true));
         this._aiVec.push(new RoleAIVO(20252,3,3,1,1,1,[[101,102]],[new RoleTrickVO(101,1,0,900),new RoleTrickVO(102,1,0,900)],false,true));
         this._aiVec.push(new RoleAIVO(20243,4,3,1,1,1,[[0,0,101]],[new RoleTrickVO(0,1,0,140),new RoleTrickVO(101,2,0,160)],true,true));
         this._aiVec.push(new RoleAIVO(20253,3,2,1,1,1,[[101,102]],[new RoleTrickVO(101,2,0,900),new RoleTrickVO(102,2.5,0,900)],false,true));
         this._aiVec.push(new RoleAIVO(20261,8,2.5,1.5,1.5,2,[[0,101,102,103,104,105,106]],[new RoleTrickVO(0,1,1,200),new RoleTrickVO(101,1.5,1,200),new RoleTrickVO(102,2,1,200),new RoleTrickVO(103,2.5,1,200),new RoleTrickVO(104,3,1,200),new RoleTrickVO(105,3,1,400),new RoleTrickVO(106,3,1,400)],false));
         this._aiVec.push(new RoleAIVO(20271,8,2.5,1.5,1.5,2,[[0]],[new RoleTrickVO(0,1,1,200)],false));
         this._aiVec.push(new RoleAIVO(20281,8,1,1.5,1.5,2,[[101,0,0]],[new RoleTrickVO(0,1.5,0,140),new RoleTrickVO(101,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20282,3,3,1.5,1.5,2,[[101,0,0]],[new RoleTrickVO(0,1,0,140),new RoleTrickVO(101,1,0,900)],false));
         this._aiVec.push(new RoleAIVO(20291,8,1,1.5,1.5,2,[[101,0,0]],[new RoleTrickVO(0,1,0,600),new RoleTrickVO(101,2,0,150)],true));
         this._aiVec.push(new RoleAIVO(20292,3,3,1.5,1.5,2,[[101,0,0]],[new RoleTrickVO(0,1,0,600),new RoleTrickVO(101,1,0,150)],true));
         this._aiVec.push(new RoleAIVO(20301,8,2.5,1.5,1.5,2,[[0,101,102,103,104,105,106]],[new RoleTrickVO(0,1,1,200),new RoleTrickVO(101,1.5,1,200),new RoleTrickVO(102,2,1,200),new RoleTrickVO(103,2.5,1,200),new RoleTrickVO(104,3,1,200),new RoleTrickVO(105,3,1,400),new RoleTrickVO(106,3,1,400)],false));
         this._aiVec.push(new RoleAIVO(20311,8,1,1,1,1,[[101,0,101]],[new RoleTrickVO(0,1,0,140),new RoleTrickVO(101,1,0,160)],false,true));
         this._aiVec.push(new RoleAIVO(20312,3,3,1,1,1,[[101,0,101]],[new RoleTrickVO(0,1,0,140),new RoleTrickVO(101,1,0,160)],false,true));
         this._aiVec.push(new RoleAIVO(20064,8,1,1,1,1,[[101,101,0]],[new RoleTrickVO(0,1.5,0,250),new RoleTrickVO(101,2,0.5,400)],true));
         this._aiVec.push(new RoleAIVO(20034,8,1,1,1,1,[[101,0,101]],[new RoleTrickVO(0,1.5,0,100),new RoleTrickVO(101,1.5,0,400)],false));
         this._aiVec.push(new RoleAIVO(20321,8,1,1,1,1,[[0]],[new RoleTrickVO(0,1.5,0,200),new RoleTrickVO(101,0.5,0,800)],false,true));
         this._aiVec.push(new RoleAIVO(20322,3,3,1,1,1,[[0]],[new RoleTrickVO(0,1.5,0,200),new RoleTrickVO(101,0.5,0,800)],false,true));
         this._aiVec.push(new RoleAIVO(20331,8,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,1,0,500),new RoleTrickVO(101,1,0,300)],true,true));
         this._aiVec.push(new RoleAIVO(20332,3,3,1,1,1,[[0,101,0]],[new RoleTrickVO(0,1,0,500),new RoleTrickVO(101,1,0,300)],true,true));
         this._aiVec.push(new RoleAIVO(20341,8,1,1,1,1,[[102,101,0,101]],[new RoleTrickVO(0,2,0,160),new RoleTrickVO(101,2,0,900),new RoleTrickVO(102,3,0,180)],true));
         this._aiVec.push(new RoleAIVO(20351,8,1,1,1,1,[[101,0,102,0]],[new RoleTrickVO(0,2,0,160),new RoleTrickVO(101,3,0,160),new RoleTrickVO(102,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(20361,8,1,1,0,0,[[0,101,0]],[new RoleTrickVO(0,0.5,0,160),new RoleTrickVO(101,0.5,0,800)],false));
         this._aiVec.push(new RoleAIVO(20362,3,3,1,0,0,[[0,101,0]],[new RoleTrickVO(0,0.5,0,160),new RoleTrickVO(101,0.5,0,800)],false));
         this._aiVec.push(new RoleAIVO(20371,8,1,1,0,0,[[0,0,101]],[new RoleTrickVO(0,0.5,0,160),new RoleTrickVO(101,0.5,0,160)],true));
         this._aiVec.push(new RoleAIVO(20372,3,3,1,0,0,[[0,0,101]],[new RoleTrickVO(0,0.5,0,160),new RoleTrickVO(101,0.5,0,160)],true));
         this._aiVec.push(new RoleAIVO(20381,8,1,1,1,1,[[101,0,104,0]],[new RoleTrickVO(0,3,0,260),new RoleTrickVO(101,3,0,800),new RoleTrickVO(104,3,0,300)],false));
         this._aiVec.push(new RoleAIVO(20391,8,1,1,1,1,[[106,103,101,0,104,102,105]],[new RoleTrickVO(0,3,0,30),new RoleTrickVO(101,3,0,350),new RoleTrickVO(102,3,0,300),new RoleTrickVO(103,3,0,700),new RoleTrickVO(104,3,0,450),new RoleTrickVO(105,3,0,150),new RoleTrickVO(106,3,0,600)],true));
         this._aiVec.push(new RoleAIVO(20401,8,1,1,1,1,[[104,103,106,101,105,0,102]],[new RoleTrickVO(0,3,0,30),new RoleTrickVO(101,3,0,350),new RoleTrickVO(102,3,0,300),new RoleTrickVO(103,3,0,700),new RoleTrickVO(104,3,0,450),new RoleTrickVO(105,3,0,150),new RoleTrickVO(106,3,0,600)],true));
         this._aiVec.push(new RoleAIVO(20411,8,1,1,1,1,[[101,0]],[new RoleTrickVO(0,0.5,0,160),new RoleTrickVO(101,1.5,0,350)],false));
         this._aiVec.push(new RoleAIVO(20412,3,3,1,1,1,[[101,0]],[new RoleTrickVO(0,0.5,0,160),new RoleTrickVO(101,0.5,0,350)],false));
         this._aiVec.push(new RoleAIVO(20421,8,1,1,1,1,[[0,101,0,0,0]],[new RoleTrickVO(0,0.5,0,160),new RoleTrickVO(101,1,0,160)],true));
         this._aiVec.push(new RoleAIVO(20431,8,1,1,1,1,[[0,0,0,101]],[new RoleTrickVO(0,0.5,0,300),new RoleTrickVO(101,1,0,960)],true));
         this._aiVec.push(new RoleAIVO(20461,5,3,0.5,2,2,[[0,0,101]],[new RoleTrickVO(0,2,0.5,350),new RoleTrickVO(101,1,0.5,700)],false));
         this._aiVec.push(new RoleAIVO(20313,8,1,1,1,1,[[0,101,0]],[new RoleTrickVO(0,1,0,140),new RoleTrickVO(101,1,0,160)],false,true));
         this._aiVec.push(new RoleAIVO(20471,5,3,0.5,2,2,[[0,0,101]],[new RoleTrickVO(0,2,0.5,120),new RoleTrickVO(101,1,0.5,300)],false));
         this._aiVec.push(new RoleAIVO(20481,5,3,0.5,1,1,[[0,0,101]],[new RoleTrickVO(0,2,0.5,120),new RoleTrickVO(101,1,0.5,300)],false));
         this._aiVec.push(new RoleAIVO(20422,8,1,1,1,1,[[0,101,0,0]],[new RoleTrickVO(0,1,0,160),new RoleTrickVO(101,1.5,0,160)],true));
         this._aiVec.push(new RoleAIVO(20372,3,1,1,0,0,[[0,0,101]],[new RoleTrickVO(0,0.5,0,160),new RoleTrickVO(101,0.5,0,160)],false));
         this._aiVec.push(new RoleAIVO(20462,1,3,0.5,2,2,[[0,101,0]],[new RoleTrickVO(0,1,0.5,350),new RoleTrickVO(101,1,0.5,700)],false));
         this._aiVec.push(new RoleAIVO(20441,5,3,0.5,2,2,[[103,0,102]],[new RoleTrickVO(0,2,0.5,200),new RoleTrickVO(102,3,0.5,800),new RoleTrickVO(103,2,0.5,400)],false));
         this._aiVec.push(new RoleAIVO(20451,5,3,0.5,2,2,[[103,0,105,101,102]],[new RoleTrickVO(0,2,0.5,200),new RoleTrickVO(101,2,0.5,800),new RoleTrickVO(102,3,0.5,800),new RoleTrickVO(103,2,0.5,400),new RoleTrickVO(105,2,0.5,800)],false));
         this._aiVec.push(new RoleAIVO(20491,8,1,1,1,1,[[103,101,0,104,102,105]],[new RoleTrickVO(0,1,0,30),new RoleTrickVO(101,1,0,350),new RoleTrickVO(102,1,0,300),new RoleTrickVO(103,2,0,700),new RoleTrickVO(104,1,0,450),new RoleTrickVO(105,2,0,150)],true));
         this._aiVec.push(new RoleAIVO(20482,5,3,0.5,2,2,[[0,0,101]],[new RoleTrickVO(0,2,0.5,120),new RoleTrickVO(101,1,0.5,300)],false));
         this._aiVec.push(new RoleAIVO(20501,8,1,1,1,1,[[101,0]],[new RoleTrickVO(0,1,0,400),new RoleTrickVO(101,2,0,600)],false));
         this._aiVec.push(new RoleAIVO(20511,8,1,1,1,1,[[101,0]],[new RoleTrickVO(0,1,0,400),new RoleTrickVO(101,2,0,600)],false));
         this._aiVec.push(new RoleAIVO(20521,8,1,1,1,1,[[0]],[new RoleTrickVO(0,1,0,50)],false));
         this._aiVec.push(new RoleAIVO(30011,20,1,1,2,3,[[0]],[new RoleTrickVO(0,3,0.5,210)],false));
         this._aiVec.push(new RoleAIVO(30021,10,1,1,1,2,[[0,1,101]],[new RoleTrickVO(0,2,0.5,212),new RoleTrickVO(1,2,0.5,250),new RoleTrickVO(101,2,0.5,500)],false));
         this._aiVec.push(new RoleAIVO(30022,10,1,1,1,2,[[101,1,0]],[new RoleTrickVO(0,1,0.5,212),new RoleTrickVO(1,1,0.5,250),new RoleTrickVO(101,1,0.5,500)],false));
         this._aiVec.push(new RoleAIVO(30023,5,3,1,1,1,[[101,1,0]],[new RoleTrickVO(0,1,0.5,212),new RoleTrickVO(1,1,0.5,250),new RoleTrickVO(101,1,0.5,500)],false));
         this._aiVec.push(new RoleAIVO(30031,15,2,1,2,3,[[102,0,0,1,101]],[new RoleTrickVO(0,2,0.5,120),new RoleTrickVO(1,2,1,450),new RoleTrickVO(101,2,0.5,300),new RoleTrickVO(102,2,0.5,600)],true));
         this._aiVec.push(new RoleAIVO(30032,15,2,1,2,3,[[1,0,102,1,101]],[new RoleTrickVO(0,1,0.5,120),new RoleTrickVO(1,1,1,450),new RoleTrickVO(101,1,0.5,300),new RoleTrickVO(102,1,0.5,600)],true));
         this._aiVec.push(new RoleAIVO(30033,10,2,1,2,3,[[102,0,101,1,102]],[new RoleTrickVO(0,1,0.5,120),new RoleTrickVO(1,1,1,450),new RoleTrickVO(101,1,0.5,300),new RoleTrickVO(102,1,0.5,600)],true));
         this._aiVec.push(new RoleAIVO(30034,5,3,1,1,1,[[102,0,101,1,102]],[new RoleTrickVO(0,1,0.5,120),new RoleTrickVO(1,1,1,450),new RoleTrickVO(101,1,0.5,300),new RoleTrickVO(102,1,0.5,600)],true));
         this._aiVec.push(new RoleAIVO(30041,15,3,1,2,3,[[0,101,0,102]],[new RoleTrickVO(0,2,0.5,150),new RoleTrickVO(1,2,0.5,450),new RoleTrickVO(101,2,0.5,500),new RoleTrickVO(102,2,0.5,600)],false));
         this._aiVec.push(new RoleAIVO(30042,15,3,1,2,3,[[101,0,102,101]],[new RoleTrickVO(0,3,0.5,150),new RoleTrickVO(1,3,0.5,450),new RoleTrickVO(101,3,0.5,500),new RoleTrickVO(102,3,0.5,600)],false));
         this._aiVec.push(new RoleAIVO(30043,15,3,1,2,3,[[102,0,101,102]],[new RoleTrickVO(0,3,0.5,150),new RoleTrickVO(1,3,0.5,450),new RoleTrickVO(101,3,0.5,500),new RoleTrickVO(102,3,0.5,600)],false));
         this._aiVec.push(new RoleAIVO(30044,5,3,1,1,1,[[102,0,101,102]],[new RoleTrickVO(0,3,0.5,150),new RoleTrickVO(1,3,0.5,450),new RoleTrickVO(101,3,0.5,500),new RoleTrickVO(102,3,0.5,600)],false));
         this._aiVec.push(new RoleAIVO(30071,15,3,1,2,3,[[500,500]],[new RoleTrickVO(0,3,0.5,360),new RoleTrickVO(1,3,0.5,500)],false));
         this._aiVec.push(new RoleAIVO(30081,15,3,1,2,3,[[0,1,2,3]],[new RoleTrickVO(0,3,0.5,360),new RoleTrickVO(1,3,0.5,360),new RoleTrickVO(2,3,0.5,360),new RoleTrickVO(3,3,0.5,360)],false));
         this._aiVec.push(new RoleAIVO(30091,15,3,1,2,3,[[0,101,0,101,102]],[new RoleTrickVO(0,4,0.5,500),new RoleTrickVO(101,4,0.5,250),new RoleTrickVO(102,4,0.5,400)],false));
         this._aiVec.push(new RoleAIVO(30101,10,1,1,2,3,[[1,2,1,101,102,103,0]],[new RoleTrickVO(0,3,0.5,120),new RoleTrickVO(1,3,0.5,960),new RoleTrickVO(2,3,0.5,960),new RoleTrickVO(101,3,0.5,120),new RoleTrickVO(102,3,0.5,960),new RoleTrickVO(103,3,0.5,360)],false));
         this._aiVec.push(new RoleAIVO(30102,5,3,1,1,1,[[1,2,1,101,102,103,0]],[new RoleTrickVO(0,3,0.5,120),new RoleTrickVO(1,3,0.5,960),new RoleTrickVO(2,3,0.5,960),new RoleTrickVO(101,3,0.5,120),new RoleTrickVO(102,3,0.5,960),new RoleTrickVO(103,3,0.5,360)],false));
         this._aiVec.push(new RoleAIVO(30051,10,1,1,2,3,[[0,1,102,0,101]],[new RoleTrickVO(0,2,0.5,200),new RoleTrickVO(1,2,0.5,280),new RoleTrickVO(101,2,0.5,800),new RoleTrickVO(102,2,0.5,960)],false));
         this._aiVec.push(new RoleAIVO(30052,5,3,1,1,1,[[0,1,102,0,101]],[new RoleTrickVO(0,2,0.5,200),new RoleTrickVO(1,2,0.5,280),new RoleTrickVO(101,2,0.5,800),new RoleTrickVO(102,2,0.5,960)],false));
         this._aiVec.push(new RoleAIVO(30061,10,1,1,2,3,[[1,102,0,101,1]],[new RoleTrickVO(0,2,0.5,600),new RoleTrickVO(1,2,0.5,500),new RoleTrickVO(101,2,0.5,800),new RoleTrickVO(102,2,0.5,960)],false));
         this._aiVec.push(new RoleAIVO(30062,5,3,1,1,1,[[1,102,0,101,1]],[new RoleTrickVO(0,2,0.5,600),new RoleTrickVO(1,2,0.5,500),new RoleTrickVO(101,2,0.5,800),new RoleTrickVO(102,2,0.5,960)],false));
         this._aiVec.push(new RoleAIVO(30111,10,1,1,2,3,[[101,102,102,101,103]],[new RoleTrickVO(101,3,0.5,1400),new RoleTrickVO(1020,2,0.5,1400),new RoleTrickVO(1021,2,0.5,1400),new RoleTrickVO(103,3,0.5,1400)],false));
         this._aiVec.push(new RoleAIVO(30121,15,5,1,1,1,[[0,2,101,0,102,1,0,104]],[new RoleTrickVO(0,3,0.5,100),new RoleTrickVO(1,3,0.5,100),new RoleTrickVO(2,3,0.5,100),new RoleTrickVO(101,3,0.5,250),new RoleTrickVO(102,3,0.5,1400),new RoleTrickVO(103,3,0.5,280),new RoleTrickVO(104,3,0.5,1400),new RoleTrickVO(105,3,0.5,1400)],false));
         this._aiVec.push(new RoleAIVO(30122,10,6,1.5,1,2,[[101,2,101,0,102,1,0,104]],[new RoleTrickVO(0,2,0.5,100),new RoleTrickVO(1,2,0.5,100),new RoleTrickVO(2,2,0.5,100),new RoleTrickVO(101,2,0.5,250),new RoleTrickVO(102,2,0.5,1400),new RoleTrickVO(103,2,0.5,280),new RoleTrickVO(104,2,0.5,1400),new RoleTrickVO(105,2,0.5,1400)],false));
         this._aiVec.push(new RoleAIVO(30123,10,7,2,2,2,[[103,2,101,0,102,1,0,104]],[new RoleTrickVO(0,1.5,0.5,100),new RoleTrickVO(1,1.5,0.5,100),new RoleTrickVO(2,1.5,0.5,100),new RoleTrickVO(101,1.5,0.5,250),new RoleTrickVO(102,1.5,0.5,1400),new RoleTrickVO(103,1.5,0.5,280),new RoleTrickVO(104,1.5,0.5,1400),new RoleTrickVO(105,1.5,0.5,1400)],false));
         this._aiVec.push(new RoleAIVO(30124,8,5,2,2,3,[[102,2,101,0,103,1,0,104,105]],[new RoleTrickVO(0,1,0.5,100),new RoleTrickVO(1,1,0.5,100),new RoleTrickVO(2,1,0.5,100),new RoleTrickVO(101,1,0.5,250),new RoleTrickVO(102,1,0.5,1400),new RoleTrickVO(103,1,0.5,280),new RoleTrickVO(104,1,0.5,1400),new RoleTrickVO(105,1,0.5,1400)],false));
         this._aiVec.push(new RoleAIVO(30131,15,3,0.5,2,3,[[103,1,102,101,0]],[new RoleTrickVO(0,2.5,0.5,180),new RoleTrickVO(1,2.5,0,190),new RoleTrickVO(101,2.5,0.5,190),new RoleTrickVO(102,2.5,0.5,170),new RoleTrickVO(103,2.5,0.5,500)],false));
         this._aiVec.push(new RoleAIVO(30132,5,3,0.5,2,3,[[103,1,102,101,0]],[new RoleTrickVO(0,2.5,0.5,180),new RoleTrickVO(1,2.5,0,190),new RoleTrickVO(101,2.5,0.5,190),new RoleTrickVO(102,2.5,0.5,170),new RoleTrickVO(103,2.5,0.5,500)],false));
         this._aiVec.push(new RoleAIVO(30141,10,5,0.5,2,3,[[102,103,0,0]],[new RoleTrickVO(0,2,0,170),new RoleTrickVO(101,2,0,500),new RoleTrickVO(102,2.5,0,500),new RoleTrickVO(103,2.5,0,120)],false));
         this._aiVec.push(new RoleAIVO(30142,5,3,0.5,2,3,[[102,103,0,0]],[new RoleTrickVO(0,2,0,170),new RoleTrickVO(101,2,0,500),new RoleTrickVO(102,2.5,0,500),new RoleTrickVO(103,2.5,0,120)],false));
         this._aiVec.push(new RoleAIVO(30151,12,2,1,1,2,[[1,101,0,0,102]],[new RoleTrickVO(0,2,0,300),new RoleTrickVO(1,2,0,200),new RoleTrickVO(101,2,0,300),new RoleTrickVO(102,2,0,900),new RoleTrickVO(103,2,0,900),new RoleTrickVO(104,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(30152,8,1,1,1,2,[[1,101,0,0,102]],[new RoleTrickVO(0,2,0,300),new RoleTrickVO(1,2,0,200),new RoleTrickVO(101,2,0,300),new RoleTrickVO(102,2,0,900),new RoleTrickVO(103,2,0,900),new RoleTrickVO(104,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(30161,7,2,1,1,2,[[102,101,1,103,0,102,105,103,104]],[new RoleTrickVO(0,2,0,90),new RoleTrickVO(1,2,0,90),new RoleTrickVO(101,2,0,900),new RoleTrickVO(102,2,0,300),new RoleTrickVO(103,2,0,900),new RoleTrickVO(104,2,0,360),new RoleTrickVO(105,2,0,210)],false));
         this._aiVec.push(new RoleAIVO(30162,7,2,1,1,2,[[1,101,102,103,105,0,104]],[new RoleTrickVO(0,2,0,90),new RoleTrickVO(1,2,0,90),new RoleTrickVO(101,2,0,900),new RoleTrickVO(102,2,0,300),new RoleTrickVO(103,2,0,900),new RoleTrickVO(104,2,0,360),new RoleTrickVO(105,2,0,250)],false));
         this._aiVec.push(new RoleAIVO(30163,5,3,1,1,2,[[105,0,102,104,101,1,104]],[new RoleTrickVO(0,1,0,90),new RoleTrickVO(1,2,0,90),new RoleTrickVO(101,1.5,0,900),new RoleTrickVO(102,1.5,0,300),new RoleTrickVO(103,1.5,0,900),new RoleTrickVO(104,1.5,0,360),new RoleTrickVO(105,1.5,0,250)],false));
         this._aiVec.push(new RoleAIVO(30164,5,3,1,1,2,[[1,101,102,103,105,0,104]],[new RoleTrickVO(0,1,0,90),new RoleTrickVO(1,1,0,90),new RoleTrickVO(101,0.5,0,900),new RoleTrickVO(102,0.5,0,300),new RoleTrickVO(103,0.5,0,900),new RoleTrickVO(104,0.5,0,360),new RoleTrickVO(105,0.5,0,250)],false));
         this._aiVec.push(new RoleAIVO(30171,12,3,1,1,2,[[102,0,0,101]],[new RoleTrickVO(0,2,0,90),new RoleTrickVO(101,2,0,400),new RoleTrickVO(102,2,0,300)],false));
         this._aiVec.push(new RoleAIVO(30181,12,3,1,1,2,[[103,102,101,102]],[new RoleTrickVO(101,2.5,0,900),new RoleTrickVO(102,2.5,0,300),new RoleTrickVO(103,2.5,0,300)],false));
         this._aiVec.push(new RoleAIVO(30191,11,3,1,1,2,[[0,0,102,0,101]],[new RoleTrickVO(0,2.5,0,168),new RoleTrickVO(101,2.5,0,200),new RoleTrickVO(102,2.5,0,320)],false));
         this._aiVec.push(new RoleAIVO(30201,11,3,1,1,2,[[0,0,0,101,0,0,102]],[new RoleTrickVO(0,2,0,900),new RoleTrickVO(101,5,0,900),new RoleTrickVO(102,5,0,900)],true));
         this._aiVec.push(new RoleAIVO(30202,11,1,1,1,2,[[0,0,0,101,0,0,0,102]],[new RoleTrickVO(0,3,0,900),new RoleTrickVO(101,4,0,300),new RoleTrickVO(102,4,0,300)],true));
         this._aiVec.push(new RoleAIVO(30211,11,3,1,1,2,[[101,102,0,0,101]],[new RoleTrickVO(0,2,0,170),new RoleTrickVO(101,3,0,700),new RoleTrickVO(102,3,0,900)],false));
         this._aiVec.push(new RoleAIVO(30212,11,5,1,1,2,[[101,102,0,0,0]],[new RoleTrickVO(0,3,0,170),new RoleTrickVO(101,3,0,700),new RoleTrickVO(102,3,0,900)],false));
         this._aiVec.push(new RoleAIVO(30221,7,3,1,1,2,[[101,102,104,103,102]],[new RoleTrickVO(101,6,0,900),new RoleTrickVO(102,6,0,900),new RoleTrickVO(103,6,0,900),new RoleTrickVO(104,6,0,900)],false));
         this._aiVec.push(new RoleAIVO(30231,7,5,1,1,2,[[1,101,0,0,102,1,104]],[new RoleTrickVO(0,2,0,178),new RoleTrickVO(1,2,0,210),new RoleTrickVO(101,2,0,900),new RoleTrickVO(102,2,0,900),new RoleTrickVO(103,2,0,900),new RoleTrickVO(104,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(30241,8,3,1,1,2,[[101,102,103,0,102]],[new RoleTrickVO(0,2,0,178),new RoleTrickVO(101,2,0,900),new RoleTrickVO(102,2,0,900),new RoleTrickVO(103,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(30251,8,3,1,1,2,[[103,0,104,101,102,0]],[new RoleTrickVO(0,2,0,178),new RoleTrickVO(101,2,0,900),new RoleTrickVO(102,2,0,900),new RoleTrickVO(103,2,0,900),new RoleTrickVO(104,2.5,0,900)],false));
         this._aiVec.push(new RoleAIVO(30261,8,3,1,1,2,[[101,1,0,102,103]],[new RoleTrickVO(0,2.5,0,178),new RoleTrickVO(1,2.5,0,178),new RoleTrickVO(101,2.5,0,150),new RoleTrickVO(102,2.5,0,900),new RoleTrickVO(103,2.5,0,900)],false));
         this._aiVec.push(new RoleAIVO(30271,8,3,1,1,2,[[101,102,103,104]],[new RoleTrickVO(101,2.5,0,178),new RoleTrickVO(102,2.5,0,900),new RoleTrickVO(103,2.5,0,900),new RoleTrickVO(104,2.5,0,900)],false));
         this._aiVec.push(new RoleAIVO(30281,8,3,1,1,2,[[102,0,1,103,101]],[new RoleTrickVO(0,1,0,178),new RoleTrickVO(1,0.5,0,210),new RoleTrickVO(101,1.5,0,178),new RoleTrickVO(102,1.5,0,900),new RoleTrickVO(103,1.5,0,900)],false));
         this._aiVec.push(new RoleAIVO(30282,8,3,1,1,2,[[102,0,1,103,101]],[new RoleTrickVO(0,1,0,178),new RoleTrickVO(1,1.5,0,210),new RoleTrickVO(101,2,0,178),new RoleTrickVO(102,2,0,900),new RoleTrickVO(103,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(30291,5,3,1,1,2,[[1,101,0,103,102,0,1]],[new RoleTrickVO(0,1.5,0,178),new RoleTrickVO(1,1.5,0,210),new RoleTrickVO(101,2.5,0,178),new RoleTrickVO(102,2.5,0,900),new RoleTrickVO(103,2.5,0,900)],false));
         this._aiVec.push(new RoleAIVO(30292,5,3,1,1,2,[[1,101,0,103,102,0,1]],[new RoleTrickVO(0,0.6,0,178),new RoleTrickVO(1,0.6,0,210),new RoleTrickVO(101,0.6,0,178),new RoleTrickVO(102,0.6,0,900),new RoleTrickVO(103,0.6,0,900)],false));
         this._aiVec.push(new RoleAIVO(30301,5,3,1,1,2,[[103,101,102,105,103,104,103]],[new RoleTrickVO(101,3.5,0,960),new RoleTrickVO(102,3.5,0,960),new RoleTrickVO(103,3.5,0,960),new RoleTrickVO(104,3.5,0,960),new RoleTrickVO(105,3.5,0,960)],false));
         this._aiVec.push(new RoleAIVO(30311,5,3,1,1,2,[[102,0,101,1,105,103,2,104]],[new RoleTrickVO(0,2,0,178),new RoleTrickVO(1,2,0,210),new RoleTrickVO(2,2,0,178),new RoleTrickVO(101,2,0,300),new RoleTrickVO(102,2,0,120),new RoleTrickVO(103,2,0,960),new RoleTrickVO(104,2,0,200),new RoleTrickVO(105,2,0,350)],false));
         this._aiVec.push(new RoleAIVO(30312,5,3,1,1,2,[[103,105,2,1,101,102,104,0]],[new RoleTrickVO(0,1,0,178),new RoleTrickVO(1,1.5,0,210),new RoleTrickVO(2,1.5,0,178),new RoleTrickVO(101,2,0,300),new RoleTrickVO(102,2,0,120),new RoleTrickVO(103,2,0,960),new RoleTrickVO(104,2,0,200),new RoleTrickVO(105,2,0,350)],false));
         this._aiVec.push(new RoleAIVO(30313,5,3,1,1,2,[[105,104,2,1,102,103,104,0]],[new RoleTrickVO(0,1,0,178),new RoleTrickVO(1,1,0,210),new RoleTrickVO(2,1,0,178),new RoleTrickVO(101,1,0,300),new RoleTrickVO(102,1,0,120),new RoleTrickVO(103,1,0,960),new RoleTrickVO(104,1,0,200),new RoleTrickVO(105,1,0,350)],false));
         this._aiVec.push(new RoleAIVO(30314,5,3,1,1,2,[[104,105,102,1,2,104,0,103]],[new RoleTrickVO(0,1,0,178),new RoleTrickVO(1,0.5,0,210),new RoleTrickVO(2,0.5,0,178),new RoleTrickVO(101,0.5,0,300),new RoleTrickVO(102,0.5,0,120),new RoleTrickVO(103,0.5,0,960),new RoleTrickVO(104,0.5,0,200),new RoleTrickVO(105,0.5,0,350)],false));
         this._aiVec.push(new RoleAIVO(30321,3,3,1,1,2,[[102,0,103,0,101]],[new RoleTrickVO(0,1,0,178),new RoleTrickVO(101,2.5,0,300),new RoleTrickVO(102,2.5,0,120),new RoleTrickVO(103,2.5,0,960)],false));
         this._aiVec.push(new RoleAIVO(30322,3,3,1,1,2,[[102,0,103,0,101]],[new RoleTrickVO(0,1.5,0,178),new RoleTrickVO(101,1.5,0,300),new RoleTrickVO(102,1.5,0,120),new RoleTrickVO(103,1.5,0,960)],false));
         this._aiVec.push(new RoleAIVO(30322,3,3,1,1,2,[[0]],[new RoleTrickVO(0,2,0,178)],false));
         this._aiVec.push(new RoleAIVO(30331,3,3,1,1,2,[[104,103,102,101,103]],[new RoleTrickVO(101,3,0,178),new RoleTrickVO(102,4,0,300),new RoleTrickVO(103,3,0,120),new RoleTrickVO(104,3,0,960)],false));
         this._aiVec.push(new RoleAIVO(30332,3,3,1,1,2,[[104,103,102,101,103]],[new RoleTrickVO(101,0.5,0,178),new RoleTrickVO(102,0.5,0,300),new RoleTrickVO(103,0.5,0,120),new RoleTrickVO(104,0.5,0,960)],false));
         this._aiVec.push(new RoleAIVO(30341,3,3,1,1,2,[[103,104,102,1,101,0]],[new RoleTrickVO(0,1,0,178),new RoleTrickVO(1,1,0,178),new RoleTrickVO(101,3,0,300),new RoleTrickVO(102,3,0,120),new RoleTrickVO(103,3,0,960),new RoleTrickVO(104,3,0,960)],false));
         this._aiVec.push(new RoleAIVO(30342,3,3,1,1,2,[[103,104,102,1,101,0]],[new RoleTrickVO(0,0.5,0,178),new RoleTrickVO(1,0.5,0,178),new RoleTrickVO(101,0.5,0,300),new RoleTrickVO(102,0.5,0,120),new RoleTrickVO(103,0.5,0,960),new RoleTrickVO(104,0.5,0,960)],false));
         this._aiVec.push(new RoleAIVO(30351,3,3,1,1,2,[[101,103,105,102,104,0]],[new RoleTrickVO(0,1.5,0,125),new RoleTrickVO(101,1.5,0,960),new RoleTrickVO(102,1.5,0,500),new RoleTrickVO(103,2,0,800),new RoleTrickVO(104,1.5,0,400),new RoleTrickVO(105,2,0,700)],false));
         this._aiVec.push(new RoleAIVO(30352,3,3,1,1,2,[[101,103,105,102,104,0]],[new RoleTrickVO(0,0.5,0,125),new RoleTrickVO(101,0.5,0,960),new RoleTrickVO(102,0.5,0,500),new RoleTrickVO(103,0.5,0,800),new RoleTrickVO(104,0.5,0,400),new RoleTrickVO(105,0.5,0,700)],false));
         this._aiVec.push(new RoleAIVO(30361,3,3,1,1,2,[[104,101,104,102,101,103,0]],[new RoleTrickVO(0,1,0,125),new RoleTrickVO(101,1,0,800),new RoleTrickVO(102,1.5,0,300),new RoleTrickVO(103,1.5,0,400),new RoleTrickVO(104,1,0,700)],false));
         this._aiVec.push(new RoleAIVO(30362,3,3,1,1,2,[[104,101,104,102,101,103,0]],[new RoleTrickVO(0,0.5,0,125),new RoleTrickVO(101,0.5,0,800),new RoleTrickVO(102,0.5,0,300),new RoleTrickVO(103,0.5,0,400),new RoleTrickVO(104,0.5,0,700)],false));
         this._aiVec.push(new RoleAIVO(30371,3,3,1,1,2,[[101,102,103,104,105]],[new RoleTrickVO(101,1,0,800),new RoleTrickVO(102,2,0,150),new RoleTrickVO(103,1,0,350),new RoleTrickVO(104,2,0,700),new RoleTrickVO(105,2,0,250)],false));
         this._aiVec.push(new RoleAIVO(30372,3,3,1,1,2,[[101,102,103,104,105]],[new RoleTrickVO(101,1.6,0,800),new RoleTrickVO(102,1,0,150),new RoleTrickVO(103,1.5,0,350),new RoleTrickVO(104,1.6,0,700),new RoleTrickVO(105,2,0,250)],false));
         this._aiVec.push(new RoleAIVO(30381,3,3,1,1,2,[[101,0,104,0,102,0,104,0,103]],[new RoleTrickVO(0,0.8,0,200),new RoleTrickVO(101,1.5,0,800),new RoleTrickVO(102,1.5,0,400),new RoleTrickVO(103,1,0,900),new RoleTrickVO(104,1.5,0,300)],false));
         this._aiVec.push(new RoleAIVO(30382,3,3,1,1,2,[[101,0,104,0,102,0,104,0,103]],[new RoleTrickVO(0,0.8,0,200),new RoleTrickVO(101,0.5,0,800),new RoleTrickVO(102,0.5,0,400),new RoleTrickVO(103,0.5,0,900),new RoleTrickVO(104,0.5,0,300)],false));
         this._aiVec.push(new RoleAIVO(30391,3,3,1,1,2,[[101,0,104,0,102,0,104,0,103]],[new RoleTrickVO(0,1.5,0,200),new RoleTrickVO(101,1.5,0,800),new RoleTrickVO(102,1.5,0,400),new RoleTrickVO(103,1,0,700),new RoleTrickVO(104,1.5,0,270)],false));
         this._aiVec.push(new RoleAIVO(30392,3,3,1,1,2,[[101,0,104,0,102,0,104,0,103]],[new RoleTrickVO(0,1,0,200),new RoleTrickVO(101,1,0,800),new RoleTrickVO(102,1,0,400),new RoleTrickVO(103,1,0,700),new RoleTrickVO(104,1.5,0,270)],false));
         this._aiVec.push(new RoleAIVO(30401,3,3,1,1,2,[[101,0,104,0,102,0,104,0,103]],[new RoleTrickVO(0,1.5,0,200),new RoleTrickVO(101,1,0,800),new RoleTrickVO(102,1.5,0,400),new RoleTrickVO(103,1,0,800),new RoleTrickVO(104,1,0,300)],false));
         this._aiVec.push(new RoleAIVO(30402,3,3,1,1,2,[[101,0,104,0,102,0,104,0,103]],[new RoleTrickVO(0,1,0,200),new RoleTrickVO(101,1,0,800),new RoleTrickVO(102,1,0,400),new RoleTrickVO(103,1,0,800),new RoleTrickVO(104,1,0,300)],false));
         this._aiVec.push(new RoleAIVO(30411,3,3,1,1,2,[[101,0,104,0,102,0,104,0,103]],[new RoleTrickVO(0,1,0,600),new RoleTrickVO(101,1,0,800),new RoleTrickVO(102,0.8,0,300),new RoleTrickVO(103,1,0,600),new RoleTrickVO(104,0.8,0,300)],false));
         this._aiVec.push(new RoleAIVO(30421,3,3,1,1,2,[[101,0,102,0,0,103,0]],[new RoleTrickVO(0,1.5,0,150),new RoleTrickVO(101,1.5,0,300),new RoleTrickVO(102,1.5,0,300),new RoleTrickVO(103,1.5,0,300)],true));
         this._aiVec.push(new RoleAIVO(30422,3,3,1,1,2,[[101,0,102,0,0,103,0]],[new RoleTrickVO(0,1,0,150),new RoleTrickVO(101,1,0,300),new RoleTrickVO(102,1,0,300),new RoleTrickVO(103,1,0,300)],true));
         this._aiVec.push(new RoleAIVO(30431,3,3,1,1,2,[[102,101,103,0,104,0,102]],[new RoleTrickVO(0,1,0,150),new RoleTrickVO(101,1.5,0,300),new RoleTrickVO(102,1.5,0,300),new RoleTrickVO(103,2,0,300),new RoleTrickVO(104,1.5,0,150)],true));
         this._aiVec.push(new RoleAIVO(30432,3,3,1,1,2,[[102,101,103,0,104,0,102]],[new RoleTrickVO(0,1,0,150),new RoleTrickVO(101,1,0,300),new RoleTrickVO(102,1,0,300),new RoleTrickVO(103,1,0,300),new RoleTrickVO(104,1,0,150)],true));
         this._aiVec.push(new RoleAIVO(30441,3,3,1,1,2,[[102,101,103,0,104,0,102]],[new RoleTrickVO(0,1.5,0,150),new RoleTrickVO(101,1.5,0,200),new RoleTrickVO(102,1.5,0,300),new RoleTrickVO(103,1.5,0,210),new RoleTrickVO(104,2,0,600)],true));
         this._aiVec.push(new RoleAIVO(30442,3,3,1,1,2,[[102,101,103,0,104,0,102]],[new RoleTrickVO(0,1,0,150),new RoleTrickVO(101,1,0,200),new RoleTrickVO(102,1,0,300),new RoleTrickVO(103,1,0,210),new RoleTrickVO(104,1,0,600)],true));
         this._aiVec.push(new RoleAIVO(30451,3,3,1,1,2,[[102,101,103,0,104,0,102]],[new RoleTrickVO(0,1,0,150),new RoleTrickVO(101,2,0,500),new RoleTrickVO(102,1.5,0,300),new RoleTrickVO(103,1.5,0,150),new RoleTrickVO(104,1,0,600)],false));
         this._aiVec.push(new RoleAIVO(30452,3,3,1,1,2,[[102,101,103,0,104,0,102]],[new RoleTrickVO(0,1,0,150),new RoleTrickVO(101,1,0,500),new RoleTrickVO(102,1,0,300),new RoleTrickVO(103,1,0,150),new RoleTrickVO(104,1,0,600)],false));
         this._aiVec.push(new RoleAIVO(30461,3,2,1,1,2,[[102,104,103,101,102]],[new RoleTrickVO(0,1,0,700),new RoleTrickVO(101,1.5,0,700),new RoleTrickVO(102,1,0,700),new RoleTrickVO(103,1,0,700),new RoleTrickVO(104,1.5,0,600)],false));
         this._aiVec.push(new RoleAIVO(30462,3,2,1,1,2,[[102,104,103,101,102]],[new RoleTrickVO(0,1,0,700),new RoleTrickVO(101,1,0,700),new RoleTrickVO(102,1,0,700),new RoleTrickVO(103,1,0,700),new RoleTrickVO(104,1,0,600)],false));
         this._aiVec.push(new RoleAIVO(30471,3,2,1,1,2,[[103,101,104,101,102]],[new RoleTrickVO(0,1,0,150),new RoleTrickVO(101,1.5,0,400),new RoleTrickVO(102,1.5,0,300),new RoleTrickVO(103,1,0,480),new RoleTrickVO(104,1,0,700)],false));
         this._aiVec.push(new RoleAIVO(30472,3,2,1,1,2,[[103,101,104,101,102]],[new RoleTrickVO(0,1,0,150),new RoleTrickVO(101,1,0,400),new RoleTrickVO(102,1.5,0,300),new RoleTrickVO(103,1,0,480),new RoleTrickVO(104,1,0,700)],false));
         this._aiVec.push(new RoleAIVO(30481,3,2,1,1,2,[[0,103,101,102]],[new RoleTrickVO(0,1,0,150),new RoleTrickVO(101,2.5,0,400),new RoleTrickVO(102,2,0,300),new RoleTrickVO(103,1.5,0,480)],false));
         this._aiVec.push(new RoleAIVO(30491,3,2,1,1,2,[[0,101,0,102,0,103]],[new RoleTrickVO(0,2,0,150),new RoleTrickVO(101,1,0,400),new RoleTrickVO(102,2,0,300),new RoleTrickVO(103,2.5,0,480)],true));
         this._aiVec.push(new RoleAIVO(30501,3,2,1,1,2,[[102,0,0,101,0,0,102]],[new RoleTrickVO(0,1.5,0,150),new RoleTrickVO(101,3,0,400),new RoleTrickVO(102,1.5,0,300),new RoleTrickVO(103,2.5,0,480)],false));
         this._aiVec.push(new RoleAIVO(30511,3,2,1,1,2,[[102,101,101,0,102,0,101,101]],[new RoleTrickVO(0,1.5,0,850),new RoleTrickVO(101,1,0,800),new RoleTrickVO(102,2,0,900)],false));
         this._aiVec.push(new RoleAIVO(30521,3,2,1,1,2,[[101,103,101,102,102,0,101,103]],[new RoleTrickVO(0,2,0,450),new RoleTrickVO(101,1.5,0,200),new RoleTrickVO(102,2,0,600),new RoleTrickVO(103,1.5,0,600)],false));
         this._aiVec.push(new RoleAIVO(30531,3,2,1,1,2,[[102,101,101,103,101,101]],[new RoleTrickVO(101,1,0,200),new RoleTrickVO(102,2,0,600),new RoleTrickVO(103,2,0,600)],false));
         this._aiVec.push(new RoleAIVO(30541,3,2,1,1,2,[[101,102,101,103]],[new RoleTrickVO(101,1,0,200),new RoleTrickVO(102,2,0,600),new RoleTrickVO(103,2.5,0,600)],false));
         this._aiVec.push(new RoleAIVO(20531,3,2,1,1,2,[[101,103,104]],[new RoleTrickVO(101,2.5,0,400),new RoleTrickVO(103,2.5,0,600),new RoleTrickVO(104,2.5,0,600)],false));
         this._aiVec.push(new RoleAIVO(30551,3,2,1,1,2,[[101,102,103,101,104,101,103]],[new RoleTrickVO(101,2,0,600),new RoleTrickVO(102,1,0,900),new RoleTrickVO(103,2,0,900),new RoleTrickVO(104,2,0,280)],false));
         this._aiVec.push(new RoleAIVO(30561,3,2,1,1,2,[[101,102,104,103,104,102,101]],[new RoleTrickVO(101,2,0,500),new RoleTrickVO(102,2,0,900),new RoleTrickVO(103,2,0,900),new RoleTrickVO(104,2,0,160)],false));
         this._aiVec.push(new RoleAIVO(30571,3,2,1,1,2,[[101,102,104,103,105,102,101]],[new RoleTrickVO(101,6,0,300),new RoleTrickVO(102,6,0,350),new RoleTrickVO(103,6,0,400),new RoleTrickVO(104,6,0,200),new RoleTrickVO(105,6,0,300)],false));
         this._aiVec.push(new RoleAIVO(30581,3,2,1,1,2,[[104,105,102,101,105,104,103]],[new RoleTrickVO(101,2,0,300),new RoleTrickVO(102,2,0,300),new RoleTrickVO(103,2,0,700),new RoleTrickVO(104,2,0,400),new RoleTrickVO(105,2,0,400)],false));
         this._aiVec.push(new RoleAIVO(30591,3,2,1,1,2,[[104,102,101,103,101,102,104]],[new RoleTrickVO(101,2,0,400),new RoleTrickVO(102,2,0,250),new RoleTrickVO(103,2,0,500),new RoleTrickVO(104,2,0,350)],false));
         this._aiVec.push(new RoleAIVO(30601,3,2,1,1,2,[[104,102,105,103,101]],[new RoleTrickVO(101,2,0,400),new RoleTrickVO(102,1,0,900),new RoleTrickVO(103,2,0,300),new RoleTrickVO(104,2,0,1000),new RoleTrickVO(105,2,0,350)],false));
         this._aiVec.push(new RoleAIVO(30611,3,2,1,1,2,[[102,103,104,101,104,103,105]],[new RoleTrickVO(101,1,0,900),new RoleTrickVO(102,2,0,300),new RoleTrickVO(103,2,0,500),new RoleTrickVO(104,2,0,300),new RoleTrickVO(105,2,0,500)],false));
         this._aiVec.push(new RoleAIVO(30621,3,2,1,1,2,[[0,101,102,104,0,103,0,104,0,103,0]],[new RoleTrickVO(0,1.5,0.5,400),new RoleTrickVO(101,1,0,200),new RoleTrickVO(102,2,0,200),new RoleTrickVO(103,1,0,300),new RoleTrickVO(104,2,0,600)],false));
         this._aiVec.push(new RoleAIVO(30631,3,2,1,1,2,[[104,101,102,0,103,0,102]],[new RoleTrickVO(0,1,0.5,200),new RoleTrickVO(101,2,0,700),new RoleTrickVO(102,2,0,200),new RoleTrickVO(103,1,0,400),new RoleTrickVO(104,2,0,600)],false));
         this._aiVec.push(new RoleAIVO(30641,3,2,1,1,2,[[101,104,103,102,0]],[new RoleTrickVO(0,2,0.5,900),new RoleTrickVO(101,2,0,600),new RoleTrickVO(102,2,0,250),new RoleTrickVO(103,2,0,200),new RoleTrickVO(104,2,0,150)],false));
         this._aiVec.push(new RoleAIVO(30651,3,2,1,1,2,[[0,101,102,0,103]],[new RoleTrickVO(0,2,0.5,900),new RoleTrickVO(101,1,0,500),new RoleTrickVO(102,1,0,300),new RoleTrickVO(103,1,0,250)],false));
         this._aiVec.push(new RoleAIVO(30661,3,2,1,1,2,[[102,101,103,101,104,101]],[new RoleTrickVO(101,0.5,0,200),new RoleTrickVO(102,0.1,0,500),new RoleTrickVO(103,0.5,0,900),new RoleTrickVO(104,0.5,0,900)],false));
         this._aiVec.push(new RoleAIVO(40011,10,10,0.5,2,3,[[1,102,101,0,102]],[new RoleTrickVO(0,1,0.5,290),new RoleTrickVO(1,1,0.5,200),new RoleTrickVO(101,3,0.5,700),new RoleTrickVO(102,3,0.5,300)],false));
         this._aiVec.push(new RoleAIVO(40021,10,10,0.5,2,3,[[103,0,101,102,0,0]],[new RoleTrickVO(0,1,0.5,290),new RoleTrickVO(101,2,0.5,800),new RoleTrickVO(102,2,0.5,300),new RoleTrickVO(103,2,0.5,600)],false));
         this._aiVec.push(new RoleAIVO(40031,5,3,0.5,2,3,[[0,101,102,103]],[new RoleTrickVO(0,1,0.5,290),new RoleTrickVO(101,1,0.5,800),new RoleTrickVO(102,1,0.5,300),new RoleTrickVO(103,1,0.5,600)],false));
         this._aiVec.push(new RoleAIVO(40041,5,3,0.5,2,3,[[102,0,103,101,103]],[new RoleTrickVO(0,1.5,0.5,900),new RoleTrickVO(101,1.5,0.5,900),new RoleTrickVO(102,1.5,0.5,900),new RoleTrickVO(103,1.5,0.5,400)],false));
         this._aiVec.push(new RoleAIVO(40042,5,3,0.5,2,3,[[0,103]],[new RoleTrickVO(0,0.5,0.5,900),new RoleTrickVO(103,0.5,0.5,400)],false));
         this._aiVec.push(new RoleAIVO(40051,5,3,0.5,2,2,[[104,102,101,102]],[new RoleTrickVO(101,2,0.5,300),new RoleTrickVO(102,2,0.5,900),new RoleTrickVO(103,2,0.5,900),new RoleTrickVO(104,2,0.5,400)],false));
         this._aiVec.push(new RoleAIVO(40052,5,1,0.5,2,2,[[104,102,101,102]],[new RoleTrickVO(101,2,0.5,300),new RoleTrickVO(102,2,0.5,900),new RoleTrickVO(103,2,0.5,900),new RoleTrickVO(104,2,0.5,400)],false));
         this._aiVec.push(new RoleAIVO(40061,5,3,0.5,2,2,[[101,102,103,0,102]],[new RoleTrickVO(0,2,0.5,250),new RoleTrickVO(101,2,0.5,300),new RoleTrickVO(102,2,0.5,900),new RoleTrickVO(103,2,0.5,900)],false));
         this._aiVec.push(new RoleAIVO(40062,5,3,0.5,2,2,[[101,102,103,0,102]],[new RoleTrickVO(0,1,0.5,250),new RoleTrickVO(101,1,0.5,300),new RoleTrickVO(102,1,0.5,900),new RoleTrickVO(103,1,0.5,900)],false));
         this._aiVec.push(new RoleAIVO(40071,5,3,0.5,2,2,[[101,0,102,0,1,103]],[new RoleTrickVO(0,2,0.5,250),new RoleTrickVO(1,3,0.5,250),new RoleTrickVO(101,2,0.5,300),new RoleTrickVO(102,3,0.5,900),new RoleTrickVO(103,2,0.5,900)],false));
         this._aiVec.push(new RoleAIVO(40072,5,3,0.5,2,2,[[101,0,102,0,1,103]],[new RoleTrickVO(0,1,0.5,250),new RoleTrickVO(1,1,0.5,250),new RoleTrickVO(101,1,0.5,300),new RoleTrickVO(102,1,0.5,900),new RoleTrickVO(103,1,0.5,900)],false));
         this._aiVec.push(new RoleAIVO(40081,5,3,0.5,2,2,[[103,0,104,0,101,102]],[new RoleTrickVO(0,2,0.5,250),new RoleTrickVO(101,2,0.5,300),new RoleTrickVO(102,3,0.5,900),new RoleTrickVO(103,2,0.5,900),new RoleTrickVO(104,2,0.5,200)],false));
         this._aiVec.push(new RoleAIVO(40082,5,3,0.5,2,2,[[103,0,104,0,101,102]],[new RoleTrickVO(0,1,0.5,250),new RoleTrickVO(101,1,0.5,300),new RoleTrickVO(102,1,0.5,900),new RoleTrickVO(103,1,0.5,900),new RoleTrickVO(104,1,0.5,200)],false));
         this._aiVec.push(new RoleAIVO(40091,5,3,0.5,2,2,[[103,0,104,0,105,101,102]],[new RoleTrickVO(0,2,0.5,800),new RoleTrickVO(101,2,0.5,800),new RoleTrickVO(102,3,0.5,800),new RoleTrickVO(103,2,0.5,800),new RoleTrickVO(104,2,0.5,600),new RoleTrickVO(105,2,0.5,400)],true));
         this._aiVec.push(new RoleAIVO(40101,5,3,0.5,2,2,[[105,1,103,102,104,0,101]],[new RoleTrickVO(0,1.3,0.5,200),new RoleTrickVO(1,2,0.5,200),new RoleTrickVO(101,1,0.5,800),new RoleTrickVO(102,1,0.5,800),new RoleTrickVO(103,1.5,0.5,400),new RoleTrickVO(104,1,0.5,300),new RoleTrickVO(105,1.5,0.5,800)],false));
         this._aiVec.push(new RoleAIVO(40111,5,3,0.5,2,2,[[103,0,105,102,1,101,104]],[new RoleTrickVO(0,1,0.5,200),new RoleTrickVO(1,1,0.5,200),new RoleTrickVO(101,1,0.5,800),new RoleTrickVO(102,1,0.5,800),new RoleTrickVO(103,1.5,0.5,400),new RoleTrickVO(104,0.5,0.5,300),new RoleTrickVO(105,1.5,0.5,800)],false));
         this._aiVec.push(new RoleAIVO(40121,5,3,0.5,2,2,[[104,103,0,101,102,104,103,0,101,105]],[new RoleTrickVO(0,2,0.5,200),new RoleTrickVO(101,2,0.5,400),new RoleTrickVO(102,2,0.5,400),new RoleTrickVO(103,3,0.5,300),new RoleTrickVO(104,2,0.5,200),new RoleTrickVO(105,2,0.5,400)],false));
         this._aiVec.push(new RoleAIVO(40131,5,3,0.5,2,2,[[102,0,101,104,102,0]],[new RoleTrickVO(0,0.5,0.5,200),new RoleTrickVO(101,1,0.5,800),new RoleTrickVO(102,1,0.5,800),new RoleTrickVO(103,1,0.5,400),new RoleTrickVO(104,2,0.5,300)],false));
         this._aiVec.push(new RoleAIVO(40141,5,3,0.5,2,2,[[102,0,101,105,101,103,102,0,101,105,101,103,104]],[new RoleTrickVO(0,1,0.5,200),new RoleTrickVO(101,1.5,0.5,200),new RoleTrickVO(102,1,0.5,800),new RoleTrickVO(103,2,0.5,400),new RoleTrickVO(104,1,0.5,300),new RoleTrickVO(105,1,0.5,800)],false));
         this._aiVec.push(new RoleAIVO(40151,5,3,0.5,2,2,[[102,0,104,103,0,101,0,101]],[new RoleTrickVO(0,1.5,0.5,200),new RoleTrickVO(101,1.5,0.5,100),new RoleTrickVO(102,1.5,0.5,230),new RoleTrickVO(103,1.5,0.5,460),new RoleTrickVO(104,2,0.5,100),new RoleTrickVO(105,1.5,0.5,300)],false));
         this._aiVec.push(new RoleAIVO(40161,5,3,0.5,2,2,[[102,101,0,103,104,105,0,103]],[new RoleTrickVO(0,1.5,0.5,200),new RoleTrickVO(101,1.5,0.5,100),new RoleTrickVO(102,1.5,0.5,500),new RoleTrickVO(103,2,0.5,100),new RoleTrickVO(104,2,0.5,400),new RoleTrickVO(105,2,0.5,300)],false));
         this._aiVec.push(new RoleAIVO(40171,3,2.5,0.5,2,2,[[101,105,102,104,103,0]],[new RoleTrickVO(0,1,0,200),new RoleTrickVO(101,1,0,130),new RoleTrickVO(102,1,0,300),new RoleTrickVO(103,1,0,170),new RoleTrickVO(104,2,0,200),new RoleTrickVO(105,2,0,150),new RoleTrickVO(106,2,0,200)],false));
         this._aiVec.push(new RoleAIVO(40181,3,2.5,0.5,2,2,[[101,105,102,104,103,0]],[new RoleTrickVO(0,1,0,100),new RoleTrickVO(101,1,0,600),new RoleTrickVO(102,1,0,500),new RoleTrickVO(103,1,0,170),new RoleTrickVO(104,2,0,180),new RoleTrickVO(105,2,0,150),new RoleTrickVO(106,2,0,200)],false));
         this._aiVec.push(new RoleAIVO(40191,3,2.5,0.5,2,2,[[101,0,102,101,104,0,103]],[new RoleTrickVO(0,1,0,100),new RoleTrickVO(101,1,0,600),new RoleTrickVO(102,1,0,500),new RoleTrickVO(103,1,0,600),new RoleTrickVO(104,1,0,700)],false));
         this._aiVec.push(new RoleAIVO(40201,3,2.5,0.5,2,2,[[101,102,101,105,104,103,104]],[new RoleTrickVO(101,1,0,300),new RoleTrickVO(102,1,0,350),new RoleTrickVO(103,2,0,400),new RoleTrickVO(104,1,0,500),new RoleTrickVO(105,1,0,500)],false));
         this._aiVec.push(new RoleAIVO(40211,3,2.5,0.5,0.5,1,[[102,101,0,103,104,0,105]],[new RoleTrickVO(0,0.5,0,100),new RoleTrickVO(101,1.5,0,300),new RoleTrickVO(102,1.5,0,150),new RoleTrickVO(103,1.5,0,300),new RoleTrickVO(104,1.5,0,150),new RoleTrickVO(105,2.5,0,200),new RoleTrickVO(106,1.5,0,200)],false));
         this._aiVec.push(new RoleAIVO(40221,3,2.5,0.5,0.5,1,[[103,101,104,102,104,101,103]],[new RoleTrickVO(101,1,0,400),new RoleTrickVO(102,1.5,0,600),new RoleTrickVO(103,2,0,500),new RoleTrickVO(104,1.5,0,250)],false));
         this._aiVec.push(new RoleAIVO(40231,3,2.5,0.5,0.5,1,[[101,102,0,101,103,0]],[new RoleTrickVO(0,1.5,0,500),new RoleTrickVO(101,1.5,0,400),new RoleTrickVO(102,1.5,0,600),new RoleTrickVO(103,1.5,0,300)],false));
         this._aiVec.push(new RoleAIVO(40241,3,2.5,0.5,0.5,1,[[103,102,102,104,101]],[new RoleTrickVO(101,1.5,0,600),new RoleTrickVO(102,1.5,0,500),new RoleTrickVO(103,1.5,0,400),new RoleTrickVO(104,1.5,0,250)],false));
         this._aiVec.push(new RoleAIVO(40251,3,2.5,0.5,0.5,1,[[103,101,102,104,102]],[new RoleTrickVO(101,1.5,0,500),new RoleTrickVO(102,1.5,0,800),new RoleTrickVO(103,1.5,0,400),new RoleTrickVO(104,1.5,0,500)],false));
      }
      
      public function findRoleAI(param1:int) : RoleAIVO
      {
         var _loc2_:RoleAIVO = null;
         for each(_loc2_ in this._aiVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

