package file
{
   import mogames.gameData.ConstData;
   import mogames.gameData.flag.vo.GoodFlag;
   import mogames.gameData.flag.vo.LevelFlag;
   import mogames.gameData.flag.vo.MaxValueFlag;
   import mogames.gameData.flag.vo.NumFlag;
   import mogames.gameData.flag.vo.UnionFlag;
   import mogames.gameData.flag.vo.WeekRefreshFlag;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class FlagConfig
   {
      private static var _instance:FlagConfig;
      
      private var flags:Vector.<NumFlag>;
      
      public function FlagConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : FlagConfig
      {
         if(!_instance)
         {
            _instance = new FlagConfig();
         }
         return _instance;
      }
      
      public function newFlags() : Vector.<NumFlag>
      {
         this.flags = new Vector.<NumFlag>();
         this.flags.push(new NumFlag(10,1,false,true));
         this.flags.push(new NumFlag(11));
         this.flags.push(new NumFlag(12));
         this.flags.push(new NumFlag(13));
         this.flags.push(new NumFlag(14));
         this.flags.push(new NumFlag(15));
         this.flags.push(new NumFlag(16));
         this.flags.push(new NumFlag(17));
         this.flags.push(new NumFlag(101));
         this.flags.push(new NumFlag(102));
         this.flags.push(new NumFlag(103));
         this.flags.push(new NumFlag(104));
         this.flags.push(new NumFlag(105));
         this.flags.push(new NumFlag(106));
         this.flags.push(new NumFlag(107));
         this.flags.push(new NumFlag(108));
         this.flags.push(new NumFlag(109));
         this.flags.push(new NumFlag(110));
         this.flags.push(new NumFlag(200));
         this.flags.push(new NumFlag(204,6,false,true));
         this.flags.push(new NumFlag(210,1,false,true));
         this.flags.push(new NumFlag(211,1,false,true));
         this.flags.push(new WeekRefreshFlag(212));
         this.flags.push(new WeekRefreshFlag(213));
         this.flags.push(new WeekRefreshFlag(214));
         this.flags.push(new NumFlag(215));
         this.flags.push(new NumFlag(216,1,false,true));
         this.flags.push(new NumFlag(221,1,false,true));
         this.flags.push(new NumFlag(222,1,false,true));
         this.flags.push(new NumFlag(223,1,false,true));
         this.flags.push(new NumFlag(224,1,false,true));
         this.flags.push(new NumFlag(225,1,false,true));
         this.flags.push(new NumFlag(226,1,false,true));
         this.flags.push(new NumFlag(228,8,false,false));
         this.flags.push(new NumFlag(229,5,true));
         this.flags.push(new NumFlag(230,5,true));
         this.flags.push(new NumFlag(231,5,true));
         this.flags.push(new NumFlag(232,5,true));
         this.flags.push(new NumFlag(233,1,false,true));
         this.flags.push(new UnionFlag(234));
         this.flags.push(new NumFlag(235,2,false,true));
         this.flags.push(new UnionFlag(236));
         this.flags.push(new NumFlag(237,2,false,true));
         this.flags.push(new NumFlag(302));
         this.flags.push(new NumFlag(303));
         this.flags.push(new NumFlag(304));
         this.flags.push(new NumFlag(305));
         this.flags.push(new NumFlag(306));
         this.flags.push(new NumFlag(307));
         this.flags.push(new NumFlag(308));
         this.flags.push(new NumFlag(309));
         this.flags.push(new NumFlag(310));
         this.flags.push(new NumFlag(311));
         this.flags.push(new NumFlag(312));
         this.flags.push(new NumFlag(315));
         this.flags.push(new NumFlag(316));
         this.flags.push(new NumFlag(317));
         this.flags.push(new NumFlag(318));
         this.flags.push(new NumFlag(319));
         this.flags.push(new NumFlag(402));
         this.flags.push(new NumFlag(403));
         this.flags.push(new NumFlag(404));
         this.flags.push(new NumFlag(405));
         this.flags.push(new NumFlag(406));
         this.flags.push(new NumFlag(400));
         this.flags.push(new NumFlag(401));
         this.flags.push(new NumFlag(402));
         this.flags.push(new NumFlag(403));
         this.flags.push(new NumFlag(404));
         this.flags.push(new NumFlag(405));
         this.flags.push(new NumFlag(406));
         this.flags.push(new NumFlag(407));
         this.flags.push(new NumFlag(408));
         this.flags.push(new NumFlag(409));
         this.flags.push(new NumFlag(410));
         this.flags.push(new NumFlag(411));
         this.flags.push(new NumFlag(412));
         this.flags.push(new NumFlag(413));
         this.flags.push(new NumFlag(414));
         this.flags.push(new NumFlag(415));
         this.flags.push(new NumFlag(416));
         this.flags.push(new NumFlag(417));
         this.flags.push(new NumFlag(418));
         this.flags.push(new NumFlag(419));
         this.flags.push(new NumFlag(420));
         this.flags.push(new NumFlag(421));
         this.flags.push(new NumFlag(422));
         this.flags.push(new NumFlag(423));
         this.flags.push(new NumFlag(424));
         this.flags.push(new NumFlag(425));
         this.flags.push(new NumFlag(426));
         this.flags.push(new NumFlag(427));
         this.flags.push(new NumFlag(428));
         this.flags.push(new NumFlag(429));
         this.flags.push(new NumFlag(430));
         this.flags.push(new NumFlag(431));
         this.flags.push(new NumFlag(432));
         this.flags.push(new NumFlag(433));
         this.flags.push(new NumFlag(434));
         this.flags.push(new NumFlag(435));
         this.flags.push(new NumFlag(436));
         this.flags.push(new NumFlag(437));
         this.flags.push(new NumFlag(438));
         this.flags.push(new NumFlag(439));
         this.flags.push(new NumFlag(440));
         this.flags.push(new NumFlag(441));
         this.flags.push(new NumFlag(442));
         this.flags.push(new NumFlag(443));
         this.flags.push(new NumFlag(444));
         this.flags.push(new NumFlag(445));
         this.flags.push(new NumFlag(446));
         this.flags.push(new NumFlag(447));
         this.flags.push(new NumFlag(448));
         this.flags.push(new NumFlag(449));
         this.flags.push(new NumFlag(450));
         this.flags.push(new NumFlag(451));
         this.flags.push(new NumFlag(452));
         this.flags.push(new NumFlag(453));
         this.flags.push(new NumFlag(454));
         this.flags.push(new NumFlag(455));
         this.flags.push(new NumFlag(456));
         this.flags.push(new NumFlag(457));
         this.flags.push(new NumFlag(458));
         this.flags.push(new NumFlag(459));
         this.flags.push(new NumFlag(460));
         this.flags.push(new NumFlag(461));
         this.flags.push(new NumFlag(462));
         this.flags.push(new NumFlag(463));
         this.flags.push(new NumFlag(464));
         this.flags.push(new NumFlag(465));
         this.flags.push(new NumFlag(466));
         this.flags.push(new NumFlag(467));
         this.flags.push(new NumFlag(468));
         this.flags.push(new NumFlag(469));
         this.flags.push(new NumFlag(470));
         this.flags.push(new NumFlag(471));
         this.flags.push(new NumFlag(472));
         this.flags.push(new NumFlag(473));
         this.flags.push(new NumFlag(474));
         this.flags.push(new NumFlag(475));
         this.flags.push(new NumFlag(476));
         this.flags.push(new NumFlag(477));
         this.flags.push(new NumFlag(478));
         this.flags.push(new NumFlag(479));
         this.flags.push(new NumFlag(480));
         this.flags.push(new NumFlag(481));
         this.flags.push(new NumFlag(482));
         this.flags.push(new NumFlag(483));
         this.flags.push(new NumFlag(484));
         this.flags.push(new NumFlag(485));
         this.flags.push(new NumFlag(486));
         this.flags.push(new NumFlag(487));
         this.flags.push(new NumFlag(488));
         this.flags.push(new NumFlag(489));
         this.flags.push(new NumFlag(490));
         this.flags.push(new NumFlag(491));
         this.flags.push(new NumFlag(492));
         this.flags.push(new NumFlag(493));
         this.flags.push(new NumFlag(494));
         this.flags.push(new NumFlag(495));
         this.flags.push(new NumFlag(496));
         this.flags.push(new NumFlag(500));
         this.flags.push(new NumFlag(501));
         this.flags.push(new NumFlag(502));
         this.flags.push(new NumFlag(503));
         this.flags.push(new NumFlag(504));
         this.flags.push(new NumFlag(505));
         this.flags.push(new NumFlag(506));
         this.flags.push(new NumFlag(507));
         this.flags.push(new NumFlag(508));
         this.flags.push(new NumFlag(509));
         this.flags.push(new NumFlag(510));
         this.flags.push(new NumFlag(511));
         this.flags.push(new NumFlag(512));
         this.flags.push(new NumFlag(513));
         this.flags.push(new NumFlag(515));
         this.flags.push(new NumFlag(516));
         this.flags.push(new NumFlag(517));
         this.flags.push(new NumFlag(518));
         this.flags.push(new NumFlag(519));
         this.flags.push(new NumFlag(520));
         this.flags.push(new NumFlag(521));
         this.flags.push(new NumFlag(522));
         this.flags.push(new NumFlag(523));
         this.flags.push(new NumFlag(524));
         this.flags.push(new NumFlag(1001));
         this.flags.push(new NumFlag(1002));
         this.flags.push(new NumFlag(1003));
         this.flags.push(new NumFlag(1004));
         this.flags.push(new NumFlag(1005));
         this.flags.push(new NumFlag(1006));
         this.flags.push(new NumFlag(1007));
         this.flags.push(new NumFlag(1008));
         this.flags.push(new NumFlag(1009));
         this.flags.push(new NumFlag(1010));
         this.flags.push(new NumFlag(1011));
         this.flags.push(new NumFlag(1012));
         this.flags.push(new NumFlag(1013));
         this.flags.push(new NumFlag(1014));
         this.flags.push(new NumFlag(1015));
         this.flags.push(new NumFlag(1016));
         this.flags.push(new NumFlag(1017));
         this.flags.push(new NumFlag(1018));
         this.flags.push(new NumFlag(1019));
         this.flags.push(new NumFlag(1020));
         this.flags.push(new NumFlag(1021));
         this.flags.push(new NumFlag(1022));
         this.flags.push(new NumFlag(1023));
         this.flags.push(new NumFlag(1024));
         this.flags.push(new NumFlag(1025));
         this.flags.push(new NumFlag(1026));
         this.flags.push(new NumFlag(1027));
         this.flags.push(new NumFlag(1028));
         this.flags.push(new NumFlag(1029));
         this.flags.push(new NumFlag(1030));
         this.flags.push(new NumFlag(1031));
         this.flags.push(new NumFlag(1032));
         this.flags.push(new NumFlag(1033));
         this.flags.push(new NumFlag(1034));
         this.flags.push(new NumFlag(1035));
         this.flags.push(new NumFlag(1036));
         this.flags.push(new NumFlag(1037));
         this.flags.push(new NumFlag(1038));
         this.flags.push(new NumFlag(1039));
         this.flags.push(new NumFlag(1040));
         this.flags.push(new NumFlag(1041));
         this.flags.push(new NumFlag(1042));
         this.flags.push(new NumFlag(1043));
         this.flags.push(new NumFlag(1044));
         this.flags.push(new NumFlag(1045));
         this.flags.push(new NumFlag(1050));
         this.flags.push(new NumFlag(2001));
         this.flags.push(new NumFlag(2002));
         this.flags.push(new NumFlag(2003));
         this.flags.push(new NumFlag(2004));
         this.flags.push(new NumFlag(2006));
         this.flags.push(new NumFlag(2007));
         this.flags.push(new NumFlag(2008));
         this.flags.push(new NumFlag(2009));
         this.flags.push(new NumFlag(2010));
         this.flags.push(new NumFlag(2011));
         this.flags.push(new NumFlag(2012));
         this.flags.push(new NumFlag(2013));
         this.flags.push(new NumFlag(2014));
         this.flags.push(new NumFlag(2015));
         this.flags.push(new NumFlag(2016));
         this.flags.push(new NumFlag(2017));
         this.flags.push(new NumFlag(2018));
         this.flags.push(new NumFlag(2019));
         this.flags.push(new NumFlag(2020));
         this.flags.push(new NumFlag(2021));
         this.flags.push(new NumFlag(2022));
         this.flags.push(new NumFlag(2023));
         this.flags.push(new NumFlag(2024));
         this.flags.push(new NumFlag(2025));
         this.flags.push(new NumFlag(2026));
         this.flags.push(new NumFlag(2027));
         this.flags.push(new NumFlag(2028));
         this.flags.push(new NumFlag(2029));
         this.flags.push(new NumFlag(2030));
         this.flags.push(new NumFlag(2031));
         this.flags.push(new NumFlag(2032));
         this.flags.push(new NumFlag(2033));
         this.flags.push(new NumFlag(2034));
         this.flags.push(new NumFlag(2035));
         this.flags.push(new NumFlag(2036));
         this.flags.push(new NumFlag(2037));
         this.flags.push(new NumFlag(2038));
         this.flags.push(new NumFlag(2039));
         this.flags.push(new NumFlag(2040));
         this.flags.push(new NumFlag(2041));
         this.flags.push(new NumFlag(2042));
         this.flags.push(new NumFlag(2043));
         this.flags.push(new NumFlag(2044));
         this.flags.push(new NumFlag(2045));
         this.flags.push(new NumFlag(2046));
         this.flags.push(new NumFlag(2047));
         this.flags.push(new NumFlag(2048));
         this.flags.push(new NumFlag(2049));
         this.flags.push(new NumFlag(2050));
         this.flags.push(new NumFlag(2051));
         this.flags.push(new NumFlag(2052));
         this.flags.push(new NumFlag(2053));
         this.flags.push(new NumFlag(2054));
         this.flags.push(new NumFlag(2055));
         this.flags.push(new NumFlag(2056));
         this.flags.push(new NumFlag(2057));
         this.flags.push(new NumFlag(2058));
         this.flags.push(new NumFlag(2059));
         this.initGoodConfig();
         this.flags.push(new LevelFlag(100100));
         this.flags.push(new LevelFlag(100200));
         this.flags.push(new LevelFlag(100300));
         this.flags.push(new LevelFlag(100400));
         this.flags.push(new LevelFlag(100500));
         this.flags.push(new MaxValueFlag(200000));
         this.flags.push(new MaxValueFlag(200001));
         return this.flags;
      }
      
      private function initGoodConfig() : void
      {
         var _loc2_:ConstGoodVO = null;
         var _loc3_:GoodFlag = null;
         var _loc1_:Array = GoodConfig.instance().goodDic[ConstData.GOOD_PROP];
         for each(_loc2_ in _loc1_)
         {
            _loc3_ = new GoodFlag(_loc2_.id.v);
            _loc3_.setValue(1);
            this.flags.push(_loc3_ as NumFlag);
         }
      }
   }
}

