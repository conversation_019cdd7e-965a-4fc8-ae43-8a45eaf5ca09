package file
{
   import mogames.gameData.ConstData;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.drop.vo.EquipRewardVO;
   import mogames.gameData.task.constvo.ConstTask;
   import mogames.gameData.task.vo.BaseTask;
   import mogames.gameData.task.vo.EnemyTask;
   import mogames.gameData.task.vo.GoodTask;
   import mogames.gameData.task.vo.StoryTask;
   import mogames.gameData.task.vo.TaskEnemyVO;
   import mogames.gameData.task.vo.TaskGoodVO;
   import utils.TxtUtil;
   
   public class TaskConfig
   {
      private static var _instance:TaskConfig;
      
      protected var _tasks:Vector.<ConstTask>;
      
      public function TaskConfig()
      {
         super();
         this.handlerInit();
      }
      
      public static function instance() : TaskConfig
      {
         if(!_instance)
         {
            _instance = new TaskConfig();
         }
         return _instance;
      }
      
      protected function handlerInit() : void
      {
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public function init() : void
      {
         this._tasks = new Vector.<ConstTask>();
         this._tasks.push(new ConstTask(11001,"解救刘伯钦",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(13102,5),new BaseRewardVO(13101,2),new BaseRewardVO(12002,2),new BaseRewardVO(14001,2)],"消灭寅将军获得钥匙，解救刘伯钦。","唐僧师徒来到双叉岭，遇到一个被锁在牢笼的铁匠，牢笼坚固异常，听铁匠说钥匙和【照妖镜图谱】在一个虎精身上。"));
         this._tasks.push(new ConstTask(11024,"装备强化",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(14150,1),new BaseRewardVO(13105,5),new EquipRewardVO(21001,3),new EquipRewardVO(20003,3)],"强化一件装备","去花果山找刘伯钦进行装备强化。"));
         this._tasks.push(new ConstTask(11002,"追回佛衣",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(13103,5),new BaseRewardVO(14001,5),new EquipRewardVO(20002,3),new EquipRewardVO(21002,3)],"追回失窃的佛衣。","观世音赠与唐僧的佛衣被盗，究竟是何人所为？"));
         this._tasks.push(new ConstTask(11003,"勇斗老妖",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(13101,5),new BaseRewardVO(13104,9),new EquipRewardVO(20005,3),new EquipRewardVO(21005,3)],"战胜黄风怪。","唐僧被妖怪拐走，询问土地公公得知原是黄风怪所为,于是众人立马前去营救唐僧。"));
         this._tasks.push(new ConstTask(11025,"法宝合成",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(14150,1),new BaseRewardVO(14009,3)],"合成一件法宝（任意）。","去花果山找灵吉菩萨合成法宝。"));
         this._tasks.push(new ConstTask(12004,"寻找止泻草",[new TaskGoodVO(10013,3)],[new BaseRewardVO(10000,5000),new BaseRewardVO(14001,5)],"","在孙悟空和黄风怪斗法时，猪八戒在黄风洞里找到大量美食，一时忍不住美食的诱惑，吃坏了肚子，听说黄风洞左边洞里有几株草药能治。"));
         this._tasks.push(new ConstTask(11005,"救活人参树",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(16001,3),new BaseRewardVO(16002,3),new BaseRewardVO(16003,3)],"获得甘泉后点击镇元大仙活树。","五庄观的人参树被孙悟空一棒子砸坏了，镇元大仙十分生气，扣住唐僧等人，责令救活人参树。"));
         this._tasks.push(new ConstTask(11027,"捕捉宠物",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(16753,2),new BaseRewardVO(16754,2),new BaseRewardVO(16750,1)],"捕捉到任意一个宠物。","孙悟空在后园发现一个宝箱，里面找到了【驭兽袋图谱】,可以合成法宝【驭兽袋】用来捕捉宠物。"));
         this._tasks.push(new ConstTask(12006,"寻找野果",[new TaskGoodVO(10014,20)],[new BaseRewardVO(10000,5000),new BaseRewardVO(14001,5),new BaseRewardVO(16711,1)],"","一日，走到一座大山中，只见天色阴沉，谷中浓雾弥漫，悟空料定必有妖怪。便让八戒、沙僧保护师父休息，他去探听虚实，顺便采些鲜果来充饥。流传【缚妖索图谱】是白骨夫人所盗。"));
         this._tasks.push(new ConstTask(11007,"三打白骨精",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(14001,5),new EquipRewardVO(20007,3)],"战胜白骨精。","白骨夫人本是白虎岭上的一具化为白骨的女尸，采天地灵气，受日月精华，变幻成了人形。" + TxtUtil.setColor("6、用照妖镜找出白骨夫人三个化身便能消弱她。","EE82EE")));
         this._tasks.push(new ConstTask(11008,"寻找线索",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(16001,2),new BaseRewardVO(14002,10)],"探索黑松林寻找百花羞。","唐僧又一次被妖怪掳走，猪八戒去花果山好言相劝请来了大师兄，告知大师兄妖怪往黑松林方向跑了。宝象国的三公主百花羞也被这大妖关押，需要找到机关才能解救。"));
         this._tasks.push(new ConstTask(11009,"大战黄袍",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(16002,2),new BaseRewardVO(16003,2),new EquipRewardVO(20010,3)],"战胜黄袍怪。","从百花羞那里得知，解救她的地方可以直接传送到大妖黄袍怪老巢，听说【净瓶图谱】就在它身上，唐僧就是被他掳走，迷踪林里只要按照吊起【石头】的方向,就能出来。"));
         this._tasks.push(new ConstTask(11010,"扫荡平顶山",[],[new BaseRewardVO(10000,5000),new BaseRewardVO(13321,20),new BaseRewardVO(10021,20),new BaseRewardVO(14150,1)],"打败金角大王，银角大王。","金角大王原来是太上老君看金炉的金、银灵童子，他们偷了太上老君的五件宝贝，私自下界来到了平顶山，在悟空眼皮底下，设计抓走唐僧，悟空大怒，决定扫荡平顶山。"));
         this._tasks.push(new ConstTask(11011,"刺探乌鸡国",[],[new BaseRewardVO(10000,6000),new BaseRewardVO(13105,3),new BaseRewardVO(13107,3)],"打败狮俐王。","乌鸡国妖气弥漫，国王也性情大变，【避水珠图谱】被狮俐王藏了起来，这究竟是怎么回事？"));
         this._tasks.push(new ConstTask(11012,"寻找乌鸡国王",[],[new BaseRewardVO(10000,6000),new BaseRewardVO(16004,1),new BaseRewardVO(16005,1)],"进入水井。","大妖被打败，真正的国王又在哪里？唐僧回忆道有个老国王托梦于他，说被压在殿外的水井里，【避水珠图谱】就在狮俐王身上，难道此人就是真正的国王？"));
         this._tasks.push(new ConstTask(11013,"救活乌鸡国王",[],[new BaseRewardVO(10000,6000),new BaseRewardVO(13101,5)],"炼制还魂丹，回到水井点击救活老国王。","老国王因只是凡人，在水下井龙王只能用法力保护他的肉身，但魂魄已经十之去九，只能用太上老君的【还魂丹】才能救活,救后找井龙王开启【水牢】副本。"));
         this._tasks.push(new ConstTask(11014,"大战红孩儿",[],[new BaseRewardVO(10000,6500),new BaseRewardVO(16222,1)],"战胜红孩儿。","唐僧跟一群小孩打听去路，不曾想居然是一群小妖。这下唐僧又被抓了，看来要想救回唐僧得先战胜他们的头领。"));
         this._tasks.push(new ConstTask(11015,"遇险黑水河",[],[new BaseRewardVO(10000,7000),new BaseRewardVO(16202,1),new BaseRewardVO(16212,1)],"打败鼍龙。","唐僧与众人乘船欲渡过黑水河，不料唐僧被水下怪物拽进水中。众人纷纷跳入水中，前去营救。"));
         this._tasks.push(new ConstTask(11016,"进入三清殿",[],[new BaseRewardVO(10000,7500),new BaseRewardVO(14021,1)],"寻找三清殿钥匙。","师徒几人到了车迟国，徒弟们都饿了，得知三清殿里面有许多贡品，无奈大门紧闭，无法进入，遂到处寻找大门钥匙。"));
         this._tasks.push(new ConstTask(11017,"探查三清殿",[],[new BaseRewardVO(10000,7500),new BaseRewardVO(14021,1)],"打败殿里的妖怪。","钥匙居然在妖怪身上，异样的三清殿，里面到底有什么玄机？"));
         this._tasks.push(new ConstTask(11018,"除妖通天河",[],[new BaseRewardVO(10000,8000),new BaseRewardVO(14010,1)],"打败灵感大王。","唐僧遇阻通天河，夜宿陈家村，得知河里有个灵感大王用村里的童男童女祭祀，悟空等人下河解救。"));
         this._tasks.push(new ConstTask(11019,"金兜山大战",[],[new BaseRewardVO(10000,8500),new BaseRewardVO(14010,2)],"打败独角兜大王。","独角兜大王得知唐僧等人即将来到这里，于是设了一些小伎俩将唐僧抓回来。孙悟空得知师傅被抓的消息后，很是气愤，于是找独角兜大王算账。"));
         this._tasks.push(new ConstTask(11020,"救师琵琶洞",[],[new BaseRewardVO(10000,9000),new BaseRewardVO(14010,3)],"打败蝎子精。","蝎子精用法术卷走唐僧，欲成夫妻美事，众师一路追赶至琵琶洞。"));
         this._tasks.push(new ConstTask(11021,"寻找罗刹女",[],[new BaseRewardVO(10000,9500),new BaseRewardVO(14020,1)],"前往芭蕉洞。","师徒途径火焰山，炎热异常，悟空遂决定前往芭蕉洞，欲问罗刹女借芭蕉扇一用，谁知红孩儿乃罗刹女之子，为了帮儿报仇，与孙悟空展开了大战。"));
         this._tasks.push(new ConstTask(11022,"激战摩云洞",[],[new BaseRewardVO(10000,9500),new BaseRewardVO(14020,1)],"打败牛魔王。","因红孩儿和罗刹女之事牛魔王与孙悟空结怨，这次孙悟空找上门来，牛魔王勃然大怒，与悟空厮斗起来。"));
         this._tasks.push(new ConstTask(11023,"大战九头虫",[],[new BaseRewardVO(10000,10000),new BaseRewardVO(16014,1)],"打败九头虫。","因下血雨盗取祭赛国佛宝舍利子而和孙悟空师兄弟发生冲突。"));
         this._tasks.push(new ConstTask(11026,"遇难小雷音",[],[new BaseRewardVO(10000,11000),new BaseRewardVO(14002,5)],"打败黄眉怪。","师徒一行经过【小雷音寺】，一时大意唐僧被幻化的妖怪掳去。<br>注：蜡烛台的蜡烛颜色和附近小妖或BOSS身上火焰颜色相同时，小妖或BOSS才会受到伤害。"));
         this._tasks.push(new ConstTask(11028,"七绝山降妖",[],[new BaseRewardVO(10000,12000),new BaseRewardVO(14009,2)],"打败蟒蛇精。","受李老汉之托，前往七绝山稀柿沟捉妖。"));
         this._tasks.push(new ConstTask(11029,"麒麟山斗法",[],[new BaseRewardVO(10000,13000),new BaseRewardVO(16752,2)],"打败赛太岁。","麒麟山上太岁府里的赛太岁贪图其美色，前来掳走了金圣宫娘娘，致使国王卧床一病不起。三年后，唐僧师徒路经朱紫国，孙悟空治好国王的病，并答应救回金圣宫。"));
         this._tasks.push(new ConstTask(11030,"盘丝洞救师",[],[new BaseRewardVO(10000,14000),new BaseRewardVO(14201,5)],"打败蜘蛛精。","唐僧师徒在化缘的时候误入盘丝洞，蜘蛛精利用法术捉住唐僧和八戒，想吃圣僧的肉长生不老。孙悟空气愤之极，施展本领救出师父。"));
         this._tasks.push(new ConstTask(11031,"大战蜈蚣精",[],[new BaseRewardVO(10000,15000),new BaseRewardVO(14201,5)],"打败蜈蚣精和蜘蛛精。","蜘蛛精逃走，扬言要找它的师兄对付孙悟空等人，众人追至黄花观，一场大战在所难免！"));
         this._tasks.push(new ConstTask(11032,"激战狮驼岭",[],[new BaseRewardVO(10000,16000),new BaseRewardVO(14211,5)],"打败狮驼岭三兄弟。","悟空变化小妖前去探路，打入了妖怪洞府把师父救了出来。"));
         this._tasks.push(new ConstTask(11033,"清华洞",[],[new BaseRewardVO(10000,17000),new BaseRewardVO(14221,5)],"打败白鹿精。","白鹿精将狐狸精献给了比丘国国王，自己当起国丈，常用小孩心肝作为药引子，以求千年不老。唐僧看见于心不忍，让孙悟空解救无辜的孩童。"));
         this._tasks.push(new ConstTask(11034,"陷空山",[],[new BaseRewardVO(10000,18000),new BaseRewardVO(16757,10)],"打败地涌夫人。","金鼻白毛老鼠精拜李天王为父，哪吒为兄。下界后改名为地涌夫人。在镇海禅林寺中设计抓走了唐僧，最后几经周折救出唐僧。"));
         this._tasks.push(new ConstTask(11035,"隐雾山",[],[new BaseRewardVO(10000,19000),new BaseRewardVO(16756,2)],"打败豹子精。","豹子精武力和法力都很低下，但是颇有智慧。先是采用“分瓣梅花计”智擒了唐玄奘，孙悟空潜入洞府才识破妖怪奸计，救出唐僧。"));
         this._tasks.push(new ConstTask(11036,"豹头山",[],[new BaseRewardVO(10000,20000),new BaseRewardVO(16755,2)],"打败黄狮精。","黄狮精因一时糊涂偷走悟空师兄弟的三件宝贝兵器，导致了悟空师兄弟对他的围剿。"));
         this._tasks.push(new ConstTask(11037,"竹节山",[],[new BaseRewardVO(10000,21000),new BaseRewardVO(14021,5)],"打败九灵元圣。","黄狮精被悟空打死，谁知黄狮精乃九灵元圣徒孙，一场大战不可避免。"));
         this._tasks.push(new ConstTask(11038,"青牛山大战",[],[new BaseRewardVO(10000,22000),new BaseRewardVO(14021,5)],"打败犀牛精三兄弟。","唐僧听说后就要留下来拜佛，顷刻之间，风中果然出现了三位佛身，一阵狂风刮过来，唐僧立刻就不见了。"));
         this._tasks.push(new ConstTask(11039,"布金禅寺",[],[new BaseRewardVO(10000,23000),new BaseRewardVO(16757,5)],"打败玉兔精。","原为广寒宫中捣药的玉兔，溜出宫门，逃至下界，摄藏了天竺国的公主，而变成公主模样，戏哄国王，欲搭台抛彩球，招唐僧为驸马，诱取唐僧的元阳。"));
         this._tasks.push(new ConstTask(11040,"雷音寺",[],[new BaseRewardVO(10000,24000),new BaseRewardVO(14039,5)],"打败阿难迦叶。","负责传经的阿难、迦叶二尊者竟向唐僧索要人事。悟空没给，领走之后发现的经书皆是无字。于是前来理论。"));
         this._tasks.push(new ConstTask(22001,"寻找虎筋",[new TaskGoodVO(13102,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22101,"寻找虎筋",[new TaskGoodVO(13102,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22201,"寻找虎筋",[new TaskGoodVO(13102,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22301,"寻找虎筋",[new TaskGoodVO(13102,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22401,"寻找虎筋",[new TaskGoodVO(13102,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22002,"寻找熊筋",[new TaskGoodVO(13103,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22102,"寻找熊筋",[new TaskGoodVO(13103,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22202,"寻找熊筋",[new TaskGoodVO(13103,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22302,"寻找熊筋",[new TaskGoodVO(13103,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22402,"寻找熊筋",[new TaskGoodVO(13103,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22003,"寻找玄金",[new TaskGoodVO(13104,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22103,"寻找玄金",[new TaskGoodVO(13104,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22203,"寻找玄金",[new TaskGoodVO(13104,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22303,"寻找玄金",[new TaskGoodVO(13104,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22403,"寻找玄金",[new TaskGoodVO(13104,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22004,"寻找玉骨",[new TaskGoodVO(13306,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22104,"寻找玉骨",[new TaskGoodVO(13306,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22204,"寻找玉骨",[new TaskGoodVO(13306,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22304,"寻找玉骨",[new TaskGoodVO(13306,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22404,"寻找玉骨",[new TaskGoodVO(13306,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22005,"寻找狼毛",[new TaskGoodVO(13308,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22105,"寻找狼毛",[new TaskGoodVO(13308,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22205,"寻找狼毛",[new TaskGoodVO(13308,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22305,"寻找狼毛",[new TaskGoodVO(13308,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22405,"寻找狼毛",[new TaskGoodVO(13308,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22006,"寻找银角",[new TaskGoodVO(13309,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22106,"寻找银角",[new TaskGoodVO(13309,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22206,"寻找银角",[new TaskGoodVO(13309,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22306,"寻找银角",[new TaskGoodVO(13309,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22406,"寻找银角",[new TaskGoodVO(13309,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22007,"寻找珍珠",[new TaskGoodVO(13107,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22107,"寻找珍珠",[new TaskGoodVO(13107,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22207,"寻找珍珠",[new TaskGoodVO(13107,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22307,"寻找珍珠",[new TaskGoodVO(13107,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22407,"寻找珍珠",[new TaskGoodVO(13107,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23007,"挑战真·狮俐",[new TaskEnemyVO(3014,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23107,"挑战真·狮俐",[new TaskEnemyVO(3014,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23207,"挑战真·狮俐",[new TaskEnemyVO(3014,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23307,"挑战真·狮俐",[new TaskEnemyVO(3014,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23407,"挑战真·狮俐",[new TaskEnemyVO(3014,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22008,"寻找地火石",[new TaskGoodVO(13108,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22108,"寻找地火石",[new TaskGoodVO(13108,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22208,"寻找地火石",[new TaskGoodVO(13108,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22308,"寻找地火石",[new TaskGoodVO(13108,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22408,"寻找地火石",[new TaskGoodVO(13108,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23008,"挑战红孩儿",[new TaskEnemyVO(3015,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23108,"挑战红孩儿",[new TaskEnemyVO(3015,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23208,"挑战红孩儿",[new TaskEnemyVO(3015,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23308,"挑战红孩儿",[new TaskEnemyVO(3015,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23408,"挑战红孩儿",[new TaskEnemyVO(3015,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22009,"寻找鼍角",[new TaskGoodVO(10008,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22109,"寻找鼍角",[new TaskGoodVO(10008,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22209,"寻找鼍角",[new TaskGoodVO(10008,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22309,"寻找鼍角",[new TaskGoodVO(10008,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22409,"寻找鼍角",[new TaskGoodVO(10008,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23009,"挑战真·鼍龙",[new TaskEnemyVO(3018,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23109,"挑战真·鼍龙",[new TaskEnemyVO(3018,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23209,"挑战真·鼍龙",[new TaskEnemyVO(3018,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23309,"挑战真·鼍龙",[new TaskEnemyVO(3018,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23409,"挑战真·鼍龙",[new TaskEnemyVO(3018,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23010,"挑战妖魂王",[new TaskEnemyVO(3022,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23110,"挑战妖魂王",[new TaskEnemyVO(3022,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23210,"挑战妖魂王",[new TaskEnemyVO(3022,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23310,"挑战妖魂王",[new TaskEnemyVO(3022,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23410,"挑战妖魂王",[new TaskEnemyVO(3022,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22011,"寻找鱼鳍",[new TaskGoodVO(10009,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22111,"寻找鱼鳍",[new TaskGoodVO(10009,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22211,"寻找鱼鳍",[new TaskGoodVO(10009,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22311,"寻找鱼鳍",[new TaskGoodVO(10009,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22411,"寻找鱼鳍",[new TaskGoodVO(10009,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23011,"挑战灵感大王",[new TaskEnemyVO(3023,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23111,"挑战灵感大王",[new TaskEnemyVO(3023,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23211,"挑战灵感大王",[new TaskEnemyVO(3023,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23311,"挑战灵感大王",[new TaskEnemyVO(3023,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23411,"挑战灵感大王",[new TaskEnemyVO(3023,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(22012,"寻找褐玉",[new TaskGoodVO(13314,5)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(22112,"寻找褐玉",[new TaskGoodVO(13314,5)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(22212,"寻找褐玉",[new TaskGoodVO(13314,5)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(22312,"寻找褐玉",[new TaskGoodVO(13314,5)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(22412,"寻找褐玉",[new TaskGoodVO(13314,5)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23012,"挑战独角兜大王",[new TaskEnemyVO(3025,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23112,"挑战独角兜大王",[new TaskEnemyVO(3025,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23212,"挑战独角兜大王",[new TaskEnemyVO(3025,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23312,"挑战独角兜大王",[new TaskEnemyVO(3025,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23412,"挑战独角兜大王",[new TaskEnemyVO(3025,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23013,"挑战真·蝎子精",[new TaskEnemyVO(3027,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23113,"挑战真·蝎子精",[new TaskEnemyVO(3027,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23213,"挑战真·蝎子精",[new TaskEnemyVO(3027,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23313,"挑战真·蝎子精",[new TaskEnemyVO(3027,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23413,"挑战真·蝎子精",[new TaskEnemyVO(3027,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23014,"挑战牛魔王",[new TaskEnemyVO(3030,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23114,"挑战牛魔王",[new TaskEnemyVO(3030,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23214,"挑战牛魔王",[new TaskEnemyVO(3030,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23314,"挑战牛魔王",[new TaskEnemyVO(3030,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23414,"挑战牛魔王",[new TaskEnemyVO(3030,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23015,"真·九头虫",[new TaskEnemyVO(3033,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23115,"真·九头虫",[new TaskEnemyVO(3033,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23215,"真·九头虫",[new TaskEnemyVO(3033,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23315,"真·九头虫",[new TaskEnemyVO(3033,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23415,"真·九头虫",[new TaskEnemyVO(3033,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23016,"挑战黄眉怪",[new TaskEnemyVO(3034,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23116,"挑战黄眉怪",[new TaskEnemyVO(3034,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23216,"挑战黄眉怪",[new TaskEnemyVO(3034,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23316,"挑战黄眉怪",[new TaskEnemyVO(3034,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23416,"挑战黄眉怪",[new TaskEnemyVO(3034,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23017,"挑战蟒蛇精",[new TaskEnemyVO(3035,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23117,"挑战蟒蛇精",[new TaskEnemyVO(3035,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23217,"挑战蟒蛇精",[new TaskEnemyVO(3035,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23317,"挑战蟒蛇精",[new TaskEnemyVO(3035,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23417,"挑战蟒蛇精",[new TaskEnemyVO(3035,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23018,"挑战金毛犼",[new TaskEnemyVO(3036,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23118,"挑战金毛犼",[new TaskEnemyVO(3036,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23218,"挑战金毛犼",[new TaskEnemyVO(3036,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23318,"挑战金毛犼",[new TaskEnemyVO(3036,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23418,"挑战金毛犼",[new TaskEnemyVO(3036,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23019,"挑战蜘蛛精",[new TaskEnemyVO(3037,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23119,"挑战蜘蛛精",[new TaskEnemyVO(3037,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23219,"挑战蜘蛛精",[new TaskEnemyVO(3037,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23319,"挑战蜘蛛精",[new TaskEnemyVO(3037,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23419,"挑战蜘蛛精",[new TaskEnemyVO(3037,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23020,"挑战蜈蚣精",[new TaskEnemyVO(3038,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23120,"挑战蜈蚣精",[new TaskEnemyVO(3038,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23220,"挑战蜈蚣精",[new TaskEnemyVO(3038,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23320,"挑战蜈蚣精",[new TaskEnemyVO(3038,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23420,"挑战蜈蚣精",[new TaskEnemyVO(3038,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23021,"挑战狮子精",[new TaskEnemyVO(3039,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23121,"挑战狮子精",[new TaskEnemyVO(3039,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23221,"挑战狮子精",[new TaskEnemyVO(3039,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23321,"挑战狮子精",[new TaskEnemyVO(3039,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23421,"挑战狮子精",[new TaskEnemyVO(3039,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23022,"挑战白象精",[new TaskEnemyVO(3040,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23122,"挑战白象精",[new TaskEnemyVO(3040,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23222,"挑战白象精",[new TaskEnemyVO(3040,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23322,"挑战白象精",[new TaskEnemyVO(3040,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23422,"挑战白象精",[new TaskEnemyVO(3040,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23023,"挑战大鹏精",[new TaskEnemyVO(3041,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23123,"挑战大鹏精",[new TaskEnemyVO(3041,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23223,"挑战大鹏精",[new TaskEnemyVO(3041,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23323,"挑战大鹏精",[new TaskEnemyVO(3041,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23423,"挑战大鹏精",[new TaskEnemyVO(3041,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23024,"挑战白鹿精",[new TaskEnemyVO(3042,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23124,"挑战白鹿精",[new TaskEnemyVO(3042,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23224,"挑战白鹿精",[new TaskEnemyVO(3042,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23324,"挑战白鹿精",[new TaskEnemyVO(3042,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23424,"挑战白鹿精",[new TaskEnemyVO(3042,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23025,"挑战地涌夫人",[new TaskEnemyVO(3043,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23125,"挑战地涌夫人",[new TaskEnemyVO(3043,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23225,"挑战地涌夫人",[new TaskEnemyVO(3043,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23325,"挑战地涌夫人",[new TaskEnemyVO(3043,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23425,"挑战地涌夫人",[new TaskEnemyVO(3043,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23026,"挑战豹子精",[new TaskEnemyVO(3044,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23126,"挑战豹子精",[new TaskEnemyVO(3044,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23226,"挑战豹子精",[new TaskEnemyVO(3044,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23326,"挑战豹子精",[new TaskEnemyVO(3044,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23426,"挑战豹子精",[new TaskEnemyVO(3044,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23027,"挑战黄狮精",[new TaskEnemyVO(3045,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23127,"挑战黄狮精",[new TaskEnemyVO(3045,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23227,"挑战黄狮精",[new TaskEnemyVO(3045,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23327,"挑战黄狮精",[new TaskEnemyVO(3045,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23427,"挑战黄狮精",[new TaskEnemyVO(3045,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23028,"挑战九灵元圣",[new TaskEnemyVO(3047,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23128,"挑战九灵元圣",[new TaskEnemyVO(3047,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23228,"挑战九灵元圣",[new TaskEnemyVO(3047,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23328,"挑战九灵元圣",[new TaskEnemyVO(3047,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23428,"挑战九灵元圣",[new TaskEnemyVO(3047,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23029,"挑战辟尘大王",[new TaskEnemyVO(3050,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23129,"挑战辟尘大王",[new TaskEnemyVO(3050,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23229,"挑战辟尘大王",[new TaskEnemyVO(3050,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23329,"挑战辟尘大王",[new TaskEnemyVO(3050,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23429,"挑战辟尘大王",[new TaskEnemyVO(3050,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
         this._tasks.push(new ConstTask(23030,"挑战玉兔精",[new TaskEnemyVO(3052,1)],[new BaseRewardVO(10000,400),new BaseRewardVO(10007,4)],"",""));
         this._tasks.push(new ConstTask(23130,"挑战玉兔精",[new TaskEnemyVO(3052,1)],[new BaseRewardVO(10000,800),new BaseRewardVO(10007,8)],"",""));
         this._tasks.push(new ConstTask(23230,"挑战玉兔精",[new TaskEnemyVO(3052,1)],[new BaseRewardVO(10000,1200),new BaseRewardVO(10007,16)],"",""));
         this._tasks.push(new ConstTask(23330,"挑战玉兔精",[new TaskEnemyVO(3052,1)],[new BaseRewardVO(10000,2500),new BaseRewardVO(10007,32)],"",""));
         this._tasks.push(new ConstTask(23430,"挑战玉兔精",[new TaskEnemyVO(3052,1)],[new BaseRewardVO(10000,5000),new BaseRewardVO(10007,64)],"",""));
      }
      
      public function newTask(param1:int) : BaseTask
      {
         var _loc2_:String = this.findTaskType(param1);
         switch(_loc2_)
         {
            case ConstData.TASK_STORY:
               return new StoryTask(this.findConstTask(param1));
            case ConstData.TASK_GOOD:
               return new GoodTask(this.findConstTask(param1));
            case ConstData.TASK_ENEMY:
               return new EnemyTask(this.findConstTask(param1));
            default:
               return null;
         }
      }
      
      public function findConstTask(param1:int) : ConstTask
      {
         var _loc2_:ConstTask = null;
         for each(_loc2_ in this._tasks)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findTaskTab(param1:int) : String
      {
         return String(param1).charAt(0);
      }
      
      public function findTaskType(param1:int) : String
      {
         return String(param1).charAt(1);
      }
   }
}

