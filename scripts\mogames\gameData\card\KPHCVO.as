package mogames.gameData.card
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.good.GameCardVO;
   
   public class KPHCVO extends BaseUseVO
   {
      public var oldID:Sint;
      
      public var newID:Sint;
      
      public function KPHCVO(param1:int, param2:int, param3:int, param4:Array = null)
      {
         this.oldID = new Sint(param1);
         this.newID = new Sint(param2);
         super(param3,param4);
      }
      
      public function get newCard() : GameCardVO
      {
         return GoodConfig.instance().newGood(this.newID.v) as GameCardVO;
      }
   }
}

