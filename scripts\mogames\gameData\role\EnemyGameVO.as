package mogames.gameData.role
{
   import file.EnemyAttConfig;
   import file.EnemyConfig;
   import mogames.gameData.base.Snum;
   import mogames.gameData.role.vo.RoleAttVO;
   import mogames.gameData.role.vo.RoleGameVO;
   import mogames.gameRole.HeroManager;
   import org.osflash.signals.Signal;
   
   public class EnemyGameVO extends RoleGameVO
   {
      public var baseVO:RoleAttVO;
      
      public var onUpdate:Signal = new Signal(String);
      
      public var enemyType:int = 0;
      
      private var _hpArg0:Snum = new Snum(1.25);
      
      private var _hpArg1:Snum = new Snum(1.25);
      
      private var _atkArg:Snum = new Snum(1.5);
      
      public var maxBar:int;
      
      public function EnemyGameVO(param1:int, param2:int = 0)
      {
         this.enemyType = param2;
         constVO = EnemyConfig.instance().findConstRole(param1);
         assetVO = EnemyConfig.instance().findRoleAsset(param1);
         super(param1);
      }
      
      public function initData(param1:int) : void
      {
         this.baseVO = EnemyAttConfig.instance().findRoleAtt(param1);
         this.maxBar = this.baseVO.maxBar;
         if(HeroManager.isDouble)
         {
            baseHP.v = this.enemyType == 1 ? int(this.baseVO.baseHP.v * this._hpArg1.v) : int(this.baseVO.baseHP.v * this._hpArg0.v);
            baseATK.v = this.baseVO.baseATK.v * this._atkArg.v;
         }
         else
         {
            baseHP.v = this.baseVO.baseHP.v;
            baseATK.v = this.baseVO.baseATK.v;
         }
         baseMP.v = this.baseVO.baseMP.v;
         basePDEF.v = this.baseVO.basePDEF.v;
         baseMDEF.v = this.baseVO.baseMDEF.v;
         baseCRIT.v = this.baseVO.baseCRIT.v;
         baseMISS.v = this.baseVO.baseMISS.v;
         baseMOVE.v = this.baseVO.baseMOVE.v;
         baseRUN.v = this.baseVO.baseMOVE.v * 2;
         updateAll();
         curHP.v = totalHP.v;
      }
      
      override public function changeHP(param1:int) : void
      {
         super.changeHP(param1);
         if(this.enemyType == 1)
         {
            this.onUpdate.dispatch("updateHP");
         }
      }
      
      override public function isBOSS() : Boolean
      {
         return this.enemyType == 1;
      }
   }
}

