package mogames.gameMission.mission.pindingshan.scene
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.WinEffect;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameRole.enemy.BOSSJinJiaoDaWang;
   import mogames.gameRole.enemy.BOSSJiuWeiHu;
   import mogames.gameRole.enemy.BOSSYinJiaoDaWang;
   import mogames.gameSystem.CitrusRender;
   import mogames.gameUI.dialog.StoryDialogModule;
   
   public class ScenePingDingShan04 extends BossScene
   {
      private var _pTrigger:LocalTriggerX;
      
      private var _bossYinJiao:BOSSYinJiaoDaWang;
      
      private var _bossJinJiao:BOSSJinJiaoDaWang;
      
      private var _bossJiuWei:BOSSJiuWeiHu;
      
      private var _enemies:Array = [{
         "id":new Sint(2006),
         "attID":new Sint(20062),
         "aiID":new Sint(20062),
         "dropID":new Sint(31)
      },{
         "id":new Sint(2012),
         "attID":new Sint(20121),
         "aiID":new Sint(20121),
         "dropID":new Sint(34)
      }];
      
      public function ScenePingDingShan04(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(384,212,666,174),new Point(711,426));
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this.initBossTrigger();
      }
      
      private function initBossTrigger() : void
      {
         this._pTrigger = new LocalTriggerX(_mission.onePlayer,new Point(713,487),1);
         this._pTrigger.okFunc = this.triggerBoss;
         this._pTrigger.start();
      }
      
      private function triggerBoss() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2013,1);
            startBossBattle();
         };
         this.addBOSS();
         EffectManager.instance().playAudio("DANGER");
         Layers.startShaking(8,10);
         if(FlagProxy.instance().isComplete(2013))
         {
            this.startBossBattle();
         }
         else
         {
            showDialog("STORY0024",func);
         }
      }
      
      override protected function addBOSS() : void
      {
         this._bossYinJiao = new BOSSYinJiaoDaWang();
         this._bossYinJiao.x = 1277;
         this._bossYinJiao.y = 325;
         this._bossYinJiao.initData(30051,30051);
         this._bossYinJiao.target = _mission.curTarget;
         add(this._bossYinJiao);
      }
      
      private function addJiuWei() : void
      {
         this._bossJiuWei = new BOSSJiuWeiHu();
         this._bossJiuWei.x = 705;
         this._bossJiuWei.y = 242;
         this._bossJiuWei.initBallPoint(new Point(705,202));
         this._bossJiuWei.initData(30111,30111,35);
         this._bossJiuWei.target = _mission.curTarget;
         this._bossJiuWei.enemyList = this._enemies.slice();
         this._bossJiuWei.activeEnemy(false);
         add(this._bossJiuWei);
         _boss = this._bossJiuWei;
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         setHeroEnable(true);
         this._bossYinJiao.activeEnemy(false);
         CitrusRender.instance().add(this.checkJinJiaoOut);
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      private function checkJinJiaoOut() : void
      {
         if(this._bossYinJiao.roleVO.hpPer <= 0.9)
         {
            StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(480,87),"哇呀呀，敢伤我兄弟！","BODY_JIN_JIAO",this.showJinJiao);
            CitrusRender.instance().remove(this.checkJinJiaoOut);
            EffectManager.instance().playBGM("BGM_BATTLE2");
         }
      }
      
      private function showJinJiao() : void
      {
         this._bossJinJiao = new BOSSJinJiaoDaWang(new Rectangle(0,0,1440,745));
         this._bossJinJiao.x = 1311;
         this._bossJinJiao.y = 177;
         this._bossJinJiao.initData(30061,30061);
         this._bossJinJiao.target = _mission.curTarget;
         add(this._bossJinJiao);
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,this._bossJinJiao.x,this._bossJinJiao.y);
         this._bossJinJiao.activeEnemy(false);
         this._bossJinJiao.setVelocityX(-10);
         this._bossJinJiao.setVelocityY(-10);
         this._bossJinJiao.forceJump();
         CitrusRender.instance().add(this.checkJiuWeiOut);
         CitrusRender.instance().add(this.checkWin);
      }
      
      private function checkJiuWeiOut() : void
      {
         if(this._bossYinJiao.isDead && this._bossJinJiao.isDead)
         {
            this.addJiuWei();
            CitrusRender.instance().remove(this.checkJiuWeiOut);
         }
      }
      
      private function checkWin() : void
      {
         if(this._bossYinJiao.isDead && this._bossJinJiao.isDead && this._bossJiuWei.isDead)
         {
            this.showWin();
            TaskProxy.instance().setComplete(11010);
            CitrusRender.instance().remove(this.checkWin);
         }
      }
      
      private function showWin() : void
      {
         Layers.lockGame = true;
         EffectManager.instance().stopAllSound();
         var _loc1_:WinEffect = new WinEffect();
         _loc1_.start();
         _winTimer.setTimeOut(3,handlerWin);
         _isWin = true;
      }
      
      override public function destroy() : void
      {
         this._bossJinJiao = null;
         this._bossYinJiao = null;
         this._bossJiuWei = null;
         CitrusRender.instance().remove(this.checkJinJiaoOut);
         CitrusRender.instance().remove(this.checkJiuWeiOut);
         CitrusRender.instance().remove(this.checkWin);
         if(this._pTrigger)
         {
            this._pTrigger.destroy();
         }
         this._pTrigger = null;
         super.destroy();
      }
   }
}

