package mogames.gameMission.mission.qisheng
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.enemy.BaseEnemy;
   import mogames.gameRole.enemy.QSJiaoMoWang;
   import mogames.gameRole.enemy.QSMeiHouWang;
   import mogames.gameRole.enemy.QSMiHouWang;
   import mogames.gameRole.enemy.QSNiuMoWang;
   import mogames.gameRole.enemy.QSPengMoWang;
   import mogames.gameRole.enemy.QSShiTuoWang;
   import mogames.gameRole.enemy.QSYuRongWang;
   import mogames.gameUI.fuben.TZQS.TZQSEnterModule;
   
   public class SceneQiSheng extends BossScene
   {
      private var _realBOSS:BaseEnemy;
      
      public function SceneQiSheng(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_BATTLE1");
         setWinPosition(new Rectangle(184,150,570,228),new Point(490,384));
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.addBOSS();
         this.startBossBattle();
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         setHeroEnable(true);
         this._realBOSS.activeEnemy(false);
         this._realBOSS.aiEnabled = true;
         this._realBOSS.onRole.add(listenBOSS);
      }
      
      override protected function addBOSS() : void
      {
         this._realBOSS = this.newBOSS();
         this._realBOSS.x = 816;
         this._realBOSS.y = 200;
         this._realBOSS.target = _mission.curTarget;
         add(this._realBOSS);
      }
      
      private function newBOSS() : BaseEnemy
      {
         var _loc1_:BaseEnemy = null;
         switch(TZQSEnterModule.activeVO.index.v)
         {
            case 0:
               _loc1_ = new QSNiuMoWang();
               _loc1_.initData(40101,40101,137);
               break;
            case 1:
               _loc1_ = new QSJiaoMoWang();
               _loc1_.initData(40111,40111,138);
               break;
            case 2:
               _loc1_ = new QSPengMoWang();
               _loc1_.initData(40121,40121,139);
               break;
            case 3:
               _loc1_ = new QSShiTuoWang();
               _loc1_.initData(40131,40131,140);
               break;
            case 4:
               _loc1_ = new QSMiHouWang();
               _loc1_.initData(40141,40141,141);
               break;
            case 5:
               _loc1_ = new QSYuRongWang();
               _loc1_.initData(40151,40151,142);
               break;
            case 6:
               _loc1_ = new QSMeiHouWang();
               _loc1_.initData(40161,40161,143);
         }
         return _loc1_;
      }
      
      override protected function handlerWin() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._realBOSS,
            "rect":_dropRect,
            "hero":this._realBOSS.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
         TZQSEnterModule.activeVO.setComplete();
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         Layers.setKeyEnable(false);
         MissionManager.instance().handlerWin(false,true);
      }
      
      override public function destroy() : void
      {
         this._realBOSS = null;
         super.destroy();
      }
   }
}

