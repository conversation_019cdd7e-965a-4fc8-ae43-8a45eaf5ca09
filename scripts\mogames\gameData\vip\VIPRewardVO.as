package mogames.gameData.vip
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gamePKG.PKGManager;
   
   public class VIPRewardVO
   {
      public var vip:Sint;
      
      protected var _list:Array;
      
      protected var _flag:Sint;
      
      public function VIPRewardVO(param1:int, param2:int, param3:Array)
      {
         super();
         this.vip = new Sint(param1);
         this._flag = new Sint(param2);
         this._list = param3;
      }
      
      public function get list() : Array
      {
         return this._list;
      }
      
      public function handlerGet(param1:Function) : void
      {
         var _loc2_:Array = RewardHandler.instance().newGiftReward(this._list);
         if(RewardHandler.instance().onReward(_loc2_))
         {
            FlagProxy.instance().setValue(this._flag.v,1);
            param1();
            PKGManager.saveData();
         }
      }
      
      public function get isGet() : Boolean
      {
         return FlagProxy.instance().isComplete(this._flag.v);
      }
   }
}

