package mogames.gameData.chenghao.ch
{
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.chenghao.BaseChenghaoVO;
   
   public class ChenghaoZQKL extends BaseChenghaoVO
   {
      public function ChenghaoZQKL(param1:int, param2:int, param3:String, param4:Boolean = false)
      {
         super(param1,param2,param3,param4);
      }
      
      override public function get hasGet() : Boolean
      {
         if(isGet.v == 1)
         {
            return true;
         }
         if(BagProxy.instance().getGoodNum(10016) >= 50)
         {
            isGet.v = 1;
         }
         return isGet.v == 1;
      }
      
      override public function get wayInfors() : String
      {
         if(this.hasGet)
         {
            return _wayInfors;
         }
         return _wayInfors + "<br>（已收集：" + BagProxy.instance().getGoodNum(10016) + "）";
      }
   }
}

