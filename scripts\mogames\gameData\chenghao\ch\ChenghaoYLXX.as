package mogames.gameData.chenghao.ch
{
   import mogames.gameData.chenghao.BaseChenghaoVO;
   import mogames.gameData.role.HeroProxy;
   
   public class ChenghaoYLXX extends BaseChenghaoVO
   {
      public function ChenghaoYLXX(param1:int, param2:int, param3:String)
      {
         super(param1,param2,param3);
      }
      
      override public function get hasGet() : Bo<PERSON>an
      {
         return HeroProxy.instance().horseVO != null && HeroProxy.instance().pigVO != null && HeroProxy.instance().bonzeVO != null;
      }
   }
}

