package mogames.gameData.heroSkill
{
   import mogames.gameData.base.Sint;
   
   public class SkillCountVO
   {
      public var id:Sint;
      
      public var baseMP:Sint;
      
      public var argDic:Object;
      
      public function SkillCountVO(param1:int, param2:int, param3:Object = null)
      {
         super();
         this.id = new Sint(param1);
         this.baseMP = new Sint(param2);
         this.argDic = param3;
      }
   }
}

