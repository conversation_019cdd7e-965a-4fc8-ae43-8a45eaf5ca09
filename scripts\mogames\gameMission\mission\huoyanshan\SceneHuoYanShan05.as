package mogames.gameMission.mission.huoyanshan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.WinEffect;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.huoyundong.TrapHuoZhu;
   import mogames.gameMission.mission.huoyundong.TrapTieLian;
   import mogames.gameObj.box2d.BlockTile;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BOSSNiuMoWang;
   import mogames.gameRole.enemy.BOSSTieShan;
   import mogames.gameRole.enemy.BOSSZhenNiuMo;
   import mogames.gameSystem.CitrusRender;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.dialog.StoryDialogModule;
   
   public class SceneHuoYanShan05 extends BossScene
   {
      private var _arr0:Array = [-23,150,616,790];
      
      private var _arr1:Array = [-22,163,633,806];
      
      private var _huozhus:Array;
      
      private var _curs:Array;
      
      private var _huozhuTimer0:CitrusTimer;
      
      private var _huozhuTimer1:CitrusTimer;
      
      private var _tielianArg:Object = {
         "hurt":new Sint(180),
         "keepTime":new Snum(5)
      };
      
      private var _huozhuArg:Object = {
         "hurt":new Sint(380),
         "keepTime":new Snum(3),
         "interval":new Snum(11)
      };
      
      private var _tieshan:BOSSTieShan;
      
      private var _failBoss:BOSSNiuMoWang;
      
      private var _zhenBoss:BOSSZhenNiuMo;
      
      private var _block:BlockTile;
      
      public function SceneHuoYanShan05(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(405,204,428,124),new Point(490,390));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            startBattle();
            FlagProxy.instance().setValue(2037,1);
            TaskProxy.instance().addTask(11022);
            TaskProxy.instance().setComplete(11021);
         };
         this.initHuoZhu();
         this.addFailBOSS();
         if(!FlagProxy.instance().isComplete(2037))
         {
            showDialog("STORY0063",func);
         }
         else
         {
            this.startBattle();
         }
      }
      
      private function initHuoZhu() : void
      {
         var _loc1_:int = 0;
         var _loc2_:TrapTieLian = null;
         var _loc3_:TrapHuoZhu = null;
         this._huozhus = [];
         this._curs = [];
         _loc1_ = 0;
         while(_loc1_ < 4)
         {
            _loc2_ = new TrapTieLian();
            _loc2_.x = this._arr0[_loc1_];
            _loc2_.y = 491;
            _loc2_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",2,5);
            _loc2_.createHitHurt(this._tielianArg.hurt.v,0,false);
            _loc3_ = new TrapHuoZhu();
            _loc3_.initArg(this._arr1[_loc1_],530,344);
            _loc3_.createHitEffect("EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE",5,5);
            _loc3_.createHitHurt(this._huozhuArg.hurt.v,0,false);
            _loc3_.relateTrap = _loc2_;
            add(_loc2_);
            add(_loc3_);
            this._huozhus[_loc1_] = _loc3_;
            _loc1_++;
         }
         this._huozhuTimer0 = new CitrusTimer();
         this._huozhuTimer1 = new CitrusTimer();
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v,this.showHuoZhu);
      }
      
      private function showHuoZhu() : void
      {
         var _loc3_:int = 0;
         var _loc5_:TrapHuoZhu = null;
         this._curs = [];
         var _loc1_:int = Math.random() * 4 + 1;
         var _loc2_:Array = this._huozhus.slice();
         var _loc4_:int = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = Math.random() * _loc2_.length;
            this._curs[_loc4_] = _loc2_[_loc3_];
            _loc2_.splice(_loc3_,1);
            _loc4_++;
         }
         for each(_loc5_ in this._curs)
         {
            _loc5_.startMove();
         }
         this._huozhuTimer1.setTimeOut(this._huozhuArg.keepTime.v,this.resumeHuoZhu);
      }
      
      private function resumeHuoZhu() : void
      {
         var _loc1_:TrapHuoZhu = null;
         for each(_loc1_ in this._curs)
         {
            _loc1_.resume();
            _loc1_.relateTrap.showRed(this._tielianArg.keepTime.v);
         }
         this._huozhuTimer0.setTimeOut(this._huozhuArg.interval.v + this._tielianArg.keepTime.v,this.showHuoZhu);
      }
      
      private function startBattle() : void
      {
         setHeroEnable(true);
         this._failBoss.activeEnemy(false);
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      private function addFailBOSS() : void
      {
         this._failBoss = new BOSSNiuMoWang();
         this._failBoss.x = 850;
         this._failBoss.y = 300;
         this._failBoss.initData(30291,30291);
         this._failBoss.target = _mission.onePlayer;
         this._failBoss.onRole.add(this.listenFailDead);
         add(this._failBoss);
      }
      
      private function listenFailDead(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(680,87),"吼——————泼猴，让你见见你大哥的真本事！","BODY_NIU_MO_WANG",this.addBOSS,2,false);
         EffectManager.instance().playBGM("BGM_BATTLE1");
      }
      
      override protected function addBOSS() : void
      {
         this._zhenBoss = new BOSSZhenNiuMo();
         this._zhenBoss.x = 808;
         this._zhenBoss.y = 392;
         this._zhenBoss.initData(30301,30301,77);
         this._zhenBoss.target = _mission.onePlayer;
         add(this._zhenBoss);
         this._zhenBoss.activeEnemy(false);
         _boss = this._zhenBoss;
         CitrusRender.instance().add(this.checkTieShan);
         Layers.startShaking(10,10);
         if(Boolean(_mission.onePlayer) && !_mission.onePlayer.isDead)
         {
            _mission.onePlayer.x = 100;
            _mission.onePlayer.x = 300;
         }
         if(Boolean(_mission.twoPlayer) && !_mission.twoPlayer.isDead)
         {
            _mission.twoPlayer.x = 100;
            _mission.twoPlayer.x = 300;
         }
         this._block = new BlockTile("block",{
            "width":20,
            "height":1070,
            "x":645,
            "y":-30
         });
         add(this._block);
         this._zhenBoss.block = this._block;
      }
      
      private function checkTieShan() : void
      {
         if(this._zhenBoss.roleVO.hpPer <= 0.5)
         {
            StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(680,87),"夫君，我来助你！","BODY_XIE_ZI",this.addTieShan,2,false);
            EffectManager.instance().playBGM("BGM_BATTLE2");
            CitrusRender.instance().remove(this.checkTieShan);
            CitrusRender.instance().add(this.checkWin);
         }
      }
      
      private function addTieShan() : void
      {
         this._tieshan = new BOSSTieShan();
         this._tieshan.x = 250;
         this._tieshan.y = 300;
         if(_mission.hasMark("battleTieShan"))
         {
            this._tieshan.initData(30282,30281);
         }
         else
         {
            this._tieshan.initData(30281,30281);
         }
         this._tieshan.connect = this._zhenBoss;
         this._tieshan.target = _mission.onePlayer;
         this._tieshan.activeEnemy(false);
         add(this._tieshan);
      }
      
      private function checkWin() : void
      {
         var _loc1_:WinEffect = null;
         if(this._zhenBoss.isDead && this._tieshan.isDead)
         {
            CitrusRender.instance().remove(this.checkWin);
            Layers.lockGame = true;
            EffectManager.instance().stopAllSound();
            _loc1_ = new WinEffect();
            _loc1_.start();
            _winTimer.setTimeOut(3,handlerWin);
            _isWin = true;
            TaskProxy.instance().setComplete(11022);
            this.destroyHuoZhu();
         }
      }
      
      private function destroyHuoZhu() : void
      {
         var _loc1_:TrapHuoZhu = null;
         this._huozhuTimer0.pause();
         this._huozhuTimer1.pause();
         for each(_loc1_ in this._huozhus)
         {
            _loc1_.resume();
            _loc1_.relateTrap.resume();
         }
      }
      
      override public function destroy() : void
      {
         this._curs = null;
         this._huozhus = null;
         this._huozhuTimer0.destroy();
         this._huozhuTimer1.destroy();
         this._huozhuTimer0 = null;
         this._huozhuTimer1 = null;
         CitrusRender.instance().remove(this.checkWin);
         CitrusRender.instance().remove(this.checkTieShan);
         this._zhenBoss = null;
         this._failBoss = null;
         this._tieshan = null;
         super.destroy();
      }
   }
}

