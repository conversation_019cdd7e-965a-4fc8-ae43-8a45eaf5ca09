package mogames.gameMission.mission.heifengdong
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.box2d.AISensor;
   import mogames.gameObj.box2d.Door;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class SceneHeiFengDong01 extends BaseScene
   {
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _tengman:BlockTip;
      
      public function SceneHeiFengDong01(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [AISensor];
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER0");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            initEnemies();
            FlagProxy.instance().setValue(2002,1);
            TaskProxy.instance().addTask(11002);
         };
         _mission.cleanLoadUI();
         this._tengman = new BlockTip(this.checkTengManDialog,this.openDoor,"PIC_TENG_MAN");
         this._tengman.x = 1806;
         this._tengman.y = 119;
         add(this._tengman);
         if(FlagProxy.instance().isComplete(2003))
         {
            this._tengman.activeATK();
         }
         if(FlagProxy.instance().isComplete(2002))
         {
            this.initEnemies();
         }
         else
         {
            showDialog("STORY0010",func);
         }
      }
      
      private function initEnemies() : void
      {
         this._eTrigger0 = new EnemyTrigger(_mission,[[44,400,150,90],[776,400,150,90]],3011,this.triggerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1314,100,150,90],[1718,400,150,90]],3012,null);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(280,480),1,new Rectangle(0,0,960,600));
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1060,480),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger0.setBlock(false,false,false,true);
         this._pTrigger1.setBlock(false,false,false,false);
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function checkTengManDialog() : void
      {
         var func:Function = null;
         func = function():void
         {
            MiniMsgMediator.instance().showMsg("藤蔓看起来很脆弱，攻击一下试试看！",480,50);
            _tengman.activeATK();
            FlagProxy.instance().setValue(2003,1);
         };
         if(FlagProxy.instance().isComplete(2003))
         {
            return;
         }
         showDialog("STORY0011",func);
      }
      
      private function openDoor() : void
      {
         MiniMsgMediator.clean();
         EffectManager.instance().addSkinSpread(5,new Point(this._tengman.x,this._tengman.y),["PIC_DEAD_TENG_MAN6","PIC_DEAD_TENG_MAN5","PIC_DEAD_TENG_MAN4","PIC_DEAD_TENG_MAN3","PIC_DEAD_TENG_MAN2","PIC_DEAD_TENG_MAN1"]);
         (getObjectByName("holeDoor") as Door).setActive();
      }
      
      override public function destroy() : void
      {
         MiniMsgMediator.clean();
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         super.destroy();
      }
   }
}

