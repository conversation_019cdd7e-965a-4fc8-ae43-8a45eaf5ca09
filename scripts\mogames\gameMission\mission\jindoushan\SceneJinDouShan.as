package mogames.gameMission.mission.jindoushan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.CitrusMCSprite;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BOSSDuJiao;
   import mogames.gameRole.enemy.BOSSZhenDuJiao;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameUI.dialog.StoryDialogModule;
   
   public class SceneJinDouShan extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _failBoss:BOSSDuJiao;
      
      private var _failPos:Point;
      
      private var _moveBG:CitrusMCSprite;
      
      public function SceneJinDouShan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_SUNSHINE");
         setWinPosition(new Rectangle(4100,150,514,148),new Point(4283,420));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2031,1);
            TaskProxy.instance().addTask(11019);
         };
         _mission.cleanLoadUI();
         this._moveBG = new CitrusMCSprite("MC_JIN_DOU_SHAN_BG_CLIP",{
            "width":1920,
            "height":600,
            "parallaxX":0,
            "group":0
         });
         this._moveBG.changeAnimation("idle",true);
         add(this._moveBG);
         this.addFailBOSS();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2031))
         {
            showDialog("STORY0057",func);
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[448,345,150,100],[692,345,150,100]],15011,this.handlerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1233,306,150,100],[1509,306,150,100]],15012,this.triggerEnd1);
         this._eTrigger2 = new EnemyTrigger(_mission,[[2108,306,150,100],[2538,306,150,100]],15013,this.triggerEnd2);
         this._eTrigger3 = new EnemyTrigger(_mission,[[3154,325,150,100],[3430,325,150,100]],15014,this.triggerEnd3);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(300,480),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1178,450),1,new Rectangle(960,0,960,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2394,440),1,new Rectangle(1920,0,960,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(3099,470),1,new Rectangle(2880,0,960,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(4250,470),1,new Rectangle(3840,0,960,600));
         this._pTrigger1.setBlock(false,false,true,true);
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,false);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this.handlerTrigger1;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger3.okFunc = this.handlerTrigger3;
         this._pTrigger4.okFunc = this.startBattle;
         this._pTrigger0.start();
      }
      
      private function handlerTrigger3() : void
      {
         this._pTrigger4.start();
         this._moveBG.pauseAnimation(false);
      }
      
      private function handlerTrigger1() : void
      {
         this._pTrigger2.start();
         this._moveBG.changeAnimation("move");
      }
      
      private function triggerEnd3() : void
      {
         this.handlerEnd();
         CitrusLock.instance().lock(new Rectangle(2880,0,1920,600),false,false,true,false);
         this._moveBG.pauseAnimation(true);
      }
      
      private function triggerEnd2() : void
      {
         this.handlerEnd();
         CitrusLock.instance().lock(new Rectangle(1920,0,2880,600),false,false,true,true);
      }
      
      private function triggerEnd1() : void
      {
         this.handlerEnd();
         CitrusLock.instance().lock(new Rectangle(960,0,3840,600),false,false,true,false);
         this._moveBG.pauseAnimation(true);
      }
      
      private function handlerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function startBattle() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2032,1);
            activeFailBOSS();
         };
         if(!FlagProxy.instance().isComplete(2032))
         {
            showDialog("STORY0058",func);
         }
         else
         {
            this.activeFailBOSS();
         }
      }
      
      private function addFailBOSS() : void
      {
         this._failBoss = new BOSSDuJiao();
         this._failBoss.x = 4624;
         this._failBoss.y = 330;
         this._failBoss.initData(30241,30241);
         this._failBoss.target = _mission.onePlayer;
         add(this._failBoss);
         this._failBoss.onRole.add(this.listenDead);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSZhenDuJiao();
         _boss.x = this._failPos.x;
         _boss.y = this._failPos.y;
         _boss.initData(30251,30251,69);
         _boss.target = _mission.onePlayer;
         add(_boss);
         this._failBoss.kill = true;
         startBossBattle();
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,_boss.x,_boss.y);
      }
      
      private function listenDead(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         this._failPos = new Point(this._failBoss.x,this._failBoss.y);
         StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(680,87),"有两下子！让你看看我的变化！","BODY_DU_JIAO",this.addBOSS,2,false);
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      private function activeFailBOSS() : void
      {
         setHeroEnable(true);
         this._failBoss.activeEnemy(false);
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      override protected function handlerWin() : void
      {
         TaskProxy.instance().setComplete(11019);
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._failBoss = null;
         this._moveBG = null;
         super.destroy();
      }
   }
}

