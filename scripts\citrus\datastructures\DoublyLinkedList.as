package citrus.datastructures
{
   public class DoublyLinkedList
   {
      public var head:DoublyLinkedListNode;
      
      public var tail:DoublyLinkedListNode;
      
      protected var _count:uint;
      
      public function DoublyLinkedList()
      {
         super();
         this.tail = null;
         this.head = null;
         this._count = 0;
      }
      
      public function append(param1:*) : DoublyLinkedListNode
      {
         var _loc2_:DoublyLinkedListNode = new DoublyLinkedListNode(param1);
         if(this.tail != null)
         {
            this.tail.next = _loc2_;
            _loc2_.prev = this.tail;
            this.tail = _loc2_;
         }
         else
         {
            this.head = this.tail = _loc2_;
         }
         ++this._count;
         return this.tail;
      }
      
      public function appendNode(param1:DoublyLinkedListNode) : DoublyLinkedList
      {
         if(this.head != null)
         {
            this.tail.next = param1;
            param1.prev = this.tail;
            this.tail = param1;
         }
         else
         {
            this.head = this.tail = param1;
         }
         ++this._count;
         return this;
      }
      
      public function prepend(param1:*) : DoublyLinkedListNode
      {
         var _loc2_:DoublyLinkedListNode = new DoublyLinkedListNode(param1);
         if(this.head != null)
         {
            this.head.prev = _loc2_;
            _loc2_.next = this.head;
            this.head = _loc2_;
         }
         else
         {
            this.head = this.tail = _loc2_;
         }
         ++this._count;
         return this.head;
      }
      
      public function prependNode(param1:DoublyLinkedListNode) : DoublyLinkedList
      {
         if(this.head != null)
         {
            this.head.prev = param1;
            param1.next = this.head;
            this.head = param1;
         }
         else
         {
            this.head = this.tail = param1;
         }
         ++this._count;
         return this;
      }
      
      public function removeNode(param1:DoublyLinkedListNode) : *
      {
         var _loc2_:* = param1.data;
         var _loc3_:Boolean = false;
         if(param1 == this.head)
         {
            this.removeHead();
            _loc3_ = true;
         }
         else
         {
            param1.prev.next = param1.next;
         }
         if(param1 == this.tail)
         {
            this.removeTail();
            _loc3_ = true;
         }
         else
         {
            param1.next.prev = param1.prev;
         }
         if(!_loc3_)
         {
            --this._count;
         }
         return _loc2_;
      }
      
      public function removeHead() : *
      {
         var _loc2_:* = undefined;
         var _loc1_:DoublyLinkedListNode = this.head;
         if(this.head != null)
         {
            _loc2_ = _loc1_.data;
            this.head = this.head.next;
            if(this.head != null)
            {
               this.head.prev = null;
            }
            --this._count;
            return _loc2_;
         }
      }
      
      public function removeTail() : *
      {
         var _loc2_:* = undefined;
         var _loc1_:DoublyLinkedListNode = this.tail;
         if(this.tail != null)
         {
            _loc2_ = _loc1_.data;
            this.tail = this.tail.prev;
            if(this.tail != null)
            {
               this.tail.next = null;
            }
            --this._count;
            return _loc2_;
         }
      }
      
      public function get length() : uint
      {
         return this._count;
      }
      
      public function content() : String
      {
         var _loc1_:DoublyLinkedListNode = this.head;
         var _loc2_:String = "";
         while(_loc1_ != null)
         {
            _loc2_ += String(_loc1_.data) + " ";
            _loc1_ = _loc1_.next;
         }
         return _loc2_;
      }
   }
}

