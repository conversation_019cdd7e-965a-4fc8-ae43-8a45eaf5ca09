package mogames.gameMission.mission.heishuihe
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.HitDoor;
   import mogames.gameObj.box2d.Door;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneHeiShuiHe01 extends BaseScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _door:HitDoor;
      
      public function SceneHeiShuiHe01(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [HitDoor];
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER3");
         sceneType = 1;
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2026,1);
            TaskProxy.instance().addTask(11015);
         };
         this.initDoor1202();
         _mission.cleanLoadUI();
         this.initEnemies();
         if(!FlagProxy.instance().isComplete(2026))
         {
            showDialog("STORY0050",func);
         }
      }
      
      private function initDoor1202() : void
      {
         this._door = getObjectByName("hitDoor1202") as HitDoor;
         this._door.hitFunc = this.openDoor;
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[75,295,150,100],[388,294,150,100]],12011,this.triggerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[765,294,150,100],[1077,294,150,100]],12012,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1317,294,150,100],[1547,294,150,100]],12013,this.triggerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[2257,294,150,100],[2485,294,150,100]],12014);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(301,480),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1004,480),1,new Rectangle(340,0,1400,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1822,480),1,new Rectangle(1070,0,1400,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(2614,480),1);
         this._pTrigger1.setBlock(false,false,true,true);
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function openDoor() : void
      {
         (getObjectByName("door1202") as Door).setActive();
      }
      
      override public function destroy() : void
      {
         this._door = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         super.destroy();
      }
   }
}

