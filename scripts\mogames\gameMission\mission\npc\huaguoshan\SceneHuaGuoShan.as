package mogames.gameMission.mission.npc.huaguoshan
{
   import flash.geom.Point;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameObj.box2d.npc.BaseNPC;
   import mogames.gameUI.activity.RewardGQModule;
   import mogames.gameUI.fabao.FBMainModule;
   import mogames.gameUI.forge.ForgeModule;
   import mogames.gameUI.gem.GemMainModule;
   import mogames.gameUI.liandan.LianDanModule;
   import mogames.gameUI.shop.ShopModule;
   
   public class SceneHuaGuoShan extends BaseScene
   {
      private var _npc100:BaseNPC;
      
      private var _npc101:BaseNPC;
      
      private var _npc102:BaseNPC;
      
      private var _npc103:BaseNPC;
      
      private var _npc104:BaseNPC;
      
      private var _npc106:BaseNPC;
      
      private var _npc107:BaseNPC;
      
      public function SceneHuaGuoShan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_HUAGUOSHAN");
         EventManager.addEventListener(UIEvent.NPC_EVENT,this.onNPC,false,0,true);
      }
      
      override protected function handlerInit() : void
      {
         this.initNPC();
         super.handlerInit();
         view.camera.center = new Point(0.5,0.5);
         addLeaveDoor(1360,720);
      }
      
      private function initNPC() : void
      {
         if(FlagProxy.instance().isComplete(102))
         {
            this._npc100 = new BaseNPC(100,"MC_NPC_CLIP100",200,120);
            this._npc100.x = 370;
            this._npc100.y = 741;
            this._npc100.nearSound = "NEAR100";
            add(this._npc100);
         }
         if(FlagProxy.instance().isComplete(104))
         {
            this._npc104 = new BaseNPC(104,"MC_NPC_CLIP104",200,120);
            this._npc104.x = 1345;
            this._npc104.y = 410;
            this._npc104.nearSound = "NEAR104";
            add(this._npc104);
         }
         if(FlagProxy.instance().isComplete(105))
         {
            this._npc103 = new BaseNPC(103,"MC_NPC_CLIP103",200,120);
            this._npc103.x = 518;
            this._npc103.y = 390;
            this._npc103.nearSound = "NEAR103";
            add(this._npc103);
         }
         if(FlagProxy.instance().isComplete(106))
         {
            this._npc102 = new BaseNPC(102,"MC_NPC_CLIP102",200,120);
            this._npc102.x = 608;
            this._npc102.y = 740;
            this._npc102.nearSound = "NEAR102";
            add(this._npc102);
         }
         if(FlagProxy.instance().isComplete(107))
         {
            this._npc101 = new BaseNPC(101,"MC_NPC_CLIP101",200,120);
            this._npc101.x = 970;
            this._npc101.y = 741;
            this._npc101.nearSound = "NEAR101";
            add(this._npc101);
         }
         if(FlagProxy.instance().isComplete(109))
         {
            this._npc106 = new BaseNPC(106,"MC_NPC_CLIP106",200,120);
            this._npc106.x = 148;
            this._npc106.y = 294;
            add(this._npc106);
         }
      }
      
      private function onNPC(param1:UIEvent) : void
      {
         if(param1.data.type != "onNPC")
         {
            return;
         }
         setHeroEnable(false);
         switch(param1.data.data)
         {
            case 100:
               ForgeModule.instance().init(enabledKey);
               break;
            case 101:
               LianDanModule.instance().init(enabledKey);
               break;
            case 102:
               ShopModule.instance().init(101,enabledKey,"CLOSE102");
               break;
            case 103:
               FBMainModule.instance().init(enabledKey);
               break;
            case 104:
               ShopModule.instance().init(102,enabledKey,"CLOSE104");
               break;
            case 106:
               GemMainModule.instance().init(enabledKey);
               break;
            case 107:
               RewardGQModule.instance().init();
         }
      }
      
      override protected function listenLeave() : void
      {
         (_mission as HuaGuoShanMission).enterShuiLianDong();
      }
      
      override public function destroy() : void
      {
         EventManager.removeEventListener(UIEvent.NPC_EVENT,this.onNPC);
         this._npc100 = null;
         this._npc101 = null;
         this._npc102 = null;
         this._npc103 = null;
         this._npc104 = null;
         this._npc106 = null;
         super.destroy();
      }
   }
}

