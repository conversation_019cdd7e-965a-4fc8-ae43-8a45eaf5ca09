package citrus.input
{
   import citrus.core.CitrusEngine;
   
   public class InputController
   {
      public static var hideParamWarnings:<PERSON><PERSON><PERSON> = false;
      
      public var name:String;
      
      protected var _ce:CitrusEngine;
      
      protected var _input:Input;
      
      protected var _initialized:Boolean;
      
      protected var _enabled:Boolean = true;
      
      protected var _updateEnabled:Boolean = false;
      
      protected var _defaultChannel:uint = 0;
      
      private var action:InputAction;
      
      public function InputController(param1:String, param2:Object = null)
      {
         super();
         this.name = param1;
         this.setParams(param2);
         this._ce = CitrusEngine.getInstance();
         this._input = this._ce.input;
         this._ce.input.addController(this);
      }
      
      public function update() : void
      {
      }
      
      protected function triggerON(param1:String, param2:Number = 0, param3:String = null, param4:int = -1) : InputAction
      {
         if(this._enabled)
         {
            this.action = InputAction.create(param1,this,param4 < 0 ? this.defaultChannel : uint(param4),param2,param3);
            this._input.actionON.dispatch(this.action);
            return this.action;
         }
         return null;
      }
      
      protected function triggerOFF(param1:String, param2:Number = 0, param3:String = null, param4:int = -1) : InputAction
      {
         if(this._enabled)
         {
            this.action = InputAction.create(param1,this,param4 < 0 ? this.defaultChannel : uint(param4),param2,param3);
            this._input.actionOFF.dispatch(this.action);
            return this.action;
         }
         return null;
      }
      
      protected function triggerCHANGE(param1:String, param2:Number = 0, param3:String = null, param4:int = -1) : InputAction
      {
         if(this._enabled)
         {
            this.action = InputAction.create(param1,this,param4 < 0 ? this.defaultChannel : uint(param4),param2,param3);
            this._input.actionCHANGE.dispatch(this.action);
            return this.action;
         }
         return null;
      }
      
      protected function triggerONCE(param1:String, param2:Number = 0, param3:String = null, param4:int = -1) : InputAction
      {
         if(this._enabled)
         {
            this.action = InputAction.create(param1,this,param4 < 0 ? this.defaultChannel : uint(param4),param2,param3,InputPhase.END);
            this._input.actionON.dispatch(this.action);
            this.action = InputAction.create(param1,this,param4 < 0 ? this.defaultChannel : uint(param4),param2,param3,InputPhase.END);
            this._input.actionOFF.dispatch(this.action);
            return this.action;
         }
         return null;
      }
      
      public function get defaultChannel() : uint
      {
         return this._defaultChannel;
      }
      
      public function set defaultChannel(param1:uint) : void
      {
         if(param1 == this._defaultChannel)
         {
            return;
         }
         this._input.stopActionsOf(this);
         this._defaultChannel = param1;
      }
      
      public function get enabled() : Boolean
      {
         return this._enabled;
      }
      
      public function set enabled(param1:Boolean) : void
      {
         this._enabled = param1;
      }
      
      public function get updateEnabled() : Boolean
      {
         return this._updateEnabled;
      }
      
      public function destroy() : void
      {
         this._input.removeController(this);
         this.action = null;
      }
      
      public function toString() : String
      {
         return this.name;
      }
      
      protected function setParams(param1:Object) : void
      {
         var param:String = null;
         var object:Object = param1;
         for(param in object)
         {
            try
            {
               if(object[param] == "true")
               {
                  this[param] = true;
               }
               else if(object[param] == "false")
               {
                  this[param] = false;
               }
               else
               {
                  this[param] = object[param];
               }
            }
            catch(e:Error)
            {
               if(hideParamWarnings)
               {
               }
            }
         }
         this._initialized = true;
      }
   }
}

