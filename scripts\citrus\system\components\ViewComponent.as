package citrus.system.components
{
   import citrus.system.Component;
   import citrus.view.ICitrusArt;
   import citrus.view.ISpriteView;
   import org.osflash.signals.Signal;
   
   public class ViewComponent extends Component implements ISpriteView
   {
      public var onAnimationChange:Signal;
      
      protected var _x:Number = 0;
      
      protected var _y:Number = 0;
      
      protected var _rotation:Number = 0;
      
      protected var _inverted:Boolean = false;
      
      protected var _parallaxX:Number = 1;
      
      protected var _parallaxY:Number = 1;
      
      protected var _animation:String = "";
      
      protected var _visible:Boolean = true;
      
      protected var _touchable:Boolean = false;
      
      protected var _view:* = 16711680;
      
      protected var _art:ICitrusArt;
      
      private var _group:uint = 0;
      
      private var _offsetX:Number = 0;
      
      private var _offsetY:Number = 0;
      
      private var _registration:String = "center";
      
      public function ViewComponent(param1:String, param2:Object = null)
      {
         super(param1,param2);
         this.onAnimationChange = new Signal();
      }
      
      public function handleArtReady(param1:ICitrusArt) : void
      {
      }
      
      public function handleArtChanged(param1:ICitrusArt) : void
      {
      }
      
      override public function update(param1:Number) : void
      {
         super.update(param1);
      }
      
      override public function destroy() : void
      {
         this.onAnimationChange.removeAll();
         this._art = null;
         super.destroy();
      }
      
      public function getBody() : *
      {
         return null;
      }
      
      public function get x() : Number
      {
         return this._x;
      }
      
      public function set x(param1:Number) : void
      {
         this._x = param1;
      }
      
      public function get y() : Number
      {
         return this._y;
      }
      
      public function set y(param1:Number) : void
      {
         this._y = param1;
      }
      
      public function get z() : Number
      {
         return 0;
      }
      
      public function get width() : Number
      {
         return 0;
      }
      
      public function get height() : Number
      {
         return 0;
      }
      
      public function get depth() : Number
      {
         return 0;
      }
      
      public function get velocity() : Array
      {
         return null;
      }
      
      public function get rotation() : Number
      {
         return this._rotation;
      }
      
      public function set rotation(param1:Number) : void
      {
         this._rotation = param1;
      }
      
      public function get parallaxX() : Number
      {
         return this._parallaxX;
      }
      
      public function set parallaxX(param1:Number) : void
      {
         this._parallaxX = param1;
      }
      
      public function get parallaxY() : Number
      {
         return this._parallaxY;
      }
      
      public function set parallaxY(param1:Number) : void
      {
         this._parallaxY = param1;
      }
      
      public function get group() : uint
      {
         return this._group;
      }
      
      public function set group(param1:uint) : void
      {
         this._group = param1;
      }
      
      public function get visible() : Boolean
      {
         return this._visible;
      }
      
      public function set visible(param1:Boolean) : void
      {
         this._visible = param1;
      }
      
      public function get touchable() : Boolean
      {
         return this._touchable;
      }
      
      public function set touchable(param1:Boolean) : void
      {
         this._touchable = param1;
      }
      
      public function get view() : *
      {
         return this._view;
      }
      
      public function set view(param1:*) : void
      {
         this._view = param1;
      }
      
      public function get art() : ICitrusArt
      {
         return this._art;
      }
      
      public function get animation() : String
      {
         return this._animation;
      }
      
      public function get inverted() : Boolean
      {
         return this._inverted;
      }
      
      public function get offsetX() : Number
      {
         return this._offsetX;
      }
      
      public function set offsetX(param1:Number) : void
      {
         this._offsetX = param1;
      }
      
      public function get offsetY() : Number
      {
         return this._offsetY;
      }
      
      public function set offsetY(param1:Number) : void
      {
         this._offsetY = param1;
      }
      
      public function get registration() : String
      {
         return this._registration;
      }
      
      public function set registration(param1:String) : void
      {
         this._registration = param1;
      }
   }
}

