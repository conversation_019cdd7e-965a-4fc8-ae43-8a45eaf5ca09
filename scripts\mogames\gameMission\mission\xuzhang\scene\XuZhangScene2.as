package mogames.gameMission.mission.xuzhang.scene
{
   import com.greensock.TweenLite;
   import com.greensock.easing.Linear;
   import file.StoryConfig;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import mogames.Layers;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameObj.display.BaseSprite;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.dialog.DialogMediator;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.FilterFactory;
   import utils.MethodUtil;
   import utils.TxtUtil;
   
   public class XuZhangScene2 extends BaseSprite
   {
      private var _bg:MovieClip;
      
      private var _fuzhou:MovieClip;
      
      private var _total:int;
      
      private var _mask:Sprite;
      
      private var _role:CitrusMC;
      
      private var _okFunc:Function;
      
      public function XuZhangScene2(param1:Function)
      {
         super();
         this._okFunc = param1;
         this._bg = GameInLoader.instance().findAsset("MC_SCENE_XUZHANG_12");
         addChild(this._bg);
         this._fuzhou = GameInLoader.instance().findAsset("UI_FU_ZHOU_CLIP") as MovieClip;
         this._fuzhou.x = 130;
         this._fuzhou.y = 223;
         MethodUtil.setMousable(this._fuzhou,false);
         addChild(this._fuzhou);
         this.initFuZhou();
         this._role = new CitrusMC();
         var _loc2_:CitrusTimer = new CitrusTimer();
         _loc2_.setTimeOut(1,this.handlerStory);
         EffectManager.instance().playBGM("BGM_BIRD");
      }
      
      private function handlerStory() : void
      {
         DialogMediator.instance().init(StoryConfig.instance().findDialog("STORY0003"),this.showTangSeng);
      }
      
      private function showTangSeng() : void
      {
         this._role.setupMC(GameInLoader.instance().findAsset("ROLE_TANG_SENG"),null);
         this._role.x = 1018;
         this._role.y = 507;
         addChild(this._role);
         this._role.changeAnimation("move",true);
         TweenLite.to(this._role,6,{
            "x":525,
            "ease":Linear.easeNone,
            "onComplete":this.nextDialog
         });
         EffectManager.instance().playBGM("BGM_TANGSENG");
      }
      
      private function nextDialog() : void
      {
         var func:Function = null;
         func = function():void
         {
            showArrow();
            MiniMsgMediator.instance().showMsg("请用" + TxtUtil.setColor("鼠标点击符咒","99ff00") + "以解救孙悟空！",480,50);
            MethodUtil.setMousable(_fuzhou,true);
         };
         this._role.changeAnimation("stand",true);
         DialogMediator.instance().init(StoryConfig.instance().findDialog("STORY0004"),func);
      }
      
      private function showArrow() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 6)
         {
            this._fuzhou["mcArrow" + _loc1_].visible = true;
            _loc1_++;
         }
      }
      
      private function initFuZhou() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 6)
         {
            MethodUtil.setMousable(this._fuzhou["mcArrow" + _loc1_],false);
            this._fuzhou["mcArrow" + _loc1_].visible = false;
            this._fuzhou["mc" + _loc1_].buttonMode = true;
            this._fuzhou["mc" + _loc1_].mouseChildren = false;
            this._fuzhou["mc" + _loc1_].addEventListener(MouseEvent.ROLL_OVER,this.onOver,false,0,true);
            this._fuzhou["mc" + _loc1_].addEventListener(MouseEvent.ROLL_OUT,this.onOut,false,0,true);
            this._fuzhou["mc" + _loc1_].addEventListener(MouseEvent.CLICK,this.onDown,false,0,true);
            _loc1_++;
         }
      }
      
      private function onOver(param1:MouseEvent) : void
      {
         var _loc2_:MovieClip = param1.target as MovieClip;
         _loc2_.filters = [FilterFactory.getLightFilter(300)];
      }
      
      private function onOut(param1:MouseEvent) : void
      {
         var _loc2_:MovieClip = param1.target as MovieClip;
         _loc2_.filters = [];
      }
      
      private function onDown(param1:MouseEvent) : void
      {
         var _loc2_:MovieClip = param1.target as MovieClip;
         var _loc3_:int = int(_loc2_.name.charAt(2));
         this._fuzhou["mcArrow" + _loc3_].visible = false;
         MethodUtil.setMousable(_loc2_,false);
         TweenLite.to(_loc2_,0.5,{
            "alpha":0,
            "scaleY":3
         });
         ++this._total;
         if(this._total >= 6)
         {
            this._okFunc();
         }
         else
         {
            EffectManager.instance().playAudio("BREAK");
         }
         Layers.startShaking(5,5,2);
      }
      
      private function removeFuZhou() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < 6)
         {
            this._fuzhou["mc" + _loc1_].removeEventListener(MouseEvent.MOUSE_OVER,this.onOver);
            this._fuzhou["mc" + _loc1_].removeEventListener(MouseEvent.MOUSE_OVER,this.onOut);
            this._fuzhou["mc" + _loc1_].removeEventListener(MouseEvent.CLICK,this.onDown);
            _loc1_++;
         }
      }
      
      override public function destroy() : void
      {
         this._role.destroy();
         this.removeFuZhou();
         this._okFunc = null;
         super.destroy();
      }
   }
}

