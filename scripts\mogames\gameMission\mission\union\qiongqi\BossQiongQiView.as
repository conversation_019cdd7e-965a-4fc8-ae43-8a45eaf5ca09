package mogames.gameMission.mission.union.qiongqi
{
   import mogames.gameRole.co.BaseRole;
   import mogames.gameRole.enemy.view.EnemyBaseView;
   
   public class BossQiongQiView extends EnemyBaseView
   {
      private var _boss:BOSSQiongQi;
      
      public function BossQiongQiView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         super.updateCurrentFrame(param1);
         if(param1 >= 151 && param1 <= 174)
         {
            this._boss.addForceX(this._boss.dirX,10);
         }
         if(param1 == 135)
         {
            this._boss.handlerSkillThree();
         }
         else if(param1 == 203)
         {
            this._boss.handlerSkillFive();
         }
         if(curStatus == "skillone" && Boolean(mcATK))
         {
            this._boss.dispatchSkillOne();
         }
         else if(curStatus == "skilltwo_loop" && <PERSON><PERSON><PERSON>(mcATK))
         {
            this._boss.dispatchSkillTwo();
         }
         else if(curStatus == "skillfour" && <PERSON>(mcATK))
         {
            this._boss.dispatchSkillFour();
         }
      }
      
      override protected function onAnimComplete(param1:String) : void
      {
         super.onAnimComplete(param1);
         if(param1 == "skilltwo")
         {
            this._boss.startSkillTwo();
         }
      }
      
      override public function changeSkill() : void
      {
         switch(skillType)
         {
            case "skilltwo_loop":
               changeStatus(skillType,true);
               break;
            default:
               changeStatus(skillType,false);
         }
      }
      
      override public function isCleanSkill(param1:String) : Boolean
      {
         return param1 == "skillone" || param1 == "skilltwo_end" || param1 == "skillthree" || param1 == "skillfour" || param1 == "skillfive";
      }
      
      override public function get isBreakTrick() : Boolean
      {
         return false;
      }
      
      override protected function isTrickChange(param1:String) : Boolean
      {
         return param1 == "attack0" || this.isCleanSkill(param1);
      }
      
      override public function get isAvoidAction() : Boolean
      {
         return true;
      }
      
      override public function setOwner(param1:BaseRole) : void
      {
         super.setOwner(param1);
         this._boss = param1 as BOSSQiongQi;
      }
   }
}

