package file
{
   import mogames.gameData.gem.BSHCCostVO;
   
   public class BSHCConfig
   {
      private static var _instance:BSHCConfig;
      
      private var _list:Array;
      
      private var _costVec:Vector.<BSHCCostVO>;
      
      public function BSHCConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : BSHCConfig
      {
         if(!_instance)
         {
            _instance = new BSHCConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._costVec = new Vector.<BSHCCostVO>();
         this._costVec.push(new BSHCCostVO(14201,14202,10000,0,700));
         this._costVec.push(new BSHCCostVO(14211,14212,10000,0,700));
         this._costVec.push(new BSHCCostVO(14221,14222,10000,0,700));
         this._costVec.push(new BSHCCostVO(14202,14203,20000,0,700));
         this._costVec.push(new BSHCCostVO(14212,14213,20000,0,700));
         this._costVec.push(new BSHCCostVO(14222,14223,20000,0,700));
         this._costVec.push(new BSHCCostVO(14203,14204,50000,0,600));
         this._costVec.push(new BSHCCostVO(14213,14214,50000,0,600));
         this._costVec.push(new BSHCCostVO(14223,14224,50000,0,600));
         this._costVec.push(new BSHCCostVO(14204,14205,100000,3,600));
         this._costVec.push(new BSHCCostVO(14214,14215,100000,3,600));
         this._costVec.push(new BSHCCostVO(14224,14225,100000,3,600));
         this._costVec.push(new BSHCCostVO(14205,14206,200000,5,500));
         this._costVec.push(new BSHCCostVO(14215,14216,200000,5,500));
         this._costVec.push(new BSHCCostVO(14225,14226,200000,5,500));
      }
      
      public function findCostVO(param1:int) : BSHCCostVO
      {
         var _loc2_:BSHCCostVO = null;
         for each(_loc2_ in this._costVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

