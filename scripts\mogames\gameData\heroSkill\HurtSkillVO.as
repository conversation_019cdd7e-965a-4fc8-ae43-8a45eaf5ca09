package mogames.gameData.heroSkill
{
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   import mogames.gameData.role.HeroGameVO;
   
   public class HurtSkillVO extends BaseSkillVO
   {
      public var hurt:Sint = new Sint();
      
      public function HurtSkillVO(param1:int, param2:HeroGameVO, param3:int = 0)
      {
         super(param1,param2,param3);
      }
      
      override public function setLevel(param1:int, param2:<PERSON><PERSON><PERSON> = false) : void
      {
         super.setLevel(param1,param2);
         this.updateHurt();
      }
      
      public function updateHurt() : void
      {
         if(!isActivate)
         {
            return;
         }
         this.hurt.v = (level.v * countVO.argDic.hurt.v + _owner.level.v * ConstData.DATA_NUM4.v) * countVO.argDic.arg.v + _owner.ownATK * 0.1;
      }
   }
}

