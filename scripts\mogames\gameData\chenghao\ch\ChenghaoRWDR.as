package mogames.gameData.chenghao.ch
{
   import mogames.gameData.chenghao.BaseChenghaoVO;
   import mogames.gameData.flag.FlagProxy;
   
   public class ChenghaoRWDR extends BaseChenghaoVO
   {
      public function ChenghaoRWDR(param1:int, param2:int, param3:String)
      {
         super(param1,param2,param3);
      }
      
      override public function get hasGet() : <PERSON>olean
      {
         return FlagProxy.instance().findFlagCur(215) >= 100;
      }
      
      override public function get wayInfors() : String
      {
         if(this.hasGet)
         {
            return _wayInfors;
         }
         return _wayInfors + "<br>（已完成：" + FlagProxy.instance().findFlagCur(215) + "）";
      }
   }
}

