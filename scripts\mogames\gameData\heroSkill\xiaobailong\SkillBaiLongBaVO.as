package mogames.gameData.heroSkill.xiaobailong
{
   import mogames.gameData.base.Snum;
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.role.HeroGameVO;
   
   public class SkillBaiLongBaVO extends HurtSkillVO
   {
      public var total:Snum = new Snum();
      
      public function SkillBaiLongBaVO(param1:int, param2:HeroGameVO, param3:int = 0)
      {
         super(param1,param2,param3);
      }
      
      override public function setLevel(param1:int, param2:Boolean = false) : void
      {
         super.setLevel(param1);
         this.total.v = countVO.argDic.arg2.v + countVO.argDic.arg3.v * level.v;
      }
   }
}

