package mogames.gameData.flag.fuben
{
   import mogames.gameData.ConstData;
   import mogames.gameData.MasterProxy;
   
   public class CuiShuFlag extends FBMoneyFlag
   {
      public function CuiShuFlag(param1:int, param2:int, param3:int)
      {
         super(param1,param2,param3);
      }
      
      override public function get totalBuy() : int
      {
         if(MasterProxy.instance().gameVIP.v >= 5)
         {
            return _totalBuy.v + ConstData.DATA_NUM10.v;
         }
         return _totalBuy.v;
      }
      
      override public function get status() : String
      {
         if(MasterProxy.instance().gameVIP.v < 5)
         {
            return "使用金锭二次催熟剩余：" + leftBuy + "次";
         }
         if(isFree)
         {
            return "免费二次催熟剩余：" + leftFree + "次";
         }
         return "使用金锭二次催熟剩余：" + leftBuy + "次";
      }
   }
}

