package citrus.utils.objectmakers.tmx
{
   public dynamic class TmxPropertySet
   {
      public function TmxPropertySet(param1:XML)
      {
         super();
         this.extend(param1);
      }
      
      public function extend(param1:XML) : TmxPropertySet
      {
         var _loc2_:XML = null;
         var _loc3_:String = null;
         var _loc4_:String = null;
         for each(_loc2_ in param1.property)
         {
            _loc3_ = _loc2_.@name;
            _loc4_ = _loc2_.@value;
            this[_loc3_] = _loc4_;
         }
         return this;
      }
   }
}

