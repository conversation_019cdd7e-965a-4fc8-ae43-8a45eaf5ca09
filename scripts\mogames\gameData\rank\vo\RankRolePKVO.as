package mogames.gameData.rank.vo
{
   import mogames.gameData.base.Sint;
   import utils.MathUtil;
   
   public class RankRolePKVO
   {
      public var rank:Sint;
      
      public var nick:String;
      
      public var score:Sint;
      
      public var uid:String;
      
      public var saveIndex:int;
      
      public var totalBR:int;
      
      public var pkFlags:Array;
      
      public var datas:Object;
      
      public function RankRolePKVO()
      {
         super();
         this.rank = new Sint();
         this.score = new Sint();
         this.pkFlags = [0,0,0,0];
      }
      
      public function parseData(param1:Object) : void
      {
         this.uid = param1.uId;
         this.nick = param1.userName;
         this.score.v = param1.score;
         this.rank.v = param1.rank;
         this.saveIndex = param1.index;
         if(param1.extra is Array)
         {
            this.parseExtra(param1.extra);
         }
         else if(param1.extra is String)
         {
            this.parseExtra(String(param1.extra).split(","));
         }
      }
      
      private function parseExtra(param1:Array) : void
      {
         this.totalBR = int(param1[0]);
      }
      
      public function setMode(param1:int) : void
      {
         this.pkFlags[param1] = 1;
      }
      
      public function get sortIndex() : int
      {
         return this.rank.v;
      }
      
      public function get canPK() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < 2)
         {
            if(this.pkFlags[_loc1_] == 0)
            {
               return true;
            }
            _loc1_++;
         }
         return false;
      }
      
      public function get saveData() : String
      {
         this.countNick();
         var _loc1_:Array = [];
         _loc1_[0] = [this.uid,this.nick,this.score.v,this.rank.v,this.saveIndex,this.totalBR].join("MM");
         _loc1_[1] = this.pkFlags.join("MM");
         return _loc1_.join("NN");
      }
      
      private function countNick() : void
      {
         var _loc1_:int = this.nick.length;
         var _loc2_:String = "";
         var _loc3_:String = "";
         var _loc4_:int = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = this.nick.charAt(_loc4_);
            if(!(_loc3_ == "H" || _loc3_ == "M" || _loc3_ == "N" || _loc3_ == "T"))
            {
               _loc2_ += _loc3_;
            }
            _loc4_++;
         }
         this.nick = _loc2_;
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc2_:Array = param1.split("NN");
         this.parseSave0(_loc2_[0]);
         this.pkFlags = MathUtil.arrStringToNum(_loc2_[1].split("MM"));
      }
      
      private function parseSave0(param1:String) : void
      {
         var _loc2_:Array = param1.split("MM");
         this.uid = _loc2_[0];
         this.nick = _loc2_[1];
         this.score.v = int(_loc2_[2]);
         this.rank.v = int(_loc2_[3]);
         this.saveIndex = int(_loc2_[4]);
         this.totalBR = int(_loc2_[5]);
      }
   }
}

