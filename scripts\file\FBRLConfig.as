package file
{
   import mogames.gameData.ConstData;
   import mogames.gameData.fabao.RLUseVO;
   import mogames.gameData.fabao.RongLianVO;
   import mogames.gameData.forge.NeedVO;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.TxtUtil;
   
   public class FBRLConfig
   {
      private static var _instance:FBRLConfig;
      
      private var _rlVec:Vector.<RongLianVO>;
      
      public function FBRLConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : FBRLConfig
      {
         if(!_instance)
         {
            _instance = new FBRLConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._rlVec = new Vector.<RongLianVO>();
         this._rlVec.push(new RongLianVO(23001,[new RLUseVO(0,5000,[new NeedVO(13308,10),new NeedVO(13309,10),new NeedVO(13303,5)]),new RLUseVO(1,25000,[new NeedVO(13313,30),new NeedVO(13316,30),new NeedVO(13317,30)]),new RLUseVO(2,75000,[new NeedVO(16807,20),new NeedVO(16808,20),new NeedVO(16809,20)]),new RLUseVO(3,100000,[new NeedVO(13327,20),new NeedVO(16808,20),new NeedVO(16809,20)])]));
         this._rlVec.push(new RongLianVO(23002,[new RLUseVO(0,2500,[new NeedVO(13306,10),new NeedVO(13307,10)]),new RLUseVO(1,10000,[new NeedVO(13313,20),new NeedVO(13315,20),new NeedVO(13314,20)]),new RLUseVO(2,50000,[new NeedVO(16804,20),new NeedVO(16805,20),new NeedVO(16806,20)]),new RLUseVO(3,75000,[new NeedVO(13327,20),new NeedVO(16805,20),new NeedVO(16806,20)])]));
         this._rlVec.push(new RongLianVO(23003,[new RLUseVO(0,7500,[new NeedVO(13301,5),new NeedVO(13305,5),new NeedVO(13303,8)]),new RLUseVO(1,40000,[new NeedVO(13313,20),new NeedVO(13317,20),new NeedVO(13324,20)]),new RLUseVO(2,175000,[new NeedVO(16810,20),new NeedVO(16811,20),new NeedVO(16812,20)]),new RLUseVO(3,250000,[new NeedVO(13328,20),new NeedVO(16811,20),new NeedVO(16812,20)])]));
         this._rlVec.push(new RongLianVO(23004,[new RLUseVO(0,10000,[new NeedVO(13302,8),new NeedVO(13304,8),new NeedVO(13303,10)]),new RLUseVO(1,25000,[new NeedVO(13313,30),new NeedVO(13318,30),new NeedVO(13107,30)]),new RLUseVO(2,75000,[new NeedVO(16813,20),new NeedVO(16814,20),new NeedVO(16815,20)]),new RLUseVO(3,100000,[new NeedVO(13327,20),new NeedVO(16814,20),new NeedVO(16815,20)])]));
         this._rlVec.push(new RongLianVO(23005,[new RLUseVO(0,25000,[new NeedVO(13310,10),new NeedVO(13311,15),new NeedVO(13303,20)]),new RLUseVO(1,25000,[new NeedVO(13310,30),new NeedVO(13313,30),new NeedVO(13323,30)]),new RLUseVO(2,40000,[new NeedVO(13310,20),new NeedVO(13313,20),new NeedVO(13323,20)]),new RLUseVO(3,50000,[new NeedVO(13328,20),new NeedVO(13313,20),new NeedVO(13323,20)])]));
         this._rlVec.push(new RongLianVO(23007,[new RLUseVO(0,40000,[new NeedVO(13303,20),new NeedVO(13110,10),new NeedVO(13319,15)]),new RLUseVO(1,60000,[new NeedVO(13313,30),new NeedVO(13110,20),new NeedVO(13320,20)]),new RLUseVO(2,75000,[new NeedVO(13313,30),new NeedVO(13110,20),new NeedVO(13320,20)]),new RLUseVO(3,100000,[new NeedVO(13329,20),new NeedVO(13110,20),new NeedVO(13320,20)])]));
         this._rlVec.push(new RongLianVO(23008,[new RLUseVO(0,5000,[new NeedVO(13303,5),new NeedVO(13104,10),new NeedVO(13105,10)]),new RLUseVO(1,10000,[new NeedVO(13313,20),new NeedVO(13104,20),new NeedVO(13105,20)]),new RLUseVO(2,25000,[new NeedVO(13143,10),new NeedVO(13104,20),new NeedVO(13105,20)]),new RLUseVO(3,40000,[new NeedVO(13328,10),new NeedVO(13104,20),new NeedVO(13105,20)])]));
         this._rlVec.push(new RongLianVO(23009,[new RLUseVO(0,50000,[new NeedVO(13303,20),new NeedVO(13319,20),new NeedVO(13114,10)]),new RLUseVO(1,100000,[new NeedVO(13313,20),new NeedVO(13320,20),new NeedVO(13115,15)]),new RLUseVO(2,150000,[new NeedVO(16801,20),new NeedVO(16802,20),new NeedVO(16803,20)]),new RLUseVO(3,200000,[new NeedVO(13329,20),new NeedVO(16802,20),new NeedVO(16803,20)])]));
         this._rlVec.push(new RongLianVO(23010,[new RLUseVO(0,50000,[new NeedVO(13303,20),new NeedVO(13136,20),new NeedVO(13137,20)]),new RLUseVO(1,100000,[new NeedVO(13313,20),new NeedVO(13138,20),new NeedVO(13139,20)]),new RLUseVO(2,200000,[new NeedVO(16816,20),new NeedVO(16817,20),new NeedVO(16818,20)]),new RLUseVO(3,250000,[new NeedVO(13329,20),new NeedVO(16817,20),new NeedVO(16818,20)])]));
         this._rlVec.push(new RongLianVO(23012,[new RLUseVO(0,100000,[new NeedVO(14130,20),new NeedVO(13141,20),new NeedVO(13142,20)]),new RLUseVO(1,100000,[new NeedVO(14130,20),new NeedVO(13144,20),new NeedVO(13145,20)]),new RLUseVO(2,250000,[new NeedVO(14130,20),new NeedVO(16820,20),new NeedVO(16819,20)]),new RLUseVO(3,400000,[new NeedVO(13330,20),new NeedVO(16819,20),new NeedVO(16820,20)])]));
         this._rlVec.push(new RongLianVO(23013,[new RLUseVO(0,300000,[new NeedVO(13140,66),new NeedVO(13146,20)])]));
      }
      
      public function isMax(param1:int, param2:int) : Boolean
      {
         var _loc3_:int = this.maxQuality(param1);
         return param2 > _loc3_;
      }
      
      public function checkRL(param1:int, param2:int) : Boolean
      {
         if(!this.hasFabao(param1))
         {
            MiniMsgMediator.instance().showAutoMsg("该法宝熔炼暂未开放！");
            return false;
         }
         var _loc3_:int = this.maxQuality(param1);
         if(param2 > _loc3_)
         {
            MiniMsgMediator.instance().showAutoMsg("该法宝当前只可以熔炼至【" + TxtUtil.setColor(ConstData.GOOD_QUALITY[_loc3_],ConstData.GOOD_COLOR1[_loc3_]) + "】品质！");
            return false;
         }
         return true;
      }
      
      public function findUseVO(param1:int, param2:int) : RLUseVO
      {
         var _loc3_:RongLianVO = this.findRLVO(param1);
         if(!_loc3_)
         {
            return null;
         }
         return _loc3_.findVO(param2);
      }
      
      private function maxQuality(param1:int) : int
      {
         var _loc2_:RongLianVO = this.findRLVO(param1);
         return _loc2_.list.length - 1;
      }
      
      private function findRLVO(param1:int) : RongLianVO
      {
         var _loc2_:RongLianVO = null;
         for each(_loc2_ in this._rlVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      private function hasFabao(param1:int) : Boolean
      {
         var _loc2_:RongLianVO = null;
         for each(_loc2_ in this._rlVec)
         {
            if(_loc2_.id.v == param1)
            {
               return true;
            }
         }
         return false;
      }
   }
}

