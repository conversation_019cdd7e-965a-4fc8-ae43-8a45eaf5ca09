package file
{
   import mogames.gameData.forge.NeedVO;
   import mogames.gameData.pet.vo.PetGrowCostVO;
   
   public class PetGrowConfig
   {
      private static var _instance:PetGrowConfig;
      
      private var _list:Vector.<PetGrowCostVO>;
      
      public function PetGrowConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : PetGrowConfig
      {
         if(!_instance)
         {
            _instance = new PetGrowConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._list = new Vector.<PetGrowCostVO>();
         this._list.push(new PetGrowCostVO(51,70,5000,new NeedVO(16752,1)));
         this._list.push(new PetGrowCostVO(61,80,10000,new NeedVO(16752,1)));
         this._list.push(new PetGrowCostVO(71,90,10000,new NeedVO(16752,1)));
         this._list.push(new PetGrowCostVO(81,100,20000,new NeedVO(16752,1)));
         this._list.push(new PetGrowCostVO(91,110,20000,new NeedVO(16752,1)));
         this._list.push(new PetGrowCostVO(101,120,40000,new NeedVO(16752,2)));
         this._list.push(new PetGrowCostVO(111,130,30000,new NeedVO(16752,2)));
         this._list.push(new PetGrowCostVO(121,150,60000,new NeedVO(16752,3)));
      }
      
      public function findPet(param1:int) : PetGrowCostVO
      {
         var _loc2_:PetGrowCostVO = null;
         for each(_loc2_ in this._list)
         {
            if(param1 >= _loc2_.min.v && param1 <= _loc2_.max.v)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

