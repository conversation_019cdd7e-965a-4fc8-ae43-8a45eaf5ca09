package mogames.gameData.petSkill.passive
{
   import mogames.gameData.petSkill.PetSkillVO;
   import mogames.gameData.role.HeroGameVO;
   import utils.TxtUtil;
   
   public class PetSkillJianRenVO extends PetSkillVO
   {
      private var _qualityArg:Array = [4,8,12,16,20];
      
      public function PetSkillJianRenVO(param1:int, param2:HeroGameVO)
      {
         super(param1,param2);
      }
      
      public function get addPDEF() : int
      {
         return level.v * this._qualityArg[curQuality.v];
      }
      
      override public function get tipInfor() : String
      {
         return constVO.infor + TxtUtil.setColor("<br>物理防御+" + this.addPDEF,"99ff00");
      }
   }
}

