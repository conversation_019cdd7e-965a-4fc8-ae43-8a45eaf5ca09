package mogames.gameMission.co
{
   import citrus.core.CitrusObject;
   import file.StoryConfig;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.setTimeout;
   import mogames.Layers;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.flag.TimeFlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameObj.box2d.TransDoor;
   import mogames.gameRole.enemy.BaseEnemy;
   import mogames.gameRole.hero.BaseHero;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.dialog.DialogMediator;
   import utils.MethodUtil;
   
   public class BaseScene extends Box2DScene
   {
      protected var _bg:Bitmap;
      
      protected var _sceneVO:BaseSceneVO;
      
      protected var _mission:BaseMission;
      
      protected var _leaveDoor:TransDoor;
      
      private var cameraTimer:CitrusTimer;
      
      public var sceneType:int;
      
      public function BaseScene(param1:BaseSceneVO, param2:BaseMission)
      {
         this._sceneVO = param1;
         this._mission = param2;
         super(GameInLoader.instance().findAsset(param1.resName));
         this.cameraTimer = new CitrusTimer();
         EventManager.addEventListener(UIEvent.SHOW_HEAD_HP,this.updateHPBar,false,0,true);
      }
      
      override public function initialize() : void
      {
         super.initialize();
         if(this._sceneVO.bgName != "")
         {
            this._bg = new Bitmap(GameInLoader.instance().findAsset(this._sceneVO.bgName) as BitmapData);
            Layers.bgLayer.addChild(this._bg);
         }
         this.initCamera();
         this._mission.initPlayer();
         this.handlerInit();
         _ce.input.resetActions();
         Layers.setKeyEnable(true);
      }
      
      override protected function initHero() : void
      {
         this._mission.addHero();
      }
      
      protected function initCamera() : void
      {
         var func:Function = null;
         func = function():void
         {
            if(!view)
            {
               return;
            }
            view.camera.easing = new Point(0.1,0.1);
         };
         view.camera.easing = new Point(1,1);
         view.camera.bounds = new Rectangle(0,0,this._sceneVO.width,this._sceneVO.height);
         view.camera.center = new Point(0.3,0.8);
         view.camera.enabled = true;
         this.cameraTimer.setTimeOut(0.5,func);
      }
      
      protected function handlerInit() : void
      {
         this._mission.cleanLoadUI();
      }
      
      protected function addLeaveDoor(param1:int, param2:int) : void
      {
         this._leaveDoor = new TransDoor();
         this._leaveDoor.x = param1;
         this._leaveDoor.y = param2 + 40;
         add(this._leaveDoor);
         this._leaveDoor.onDoor.add(this.listenLeave);
      }
      
      protected function listenLeave() : void
      {
         this.setRoleEnable(false);
         Layers.setKeyEnable(false);
         this._mission.missionVO.setComplete();
         var _loc1_:int = BattleMediator.instance().totalTime;
         var _loc2_:int = BattleMediator.instance().totalHurt;
         TimeFlagProxy.instance().recordFlag(this._mission.missionVO.id.v,_loc1_,_loc2_);
         MissionManager.instance().handlerWin();
      }
      
      public function unlock() : void
      {
         this.checkDisableHero();
         view.camera.bounds = new Rectangle(0,0,this._sceneVO.width,this._sceneVO.height);
         CitrusLock.instance().unlock();
      }
      
      public function showDialog(param1:String, param2:Function = null) : void
      {
         var func:Function = null;
         var id:String = param1;
         var okFunc:Function = param2;
         func = function():void
         {
            setHeroEnable(true);
            if(okFunc != null)
            {
               okFunc();
            }
         };
         this.setHeroEnable(false);
         DialogMediator.instance().init(StoryConfig.instance().findDialog(id),func);
      }
      
      protected function checkDisableHero() : void
      {
         var p:Point;
         var time:int;
         var func:Function = null;
         func = function():void
         {
            if(!view)
            {
               return;
            }
            setHeroEnable(true);
         };
         if(!view)
         {
            return;
         }
         p = Layers.camera.pointToLocal(new Point(view.camera.target.x,view.camera.target.y));
         if(p.x < 670)
         {
            return;
         }
         this.setHeroEnable(false);
         time = (p.x - 670) / 290 * 200;
         setTimeout(func,time);
      }
      
      protected function updateHPBar(param1:UIEvent) : void
      {
         var _loc5_:BaseEnemy = null;
         var _loc2_:Vector.<CitrusObject> = getObjectsByType(BaseEnemy);
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = _loc2_[_loc3_] as BaseEnemy;
            _loc5_.initBloodBar();
            _loc3_++;
         }
      }
      
      protected function enabledKey() : void
      {
         this.setHeroEnable(true);
      }
      
      public function setEnemiesEnable(param1:Boolean) : void
      {
         var _loc5_:BaseEnemy = null;
         var _loc2_:Vector.<CitrusObject> = getObjectsByType(BaseEnemy);
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = _loc2_[_loc3_] as BaseEnemy;
            _loc5_.aiEnabled = param1;
            _loc3_++;
         }
      }
      
      public function setHeroEnable(param1:Boolean) : void
      {
         var _loc5_:BaseHero = null;
         var _loc2_:Vector.<CitrusObject> = getObjectsByType(BaseHero);
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = _loc2_[_loc3_] as BaseHero;
            _loc5_.aiEnabled = param1;
            _loc3_++;
         }
      }
      
      public function setHeroLock(param1:Boolean) : void
      {
         var _loc5_:BaseHero = null;
         var _loc2_:Vector.<CitrusObject> = getObjectsByType(BaseHero);
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = _loc2_[_loc3_] as BaseHero;
            _loc5_.lockControl = param1;
            _loc3_++;
         }
      }
      
      public function setRoleEnable(param1:Boolean) : void
      {
         this.setEnemiesEnable(param1);
         this.setHeroEnable(param1);
      }
      
      public function get sceneVO() : BaseSceneVO
      {
         return this._sceneVO;
      }
      
      override public function destroy() : void
      {
         CitrusLock.instance().unlock();
         EventManager.removeEventListener(UIEvent.SHOW_HEAD_HP,this.updateHPBar);
         if(this._bg)
         {
            this._bg.bitmapData = null;
            MethodUtil.removeMe(this._bg);
            this._bg = null;
         }
         this.cameraTimer.destroy();
         this.cameraTimer = null;
         this._sceneVO = null;
         this._mission = null;
         this._leaveDoor = null;
         super.destroy();
      }
   }
}

