package mogames.gameData.flag.vo
{
   import mogames.gameData.base.Sint;
   
   public class NumFlag
   {
      public var flagID:Sint;
      
      public var isDaily:<PERSON>olean;
      
      public var isReduce:Boolean;
      
      protected var _cur:Sint;
      
      protected var _total:Sint;
      
      public function NumFlag(param1:int, param2:int = 1, param3:<PERSON><PERSON><PERSON> = false, param4:<PERSON>olean = false)
      {
         super();
         this.flagID = new Sint(param1);
         this._total = new Sint(param2);
         this.isDaily = param4;
         this.isReduce = param3;
         if(this.isReduce)
         {
            this._cur = new Sint(param2);
         }
         else
         {
            this._cur = new Sint();
         }
      }
      
      public function dailyRefresh() : void
      {
         if(this.isReduce)
         {
            this._cur.v = this.total;
         }
         else
         {
            this._cur.v = 0;
         }
      }
      
      public function setValue(param1:int) : void
      {
         this._cur.v = param1;
         if(this._cur.v <= 0)
         {
            this._cur.v = 0;
         }
      }
      
      public function changeValue(param1:int) : void
      {
         this.setValue(this._cur.v + param1);
      }
      
      public function isComplete() : Boolean
      {
         if(this.isReduce)
         {
            return this._cur.v <= 0;
         }
         return this._cur.v >= this.total;
      }
      
      public function getStatus() : String
      {
         return this._cur.v + "/" + this.total;
      }
      
      public function get left() : int
      {
         return this.total - this.cur;
      }
      
      public function get total() : int
      {
         return this._total.v;
      }
      
      public function get cur() : int
      {
         return this._cur.v;
      }
      
      public function set cur(param1:int) : void
      {
         this._cur.v = param1;
      }
      
      public function get saveStr() : String
      {
         return this.flagID.v + "H" + this._cur.v;
      }
   }
}

