package file
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.danyao.DYHCVO;
   import mogames.gameData.forge.NeedVO;
   
   public class DYHCConfig
   {
      private static var _instance:DYHCConfig;
      
      private var _list:Array;
      
      private var _hcVec:Vector.<DYHCVO>;
      
      public function DYHCConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : DYHCConfig
      {
         if(!_instance)
         {
            _instance = new DYHCConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._list = [];
         this._list[0] = [new Sint(16701),new Sint(16221),new Sint(16222),new Sint(16223),new Sint(16224),new Sint(16225),new Sint(16201),new Sint(16202),new Sint(16203),new Sint(16204),new Sint(16205),new Sint(16211),new Sint(16212),new Sint(16213),new Sint(16214),new Sint(16215)];
         this._list[1] = [new Sint(16321),new Sint(16322),new Sint(16323),new Sint(16324),new Sint(16325),new Sint(16301),new Sint(16302),new Sint(16303),new Sint(16304),new Sint(16305),new Sint(16311),new Sint(16312),new Sint(16313),new Sint(16314),new Sint(16315)];
         this._list[2] = [new Sint(16421),new Sint(16422),new Sint(16423),new Sint(16424),new Sint(16425),new Sint(16401),new Sint(16402),new Sint(16403),new Sint(16404),new Sint(16405),new Sint(16411),new Sint(16412),new Sint(16413),new Sint(16414),new Sint(16415)];
         this._list[3] = [new Sint(16501),new Sint(16502),new Sint(16503),new Sint(16504),new Sint(16505),new Sint(16511),new Sint(16512),new Sint(16513),new Sint(16514),new Sint(16515),new Sint(16521),new Sint(16522),new Sint(16523),new Sint(16524),new Sint(16525)];
         this._list[4] = [new Sint(16601),new Sint(16602),new Sint(16603),new Sint(16604),new Sint(16605),new Sint(16611),new Sint(16612),new Sint(16613),new Sint(16614),new Sint(16615),new Sint(16621),new Sint(16622),new Sint(16623),new Sint(16624),new Sint(16625)];
         this._hcVec = new Vector.<DYHCVO>();
         this._hcVec.push(new DYHCVO(16701,500,[new NeedVO(16104,1),new NeedVO(16105,1)]));
         this._hcVec.push(new DYHCVO(16201,1000,[new NeedVO(15001,3),new NeedVO(16101,2),new NeedVO(16104,1)]));
         this._hcVec.push(new DYHCVO(16202,1000,[new NeedVO(15001,3),new NeedVO(16101,2),new NeedVO(16106,1)]));
         this._hcVec.push(new DYHCVO(16203,1000,[new NeedVO(15001,3),new NeedVO(16101,2),new NeedVO(16105,1)]));
         this._hcVec.push(new DYHCVO(16204,1000,[new NeedVO(15001,3),new NeedVO(16104,1),new NeedVO(16105,1)]));
         this._hcVec.push(new DYHCVO(16205,1000,[new NeedVO(15001,3),new NeedVO(16101,2),new NeedVO(16107,1)]));
         this._hcVec.push(new DYHCVO(16211,1000,[new NeedVO(15002,3),new NeedVO(16102,2),new NeedVO(16104,1)]));
         this._hcVec.push(new DYHCVO(16212,1000,[new NeedVO(15002,3),new NeedVO(16102,2),new NeedVO(16107,1)]));
         this._hcVec.push(new DYHCVO(16213,1000,[new NeedVO(15002,3),new NeedVO(16102,2),new NeedVO(16105,1)]));
         this._hcVec.push(new DYHCVO(16214,1000,[new NeedVO(15002,3),new NeedVO(16105,1),new NeedVO(16106,1)]));
         this._hcVec.push(new DYHCVO(16215,1000,[new NeedVO(15002,3),new NeedVO(16102,2),new NeedVO(16106,1)]));
         this._hcVec.push(new DYHCVO(16221,1000,[new NeedVO(15003,3),new NeedVO(16103,2),new NeedVO(16104,1)]));
         this._hcVec.push(new DYHCVO(16222,1000,[new NeedVO(15003,3),new NeedVO(16103,2),new NeedVO(16108,1)]));
         this._hcVec.push(new DYHCVO(16223,1000,[new NeedVO(15003,3),new NeedVO(16103,2),new NeedVO(16105,1)]));
         this._hcVec.push(new DYHCVO(16224,1000,[new NeedVO(15003,3),new NeedVO(16106,1),new NeedVO(16107,1)]));
         this._hcVec.push(new DYHCVO(16225,1000,[new NeedVO(15003,3),new NeedVO(16103,2),new NeedVO(16104,1)]));
         this._hcVec.push(new DYHCVO(16301,5000,[new NeedVO(16104,2),new NeedVO(16101,2),new NeedVO(16109,1)]));
         this._hcVec.push(new DYHCVO(16302,5000,[new NeedVO(16108,2),new NeedVO(16101,2),new NeedVO(16109,1)]));
         this._hcVec.push(new DYHCVO(16303,5000,[new NeedVO(16106,2),new NeedVO(16101,2),new NeedVO(16109,1)]));
         this._hcVec.push(new DYHCVO(16304,5000,[new NeedVO(16107,2),new NeedVO(16101,2),new NeedVO(16109,1)]));
         this._hcVec.push(new DYHCVO(16305,5000,[new NeedVO(16115,2),new NeedVO(16101,2),new NeedVO(16109,1)]));
         this._hcVec.push(new DYHCVO(16311,5000,[new NeedVO(16106,2),new NeedVO(16102,2),new NeedVO(16110,1)]));
         this._hcVec.push(new DYHCVO(16312,5000,[new NeedVO(16108,2),new NeedVO(16102,2),new NeedVO(16110,1)]));
         this._hcVec.push(new DYHCVO(16313,5000,[new NeedVO(16105,2),new NeedVO(16102,2),new NeedVO(16110,1)]));
         this._hcVec.push(new DYHCVO(16314,5000,[new NeedVO(16107,2),new NeedVO(16102,2),new NeedVO(16110,1)]));
         this._hcVec.push(new DYHCVO(16315,5000,[new NeedVO(16115,2),new NeedVO(16102,2),new NeedVO(16110,1)]));
         this._hcVec.push(new DYHCVO(16321,5000,[new NeedVO(16105,2),new NeedVO(16103,2),new NeedVO(16111,1)]));
         this._hcVec.push(new DYHCVO(16322,5000,[new NeedVO(16108,2),new NeedVO(16103,2),new NeedVO(16111,1)]));
         this._hcVec.push(new DYHCVO(16323,5000,[new NeedVO(16104,2),new NeedVO(16103,2),new NeedVO(16111,1)]));
         this._hcVec.push(new DYHCVO(16324,5000,[new NeedVO(16107,2),new NeedVO(16103,2),new NeedVO(16111,1)]));
         this._hcVec.push(new DYHCVO(16325,5000,[new NeedVO(16115,2),new NeedVO(16103,2),new NeedVO(16111,1)]));
         this._hcVec.push(new DYHCVO(16401,15000,[new NeedVO(16112,2),new NeedVO(16109,3),new NeedVO(16106,5)]));
         this._hcVec.push(new DYHCVO(16402,15000,[new NeedVO(16113,2),new NeedVO(16110,3),new NeedVO(16108,5)]));
         this._hcVec.push(new DYHCVO(16403,15000,[new NeedVO(16114,2),new NeedVO(16111,3),new NeedVO(16104,5)]));
         this._hcVec.push(new DYHCVO(16404,15000,[new NeedVO(16112,2),new NeedVO(16113,2),new NeedVO(16109,3)]));
         this._hcVec.push(new DYHCVO(16405,15000,[new NeedVO(16116,2),new NeedVO(16109,3),new NeedVO(16115,5)]));
         this._hcVec.push(new DYHCVO(16411,15000,[new NeedVO(16112,2),new NeedVO(16111,3),new NeedVO(16106,5)]));
         this._hcVec.push(new DYHCVO(16412,15000,[new NeedVO(16113,2),new NeedVO(16109,3),new NeedVO(16108,5)]));
         this._hcVec.push(new DYHCVO(16413,15000,[new NeedVO(16114,2),new NeedVO(16110,3),new NeedVO(16104,5)]));
         this._hcVec.push(new DYHCVO(16414,15000,[new NeedVO(16113,2),new NeedVO(16114,2),new NeedVO(16110,3)]));
         this._hcVec.push(new DYHCVO(16415,15000,[new NeedVO(16116,2),new NeedVO(16111,3),new NeedVO(16115,5)]));
         this._hcVec.push(new DYHCVO(16421,15000,[new NeedVO(16112,2),new NeedVO(16111,3),new NeedVO(16104,5)]));
         this._hcVec.push(new DYHCVO(16422,15000,[new NeedVO(16113,2),new NeedVO(16110,3),new NeedVO(16108,5)]));
         this._hcVec.push(new DYHCVO(16423,15000,[new NeedVO(16114,2),new NeedVO(16109,3),new NeedVO(16107,5)]));
         this._hcVec.push(new DYHCVO(16424,15000,[new NeedVO(16114,2),new NeedVO(16116,2),new NeedVO(16111,3)]));
         this._hcVec.push(new DYHCVO(16425,15000,[new NeedVO(16116,2),new NeedVO(16115,3),new NeedVO(16106,5)]));
         this._hcVec.push(new DYHCVO(16501,30000,[new NeedVO(16112,2),new NeedVO(16117,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16502,30000,[new NeedVO(16113,2),new NeedVO(16117,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16503,30000,[new NeedVO(16114,2),new NeedVO(16117,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16504,30000,[new NeedVO(16120,2),new NeedVO(16117,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16505,30000,[new NeedVO(16116,2),new NeedVO(16117,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16511,30000,[new NeedVO(16112,2),new NeedVO(16117,3),new NeedVO(16119,3)]));
         this._hcVec.push(new DYHCVO(16512,30000,[new NeedVO(16113,2),new NeedVO(16117,3),new NeedVO(16119,3)]));
         this._hcVec.push(new DYHCVO(16513,30000,[new NeedVO(16114,2),new NeedVO(16117,3),new NeedVO(16119,3)]));
         this._hcVec.push(new DYHCVO(16514,30000,[new NeedVO(16120,2),new NeedVO(16117,3),new NeedVO(16119,3)]));
         this._hcVec.push(new DYHCVO(16515,30000,[new NeedVO(16116,2),new NeedVO(16117,3),new NeedVO(16119,3)]));
         this._hcVec.push(new DYHCVO(16521,30000,[new NeedVO(16112,2),new NeedVO(16120,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16522,30000,[new NeedVO(16113,2),new NeedVO(16120,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16523,30000,[new NeedVO(16114,2),new NeedVO(16120,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16524,30000,[new NeedVO(16119,2),new NeedVO(16120,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16525,30000,[new NeedVO(16116,2),new NeedVO(16120,3),new NeedVO(16118,3)]));
         this._hcVec.push(new DYHCVO(16601,50000,[new NeedVO(16122,3),new NeedVO(16123,3),new NeedVO(16124,3)]));
         this._hcVec.push(new DYHCVO(16602,50000,[new NeedVO(16121,3),new NeedVO(16122,3),new NeedVO(16123,3)]));
         this._hcVec.push(new DYHCVO(16603,50000,[new NeedVO(16122,3),new NeedVO(16123,3),new NeedVO(16124,3)]));
         this._hcVec.push(new DYHCVO(16604,50000,[new NeedVO(16121,3),new NeedVO(16123,3),new NeedVO(16124,3)]));
         this._hcVec.push(new DYHCVO(16605,50000,[new NeedVO(16121,3),new NeedVO(16122,3),new NeedVO(16123,3)]));
         this._hcVec.push(new DYHCVO(16611,50000,[new NeedVO(16121,3),new NeedVO(16125,3),new NeedVO(16126,3)]));
         this._hcVec.push(new DYHCVO(16612,50000,[new NeedVO(16122,3),new NeedVO(16125,3),new NeedVO(16126,3)]));
         this._hcVec.push(new DYHCVO(16613,50000,[new NeedVO(16123,3),new NeedVO(16125,3),new NeedVO(16126,3)]));
         this._hcVec.push(new DYHCVO(16614,50000,[new NeedVO(16122,3),new NeedVO(16125,3),new NeedVO(16126,3)]));
         this._hcVec.push(new DYHCVO(16615,50000,[new NeedVO(16124,3),new NeedVO(16125,3),new NeedVO(16126,3)]));
         this._hcVec.push(new DYHCVO(16621,50000,[new NeedVO(16121,3),new NeedVO(16126,3),new NeedVO(16127,3)]));
         this._hcVec.push(new DYHCVO(16622,50000,[new NeedVO(16122,3),new NeedVO(16126,3),new NeedVO(16127,3)]));
         this._hcVec.push(new DYHCVO(16623,50000,[new NeedVO(16123,3),new NeedVO(16126,3),new NeedVO(16127,3)]));
         this._hcVec.push(new DYHCVO(16624,50000,[new NeedVO(16125,3),new NeedVO(16126,3),new NeedVO(16127,3)]));
         this._hcVec.push(new DYHCVO(16625,50000,[new NeedVO(16124,3),new NeedVO(16126,3),new NeedVO(16127,3)]));
      }
      
      public function findDanYao(param1:int) : Array
      {
         var _loc6_:Sint = null;
         var _loc2_:Array = this._list[param1];
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         var _loc5_:Array = [];
         while(_loc3_ < _loc4_)
         {
            _loc6_ = _loc2_[_loc3_];
            _loc5_[_loc5_.length] = this.fincHCVO(_loc6_.v);
            _loc3_++;
         }
         return _loc5_;
      }
      
      private function fincHCVO(param1:int) : DYHCVO
      {
         var _loc2_:DYHCVO = null;
         for each(_loc2_ in this._hcVec)
         {
            if(_loc2_.danyaoID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

