package mogames.gameData.flag.fuben
{
   import mogames.gameData.base.Sint;
   
   public class FBBaseFlag
   {
      public var id:Sint;
      
      protected var _curFree:Sint;
      
      protected var _totalFree:Sint;
      
      protected var _simple:<PERSON>olean;
      
      public function FBBaseFlag(param1:int, param2:int, param3:Boolean = false)
      {
         super();
         this.id = new Sint(param1);
         this._curFree = new Sint();
         this._totalFree = new Sint(param2);
         this._simple = param3;
      }
      
      public function dailyRefresh() : void
      {
         this._curFree.v = 0;
      }
      
      public function useFree() : void
      {
         this._curFree.v += 1;
      }
      
      public function addFree(param1:int) : void
      {
         this._curFree.v -= param1;
         if(this._curFree.v <= 0)
         {
            this._curFree.v = 0;
         }
      }
      
      public function get isFree() : Boolean
      {
         return this._curFree.v < this.totalFree;
      }
      
      public function get leftFree() : int
      {
         return Math.max(0,this.totalFree - this._curFree.v);
      }
      
      public function get totalFree() : int
      {
         return this._totalFree.v;
      }
      
      public function get status() : String
      {
         if(this._simple)
         {
            return this.leftFree + "/" + this.totalFree;
         }
         return "免费挑战剩余：" + this.leftFree + "次";
      }
      
      public function get saveData() : String
      {
         return [this.id.v,this._curFree.v].join("H");
      }
      
      public function set loadData(param1:Array) : void
      {
         this._curFree.v = int(param1[1]);
      }
   }
}

