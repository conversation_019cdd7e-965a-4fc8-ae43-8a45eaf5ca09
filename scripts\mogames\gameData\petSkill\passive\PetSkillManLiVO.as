package mogames.gameData.petSkill.passive
{
   import mogames.gameData.petSkill.PetSkillVO;
   import mogames.gameData.role.HeroGameVO;
   import utils.TxtUtil;
   
   public class PetSkillManLiVO extends PetSkillVO
   {
      private var _qualityArg:Array = [6,12,24,36,48];
      
      public function PetSkillManLiVO(param1:int, param2:HeroGameVO)
      {
         super(param1,param2);
      }
      
      public function get addATK() : int
      {
         return level.v * this._qualityArg[curQuality.v];
      }
      
      override public function get tipInfor() : String
      {
         return constVO.infor + TxtUtil.setColor("<br>攻击力+" + this.addATK,"99ff00");
      }
   }
}

