package mogames.gameMission.mission.huangfengdong.scene
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.box2d.AISensor;
   import mogames.gameObj.box2d.DeadSensor;
   import mogames.gameObj.box2d.Door;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneHuangFengDong01 extends BaseScene
   {
      private var _mcBOSS:CitrusMC;
      
      private var _dead:DeadSensor;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      public function SceneHuangFengDong01(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [AISensor,DeadSensor];
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER1");
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2006,1);
            TaskProxy.instance().addTask(11003);
            _mcBOSS.destroy();
            _mcBOSS = null;
            EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,780,380);
            EffectManager.instance().playAudio("HUANG_FENG_GUAI_SMILE");
         };
         this.initDoor502();
         this.initDoor503();
         this.initEnemies();
         _mission.cleanLoadUI();
         if(!FlagProxy.instance().isComplete(2006))
         {
            this._mcBOSS = new CitrusMC();
            this._mcBOSS.setupMC(GameInLoader.instance().findAsset("MC_HUANG_FENG_GUAI_STAND_CLIP"));
            this._mcBOSS.x = 780;
            this._mcBOSS.y = 490;
            this._mcBOSS.scaleX = -1;
            this._mcBOSS.changeAnimation("stand",true);
            Layers.ceEffectLayer.addChild(this._mcBOSS);
            showDialog("STORY0013",func);
         }
      }
      
      private function initDoor503() : void
      {
         (getObjectByName("door503") as Door).active = true;
      }
      
      private function initDoor502() : void
      {
         (getObjectByName("door502") as Door).active = true;
      }
      
      private function initEnemies() : void
      {
         if(_mission.hasMark("enemy501"))
         {
            return;
         }
         _mission.setMark("enemy501");
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[292,150,130,74],[820,396,130,74]],5011,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1024,396,130,74],[1560,128,130,74]],5012,this.triggerEnd1);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1256,1060,130,74],[1774,940,130,74]],5013);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,460),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1350,460),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1612,1084),-1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this.onTrigger1;
         this._pTrigger0.start();
      }
      
      private function onTrigger1() : void
      {
         this._dead = new DeadSensor("dead",{
            "x":1793,
            "y":591,
            "bornX":1530,
            "bornY":425,
            "width":256,
            "height":18
         });
         add(this._dead);
         this._pTrigger2.start();
      }
      
      private function triggerEnd0() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd1() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(1,855,400);
         remove(this._dead);
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         if(this._mcBOSS)
         {
            this._mcBOSS.destroy();
         }
         this._mcBOSS = null;
         this._dead = null;
         super.destroy();
      }
   }
}

