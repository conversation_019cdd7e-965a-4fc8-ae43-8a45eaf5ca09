package mogames.gameData.base
{
   public class ValueVO
   {
      public var value:Sint;
      
      public var type:Sint;
      
      public function ValueVO(param1:int, param2:int = 0)
      {
         super();
         this.value = new Sint(param1);
         this.type = new Sint(param2);
      }
      
      public function get isPer() : <PERSON><PERSON>an
      {
         return this.type.v == 1;
      }
   }
}

