package mogames.gameData.heishi
{
   import file.MallHeiShiConfig;
   import mogames.gameData.base.Sint;
   
   public class HeiShiFlag
   {
      public var flag:Sint;
      
      public var id:Sint;
      
      public function HeiShiFlag()
      {
         super();
         this.flag = new Sint();
         this.id = new Sint();
      }
      
      public function get hasBuy() : <PERSON><PERSON>an
      {
         return this.flag.v == 1;
      }
      
      public function get heishiVO() : MallHeiShiVO
      {
         return MallHeiShiConfig.instance().findVO(this.id.v);
      }
      
      public function get saveData() : String
      {
         return this.flag.v + "H" + this.id.v;
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc2_:Array = param1.split("H");
         this.flag.v = int(_loc2_[0]);
         this.id.v = int(_loc2_[1]);
      }
   }
}

