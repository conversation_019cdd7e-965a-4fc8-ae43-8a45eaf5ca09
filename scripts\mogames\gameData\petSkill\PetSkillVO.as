package mogames.gameData.petSkill
{
   import file.PetSkillConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   import mogames.gameData.heroSkill.BaseSkillVO;
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.role.HeroGameVO;
   
   public class PetSkillVO extends BaseSkillVO
   {
      public static const MAX_LEVEL:Sint = new Sint(10);
      
      public var curQuality:Sint = new Sint();
      
      protected var _pet:PetGameVO;
      
      public function PetSkillVO(param1:int, param2:HeroGameVO)
      {
         this._pet = param2 as PetGameVO;
         super(param1,param2);
      }
      
      override public function setLevel(param1:int, param2:Boolean = true) : void
      {
         level.v = param1;
         _levelVO = constVO.findlvVO(param1);
         if(param2 && Boolean(this._pet))
         {
            this._pet.updateExtra(true);
         }
      }
      
      override protected function initSkill(param1:int) : void
      {
         countVO = PetSkillConfig.instance().findSkillCount(param1);
         constVO = PetSkillConfig.instance().findConstSkill(param1);
         this.setLevel(1,false);
      }
      
      override public function levelup() : void
      {
         _levelVO.useStuff();
         --this._pet.skillPoint.v;
         this.setLevel(level.v + ConstData.DATA_NUM1.v,true);
      }
      
      override public function get needStr() : String
      {
         if(!_levelVO || this.isMaxLevel)
         {
            return "已满级！";
         }
         return _levelVO.parseNeedStr(_owner);
      }
      
      override protected function updateNeedMP() : void
      {
      }
      
      override public function useMP() : void
      {
      }
      
      override public function get isMaxLevel() : Boolean
      {
         return level.v >= MAX_LEVEL.v;
      }
      
      public function get tipInfor() : String
      {
         return constVO.infor;
      }
      
      override public function parseLoadData(param1:Array) : void
      {
         this.setLevel(int(param1[1]));
         this.curQuality.v = int(param1[2]);
      }
      
      override public function collectSaveData() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = constVO.id.v;
         _loc1_[1] = level.v;
         _loc1_[2] = this.curQuality.v;
         return _loc1_.join("A");
      }
   }
}

