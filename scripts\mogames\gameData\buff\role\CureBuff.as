package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   import utils.TxtUtil;
   
   public class CureBuff extends BaseBuff
   {
      private var value:int;
      
      public function CureBuff()
      {
         super(1008,false,true);
      }
      
      override protected function onBuff() : void
      {
         this.value = _buffVO.argDic.per * 0.01 * _roleVO.totalHP.v;
         _roleVO.changeHP(this.value);
         if(_role.isReady && _role.visible)
         {
            EffectManager.instance().addHeadWord(TxtUtil.setColor("生命+" + this.value,"99ff00"),_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override public function get buffSkin() : BMCSprite
      {
         _skin.setupSequence(AssetManager.findAnimation("EFFECT_BUFF_YUJINPING_CLIP"),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

