package component
{
   import flash.display.DisplayObject;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.utils.clearTimeout;
   import utils.MethodUtil;
   
   public class TabSwitcher
   {
      private var funcs:Array;
      
      private var btns:Array;
      
      private var last:SimpleButton;
      
      private var index:int;
      
      private var timerOut:uint;
      
      public function TabSwitcher(param1:Array, param2:Array, param3:int = 0)
      {
         super();
         this.btns = param1;
         this.funcs = param2;
         var _loc4_:int = 0;
         while(_loc4_ < param1.length)
         {
            (param1[_loc4_] as SimpleButton).addEventListener(MouseEvent.MOUSE_DOWN,this._click);
            _loc4_++;
         }
         if(param3 != -1)
         {
            (param1[param3] as SimpleButton).dispatchEvent(new MouseEvent(MouseEvent.MOUSE_DOWN));
         }
         (param1[0] as SimpleButton).addEventListener(Event.REMOVED_FROM_STAGE,this._remove);
      }
      
      private function _click(param1:MouseEvent) : void
      {
         if(this.last != null)
         {
            this.last.mouseEnabled = true;
            this.last.enabled = true;
            MethodUtil.toggle(this.last);
         }
         this.last = SimpleButton(param1.currentTarget);
         this.last.mouseEnabled = false;
         this.last.enabled = false;
         this.last.parent.swapChildren(this.last,this.getTopBtn());
         MethodUtil.toggle(this.last);
         this.index = this.btns.indexOf(this.last);
         this.funcs[this.index]();
      }
      
      private function _remove(param1:Event) : void
      {
         param1.currentTarget.removeEventListener(Event.REMOVED_FROM_STAGE,this._remove);
         var _loc2_:int = 0;
         while(_loc2_ < this.btns.length)
         {
            (this.btns[_loc2_] as SimpleButton).removeEventListener(MouseEvent.MOUSE_DOWN,this._click);
            _loc2_++;
         }
         this.funcs = null;
         this.btns = null;
         this.last = null;
      }
      
      public function changeBtn(param1:int) : void
      {
         if(param1 < this.btns.length && param1 > -1)
         {
            (this.btns[param1] as SimpleButton).dispatchEvent(new MouseEvent(MouseEvent.MOUSE_DOWN));
         }
      }
      
      private function getTopBtn() : DisplayObject
      {
         var _loc2_:DisplayObject = null;
         var _loc4_:DisplayObject = null;
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         while(_loc3_ < this.btns.length)
         {
            _loc4_ = this.btns[_loc3_];
            if(_loc4_.parent.getChildIndex(_loc4_) > _loc1_)
            {
               _loc2_ = _loc4_;
               _loc1_ = _loc4_.parent.getChildIndex(_loc4_);
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      private function tt() : void
      {
         this.funcs[this.btns.indexOf(this.last)]();
         clearTimeout(this.timerOut);
      }
      
      private function defaultBtns() : void
      {
         if(!this.btns)
         {
            return;
         }
         var _loc1_:int = 0;
         var _loc2_:int = int(this.btns.length);
         while(_loc1_ < _loc2_)
         {
            if(this.btns[_loc1_].enabled != true)
            {
               this.btns[_loc1_].mouseEnabled = true;
               this.btns[_loc1_].enabled = true;
               MethodUtil.toggle(this.btns[_loc1_]);
            }
            _loc1_++;
         }
      }
      
      public function get selectIndex() : int
      {
         return this.index;
      }
      
      public function get selectName() : String
      {
         return this.btns[this.index].name;
      }
      
      public function indexBtn(param1:int) : SimpleButton
      {
         return this.btns[param1];
      }
   }
}

