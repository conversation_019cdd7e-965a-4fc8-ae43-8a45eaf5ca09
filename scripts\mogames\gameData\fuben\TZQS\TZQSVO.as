package mogames.gameData.fuben.TZQS
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   
   public class TZQSVO
   {
      public var index:Sint;
      
      public var flag:Sint;
      
      public var free:Sint;
      
      public var rewards:Array;
      
      public function TZQSVO(param1:int, param2:int, param3:int, param4:Array)
      {
         super();
         this.index = new Sint(param1);
         this.flag = new Sint(param2);
         this.free = new Sint(param3);
         this.rewards = param4;
      }
      
      public function get unlock() : Boolean
      {
         if(this.free.v == 0)
         {
            return true;
         }
         return FlagProxy.instance().isComplete(this.free.v);
      }
      
      public function setComplete() : void
      {
         if(this.flag.v == 0)
         {
            return;
         }
         FlagProxy.instance().setValue(this.flag.v,1);
      }
   }
}

