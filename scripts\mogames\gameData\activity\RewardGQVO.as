package mogames.gameData.activity
{
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.UseHandler;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class RewardGQVO
   {
      public var flagID:Sint;
      
      public var rewardVO:BaseRewardVO;
      
      public var needList:Array;
      
      public function RewardGQVO(param1:int, param2:BaseRewardVO, param3:Array)
      {
         super();
         this.flagID = new Sint(param1);
         this.rewardVO = param2;
         this.needList = param3;
      }
      
      public function handlerExchange() : void
      {
         UseHandler.instance().useStuff(this.needList);
         FlagProxy.instance().changeValue(this.flagID.v,-1);
      }
      
      public function get isLack() : Boolean
      {
         var _loc1_:LackVO = UseHandler.instance().checkLack(this.needList);
         if(_loc1_)
         {
            MiniMsgMediator.instance().showAutoMsg(_loc1_.str);
            return true;
         }
         return false;
      }
      
      public function get leftStr() : String
      {
         return "兑换剩余：" + FlagProxy.instance().findFlagCur(this.flagID.v) + "次";
      }
      
      public function get isExchange() : Boolean
      {
         return FlagProxy.instance().findFlagCur(this.flagID.v) > 0;
      }
   }
}

