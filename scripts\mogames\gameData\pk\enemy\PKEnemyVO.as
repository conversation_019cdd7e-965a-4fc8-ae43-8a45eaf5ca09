package mogames.gameData.pk.enemy
{
   import mogames.gameData.ConstData;
   import mogames.gameData.role.HeroGameVO;
   
   public class PKEnemyVO extends HeroGameVO
   {
      private var _vip:int;
      
      public function PKEnemyVO(param1:int, param2:int)
      {
         this._vip = param2;
         super(param1);
      }
      
      override public function get ownHP() : int
      {
         var _loc1_:int = baseHP.v + equipHP.v + extraHP.v;
         if(perHP.v != 0)
         {
            _loc1_ += _loc1_ * perHP.v * ConstData.DATA_NUM001.v;
         }
         if(this._vip)
         {
            _loc1_ += _loc1_ * this._vip * 0.08;
         }
         return _loc1_;
      }
      
      override public function get ownPDEF() : int
      {
         var _loc1_:int = basePDEF.v + equipPDEF.v + extraPDEF.v;
         if(perPDEF.v != 0)
         {
            _loc1_ += _loc1_ * perPDEF.v * ConstData.DATA_NUM001.v;
         }
         if(this._vip)
         {
            _loc1_ += _loc1_ * this._vip * 0.04;
         }
         return _loc1_;
      }
      
      override public function get ownMDEF() : int
      {
         var _loc1_:int = baseMDEF.v + equipMDEF.v + extraMDEF.v;
         if(perMDEF.v != 0)
         {
            _loc1_ += _loc1_ * perMDEF.v * ConstData.DATA_NUM001.v;
         }
         if(this._vip)
         {
            _loc1_ += _loc1_ * this._vip * 0.04;
         }
         return _loc1_;
      }
   }
}

