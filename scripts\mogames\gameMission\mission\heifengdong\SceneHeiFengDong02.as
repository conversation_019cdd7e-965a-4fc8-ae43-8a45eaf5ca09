package mogames.gameMission.mission.heifengdong
{
   import citrus.objects.CitrusSprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameObj.bullet.LocationBullet;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.enemy.BOSSHeiXiongJing;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.MathUtil;
   import utils.TxtUtil;
   
   public class SceneHeiFengDong02 extends BossScene
   {
      private var _fireArgs:Array = [{
         "skin":"EFFECT_HEI_FENG_DONG_FIRE_CLIP0",
         "width":63,
         "height":105,
         "group":5,
         "posX":89,
         "posY":443
      },{
         "skin":"EFFECT_HEI_FENG_DONG_FIRE_CLIP0",
         "width":63,
         "height":105,
         "group":5,
         "posX":449,
         "posY":444
      },{
         "skin":"EFFECT_HEI_FENG_DONG_FIRE_CLIP0",
         "width":63,
         "height":105,
         "group":5,
         "posX":832,
         "posY":444
      },{
         "skin":"EFFECT_HEI_FENG_DONG_FIRE_CLIP1",
         "width":42,
         "height":70,
         "group":2,
         "posX":212,
         "posY":264
      },{
         "skin":"EFFECT_HEI_FENG_DONG_FIRE_CLIP1",
         "width":42,
         "height":70,
         "group":2,
         "posX":727,
         "posY":277
      }];
      
      private var _windArg:Object = {
         "showTime":10,
         "hurt":new Sint(10),
         "hurtTime":new Snum(0.5)
      };
      
      private var _fires:Vector.<CitrusMC>;
      
      private var _windTimer:CitrusTimer;
      
      private var _fireTimer:CitrusTimer;
      
      public function SceneHeiFengDong02(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(270,342,446,110),new Point(480,414));
         this._windTimer = new CitrusTimer(true);
         this._fireTimer = new CitrusTimer();
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2004,1);
            startBossBattle("BGM_BATTLE0");
         };
         this.addFire();
         this.addBOSS();
         _mission.cleanLoadUI();
         if(FlagProxy.instance().isComplete(2004))
         {
            this.startBossBattle("BGM_BATTLE0");
         }
         else
         {
            showDialog("STORY0012",func);
         }
      }
      
      private function addXuanFeng() : void
      {
         var _loc1_:int = MathUtil.checkOdds(500) ? 1 : -1;
         var _loc2_:int = _loc1_ == 1 ? -300 : 1200;
         var _loc3_:int = _loc1_ == 1 ? 1200 : -300;
         var _loc5_:Point = new Point(_loc3_,454);
         var _loc6_:LocationBullet = new LocationBullet(5,_loc5_,true,{
            "x":_loc2_,
            "y":454,
            "width":118,
            "height":143,
            "group":3
         });
         _loc6_.createView("EFFECT_XUAN_FENG_CLIP",118,143,0,0);
         _loc6_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",10,10);
         _loc6_.createHitHurt(this._windArg.hurt.v,this._windArg.hurtTime.v,false,1);
         _loc6_.isAlpha = false;
         _loc6_.isHurtDead = false;
         _loc6_.cleanFrame = 10;
         add(_loc6_);
         EffectManager.instance().playAudio("XUAN_FENG");
         if(_loc1_ == 1)
         {
            this.changeFire("right");
         }
         else
         {
            this.changeFire("left");
         }
         this._fireTimer.setTimeOut(2,this.changeFire);
      }
      
      private function changeFire(param1:String = "idle") : void
      {
         var _loc2_:CitrusMC = null;
         for each(_loc2_ in this._fires)
         {
            _loc2_.changeAnimation(param1,true);
         }
      }
      
      private function addFire() : void
      {
         var _loc1_:Object = null;
         this._fires = new Vector.<CitrusMC>();
         for each(_loc1_ in this._fireArgs)
         {
            this.createFire(_loc1_);
         }
      }
      
      private function createFire(param1:Object) : void
      {
         var _loc2_:CitrusSprite = new CitrusSprite("fire",{
            "width":param1.width,
            "height":param1.height,
            "group":param1.group
         });
         _loc2_.x = param1.posX;
         _loc2_.y = param1.posY;
         var _loc3_:CitrusMC = new CitrusMC();
         _loc3_.setupMC(GameInLoader.instance().findAsset(param1.skin));
         _loc3_.changeAnimation("idle",true);
         _loc2_.view = _loc3_;
         this._fires.push(_loc3_);
         add(_loc2_);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHeiXiongJing();
         _boss.x = 700;
         _boss.y = 450;
         _boss.initData(30031,30031,4);
         _boss.target = _mission.onePlayer;
         add(_boss);
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         super.startBossBattle(param1);
         this._windTimer.setInterval(this._windArg.showTime,0,this.addXuanFeng,null,false);
         this._windTimer.startNone();
         if(!_mission.missionVO.isComplete())
         {
            MiniMsgMediator.instance().showMsg("五行相克：" + TxtUtil.setColor("【水】","00ffff") + "克" + TxtUtil.setColor("【火】","ff0000"),480,30);
         }
      }
      
      private function destroyFire() : void
      {
         var _loc1_:CitrusMC = null;
         for each(_loc1_ in this._fires)
         {
            _loc1_.destroy();
         }
         this._fires.length = 0;
         this._fires = null;
      }
      
      override protected function handlerWin() : void
      {
         var func:Function = null;
         func = function():void
         {
            BattleMediator.instance().onBattle.dispatch({
               "type":"onBossDrop",
               "boss":_boss,
               "rect":_dropRect,
               "hero":_boss.target
            });
            Layers.lockGame = false;
            addLeaveDoor(_doorPos.x,_doorPos.y);
            FlagProxy.instance().setValue(104,1);
            PromptMediator.instance().showPrompt(TxtUtil.setColor("观世音的【店铺】","99ff00") + "功能已经开放！<br>（花果山找观世音）",null,null,true,"NEW_FUNCTION");
         };
         MiniMsgMediator.clean();
         this._windTimer.pause();
         TaskProxy.instance().setComplete(11002);
         if(!FlagProxy.instance().isComplete(104))
         {
            showDialog("STORY0032",func);
         }
         else
         {
            super.handlerWin();
         }
      }
      
      override public function destroy() : void
      {
         MiniMsgMediator.clean();
         this.destroyFire();
         this._windTimer.destroy();
         this._windTimer = null;
         this._fireTimer.destroy();
         this._fireTimer = null;
         _boss = null;
         super.destroy();
      }
   }
}

