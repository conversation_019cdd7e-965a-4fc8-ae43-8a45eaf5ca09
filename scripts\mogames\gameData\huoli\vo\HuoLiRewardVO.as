package mogames.gameData.huoli.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.huoli.HuoLiProxy;
   
   public class HuoLiRewardVO
   {
      public var flagID:Sint;
      
      public var need:Sint;
      
      public var rewards:Array;
      
      public function HuoLiRewardVO(param1:int, param2:int, param3:Array)
      {
         super();
         this.flagID = new Sint(param1);
         this.need = new Sint(param2);
         this.rewards = param3;
      }
      
      public function get canGet() : Boolean
      {
         return HuoLiProxy.instance().curScore >= this.need.v;
      }
      
      public function get hasGet() : Boolean
      {
         return FlagProxy.instance().isComplete(this.flagID.v);
      }
      
      public function handerlGet() : void
      {
         FlagProxy.instance().setValue(this.flagID.v,1);
      }
      
      public function get rewardInfor() : String
      {
         var _loc1_:Array = RewardHandler.instance().newGiftReward(this.rewards);
         return "奖励内容：<br>" + RewardHandler.instance().parseRewardName(_loc1_);
      }
   }
}

