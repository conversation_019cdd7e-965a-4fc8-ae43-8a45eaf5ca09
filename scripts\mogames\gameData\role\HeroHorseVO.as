package mogames.gameData.role
{
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.heroSkill.xiaobailong.SkillBaiLongBaVO;
   
   public class HeroHorseVO extends HeroGameVO
   {
      public function HeroHorseVO()
      {
         allSkills = [];
         allSkills.push(new HurtSkillVO(1006,this,0));
         allSkills.push(new HurtSkillVO(1007,this,1));
         allSkills.push(new HurtSkillVO(1008,this,2));
         allSkills.push(new SkillBaiLongBaVO(1009,this,3));
         allSkills.push(new HurtSkillVO(1010,this,4));
         updateKeySkills();
         super(1002);
      }
   }
}

