package file
{
   import mogames.gameData.ConstData;
   import mogames.gameData.base.RandomVO;
   import mogames.gameData.pet.vo.PetAssetVO;
   import mogames.gameData.pet.vo.PetConstVO;
   import mogames.gameData.pet.vo.PetCountVO;
   import utils.TxtUtil;
   
   public class PetConfig
   {
      private static var _instance:PetConfig;
      
      private var _constVec:Vector.<PetConstVO>;
      
      private var _countVec:Vector.<PetCountVO>;
      
      private var _assetVec:Vector.<PetAssetVO>;
      
      public var normal:Array;
      
      public var mutate:Array;
      
      public var talent:RandomVO;
      
      public var wit:RandomVO;
      
      public var learn:RandomVO;
      
      public var tipGrow:String;
      
      public function PetConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : PetConfig
      {
         if(!_instance)
         {
            _instance = new PetConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this.normal = [new RandomVO(51,70),new RandomVO(71,90),new RandomVO(91,110),new RandomVO(111,130)];
         this.mutate = [new RandomVO(61,80),new RandomVO(81,100),new RandomVO(101,120),new RandomVO(121,150)];
         this.talent = new RandomVO(1,10);
         this.wit = new RandomVO(1,10);
         this.learn = new RandomVO(1,10);
         this._constVec = new Vector.<PetConstVO>();
         this._countVec = new Vector.<PetCountVO>();
         this._assetVec = new Vector.<PetAssetVO>();
         this._constVec.push(new PetConstVO(1101,4,[[22,64],[36,80],[22,64],[22,64]]));
         this._constVec.push(new PetConstVO(1102,2,[[20,52],[30,74],[22,64],[22,64]]));
         this._constVec.push(new PetConstVO(1103,1,[[22,46],[22,50],[22,64],[22,64]]));
         this._constVec.push(new PetConstVO(1104,3,[[14,40],[16,50],[20,66],[22,64]]));
         this._constVec.push(new PetConstVO(1106,3,[[14,48],[38,60],[64,86],[22,64]]));
         this._constVec.push(new PetConstVO(1107,4,[[12,36],[16,50],[64,86],[18,54]]));
         this._constVec.push(new PetConstVO(1108,5,[[54,42],[80,66],[80,88],[80,88]]));
         this._countVec.push(new PetCountVO(1101,25,12,20,25,9,10,3));
         this._countVec.push(new PetCountVO(1102,35,11,22,22,10,9,3));
         this._countVec.push(new PetCountVO(1103,41,17,24,24,5,8,3));
         this._countVec.push(new PetCountVO(1104,54,25,23,35,8,5,3));
         this._countVec.push(new PetCountVO(1106,70,45,40,50,12,14,4));
         this._countVec.push(new PetCountVO(1107,109,65,71,71,17,16,5));
         this._countVec.push(new PetCountVO(1108,130,70,105,105,14,12,4));
         this._assetVec.push(new PetAssetVO(1101,"魔炎","ICON_MO_YAN","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","BODY_MO_YAN","pet/mcMoYan"));
         this._assetVec.push(new PetAssetVO(1102,"蛇灵","ICON_SHE_LING","EFFECT_PUNCH_HURT_CLIP","BONE_BALL_HIT","BODY_SHE_LING","pet/mcSheLing770_"));
         this._assetVec.push(new PetAssetVO(1103,"金灵","ICON_JIN_LING","EFFECT_GUN_ATK","GUNHURT","BODY_JIN_LING","pet/mcJinLing"));
         this._assetVec.push(new PetAssetVO(1104,"水灵","ICON_SHUI_LING","EFFECT_SWORD_HIT_CLIP","GUNHURT","BODY_SHUI_LING","pet/mcShuiLing"));
         this._assetVec.push(new PetAssetVO(1106,"冰麒麟","ICON_QI_LIN","EFFECT_BAI_QIU_BOOM_CLIP","GUNHURT","BODY_QI_LIN","pet/mcQiLin"));
         this._assetVec.push(new PetAssetVO(1107,"火凤凰","ICON_FENG_HUANG","EFFECT_FIRE_BOOM_CLIP","FIRE_EXPLORE","BODY_FENG_HUANG","pet/mcFengHuang"));
         this._assetVec.push(new PetAssetVO(1108,"玄武","ICON_XUAN_WU","EFFECT_GUN_ATK","SS_ATK_HURT","BODY_XUAN_WU","pet/mcXuanWu"));
         this.tipGrow = this.countTipGrow();
      }
      
      public function countTipGrow() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = "成长区间<br><br>";
         _loc1_[1] = TxtUtil.setColor("【普通】：",ConstData.PET_COLOR[0]) + (this.normal[0].min.v * 0.01).toFixed(2) + "~" + (this.normal[0].max.v * 0.01).toFixed(2) + "<br>";
         _loc1_[2] = TxtUtil.setColor("【优秀】：",ConstData.PET_COLOR[1]) + (this.normal[1].min.v * 0.01).toFixed(2) + "~" + (this.normal[1].max.v * 0.01).toFixed(2) + "<br>";
         _loc1_[3] = TxtUtil.setColor("【精良】：",ConstData.PET_COLOR[2]) + (this.normal[2].min.v * 0.01).toFixed(2) + "~" + (this.normal[2].max.v * 0.01).toFixed(2) + "<br>";
         _loc1_[4] = TxtUtil.setColor("【极品】：",ConstData.PET_COLOR[3]) + (this.normal[3].min.v * 0.01).toFixed(2) + "~" + (this.normal[3].max.v * 0.01).toFixed(2) + "<br><br>";
         _loc1_[5] = TxtUtil.setColor("【普通变异】：",ConstData.PET_COLOR[0]) + (this.mutate[0].min.v * 0.01).toFixed(2) + "~" + (this.mutate[0].max.v * 0.01).toFixed(2) + "<br>";
         _loc1_[6] = TxtUtil.setColor("【优秀变异】：",ConstData.PET_COLOR[1]) + (this.mutate[1].min.v * 0.01).toFixed(2) + "~" + (this.mutate[1].max.v * 0.01).toFixed(2) + "<br>";
         _loc1_[7] = TxtUtil.setColor("【精良变异】：",ConstData.PET_COLOR[2]) + (this.mutate[2].min.v * 0.01).toFixed(2) + "~" + (this.mutate[2].max.v * 0.01).toFixed(2) + "<br>";
         _loc1_[8] = TxtUtil.setColor("【极品变异】：",ConstData.PET_COLOR[3]) + (this.mutate[3].min.v * 0.01).toFixed(2) + "~" + (this.mutate[3].max.v * 0.01).toFixed(2);
         return _loc1_.join("·");
      }
      
      public function findConstPet(param1:int) : PetConstVO
      {
         var _loc2_:PetConstVO = null;
         for each(_loc2_ in this._constVec)
         {
            if(_loc2_.roleID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findPetCount(param1:int) : PetCountVO
      {
         var _loc2_:PetCountVO = null;
         if(param1 == 0)
         {
            return null;
         }
         for each(_loc2_ in this._countVec)
         {
            if(_loc2_.petID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findPetAsset(param1:int) : PetAssetVO
      {
         var _loc2_:PetAssetVO = null;
         for each(_loc2_ in this._assetVec)
         {
            if(_loc2_.roleID.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findPetGrow(param1:int, param2:Boolean) : RandomVO
      {
         if(param2)
         {
            return this.mutate[param1];
         }
         return this.normal[param1];
      }
   }
}

