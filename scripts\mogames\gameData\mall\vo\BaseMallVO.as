package mogames.gameData.mall.vo
{
   import file.GoodConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.good.GameGoodVO;
   
   public class BaseMallVO
   {
      public var mallID:Sint;
      
      public var price:Sint;
      
      public var priceType:int;
      
      public var tip:int;
      
      public var mallGood:BaseRewardVO;
      
      public function BaseMallVO(param1:int, param2:BaseRewardVO, param3:int, param4:int = 0)
      {
         super();
         this.mallID = new Sint(param1);
         this.mallGood = param2;
         this.price = new Sint(param3);
         this.tip = param4;
      }
      
      public function newGood(param1:int = 1) : Array
      {
         if(!this.mallGood)
         {
            return [];
         }
         if(this.isEquip)
         {
            return this.countNewEquip(param1);
         }
         return this.countNewGood(param1);
      }
      
      private function countNewEquip(param1:int) : Array
      {
         var _loc2_:Array = [];
         var _loc3_:int = 0;
         while(_loc3_ < param1)
         {
            _loc2_.push(this.mallGood.newGood());
            _loc3_++;
         }
         return _loc2_;
      }
      
      private function countNewGood(param1:int) : Array
      {
         var _loc2_:GameGoodVO = this.mallGood.newGood();
         _loc2_.amount.v = param1;
         return [_loc2_];
      }
      
      public function get isEquip() : Boolean
      {
         var _loc1_:String = GoodConfig.instance().findGoodType(this.mallGood.id.v);
         return _loc1_ == ConstData.GOOD_EQUIP || _loc1_ == ConstData.GOOD_FASHION;
      }
      
      public function checkLack(param1:int = 1) : LackVO
      {
         return null;
      }
      
      public function askStr(param1:int) : String
      {
         return "";
      }
   }
}

