package mogames.gameMission.mission.huangquanlu
{
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   
   public class HQLEnemyTrigger extends EnemyTrigger
   {
      public function HQLEnemyTrigger(param1:BaseMission, param2:Array, param3:int, param4:Function = null, param5:Function = null)
      {
         super(param1,param2,param3,param4,param5);
         _nexts = _curs;
      }
      
      override protected function listenDead(param1:String) : void
      {
         if(param1 != "onDead")
         {
            return;
         }
         if(_index >= _nexts.length)
         {
            _index = 0;
         }
         var _loc2_:Object = _nexts[_index];
         addNewEnemy(_loc2_.id.v,_loc2_.attID.v,_loc2_.aiID.v,_loc2_.dropID.v,randomPos());
         ++_index;
      }
   }
}

