package file
{
   import mogames.gameData.ConstData;
   import mogames.gameData.fabao.RLUseVO;
   import mogames.gameData.fabao.RongLianVO;
   import mogames.gameData.forge.NeedVO;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import utils.TxtUtil;
   
   public class FashionRLConfig
   {
      private static var _instance:FashionRLConfig;
      
      private var _rlVec:Vector.<RongLianVO>;
      
      public function FashionRLConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : FashionRLConfig
      {
         if(!_instance)
         {
            _instance = new FashionRLConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._rlVec = new Vector.<RongLianVO>();
         this._rlVec.push(new RongLianVO(34101,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(35001,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(34102,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(35002,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(34103,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(35003,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(34104,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(35004,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(34105,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(35005,[new RLUseVO(0,20000,[new NeedVO(14025,10),new NeedVO(14031,10),new NeedVO(14029,10)]),new RLUseVO(1,40000,[new NeedVO(14027,15),new NeedVO(14035,15),new NeedVO(14037,15)]),new RLUseVO(2,60000,[new NeedVO(14028,15),new NeedVO(14027,15),new NeedVO(14025,10)])]));
         this._rlVec.push(new RongLianVO(34111,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(35011,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(34112,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(35012,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(34113,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(35013,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(34114,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(35014,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(34115,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(35015,[new RLUseVO(0,50000,[new NeedVO(14025,20),new NeedVO(14031,20),new NeedVO(14029,20)]),new RLUseVO(1,40000,[new NeedVO(14027,30),new NeedVO(14035,30),new NeedVO(14037,30)]),new RLUseVO(2,60000,[new NeedVO(14028,20),new NeedVO(14027,20),new NeedVO(14025,15)])]));
         this._rlVec.push(new RongLianVO(34121,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
         this._rlVec.push(new RongLianVO(35021,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
         this._rlVec.push(new RongLianVO(34122,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
         this._rlVec.push(new RongLianVO(35022,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
         this._rlVec.push(new RongLianVO(34123,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
         this._rlVec.push(new RongLianVO(35023,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
         this._rlVec.push(new RongLianVO(34124,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
         this._rlVec.push(new RongLianVO(35024,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
         this._rlVec.push(new RongLianVO(34125,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
         this._rlVec.push(new RongLianVO(35025,[new RLUseVO(0,80000,[new NeedVO(14025,25),new NeedVO(14031,25),new NeedVO(14029,25)]),new RLUseVO(1,80000,[new NeedVO(14027,35),new NeedVO(14035,35),new NeedVO(14037,35)]),new RLUseVO(2,80000,[new NeedVO(14028,25),new NeedVO(14027,25),new NeedVO(14025,20)])]));
      }
      
      public function checkRL(param1:int, param2:int) : Boolean
      {
         if(!this.hasFashion(param1))
         {
            MiniMsgMediator.instance().showAutoMsg("该时装熔炼暂未开放！");
            return false;
         }
         var _loc3_:int = this.maxQuality(param1);
         if(param2 >= _loc3_)
         {
            MiniMsgMediator.instance().showAutoMsg("该时装当前只可以熔炼至【" + TxtUtil.setColor(ConstData.GOOD_QUALITY[_loc3_],ConstData.GOOD_COLOR1[_loc3_]) + "】品质！");
            return false;
         }
         return true;
      }
      
      public function findUseVO(param1:int, param2:int) : RLUseVO
      {
         var _loc3_:RongLianVO = this.findRLVO(param1);
         if(!_loc3_)
         {
            return null;
         }
         return _loc3_.findVO(param2);
      }
      
      private function maxQuality(param1:int) : int
      {
         var _loc2_:RongLianVO = this.findRLVO(param1);
         return _loc2_.list.length;
      }
      
      private function findRLVO(param1:int) : RongLianVO
      {
         var _loc2_:RongLianVO = null;
         for each(_loc2_ in this._rlVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      private function hasFashion(param1:int) : Boolean
      {
         var _loc2_:RongLianVO = null;
         for each(_loc2_ in this._rlVec)
         {
            if(_loc2_.id.v == param1)
            {
               return true;
            }
         }
         return false;
      }
   }
}

