package mogames.gameData.role.vo
{
   import file.WuXingConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.RandomVO;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameData.buff.BaseBuff;
   
   public class RoleGameVO
   {
      public var id:Sint;
      
      public var constVO:RoleConstVO;
      
      public var assetVO:RoleAssetVO;
      
      public var baseHP:Sint;
      
      public var skillHP:Sint;
      
      public var totalHP:Sint;
      
      public var baseMP:Sint;
      
      public var skillMP:Sint;
      
      public var totalMP:Sint;
      
      public var baseATK:Sint;
      
      public var skillATK:Sint;
      
      public var totalATK:Sint;
      
      public var basePDEF:Sint;
      
      public var skillPDEF:Sint;
      
      public var totalPDEF:Sint;
      
      public var baseMDEF:Sint;
      
      public var skillMDEF:Sint;
      
      public var totalMDEF:Sint;
      
      public var baseMISS:Sint;
      
      public var skillMISS:Sint;
      
      public var totalMISS:Sint;
      
      public var baseCRIT:Sint;
      
      public var skillCRIT:Sint;
      
      public var totalCRIT:Sint;
      
      public var baseLUCK:Sint;
      
      public var skillLUCK:Sint;
      
      public var totalLUCK:Sint;
      
      public var baseMOVE:Snum;
      
      public var skillMOVE:Snum;
      
      public var totalMOVE:Snum;
      
      public var baseRUN:Snum;
      
      public var skillRUN:Snum;
      
      public var totalRUN:Snum;
      
      public var curHP:Sint;
      
      public var curMP:Sint;
      
      public var buffList:Array;
      
      private var _randVO:RandomVO;
      
      private var _critCount:Snum = new Snum(1.5);
      
      public function RoleGameVO(param1:int = 0)
      {
         super();
         this.id = new Sint(param1);
         this.baseHP = new Sint();
         this.skillHP = new Sint();
         this.totalHP = new Sint();
         this.baseMP = new Sint();
         this.skillMP = new Sint();
         this.totalMP = new Sint();
         this.baseATK = new Sint();
         this.skillATK = new Sint();
         this.totalATK = new Sint();
         this.basePDEF = new Sint();
         this.skillPDEF = new Sint();
         this.totalPDEF = new Sint();
         this.baseMDEF = new Sint();
         this.skillMDEF = new Sint();
         this.totalMDEF = new Sint();
         this.baseMISS = new Sint();
         this.skillMISS = new Sint();
         this.totalMISS = new Sint();
         this.baseCRIT = new Sint();
         this.skillCRIT = new Sint();
         this.totalCRIT = new Sint();
         this.baseLUCK = new Sint();
         this.skillLUCK = new Sint();
         this.totalLUCK = new Sint();
         this.baseMOVE = new Snum();
         this.skillMOVE = new Snum();
         this.totalMOVE = new Snum();
         this.baseRUN = new Snum();
         this.skillRUN = new Snum();
         this.totalRUN = new Snum();
         this.curHP = new Sint();
         this.curMP = new Sint();
         this.buffList = [];
         this._randVO = new RandomVO(8,10);
      }
      
      public function countHURT(param1:BaseHitVO) : int
      {
         var _loc2_:int = this.countHurtNum(param1);
         var _loc3_:int = this._randVO.randomValue();
         _loc2_ = _loc2_ * this._randVO.randomValue() * ConstData.DATA_NUM01.v;
         if(param1.isCrit)
         {
            _loc2_ *= this._critCount.v;
         }
         if(_loc2_ <= ConstData.DATA_NUM0.v)
         {
            _loc2_ = ConstData.DATA_NUM1.v;
         }
         this.changeHP(-_loc2_);
         return _loc2_;
      }
      
      protected function countHurtNum(param1:BaseHitVO) : int
      {
         var _loc2_:RoleWXVO = WuXingConfig.instance().findWuXingVO(param1.WU_XING.v);
         if(param1.atkType.v == ConstData.DATA_NUM0.v)
         {
            return Math.pow(param1.hurt.v,ConstData.DATA_NUM2.v) / (param1.hurt.v + this.totalPDEF.v * ConstData.DATA_NUM2.v) * _loc2_.findPer(this.constVO.wuxing.v).v * ConstData.DATA_NUM001.v;
         }
         if(param1.atkType.v == ConstData.DATA_NUM1.v)
         {
            return Math.pow(param1.hurt.v,ConstData.DATA_NUM2.v) / (param1.hurt.v + this.totalMDEF.v * ConstData.DATA_NUM2.v) * _loc2_.findPer(this.constVO.wuxing.v).v * ConstData.DATA_NUM001.v;
         }
         return ConstData.DATA_NUM1.v;
      }
      
      public function changeHP(param1:int) : void
      {
         if(this.isDead())
         {
            return;
         }
         this.curHP.v += param1;
         if(this.curHP.v <= ConstData.DATA_NUM0.v)
         {
            this.curHP.v = ConstData.DATA_NUM0.v;
         }
         else if(this.curHP.v >= this.totalHP.v)
         {
            this.curHP.v = this.totalHP.v;
         }
      }
      
      public function changeMP(param1:int) : void
      {
         this.curMP.v += param1;
         if(this.curMP.v <= ConstData.DATA_NUM0.v)
         {
            this.curMP.v = ConstData.DATA_NUM0.v;
         }
         else if(this.curMP.v >= this.totalMP.v)
         {
            this.curMP.v = this.totalMP.v;
         }
      }
      
      public function isDead() : Boolean
      {
         return this.curHP.v <= ConstData.DATA_NUM0.v;
      }
      
      public function updateAll() : void
      {
         this.updateHP();
         this.updateMP();
         this.updateATK();
         this.updatePDEF();
         this.updateMDEF();
         this.updateMISS();
         this.updateCRIT();
         this.updateMOVE();
         this.updateRUN();
      }
      
      public function addBuff(param1:BaseBuff) : void
      {
         var _loc2_:int = int(this.buffList.indexOf(param1));
         if(_loc2_ != -1)
         {
            return;
         }
         this.buffList.push(param1);
      }
      
      public function delBuff(param1:BaseBuff) : void
      {
         var _loc2_:int = int(this.buffList.indexOf(param1));
         if(_loc2_ != -1)
         {
            this.buffList.splice(_loc2_,1);
         }
      }
      
      public function hasBuff(param1:int) : Boolean
      {
         var _loc2_:BaseBuff = null;
         for each(_loc2_ in this.buffList)
         {
            if(_loc2_.buffID.v == param1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function cleanBuff() : void
      {
         var _loc1_:BaseBuff = null;
         for each(_loc1_ in this.buffList)
         {
            _loc1_.cleanBuff();
         }
         this.buffList.length = 0;
      }
      
      public function updateHP() : void
      {
         this.totalHP.v = this.baseHP.v + this.skillHP.v;
      }
      
      public function updateMP() : void
      {
         this.totalMP.v = this.baseMP.v + this.skillMP.v;
      }
      
      public function updateATK() : void
      {
         this.totalATK.v = Math.max(1,this.baseATK.v + this.skillATK.v);
      }
      
      public function updatePDEF() : void
      {
         this.totalPDEF.v = Math.max(0,this.basePDEF.v + this.skillPDEF.v);
      }
      
      public function updateMDEF() : void
      {
         this.totalMDEF.v = Math.max(0,this.baseMDEF.v + this.skillMDEF.v);
      }
      
      public function updateMISS() : void
      {
         this.totalMISS.v = this.baseMISS.v + this.skillMISS.v;
      }
      
      public function updateCRIT() : void
      {
         this.totalCRIT.v = this.baseCRIT.v + this.skillCRIT.v;
      }
      
      public function updateLUCK() : void
      {
         this.totalLUCK.v = this.baseLUCK.v + this.skillLUCK.v;
      }
      
      public function updateMOVE() : void
      {
         if(this.baseMOVE.v + this.skillMOVE.v < 0)
         {
            this.totalMOVE.v = this.baseMOVE.v;
         }
         else
         {
            this.totalMOVE.v = Math.max(0,this.baseMOVE.v + this.skillMOVE.v);
         }
      }
      
      public function updateRUN() : void
      {
         if(this.baseRUN.v + this.skillRUN.v < 0)
         {
            this.totalRUN.v = this.baseRUN.v;
         }
         else
         {
            this.totalRUN.v = Math.max(0,this.baseRUN.v + this.skillRUN.v);
         }
      }
      
      public function updateSPEED() : void
      {
         this.updateMOVE();
         this.updateRUN();
      }
      
      public function isBOSS() : Boolean
      {
         return false;
      }
      
      public function get hpPer() : Number
      {
         return this.curHP.v / this.totalHP.v;
      }
   }
}

