package mogames.gameLoad
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.utils.Timer;
   import mogames.Layers;
   import utils.MethodUtil;
   
   public class AssetLoadFrame
   {
      private static var _instance:AssetLoadFrame;
      
      public var loadTips:Array = ["经常保存游戏，这是个好习惯！","钱不够用？挑战小白龙能获得不少铜钱哦！","完成主线任务，会有神秘神仙加入你阵营","铁匠可以强化你装备，让过关变的容易点","使用合理的法宝或许是你通关的关键","绿色以上品质的装备，只有关卡BOSS掉落","掌握BOSS出招先后顺序，可以让通关变简单","关卡中按【1】【2】数字键切换角色。","关卡中按【空格】释放无双技能。","关卡中按【N】释放法宝技能。","游戏中有属性相克系统，打不过可以尝试其他角色组合。","找太上老君可以炼制丹药，服用后提升角色属性。","找灵吉菩萨可以合成法宝","五庄观里可以种植获得药。","每个角色移动力、跳跃力都不同，需灵活组合","孙悟空物攻高、猪八戒能回血、小白龙闪避暴击高、沙僧物防高","法宝镇妖塔可以吸收大妖元神","吸收的大妖元神会被关进镇妖塔","大妖血量越低，元神被收服的几率越大","加载过程中如果卡住，请按F5刷新重试","正常游戏，使用辅助外挂会被封号哦！","游戏中遇到问题，联系客服人员反馈"];
      
      private var _panel:MovieClip;
      
      private var _timer:Timer;
      
      private var _tempTips:Array;
      
      public function AssetLoadFrame()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this._panel = new UIMainLoadClip();
         Layers.topLayer.addChild(this._panel);
         this._panel.btnBBS.addEventListener(MouseEvent.CLICK,this.onBBS,false,0,true);
         if(!this._timer)
         {
            this._timer = new Timer(5000);
         }
         this._timer.addEventListener(TimerEvent.TIMER,this.randomTips,false,0,true);
         this._timer.start();
         this._tempTips = [];
         this.randomTips();
      }
      
      public static function instance() : AssetLoadFrame
      {
         if(!_instance)
         {
            _instance = new AssetLoadFrame();
         }
         return _instance;
      }
      
      public static function clean() : void
      {
         if(!_instance)
         {
            return;
         }
         _instance.destroy();
         _instance = null;
      }
      
      private function onBBS(param1:MouseEvent) : void
      {
         navigateToURL(new URLRequest("http://my.4399.com/forums-mtag-tagid-82773.html"));
      }
      
      private function randomTips(param1:TimerEvent = null) : void
      {
         if(this._tempTips.length <= 0)
         {
            this._tempTips = this.loadTips.slice();
         }
         var _loc2_:int = int(this._tempTips.length);
         var _loc3_:int = int(Math.random() * _loc2_);
         this.showTip(this._tempTips[_loc3_]);
         this._tempTips.splice(_loc3_,1);
      }
      
      public function update(param1:String, param2:int) : void
      {
         this._panel.txtNum.text = param1;
         this._panel.txtPer.text = param2 + "%";
         this._panel.mcBar.gotoAndStop(param2);
      }
      
      private function showTip(param1:String) : void
      {
         this._panel.txtTip.text = param1;
      }
      
      private function destroy() : void
      {
         this._panel.btnBBS.removeEventListener(MouseEvent.CLICK,this.onBBS);
         MethodUtil.removeMe(this._panel);
         this._panel = null;
         this._timer.removeEventListener(TimerEvent.TIMER,this.randomTips);
         this._timer = null;
         this._tempTips = null;
      }
   }
}

