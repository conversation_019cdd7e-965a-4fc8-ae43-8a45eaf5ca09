package mogames.gameData.flag.fuben
{
   import mogames.gameData.ConstData;
   import mogames.gameData.MasterProxy;
   
   public class TaskFlushFlag extends FBBaseFlag
   {
      public function TaskFlushFlag(param1:int, param2:int)
      {
         super(param1,param2);
      }
      
      override public function get totalFree() : int
      {
         if(MasterProxy.instance().gameVIP.v >= 7)
         {
            return _totalFree.v + ConstData.DATA_NUM3.v;
         }
         if(MasterProxy.instance().gameVIP.v >= 3)
         {
            return _totalFree.v + ConstData.DATA_NUM2.v;
         }
         return _totalFree.v;
      }
      
      override public function get status() : String
      {
         if(isFree)
         {
            return "免费刷新：" + leftFree + "次";
         }
         return "消耗5金锭";
      }
   }
}

