package mogames.gameData.pet.vo
{
   import mogames.gameData.role.vo.RoleAssetVO;
   
   public class PetAssetVO extends RoleAssetVO
   {
      public var mcURL:String;
      
      public function PetAssetVO(param1:int, param2:String, param3:String, param4:String, param5:String, param6:String, param7:String)
      {
         super(param1,param2,param3,param4,param5,param6);
         this.mcURL = param7;
      }
      
      public function get urls() : Array
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.mcURL + "0.swf";
         _loc1_[1] = this.mcURL + "1.swf";
         _loc1_[2] = this.mcURL + "2.swf";
         return _loc1_;
      }
   }
}

