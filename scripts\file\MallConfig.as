package file
{
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.drop.vo.EquipRewardVO;
   import mogames.gameData.mall.vo.MallMoneyVO;
   import mogames.gameData.mall.vo.MallTicketVO;
   import mogames.gamePKG.GameProxy;
   
   public class MallConfig
   {
      private static var _instance:MallConfig;
      
      private var _moneyList:Array;
      
      private var _ticketList:Array;
      
      private var cheatList:Array;
      
      public function MallConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : MallConfig
      {
         if(!_instance)
         {
            _instance = new MallConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._moneyList = [];
         this._moneyList[2] = [];
         this._moneyList[2].push(new MallMoneyVO(3786,new BaseRewardVO(10095),1980,7));
         this._moneyList[2].push(new MallMoneyVO(3436,new BaseRewardVO(16766),25));
         this._moneyList[2].push(new MallMoneyVO(3712,new BaseRewardVO(13550),5));
         this._moneyList[2].push(new MallMoneyVO(3712,new BaseRewardVO(13551),5));
         this._moneyList[2].push(new MallMoneyVO(3712,new BaseRewardVO(13552),5));
         this._moneyList[2].push(new MallMoneyVO(3712,new BaseRewardVO(13553),5));
         this._moneyList[2].push(new MallMoneyVO(3712,new BaseRewardVO(13554),5));
         this._moneyList[2].push(new MallMoneyVO(3712,new BaseRewardVO(13555),5));
         this._moneyList[2].push(new MallMoneyVO(3712,new BaseRewardVO(13556),5));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(13144),8));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(13145),8));
         this._moneyList[2].push(new MallMoneyVO(3580,new BaseRewardVO(10093),1400,5));
         this._moneyList[2].push(new MallMoneyVO(3284,new BaseRewardVO(10091),1180,5));
         this._moneyList[2].push(new MallMoneyVO(3554,new BaseRewardVO(14108),554,7));
         this._moneyList[2].push(new MallMoneyVO(3579,new BaseRewardVO(14071),396,2));
         this._moneyList[2].push(new MallMoneyVO(3649,new BaseRewardVO(13141),5));
         this._moneyList[2].push(new MallMoneyVO(3649,new BaseRewardVO(13142),5));
         this._moneyList[2].push(new MallMoneyVO(3649,new BaseRewardVO(16813),5));
         this._moneyList[2].push(new MallMoneyVO(3649,new BaseRewardVO(16814),5));
         this._moneyList[2].push(new MallMoneyVO(3649,new BaseRewardVO(16815),5));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(16810),8));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(16811),8));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(16812),8));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(16816),8));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(16817),8));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(16818),8));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(16801),8));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(16802),8));
         this._moneyList[2].push(new MallMoneyVO(3650,new BaseRewardVO(16803),8));
         this._moneyList[2].push(new MallMoneyVO(3649,new BaseRewardVO(16807),5));
         this._moneyList[2].push(new MallMoneyVO(3649,new BaseRewardVO(16808),5));
         this._moneyList[2].push(new MallMoneyVO(3649,new BaseRewardVO(16809),5));
         this._moneyList[2].push(new MallMoneyVO(3648,new BaseRewardVO(16804),3));
         this._moneyList[2].push(new MallMoneyVO(3648,new BaseRewardVO(16805),3));
         this._moneyList[2].push(new MallMoneyVO(3648,new BaseRewardVO(16806),3));
         this._moneyList[0] = [];
         this._moneyList[0].push(new MallMoneyVO(3548,new BaseRewardVO(14096),9));
         this._moneyList[0].push(new MallMoneyVO(3549,new BaseRewardVO(14097),29));
         this._moneyList[0].push(new MallMoneyVO(3550,new BaseRewardVO(14098),49));
         this._moneyList[0].push(new MallMoneyVO(3551,new BaseRewardVO(14099),69));
         this._moneyList[0].push(new MallMoneyVO(3552,new BaseRewardVO(14100),89));
         this._moneyList[0].push(new MallMoneyVO(2992,new BaseRewardVO(16751),8));
         this._moneyList[0].push(new MallMoneyVO(2830,new BaseRewardVO(14150),8));
         this._moneyList[0].push(new MallMoneyVO(2797,new BaseRewardVO(16711),20));
         this._moneyList[0].push(new MallMoneyVO(2835,new BaseRewardVO(14009),10));
         this._moneyList[0].push(new MallMoneyVO(2798,new BaseRewardVO(14002),10));
         this._moneyList[0].push(new MallMoneyVO(3137,new BaseRewardVO(14008),128));
         this._moneyList[0].push(new MallMoneyVO(3129,new BaseRewardVO(14125),30));
         this._moneyList[0].push(new MallMoneyVO(3433,new BaseRewardVO(14107),113));
         this._moneyList[0].push(new MallMoneyVO(3436,new BaseRewardVO(16765),25));
         this._moneyList[0].push(new MallMoneyVO(3288,new BaseRewardVO(14000),19));
         this._moneyList[0].push(new MallMoneyVO(3272,new BaseRewardVO(16764),25));
         this._moneyList[0].push(new MallMoneyVO(3249,new BaseRewardVO(16763),15));
         this._moneyList[0].push(new MallMoneyVO(3184,new BaseRewardVO(14045),9));
         this._moneyList[0].push(new MallMoneyVO(3195,new BaseRewardVO(50000),9));
         this._moneyList[0].push(new MallMoneyVO(3208,new BaseRewardVO(50005),59));
         this._moneyList[0].push(new MallMoneyVO(3095,new BaseRewardVO(16721),25));
         this._moneyList[0].push(new MallMoneyVO(3096,new BaseRewardVO(16722),20));
         this._moneyList[0].push(new MallMoneyVO(3030,new BaseRewardVO(16757),9));
         this._moneyList[0].push(new MallMoneyVO(3032,new BaseRewardVO(16762),9));
         this._moneyList[0].push(new MallMoneyVO(2991,new BaseRewardVO(16750),29));
         this._moneyList[0].push(new MallMoneyVO(2993,new BaseRewardVO(16752),18));
         this._moneyList[0].push(new MallMoneyVO(3005,new BaseRewardVO(16761),5));
         this._moneyList[0].push(new MallMoneyVO(2996,new BaseRewardVO(16755),29));
         this._moneyList[0].push(new MallMoneyVO(2997,new BaseRewardVO(16756),29));
         this._moneyList[0].push(new MallMoneyVO(2998,new BaseRewardVO(16758),48));
         this._moneyList[0].push(new MallMoneyVO(3001,new BaseRewardVO(16759),9));
         this._moneyList[0].push(new MallMoneyVO(3002,new BaseRewardVO(16760),49));
         this._moneyList[0].push(new MallMoneyVO(2893,new BaseRewardVO(14021),3));
         this._moneyList[0].push(new MallMoneyVO(2894,new BaseRewardVO(14039),6));
         this._moneyList[0].push(new MallMoneyVO(3553,new BaseRewardVO(10024),8));
         this._moneyList[0].push(new MallMoneyVO(3516,new BaseRewardVO(14011),8));
         this._moneyList[0].push(new MallMoneyVO(3504,new BaseRewardVO(13133),5));
         this._moneyList[0].push(new MallMoneyVO(3504,new BaseRewardVO(13134),5));
         this._moneyList[0].push(new MallMoneyVO(3504,new BaseRewardVO(13135),5));
         this._moneyList[0].push(new MallMoneyVO(3514,new BaseRewardVO(13136),8));
         this._moneyList[0].push(new MallMoneyVO(3514,new BaseRewardVO(13137),8));
         this._moneyList[0].push(new MallMoneyVO(3514,new BaseRewardVO(13138),8));
         this._moneyList[0].push(new MallMoneyVO(3514,new BaseRewardVO(13139),8));
         this._moneyList[0].push(new MallMoneyVO(3515,new BaseRewardVO(13118),5));
         this._moneyList[0].push(new MallMoneyVO(2834,new BaseRewardVO(14030),30));
         this._moneyList[0].push(new MallMoneyVO(2818,new BaseRewardVO(14101),50));
         this._moneyList[0].push(new MallMoneyVO(2796,new BaseRewardVO(16710),50));
         this._moneyList[0].push(new MallMoneyVO(3127,new BaseRewardVO(14120),4));
         this._moneyList[0].push(new MallMoneyVO(3128,new BaseRewardVO(14121),10));
         this._moneyList[0].push(new MallMoneyVO(3123,new BaseRewardVO(13326),8));
         this._moneyList[0].push(new MallMoneyVO(3104,new BaseRewardVO(14028),7));
         this._moneyList[0].push(new MallMoneyVO(2994,new BaseRewardVO(16753),5));
         this._moneyList[0].push(new MallMoneyVO(2995,new BaseRewardVO(16754),5));
         this._moneyList[0].push(new MallMoneyVO(3140,new BaseRewardVO(13111),5));
         this._moneyList[0].push(new MallMoneyVO(3141,new BaseRewardVO(13112),5));
         this._moneyList[0].push(new MallMoneyVO(3142,new BaseRewardVO(13113),5));
         this._moneyList[0].push(new MallMoneyVO(3143,new BaseRewardVO(13114),8));
         this._moneyList[0].push(new MallMoneyVO(3144,new BaseRewardVO(13115),12));
         this._moneyList[0].push(new MallMoneyVO(3162,new BaseRewardVO(13401),5));
         this._moneyList[0].push(new MallMoneyVO(3163,new BaseRewardVO(13402),5));
         this._moneyList[0].push(new MallMoneyVO(3164,new BaseRewardVO(13403),5));
         this._moneyList[0].push(new MallMoneyVO(3165,new BaseRewardVO(13404),5));
         this._moneyList[0].push(new MallMoneyVO(3166,new BaseRewardVO(13405),5));
         this._moneyList[0].push(new MallMoneyVO(3167,new BaseRewardVO(13406),5));
         this._moneyList[0].push(new MallMoneyVO(3145,new BaseRewardVO(13116),5));
         this._moneyList[0].push(new MallMoneyVO(3146,new BaseRewardVO(13117),5));
         this._moneyList[0].push(new MallMoneyVO(3147,new BaseRewardVO(50109),5));
         this._moneyList[0].push(new MallMoneyVO(3148,new BaseRewardVO(50110),5));
         this._moneyList[0].push(new MallMoneyVO(3149,new BaseRewardVO(50111),5));
         this._moneyList[0].push(new MallMoneyVO(3150,new BaseRewardVO(50112),5));
         this._moneyList[0].push(new MallMoneyVO(3040,new BaseRewardVO(50101),5));
         this._moneyList[0].push(new MallMoneyVO(3041,new BaseRewardVO(50102),5));
         this._moneyList[0].push(new MallMoneyVO(3042,new BaseRewardVO(50103),5));
         this._moneyList[0].push(new MallMoneyVO(3043,new BaseRewardVO(50104),5));
         this._moneyList[0].push(new MallMoneyVO(3044,new BaseRewardVO(50105),5));
         this._moneyList[0].push(new MallMoneyVO(3045,new BaseRewardVO(50106),5));
         this._moneyList[0].push(new MallMoneyVO(3046,new BaseRewardVO(50107),5));
         this._moneyList[0].push(new MallMoneyVO(3047,new BaseRewardVO(50108),5));
         this._moneyList[0].push(new MallMoneyVO(2890,new BaseRewardVO(13322),3));
         this._moneyList[0].push(new MallMoneyVO(2891,new BaseRewardVO(13325),6));
         this._moneyList[0].push(new MallMoneyVO(2892,new BaseRewardVO(13323),6));
         this._moneyList[0].push(new MallMoneyVO(2895,new BaseRewardVO(13109),3));
         this._moneyList[0].push(new MallMoneyVO(2896,new BaseRewardVO(13310),5));
         this._moneyList[0].push(new MallMoneyVO(2858,new BaseRewardVO(14037),5));
         this._moneyList[0].push(new MallMoneyVO(2950,new BaseRewardVO(13324),6));
         this._moneyList[0].push(new MallMoneyVO(2857,new BaseRewardVO(14035),5));
         this._moneyList[0].push(new MallMoneyVO(2856,new BaseRewardVO(14031),3));
         this._moneyList[0].push(new MallMoneyVO(2855,new BaseRewardVO(14029),3));
         this._moneyList[0].push(new MallMoneyVO(2854,new BaseRewardVO(14027),5));
         this._moneyList[0].push(new MallMoneyVO(2853,new BaseRewardVO(14025),3));
         this._moneyList[0].push(new MallMoneyVO(2843,new BaseRewardVO(13313),8));
         this._moneyList[0].push(new MallMoneyVO(2847,new BaseRewardVO(13317),8));
         this._moneyList[0].push(new MallMoneyVO(2851,new BaseRewardVO(13320),12));
         this._moneyList[0].push(new MallMoneyVO(2850,new BaseRewardVO(13319),9));
         this._moneyList[0].push(new MallMoneyVO(2844,new BaseRewardVO(13314),5));
         this._moneyList[0].push(new MallMoneyVO(2848,new BaseRewardVO(13318),8));
         this._moneyList[0].push(new MallMoneyVO(2846,new BaseRewardVO(13316),8));
         this._moneyList[0].push(new MallMoneyVO(2845,new BaseRewardVO(13315),5));
         this._moneyList[0].push(new MallMoneyVO(2826,new BaseRewardVO(14001),3));
         this._moneyList[0].push(new MallMoneyVO(2799,new BaseRewardVO(13303),8));
         this._moneyList[0].push(new MallMoneyVO(2852,new BaseRewardVO(13110),4));
         this._moneyList[0].push(new MallMoneyVO(2800,new BaseRewardVO(13104),2));
         this._moneyList[0].push(new MallMoneyVO(2801,new BaseRewardVO(13306),2));
         this._moneyList[0].push(new MallMoneyVO(2804,new BaseRewardVO(13307),2));
         this._moneyList[0].push(new MallMoneyVO(2802,new BaseRewardVO(13308),2));
         this._moneyList[0].push(new MallMoneyVO(2803,new BaseRewardVO(13309),2));
         this._moneyList[0].push(new MallMoneyVO(2807,new BaseRewardVO(13107),2));
         this._moneyList[0].push(new MallMoneyVO(2805,new BaseRewardVO(13108),2));
         this._moneyList[0].push(new MallMoneyVO(2806,new BaseRewardVO(10008),2));
         this._moneyList[1] = [];
         this._moneyList[1].push(new MallMoneyVO(3432,new BaseRewardVO(14171),1868));
         this._moneyList[1].push(new MallMoneyVO(3432,new BaseRewardVO(14172),1868));
         this._moneyList[1].push(new MallMoneyVO(3432,new BaseRewardVO(14173),1868));
         this._moneyList[1].push(new MallMoneyVO(3432,new BaseRewardVO(14174),1868));
         this._moneyList[1].push(new MallMoneyVO(3432,new BaseRewardVO(14175),1868));
         this._moneyList[1].push(new MallMoneyVO(3100,new BaseRewardVO(14078),945,4));
         this._moneyList[1].push(new MallMoneyVO(3101,new BaseRewardVO(14079),945,4));
         this._moneyList[1].push(new MallMoneyVO(2960,new BaseRewardVO(14081),228,5));
         this._moneyList[1].push(new MallMoneyVO(2961,new BaseRewardVO(14082),390,5));
         this._moneyList[1].push(new MallMoneyVO(2962,new BaseRewardVO(14083),228,5));
         this._moneyList[1].push(new MallMoneyVO(2963,new BaseRewardVO(14084),390,5));
         this._moneyList[1].push(new MallMoneyVO(2964,new BaseRewardVO(14085),228,5));
         this._moneyList[1].push(new MallMoneyVO(2965,new BaseRewardVO(14086),390,5));
         this._moneyList[1].push(new MallMoneyVO(2969,new BaseRewardVO(14051),228,5));
         this._moneyList[1].push(new MallMoneyVO(2968,new BaseRewardVO(14052),390,5));
         this._moneyList[1].push(new MallMoneyVO(3564,new BaseRewardVO(14176),716,6));
         this._moneyList[1].push(new MallMoneyVO(3565,new BaseRewardVO(14169),733,6));
         this._moneyList[1].push(new MallMoneyVO(3218,new BaseRewardVO(14065),716,6));
         this._moneyList[1].push(new MallMoneyVO(3219,new BaseRewardVO(14066),716,6));
         this._moneyList[1].push(new MallMoneyVO(3220,new BaseRewardVO(14067),716,6));
         this._moneyList[1].push(new MallMoneyVO(3221,new BaseRewardVO(14068),716,6));
         this._moneyList[1].push(new MallMoneyVO(3222,new BaseRewardVO(14165),733,6));
         this._moneyList[1].push(new MallMoneyVO(3223,new BaseRewardVO(14166),733,6));
         this._moneyList[1].push(new MallMoneyVO(3224,new BaseRewardVO(14167),733,6));
         this._moneyList[1].push(new MallMoneyVO(3225,new BaseRewardVO(14168),733,6));
         this._moneyList[1].push(new MallMoneyVO(3187,new EquipRewardVO(34121,0),385));
         this._moneyList[1].push(new MallMoneyVO(3188,new EquipRewardVO(34122,0),385));
         this._moneyList[1].push(new MallMoneyVO(3189,new EquipRewardVO(34123,0),385));
         this._moneyList[1].push(new MallMoneyVO(3190,new EquipRewardVO(34124,0),385));
         this._moneyList[1].push(new MallMoneyVO(3570,new EquipRewardVO(34125,0),385));
         this._moneyList[1].push(new MallMoneyVO(3191,new EquipRewardVO(35021,0),245));
         this._moneyList[1].push(new MallMoneyVO(3192,new EquipRewardVO(35022,0),245));
         this._moneyList[1].push(new MallMoneyVO(3193,new EquipRewardVO(35023,0),245));
         this._moneyList[1].push(new MallMoneyVO(3194,new EquipRewardVO(35024,0),245));
         this._moneyList[1].push(new MallMoneyVO(3571,new EquipRewardVO(35025,0),245));
         this._moneyList[1].push(new MallMoneyVO(3085,new EquipRewardVO(34111,0),350));
         this._moneyList[1].push(new MallMoneyVO(3089,new EquipRewardVO(35011,0),250));
         this._moneyList[1].push(new MallMoneyVO(3086,new EquipRewardVO(34112,0),350));
         this._moneyList[1].push(new MallMoneyVO(3090,new EquipRewardVO(35012,0),250));
         this._moneyList[1].push(new MallMoneyVO(3087,new EquipRewardVO(34113,0),350));
         this._moneyList[1].push(new MallMoneyVO(3091,new EquipRewardVO(35013,0),250));
         this._moneyList[1].push(new MallMoneyVO(3088,new EquipRewardVO(34114,0),350));
         this._moneyList[1].push(new MallMoneyVO(3092,new EquipRewardVO(35014,0),250));
         this._moneyList[1].push(new MallMoneyVO(3568,new EquipRewardVO(34115,0),350));
         this._moneyList[1].push(new MallMoneyVO(3569,new EquipRewardVO(35015,0),250));
         this._moneyList[1].push(new MallMoneyVO(2827,new EquipRewardVO(34101,0),150));
         this._moneyList[1].push(new MallMoneyVO(2809,new EquipRewardVO(35001,0),100));
         this._moneyList[1].push(new MallMoneyVO(2828,new EquipRewardVO(34102,0),150));
         this._moneyList[1].push(new MallMoneyVO(2810,new EquipRewardVO(35002,0),100));
         this._moneyList[1].push(new MallMoneyVO(2829,new EquipRewardVO(34103,0),150));
         this._moneyList[1].push(new MallMoneyVO(2811,new EquipRewardVO(35003,0),100));
         this._moneyList[1].push(new MallMoneyVO(2928,new EquipRewardVO(34104,0),150));
         this._moneyList[1].push(new MallMoneyVO(2929,new EquipRewardVO(35004,0),100));
         this._moneyList[1].push(new MallMoneyVO(3566,new EquipRewardVO(34105,0),150));
         this._moneyList[1].push(new MallMoneyVO(3567,new EquipRewardVO(35005,0),100));
         this._ticketList = [];
         this._ticketList[0] = [];
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(16119),75));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(16120),75));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(16017),75));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(14102),20));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(14103),40));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(14104),20));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(14105),40));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(14106),25));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(16753),25));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(16754),25));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(16012),50));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(16013),50));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(16014),50));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(14020),25));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(14021),100));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(14010),5));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(16009),40));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(13102),10));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(13103),10));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(15001),5));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(15002),5));
         this._ticketList[0].push(new MallTicketVO(0,new BaseRewardVO(15003),5));
         this.cheatList = [16017,16119,16120,14120,14121,14020,14021,14010,16009,13102,13103,15001,15002,15003,16012,16013,16014,16753,16754,16752,16757,14001,14002,14020,14009,14102,14103,14104,14105,14106,14120,14121,16017];
      }
      
      public function checkCheat(param1:int) : void
      {
         if(this.cheatList.indexOf(param1) != -1)
         {
            return;
         }
         GameProxy.instance().isCheat.v = 1300;
      }
      
      public function findMoneyMall(param1:int) : Array
      {
         return this._moneyList[param1];
      }
      
      public function findTicketMall(param1:int) : Array
      {
         return this._ticketList[param1];
      }
      
      public function findMoneyGood(param1:int) : MallMoneyVO
      {
         var _loc3_:MallMoneyVO = null;
         var _loc2_:Array = this._moneyList[0].concat(this._moneyList[1],this._moneyList[2]);
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.mallGood.id.v == param1)
            {
               return _loc3_;
            }
         }
         return null;
      }
   }
}

