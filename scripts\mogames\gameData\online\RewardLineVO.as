package mogames.gameData.online
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class RewardLineVO
   {
      public var index:Sint;
      
      public var minute:Snum;
      
      public var rewardVO:BaseRewardVO;
      
      public function RewardLineVO(param1:int, param2:Number, param3:BaseRewardVO)
      {
         super();
         this.index = new Sint(param1);
         this.minute = new Snum(param2);
         this.rewardVO = param3;
      }
      
      public function get constVO() : ConstGoodVO
      {
         return this.rewardVO.constGood;
      }
      
      public function get num() : int
      {
         return this.rewardVO.num.v;
      }
      
      public function get sec() : int
      {
         return this.minute.v * 60;
      }
   }
}

