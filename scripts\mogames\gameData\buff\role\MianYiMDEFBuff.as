package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class MianYiMDEFBuff extends BaseBuff
   {
      public function MianYiMDEFBuff()
      {
         super(1010);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         if(_role.isReady)
         {
            _role.magicWudi = true;
            EffectManager.instance().addHeadWord("法术免疫！",_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override protected function onEnd() : void
      {
         if(_role)
         {
            _role.magicWudi = false;
         }
         destroy();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         _skin.setupSequence(AssetManager.findAnimation("EFFECT_DEBUFF_MDEF_CLIP0"),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

