package file
{
   import mogames.gameData.drop.RewardHandler;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.forge.NeedVO;
   import mogames.gameUnion.UnionProxy;
   import mogames.gameUnion.data.UnionGoldTaskVO;
   import mogames.gameUnion.data.UnionHeadVO;
   import mogames.gameUnion.data.UnionMoneyTaskVO;
   
   public class UnionConfig
   {
      private static var _instance:UnionConfig;
      
      public static const UNION_CREATE_NEED:int = 200;
      
      public static const UNION_LEVEL_EXP:Array = [0,6000,18000,38000,68000,110000,152000,206000,"---"];
      
      public static const UNION_MAX_MEMBER:Array = [0,10,15,20,25,30,35,40,45];
      
      private var _heads:Array;
      
      private var _tasks:Array;
      
      public function UnionConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : UnionConfig
      {
         if(!_instance)
         {
            _instance = new UnionConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._heads = [];
         this._heads[this._heads.length] = new UnionHeadVO("入世",150,0,[new BaseRewardVO(10000,20000)]);
         this._heads[this._heads.length] = new UnionHeadVO("修行",400,0,[new BaseRewardVO(10000,30000),new BaseRewardVO(14009,1)]);
         this._heads[this._heads.length] = new UnionHeadVO("飞升",800,10,[new BaseRewardVO(10000,40000),new BaseRewardVO(14009,1),new BaseRewardVO(13140,1)]);
         this._heads[this._heads.length] = new UnionHeadVO("大道",1600,30,[new BaseRewardVO(10000,50000),new BaseRewardVO(16721,1),new BaseRewardVO(13140,1)]);
         this._heads[this._heads.length] = new UnionHeadVO("道灵",3200,60,[new BaseRewardVO(16711,2),new BaseRewardVO(16721,2),new BaseRewardVO(13140,1)]);
         this._tasks = [];
         this._tasks[this._tasks.length] = new UnionMoneyTaskVO(0,20,10,new NeedVO(10033,10));
         this._tasks[this._tasks.length] = new UnionGoldTaskVO(87,10,10,new NeedVO(10000,20000));
      }
      
      private function defaultTask() : void
      {
         var _loc1_:UnionGoldTaskVO = null;
         for each(_loc1_ in this._tasks)
         {
            _loc1_.curNum = _loc1_.taskNum;
         }
      }
      
      public function initTask(param1:Object) : void
      {
         var _loc2_:UnionGoldTaskVO = null;
         var _loc3_:Object = null;
         this.defaultTask();
         for each(_loc3_ in param1.tasklist)
         {
            _loc2_ = this.findTasks(_loc3_.taskName);
            if(_loc2_)
            {
               _loc2_.curNum = _loc2_.taskNum - int(_loc3_.value) / _loc2_.numGX;
            }
         }
         _loc2_ = this._tasks[0];
         if(param1.exchange)
         {
            _loc2_.curNum = _loc2_.taskNum - int(param1.exchange) / _loc2_.numGX;
         }
      }
      
      public function findTasks(param1:int) : UnionGoldTaskVO
      {
         var _loc2_:UnionGoldTaskVO = null;
         for each(_loc2_ in this._tasks)
         {
            if(_loc2_.taskID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findHeads(param1:int) : UnionHeadVO
      {
         if(param1 == 0)
         {
            return null;
         }
         return this._heads[param1 - 1];
      }
      
      public function get tasks() : Array
      {
         return this._tasks;
      }
      
      public function get headName() : String
      {
         var _loc1_:UnionHeadVO = this.findHeads(UnionProxy.instance().myHead.v);
         if(!_loc1_)
         {
            return "无";
         }
         return _loc1_.name;
      }
      
      public function get headRewardInfor() : String
      {
         var _loc2_:UnionHeadVO = null;
         var _loc1_:Array = ["拥有头衔后可领取每日福利<br>"];
         for each(_loc2_ in this._heads)
         {
            _loc1_[_loc1_.length] = _loc2_.name + "：<br>" + RewardHandler.instance().parseRewardName(RewardHandler.instance().newGiftReward(_loc2_.rewards));
         }
         return _loc1_.join("<br><br>");
      }
   }
}

