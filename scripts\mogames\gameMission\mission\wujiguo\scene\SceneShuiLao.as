package mogames.gameMission.mission.wujiguo.scene
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.wujiguo.WuJiGuoMission;
   import mogames.gamePKG.PKGManager;
   import mogames.gameRole.enemy.BOSSBangJing;
   import mogames.gameRole.enemy.BOSSChanChu;
   import mogames.gameRole.enemy.BOSSDengYuGuai;
   import mogames.gameRole.enemy.BOSSJiJuXie;
   import mogames.gameRole.hero.BaseHero;
   
   public class SceneShuiLao extends BossScene
   {
      public function SceneShuiLao(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(270,342,446,110),new Point(480,414));
         EffectManager.instance().playBGM("BGM_BATTLE0");
         sceneType = 1;
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.refreshHero();
         this.addBOSS();
      }
      
      override protected function addBOSS() : void
      {
         var _loc1_:WuJiGuoMission = _mission as WuJiGuoMission;
         switch(_loc1_.shuilaoVO.bossID.v)
         {
            case 4001:
               _boss = new BOSSChanChu();
               _boss.initData(40011,40011,41);
               break;
            case 4002:
               _boss = new BOSSDengYuGuai();
               _boss.initData(40021,40021,42);
               break;
            case 4003:
               _boss = new BOSSJiJuXie();
               _boss.initData(40031,40031,43);
               break;
            case 4004:
               _boss = new BOSSBangJing();
               _boss.initData(40041,40041,44);
         }
         _boss.x = 780;
         _boss.y = 80;
         _boss.target = _mission.onePlayer;
         add(_boss);
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,780,80);
         startBossBattle();
      }
      
      override protected function listenLeave() : void
      {
         this.refreshHero();
         (_mission as WuJiGuoMission).leaveShuiLao();
         PKGManager.saveData();
      }
      
      private function refreshHero() : void
      {
         var _loc2_:HeroGameVO = null;
         var _loc3_:BaseHero = null;
         var _loc1_:Vector.<BaseHero> = _mission.heros;
         for each(_loc3_ in _loc1_)
         {
            _loc3_.handlerRevive();
            _loc2_ = _loc3_.roleVO as HeroGameVO;
            _loc2_.refresh();
         }
      }
   }
}

