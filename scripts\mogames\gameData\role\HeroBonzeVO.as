package mogames.gameData.role
{
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.heroSkill.shaseng.SkillLSKVO;
   
   public class HeroBonzeVO extends HeroGameVO
   {
      public function HeroBonzeVO()
      {
         allSkills = [];
         allSkills.push(new HurtSkillVO(1016,this,0));
         allSkills.push(new HurtSkillVO(1017,this,1));
         allSkills.push(new SkillLSKVO(1018,this,2));
         allSkills.push(new HurtSkillVO(1019,this,3));
         allSkills.push(new HurtSkillVO(1020,this,4));
         updateKeySkills();
         super(1004);
      }
   }
}

