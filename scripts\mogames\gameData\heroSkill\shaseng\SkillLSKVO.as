package mogames.gameData.heroSkill.shaseng
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.heroSkill.BaseSkillVO;
   import mogames.gameData.role.HeroGameVO;
   
   public class SkillLSKVO extends BaseSkillVO
   {
      public var per:Sint = new Sint();
      
      public function SkillLSKVO(param1:int, param2:HeroGameVO, param3:int = 0)
      {
         super(param1,param2,param3);
      }
      
      override public function setLevel(param1:int, param2:Boolean = false) : void
      {
         super.setLevel(param1);
         this.per.v = countVO.argDic.arg0.v + countVO.argDic.arg1.v * level.v;
      }
      
      public function get time() : Number
      {
         return countVO.argDic.arg2.v;
      }
   }
}

