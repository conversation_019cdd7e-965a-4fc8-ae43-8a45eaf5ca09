package file
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.heroSkill.SkillCountVO;
   import mogames.gameData.heroSkill.constvo.ConstCombineVO;
   import mogames.gameData.heroSkill.constvo.ConstSkillFive;
   import mogames.gameData.heroSkill.constvo.ConstSkillFour;
   import mogames.gameData.heroSkill.constvo.ConstSkillOne;
   import mogames.gameData.heroSkill.constvo.ConstSkillThree;
   import mogames.gameData.heroSkill.constvo.ConstSkillTwo;
   import mogames.gameData.heroSkill.constvo.ConstSkillVO;
   
   public class SkillConfig
   {
      private static var _instance:SkillConfig;
      
      private var _skillVec:Vector.<ConstSkillVO>;
      
      private var _combines:Vector.<ConstCombineVO>;
      
      private var _countVec:Vector.<SkillCountVO>;
      
      public function SkillConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : SkillConfig
      {
         if(!_instance)
         {
            _instance = new SkillConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._skillVec = new Vector.<ConstSkillVO>();
         this._skillVec.push(new ConstSkillOne(1001,"破天猛击","ICON_PTMJ","skillone","悟空将金箍棒砸向地面并造成魔法伤害"));
         this._skillVec.push(new ConstSkillTwo(1002,"千钧神力","ICON_QJSL","skilltwo","瞬间提升悟空的物攻"));
         this._skillVec.push(new ConstSkillThree(1003,"火眼金睛","ICON_HYJJ","skillthree","两眼射出金光，对直线上敌人造成魔法伤害（需持续按键）"));
         this._skillVec.push(new ConstSkillFour(1004,"大闹天宫","ICON_DNTG","skillfour","转动金箍棒，对周围的敌人造成持续魔法伤害"));
         this._skillVec.push(new ConstSkillFive(1005,"群魔乱舞","ICON_QMLW","skillfive","孙悟空幻化出残影对前方敌人造成多次魔法伤害"));
         this._skillVec.push(new ConstSkillOne(1006,"猛龙出海","ICON_MENG_LONG_CHU_HAI","skillone","小白龙拿起剑向前穿刺，对沿途敌人造成魔法伤害"));
         this._skillVec.push(new ConstSkillTwo(1007,"白龙波","ICON_BAI_LONG_BO","skilltwo","向前发出3道白龙波，对敌人造成魔法伤害并击退"));
         this._skillVec.push(new ConstSkillThree(1008,"龙魂决","ICON_LONG_HUN_JUE","skillthree","召唤出巨大冰剑砸向地面，对敌人造成巨大魔法伤害"));
         this._skillVec.push(new ConstSkillFour(1009,"百龙霸","ICON_BAI_LONG_BA","skillfour","召唤出多条小冰龙，对前方敌人造成持续魔法伤害"));
         this._skillVec.push(new ConstSkillFive(1010,"真龙再现","ICON_ZHEN_LONG_ZAI_XIAN","skillfive","现出龙之真身，对前方敌人造成多次魔法伤害并冰冻"));
         this._skillVec.push(new ConstSkillOne(1011,"念气炮","ICON_NIAN_QI_PAO","skillone","挥出一道念气，对前方敌人造成魔法伤害"));
         this._skillVec.push(new ConstSkillTwo(1012,"冲锋陷阵","ICON_CHONG_FENG_XIAN_ZHEN","skilltwo","挺起肥肚撞向怪物，造成魔法伤害并弹飞，按左右改变方向"));
         this._skillVec.push(new ConstSkillThree(1013,"开饭时间","ICON_KAI_FAN_SHI_JIAN","skillthree","食用随身携带的包子，恢复部分生命值"));
         this._skillVec.push(new ConstSkillFour(1014,"倒打一耙","ICON_DAO_DA_YI_PA","skillfour","转身并用念气幻化出钉耙对身后怪物造成魔法伤害并晕眩"));
         this._skillVec.push(new ConstSkillFive(1015,"地煞功","ICON_DI_SHA_GONG","skillfive","现出地煞魔猪元魂，对前方怪物造成魔法伤害并击飞"));
         this._skillVec.push(new ConstSkillOne(1016,"怒霸爆","ICON_NU_BA_BAO","skillone","地面出现3道沙柱，对敌人造成魔法伤害并击退"));
         this._skillVec.push(new ConstSkillTwo(1017,"流沙束缚","ICON_LIU_SHA_SHU_FU","skilltwo","召唤出流沙使前方敌人浮空并造成魔法伤害"));
         this._skillVec.push(new ConstSkillThree(1018,"流沙铠","ICON_LIU_SHA_KAI","skillthree","用流沙悬浮在身体吸收形成一道铠甲，提高物理防御"));
         this._skillVec.push(new ConstSkillFour(1019,"降魔破","ICON_XIANG_MO_PO","skillfour","跳起砸向地面，对敌人造成魔法伤害并减速2秒"));
         this._skillVec.push(new ConstSkillFive(1020,"死亡之手","ICON_SI_WANG_ZHI_SHOU","skillfive","召唤土灵巨手抓取敌人，对其造成魔法伤害并石化。"));
         this._skillVec.push(new ConstSkillOne(1021,"龙炎","ICON_LONG_YAN","skillone","召唤出一条火龙，对直线上的敌人造成魔法伤害。"));
         this._skillVec.push(new ConstSkillTwo(1022,"地炎","ICON_DI_YAN","skilltwo","在前方地面先后放出3个火柱，对敌人造成魔法伤害并击退。"));
         this._skillVec.push(new ConstSkillThree(1023,"旋炎破","ICON_XUAN_YAN_PO","skillthree","生成高速旋转的火环， 使周围怪物受到魔法伤害并击飞。"));
         this._skillVec.push(new ConstSkillFour(1024,"火陨石","ICON_HUO_YUN_SHI","skillfour","天上掉落陨石，对方四周怪物造成魔法伤害并击飞。"));
         this._skillVec.push(new ConstSkillFive(1025,"凤舞九天","ICON_FENG_WU_JIU_TIAN","skillfive","召唤出火球砸向地面，对四周敌人造成魔法伤害并晕眩。"));
         this._countVec = new Vector.<SkillCountVO>();
         this._countVec.push(new SkillCountVO(1001,8,{
            "arg":new Snum(1.23),
            "hurt":new Sint(170)
         }));
         this._countVec.push(new SkillCountVO(1002,12,{
            "arg0":new Sint(12),
            "arg1":new Sint(1),
            "arg2":new Sint(10)
         }));
         this._countVec.push(new SkillCountVO(1003,4,{
            "arg":new Snum(1.25),
            "hurt":new Sint(60)
         }));
         this._countVec.push(new SkillCountVO(1004,18,{
            "arg":new Snum(1.25),
            "hurt":new Sint(49)
         }));
         this._countVec.push(new SkillCountVO(1005,27,{
            "arg":new Snum(1.23),
            "hurt":new Sint(92)
         }));
         this._countVec.push(new SkillCountVO(1006,8,{
            "arg":new Snum(1.01),
            "hurt":new Sint(170)
         }));
         this._countVec.push(new SkillCountVO(1007,12,{
            "arg":new Snum(1.05),
            "hurt":new Sint(70)
         }));
         this._countVec.push(new SkillCountVO(1008,14,{
            "arg":new Snum(0.9),
            "hurt":new Sint(270)
         }));
         this._countVec.push(new SkillCountVO(1009,18,{
            "arg":new Snum(1.15),
            "hurt":new Sint(35),
            "arg2":new Snum(3.3),
            "arg3":new Snum(0.2),
            "interval":new Snum(0.3)
         }));
         this._countVec.push(new SkillCountVO(1010,27,{
            "arg":new Snum(1.05),
            "hurt":new Sint(138)
         }));
         this._countVec.push(new SkillCountVO(1011,8,{
            "arg":new Snum(1.21),
            "hurt":new Sint(170)
         }));
         this._countVec.push(new SkillCountVO(1012,12,{
            "arg":new Snum(1.21),
            "hurt":new Sint(59)
         }));
         this._countVec.push(new SkillCountVO(1013,14,{
            "arg0":new Sint(10),
            "arg1":new Sint(100)
         }));
         this._countVec.push(new SkillCountVO(1014,18,{
            "arg":new Snum(1.01),
            "hurt":new Sint(368),
            "arg0":new Snum(0.5),
            "arg1":new Snum(0.15)
         }));
         this._countVec.push(new SkillCountVO(1015,27,{
            "arg":new Snum(1.21),
            "hurt":new Sint(107)
         }));
         this._countVec.push(new SkillCountVO(1016,8,{
            "arg":new Snum(1.21),
            "hurt":new Sint(90)
         }));
         this._countVec.push(new SkillCountVO(1017,12,{
            "arg":new Snum(1.21),
            "hurt":new Sint(210)
         }));
         this._countVec.push(new SkillCountVO(1018,14,{
            "arg0":new Sint(1),
            "arg1":new Sint(2),
            "arg2":new Sint(10)
         }));
         this._countVec.push(new SkillCountVO(1019,18,{
            "arg":new Snum(1.05),
            "hurt":new Sint(360)
         }));
         this._countVec.push(new SkillCountVO(1020,27,{
            "arg":new Snum(1.21),
            "hurt":new Sint(120)
         }));
         this._countVec.push(new SkillCountVO(1021,8,{
            "arg":new Snum(1.23),
            "hurt":new Sint(140)
         }));
         this._countVec.push(new SkillCountVO(1022,12,{
            "arg":new Snum(1.23),
            "hurt":new Sint(210)
         }));
         this._countVec.push(new SkillCountVO(1023,14,{
            "arg":new Snum(1.23),
            "hurt":new Sint(180)
         }));
         this._countVec.push(new SkillCountVO(1024,18,{
            "arg":new Snum(1.23),
            "hurt":new Sint(300)
         }));
         this._countVec.push(new SkillCountVO(1025,27,{
            "arg":new Snum(1.13),
            "hurt":new Sint(350)
         }));
         this._combines = new Vector.<ConstCombineVO>();
         this._combines.push(new ConstCombineVO(10001,1,2,5,"奥义！金光炸现","ICON_COMBINE_JGZX","EFFECT_COMBINE_JGZX_CLIP","组合技，对敌人造成伤害并提高自身魔防。"));
      }
      
      public function findConstSkill(param1:int) : ConstSkillVO
      {
         var _loc2_:ConstSkillVO = null;
         for each(_loc2_ in this._skillVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findSkillCount(param1:int) : SkillCountVO
      {
         var _loc2_:SkillCountVO = null;
         for each(_loc2_ in this._countVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findConstCombine(param1:int) : ConstCombineVO
      {
         var _loc2_:ConstCombineVO = null;
         for each(_loc2_ in this._combines)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

