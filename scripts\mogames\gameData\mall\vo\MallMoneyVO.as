package mogames.gameData.mall.vo
{
   import file.GoodConfig;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.LackVO;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gamePKG.GameProxy;
   import utils.TxtUtil;
   
   public class MallMoneyVO extends BaseMallVO
   {
      public function MallMoneyVO(param1:int, param2:BaseRewardVO, param3:int, param4:int = 0)
      {
         super(param1,param2,param3,param4);
         priceType = 1;
      }
      
      override public function checkLack(param1:int = 1) : LackVO
      {
         return MasterProxy.instance().checkLack(price.v * param1,1);
      }
      
      override public function askStr(param1:int) : String
      {
         if(price.v < 0 || param1 < 0)
         {
            GameProxy.instance().isCheat.v = 1301;
         }
         return "花费" + TxtUtil.setColor("金锭X" + price.v * param1) + "购买" + param1 + "个" + TxtUtil.setColor(GoodConfig.instance().getGoodName(mallGood.id.v),"99ff00") + "？<br>（适度娱乐，理性消费）";
      }
   }
}

