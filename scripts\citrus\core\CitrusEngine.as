package citrus.core
{
   import citrus.input.Input;
   import citrus.sounds.SoundManager;
   import citrus.utils.AGameData;
   import citrus.utils.LevelManager;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.StageAlign;
   import flash.display.StageDisplayState;
   import flash.display.StageScaleMode;
   import flash.events.Event;
   import flash.events.FullScreenEvent;
   import flash.geom.Matrix;
   import mogames.gameUI.title.PauseModule;
   import org.osflash.signals.Signal;
   
   public class CitrusEngine extends MovieClip
   {
      private static var _instance:CitrusEngine;
      
      public static const VERSION:String = "3.1.11";
      
      public var DEBUG:Boolean = false;
      
      public var onPlayingChange:Signal;
      
      public var onStageResize:Signal;
      
      public var gameData:AGameData;
      
      public var levelManager:LevelManager;
      
      public const transformMatrix:Matrix = new Matrix();
      
      protected var _state:IState;
      
      protected var _newState:IState;
      
      protected var _stateTransitionning:IState;
      
      protected var _futureState:IState;
      
      protected var _stateDisplayIndex:uint = 0;
      
      protected var _playing:Boolean = true;
      
      protected var _input:Input;
      
      protected var _fullScreen:Boolean = false;
      
      protected var _screenWidth:int = 0;
      
      protected var _screenHeight:int = 0;
      
      private var _startTime:Number;
      
      private var _gameTime:Number;
      
      private var _nowTime:Number;
      
      protected var _timeDelta:Number;
      
      private var _sound:SoundManager;
      
      public var isPauseMenu:Boolean;
      
      private var _bitmap:Bitmap;
      
      public function CitrusEngine()
      {
         super();
         _instance = this;
         this.onPlayingChange = new Signal(Boolean);
         this.onStageResize = new Signal(int,int);
         this.onPlayingChange.add(this.handlePlayingChange);
         this._gameTime = this._startTime = new Date().time;
         this._input = new Input();
         this._sound = SoundManager.getInstance();
         addEventListener(Event.ENTER_FRAME,this.handleEnterFrame);
         addEventListener(Event.ADDED_TO_STAGE,this.handleAddedToStage);
      }
      
      public static function getInstance() : CitrusEngine
      {
         if(!_instance)
         {
            _instance = new CitrusEngine();
         }
         return _instance;
      }
      
      public function destroy() : void
      {
         this.onPlayingChange.removeAll();
         this.onStageResize.removeAll();
         stage.removeEventListener(Event.ACTIVATE,this.handleStageActivated);
         stage.removeEventListener(Event.DEACTIVATE,this.handleStageDeactivated);
         stage.removeEventListener(FullScreenEvent.FULL_SCREEN,this.handleStageFullscreen);
         stage.removeEventListener(Event.RESIZE,this.handleStageResize);
         removeEventListener(Event.ENTER_FRAME,this.handleEnterFrame);
         if(this._state)
         {
            this._state.destroy();
            if(this._state is State)
            {
               removeChild(this._state as State);
            }
         }
         this._input.destroy();
         this._sound.destroy();
      }
      
      public function destroyState() : void
      {
         if(this._state)
         {
            this._state.destroy();
            if(this._state is State)
            {
               removeChild(this._state as State);
            }
         }
         this._state = null;
         this._newState = null;
         this._futureState = null;
      }
      
      public function get state() : IState
      {
         if(this._futureState)
         {
            return this._futureState;
         }
         if(this._newState)
         {
            return this._newState;
         }
         return this._state;
      }
      
      public function set state(param1:IState) : void
      {
         this._newState = param1;
      }
      
      public function get futureState() : IState
      {
         return !!this._futureState ? this._futureState : this._stateTransitionning;
      }
      
      public function set futureState(param1:IState) : void
      {
         this._stateTransitionning = param1;
      }
      
      public function get playing() : Boolean
      {
         return this._playing;
      }
      
      public function set playing(param1:Boolean) : void
      {
         if(param1 == this._playing)
         {
            return;
         }
         this._playing = param1;
         if(this._playing)
         {
            this._gameTime = new Date().time;
         }
         this.onPlayingChange.dispatch(this._playing);
      }
      
      public function get input() : Input
      {
         return this._input;
      }
      
      public function get sound() : SoundManager
      {
         return this._sound;
      }
      
      protected function handleAddedToStage(param1:Event) : void
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.handleAddedToStage);
         stage.scaleMode = StageScaleMode.NO_SCALE;
         stage.align = StageAlign.TOP_LEFT;
         this._fullScreen = stage.displayState == StageDisplayState.FULL_SCREEN || stage.displayState == StageDisplayState.FULL_SCREEN_INTERACTIVE;
         this.resetScreenSize();
         this._input.initialize();
         this.initialize();
      }
      
      public function addStageListener() : void
      {
         stage.addEventListener(Event.DEACTIVATE,this.handleStageDeactivated);
         stage.addEventListener(Event.ACTIVATE,this.handleStageActivated);
      }
      
      public function removeStageListener() : void
      {
         stage.removeEventListener(Event.DEACTIVATE,this.handleStageDeactivated);
         stage.removeEventListener(Event.ACTIVATE,this.handleStageActivated);
      }
      
      public function initialize() : void
      {
      }
      
      protected function handleStageFullscreen(param1:FullScreenEvent) : void
      {
         this._fullScreen = param1.fullScreen;
      }
      
      protected function handleStageResize(param1:Event) : void
      {
         this.resetScreenSize();
         this.onStageResize.dispatch(this._screenWidth,this._screenHeight);
      }
      
      protected function resetScreenSize() : void
      {
         this._screenWidth = stage.stageWidth;
         this._screenHeight = stage.stageHeight;
      }
      
      protected function handlePlayingChange(param1:Boolean) : void
      {
         if(this.input)
         {
            this.input.resetActions();
         }
      }
      
      protected function handleEnterFrame(param1:Event) : void
      {
         if(Boolean(this._newState) && this._newState is State)
         {
            if(Boolean(this._state) && this._state is State)
            {
               this._state.destroy();
               removeChild(this._state as State);
            }
            this._state = this._newState;
            this._newState = null;
            if(this._futureState)
            {
               this._futureState = null;
            }
            else
            {
               addChildAt(this._state as State,this._stateDisplayIndex);
               this._state.initialize();
            }
         }
         if(Boolean(this._stateTransitionning) && this._stateTransitionning is State)
         {
            this._futureState = this._stateTransitionning;
            this._stateTransitionning = null;
            addChildAt(this._futureState as State,this._stateDisplayIndex);
            this._futureState.initialize();
         }
         if(Boolean(this._state) && this._playing)
         {
            this._nowTime = new Date().time;
            this._timeDelta = (this._nowTime - this._gameTime) * 0.001;
            this._gameTime = this._nowTime;
            this._state.update(this._timeDelta);
            if(this._futureState)
            {
               this._futureState.update(this._timeDelta);
            }
         }
         this._input.citrus_internal::update();
      }
      
      protected function handleStageDeactivated(param1:Event) : void
      {
         if(PauseModule.isActive)
         {
            return;
         }
         this.playing = false;
         PauseModule.instance().init();
      }
      
      protected function handleStageActivated(param1:Event) : void
      {
         PauseModule.clean();
         if(this.isPauseMenu)
         {
            return;
         }
         this.playing = true;
      }
      
      public function get fullScreen() : Boolean
      {
         return this._fullScreen;
      }
      
      public function set fullScreen(param1:Boolean) : void
      {
         if(param1 == this._fullScreen)
         {
            return;
         }
         if(param1)
         {
            stage.displayState = StageDisplayState.FULL_SCREEN_INTERACTIVE;
         }
         else
         {
            stage.displayState = StageDisplayState.NORMAL;
         }
         this.resetScreenSize();
      }
      
      public function get screenWidth() : int
      {
         return this._screenWidth;
      }
      
      public function get screenHeight() : int
      {
         return this._screenHeight;
      }
      
      public function resumeGame() : void
      {
         this.playing = true;
         this.isPauseMenu = false;
      }
      
      public function pauseGame() : void
      {
         this.playing = false;
         this.isPauseMenu = true;
      }
   }
}

