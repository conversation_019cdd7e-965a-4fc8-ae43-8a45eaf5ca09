package mogames.gameData.rank.vo
{
   import mogames.gameData.base.Sint;
   
   public class RankTotalBRVO
   {
      public var rank:Sint;
      
      public var nick:String;
      
      public var score:Sint;
      
      public var uid:String;
      
      public var saveIndex:int;
      
      public var brMonkey:int;
      
      public var brHorse:int;
      
      public var brPig:int;
      
      public var brBonze:int;
      
      public var brDrakan:int;
      
      public function RankTotalBRVO()
      {
         super();
         this.rank = new Sint();
         this.score = new Sint();
      }
      
      public function parseData(param1:Object) : void
      {
         this.uid = param1.uId;
         this.nick = param1.userName;
         this.score.v = param1.score;
         this.rank.v = param1.rank;
         this.saveIndex = param1.index;
         if(param1.extra is Array)
         {
            this.parseExtra(param1.extra);
         }
         else if(param1.extra is String)
         {
            this.parseExtra(String(param1.extra).split(","));
         }
      }
      
      private function parseExtra(param1:Array) : void
      {
         this.brMonkey = param1[0];
         this.brHorse = param1[1];
         this.brPig = param1[2];
         this.brBonze = param1[3];
         this.brDrakan = param1[4];
      }
      
      public function get sortIndex() : int
      {
         return this.rank.v;
      }
   }
}

