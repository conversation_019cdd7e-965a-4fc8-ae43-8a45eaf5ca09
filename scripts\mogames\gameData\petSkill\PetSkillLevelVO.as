package mogames.gameData.petSkill
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.heroSkill.SkillLevelVO;
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.role.HeroGameVO;
   import utils.TxtUtil;
   
   public class PetSkillLevelVO extends SkillLevelVO
   {
      public function PetSkillLevelVO(param1:int, param2:int, param3:int)
      {
         super(param1,param2,param3);
      }
      
      override public function parseNeedStr(param1:HeroGameVO) : String
      {
         var _loc2_:Array = [];
         var _loc3_:String = "";
         if(needLv.v > 0)
         {
            _loc3_ = param1.level.v < needLv.v ? "ff0000" : "99ff00";
            _loc2_.push(TxtUtil.setColor("宠物达到" + needLv.v + "级",_loc3_));
         }
         _loc3_ = MasterProxy.instance().gameGold.v < needGold.v ? "ff0000" : "99ff00";
         _loc2_.push(TxtUtil.setColor("铜钱X" + needGold.v,_loc3_));
         _loc3_ = (param1 as PetGameVO).skillPoint.v <= 0 ? "ff0000" : "99ff00";
         _loc2_.push(TxtUtil.setColor("技能点X1",_loc3_));
         return _loc2_.join("，");
      }
      
      override public function checkLearn(param1:HeroGameVO) : Boolean
      {
         return param1.level.v >= needLv.v && MasterProxy.instance().gameGold.v >= needGold.v && (param1 as PetGameVO).skillPoint.v > 0;
      }
   }
}

