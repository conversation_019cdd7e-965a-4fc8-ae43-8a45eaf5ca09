package mogames.gameData.mission
{
   import mogames.gameData.base.Sint;
   
   public class BaseSceneVO
   {
      public var id:Sint;
      
      public var resName:String;
      
      public var bgName:String;
      
      public var width:int;
      
      public var height:int;
      
      public function BaseSceneVO(param1:int, param2:String, param3:String, param4:int, param5:int)
      {
         super();
         this.id = new Sint(param1);
         this.resName = param2;
         this.bgName = param3;
         this.width = param4;
         this.height = param5;
      }
   }
}

