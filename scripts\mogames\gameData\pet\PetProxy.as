package mogames.gameData.pet
{
   import file.PetSkillConfig;
   import mogames.AssetManager;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.pet.vo.PetAssetVO;
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetSkillVO;
   import mogames.gamePKG.PKGManager;
   import mogames.gameRole.PetFactory;
   
   public class PetProxy
   {
      private static var _instance:PetProxy;
      
      public static const MAX_PET:Sint = new Sint(5);
      
      public var petList:Array;
      
      public var handler:NewPetHandler;
      
      public function PetProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.handler = new NewPetHandler();
      }
      
      public static function instance() : PetProxy
      {
         if(!_instance)
         {
            _instance = new PetProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.petList = [];
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc4_:* = null;
         var _loc5_:PetGameVO = null;
         this.startNew();
         if(!param1 || param1 == "")
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         var _loc3_:Array = [];
         for each(_loc4_ in _loc2_)
         {
            _loc3_ = _loc4_.split("H");
            _loc5_ = PetFactory.instance().newPetVO(int(_loc3_[0]));
            if(_loc5_)
            {
               _loc5_.parseLoadData(_loc4_);
            }
            this.addNewPet(_loc5_);
         }
         this.collectLoadURL();
      }
      
      public function get saveData() : String
      {
         var _loc2_:PetGameVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.petList)
         {
            _loc1_.push(_loc2_.collectSaveData());
         }
         return _loc1_.join("T");
      }
      
      public function initBattleData() : void
      {
         var _loc1_:PetGameVO = null;
         for each(_loc1_ in this.petList)
         {
            _loc1_.initBattleData();
         }
      }
      
      public function cleanBattleData() : void
      {
         var _loc1_:PetGameVO = null;
         for each(_loc1_ in this.petList)
         {
            _loc1_.cleanBattleData();
         }
      }
      
      public function addNewPet(param1:PetGameVO, param2:Boolean = false) : void
      {
         if(this.isFullPet)
         {
            return;
         }
         this.petList.push(param1);
         if(param2)
         {
            PKGManager.saveData();
         }
      }
      
      public function releasePet(param1:PetGameVO) : void
      {
         var _loc2_:int = int(this.petList.indexOf(param1));
         if(_loc2_ != -1)
         {
            this.petList.splice(_loc2_,1);
         }
      }
      
      public function randomActive(param1:PetGameVO) : PetSkillVO
      {
         var _loc3_:int = 0;
         var _loc4_:PetSkillVO = null;
         var _loc5_:PetSkillVO = null;
         var _loc2_:Array = param1.allSkills.slice();
         for each(_loc4_ in param1.activeSkills)
         {
            _loc3_ = int(_loc2_.indexOf(_loc4_));
            if(_loc3_ != -1)
            {
               _loc2_.splice(_loc3_,1);
            }
         }
         _loc5_ = _loc2_[int(Math.random() * _loc2_.length)];
         _loc5_.curQuality.v = this.handler.countSkillQuality(param1);
         return _loc5_;
      }
      
      public function randomPassive(param1:PetGameVO) : PetSkillVO
      {
         var _loc3_:PetSkillVO = null;
         var _loc4_:PetSkillVO = null;
         var _loc2_:Array = PetSkillConfig.instance().newPassiveSkills(param1);
         for each(_loc3_ in param1.passiveSkills)
         {
            this.delMulitPassive(_loc3_.id.v,_loc2_);
         }
         _loc4_ = _loc2_[int(Math.random() * _loc2_.length)];
         _loc4_.curQuality.v = this.handler.countSkillQuality(param1);
         return _loc4_;
      }
      
      public function unlockActive(param1:int, param2:PetGameVO) : PetSkillVO
      {
         var _loc3_:PetSkillVO = param2.activeSkills[param1];
         if(_loc3_ != null)
         {
            _loc3_.setLevel(1);
         }
         var _loc4_:PetSkillVO = this.randomActive(param2);
         param2.activeSkills[param1] = _loc4_;
         return _loc4_;
      }
      
      public function unlockPassive(param1:int, param2:PetGameVO) : PetSkillVO
      {
         var _loc3_:PetSkillVO = this.randomPassive(param2);
         param2.passiveSkills[param1] = _loc3_;
         return _loc3_;
      }
      
      private function delMulitPassive(param1:int, param2:Array) : void
      {
         var _loc3_:int = int(param2.length);
         var _loc4_:int = 0;
         while(_loc4_ < _loc3_)
         {
            if(param2[_loc4_].id.v == param1)
            {
               param2.splice(_loc4_,1);
               return;
            }
            _loc4_++;
         }
      }
      
      public function get maxPet() : int
      {
         return MAX_PET.v + MasterProxy.instance().gameVIP.v * 2;
      }
      
      public function get isFullPet() : Boolean
      {
         return this.petList.length >= this.maxPet;
      }
      
      public function get hasPet() : Boolean
      {
         return this.petList.length > 0;
      }
      
      public function get leftPet() : int
      {
         return this.maxPet - this.petList.length;
      }
      
      public function findPets(param1:int) : Array
      {
         var _loc3_:PetGameVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in this.petList)
         {
            if(_loc3_.id.v == param1)
            {
               _loc2_.push(_loc3_);
            }
         }
         return _loc2_;
      }
      
      public function battlePet(param1:int) : PetGameVO
      {
         var _loc2_:PetGameVO = null;
         for each(_loc2_ in this.petList)
         {
            if(_loc2_.ownerID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function activeNum(param1:PetGameVO) : int
      {
         var _loc3_:PetSkillVO = null;
         var _loc2_:int = 0;
         for each(_loc3_ in param1.activeSkills)
         {
            if(_loc3_)
            {
               _loc2_++;
            }
         }
         return _loc2_;
      }
      
      public function passiveNum(param1:PetGameVO) : int
      {
         var _loc3_:PetSkillVO = null;
         var _loc2_:int = 0;
         for each(_loc3_ in param1.passiveSkills)
         {
            if(_loc3_)
            {
               _loc2_++;
            }
         }
         return _loc2_;
      }
      
      public function get numStatus() : String
      {
         return "（" + this.petList.length + "/" + this.maxPet + "）";
      }
      
      public function get petURLs() : Array
      {
         var _loc2_:PetAssetVO = null;
         var _loc3_:PetGameVO = null;
         var _loc1_:Array = [];
         for each(_loc3_ in this.petList)
         {
            _loc2_ = _loc3_.assetVO as PetAssetVO;
            if(AssetManager.getRes(_loc2_.bodySkin + "0") == null)
            {
               this.addURL(_loc2_.urls,_loc1_);
            }
         }
         return _loc1_;
      }
      
      private function addURL(param1:Array, param2:Array) : void
      {
         var _loc3_:* = null;
         for each(_loc3_ in param1)
         {
            if(param2.indexOf(_loc3_) == -1)
            {
               param2.push(_loc3_);
            }
         }
      }
      
      private function collectLoadURL() : void
      {
         var _loc1_:PetAssetVO = null;
         var _loc2_:Array = null;
         var _loc3_:PetGameVO = null;
         var _loc4_:* = null;
         for each(_loc3_ in this.petList)
         {
            _loc1_ = _loc3_.assetVO as PetAssetVO;
            _loc2_ = _loc1_.urls;
            for each(_loc4_ in _loc2_)
            {
               PKGManager.addURL(_loc4_);
            }
         }
      }
   }
}

