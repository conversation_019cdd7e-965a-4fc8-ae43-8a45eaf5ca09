package mogames.gameData.activity.zhuanpan
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.vo.BaseRewardVO;
   
   public class BetVO
   {
      private var _id:Sint;
      
      private var _flag:Sint;
      
      private var _rewardVO:BaseRewardVO;
      
      public function BetVO(param1:int, param2:BaseRewardVO)
      {
         super();
         this._id = new Sint(param1);
         this._rewardVO = param2;
         this._flag = new Sint();
      }
      
      public function setGet() : void
      {
         this._flag.v = 1;
      }
      
      public function get isGet() : Boolean
      {
         return this._flag.v == 1;
      }
      
      public function get rewardVO() : BaseRewardVO
      {
         return this._rewardVO;
      }
      
      public function get index() : int
      {
         return int(String(this.id).charAt(0));
      }
      
      public function get position() : int
      {
         return int(String(this.id).charAt(2));
      }
      
      public function get id() : int
      {
         return this._id.v;
      }
      
      public function get saveData() : String
      {
         return [this._id.v,this._flag.v].join("H");
      }
      
      public function set loadData(param1:Array) : void
      {
         this._flag.v = param1[1];
      }
      
      public function dailyRefresh() : void
      {
         this._flag.v = 0;
      }
   }
}

