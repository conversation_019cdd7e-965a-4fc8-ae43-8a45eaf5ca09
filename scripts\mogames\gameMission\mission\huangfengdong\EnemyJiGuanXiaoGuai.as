package mogames.gameMission.mission.huangfengdong
{
   import citrus.objects.CitrusSprite;
   import citrus.view.ICitrusArt;
   import com.greensock.TweenLite;
   import flash.display.MovieClip;
   import mogames.Layers;
   import mogames.gameData.ConstData;
   import mogames.gameData.ConstRole;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.co.HurtEffect;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   import org.osflash.signals.Signal;
   
   public class EnemyJiGuanXiaoGuai extends CitrusSprite implements IRole
   {
      private var _totalHP:int;
      
      private var _mc:CitrusMC;
      
      private var _hurtEffect:HurtEffect;
      
      public var onRole:Signal = new Signal(String);
      
      public function EnemyJiGuanXiaoGuai(param1:int)
      {
         this._totalHP = param1;
         super("EnemyJiGuanXiaoGuai",{
            "width":100,
            "height":140,
            "group":2
         });
         this.createView();
      }
      
      private function createView() : void
      {
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("MC_JI_GUAN_XIAO_GUAI"),null,this.updateFrame);
         this._mc.changeAnimation("start",true);
         this._hurtEffect = new HurtEffect();
         this.view = this._mc;
      }
      
      private function updateFrame(param1:int) : void
      {
         if(param1 == 8)
         {
            this.onRole.dispatch("onRelease");
         }
      }
      
      override public function handleArtReady(param1:ICitrusArt) : void
      {
         super.handleArtReady(param1);
         BattleMediator.instance().onHeroATK.add(this.listenHurt);
      }
      
      protected function listenHurt(param1:BaseHitVO) : void
      {
         if(Boolean(param1.source) && param1.source.hasTargets(this))
         {
            return;
         }
         if(!this.mcHurt || !this.mcHurt.hitTestObject(param1.range))
         {
            return;
         }
         if(param1.source)
         {
            param1.source.addTargets(this);
         }
         this.countHurt(param1);
         if(this.isDead)
         {
            this.handlerDead(param1.dir);
         }
      }
      
      public function handlerDead(param1:int) : void
      {
         var func:Function = null;
         var dir:int = param1;
         func = function():void
         {
            onRole.dispatch("onDead");
         };
         this._mc.changeAnimation(ConstRole.DEAD,false);
         TweenLite.to(this._mc,1,{
            "alpha":0,
            "onComplete":func
         });
      }
      
      private function countHurt(param1:BaseHitVO) : void
      {
         this._totalHP -= param1.hurt.v;
         if(param1.source)
         {
            param1.dir = param1.source.x > x ? -1 : 1;
         }
         this.addHurtNumber(int(param1.isCrit),param1.hurt.v,param1.dir);
         EffectManager.instance().addBMCEffect(param1.hurtSkin,Layers.ceEffectLayer,x + width * 0.5,y + height * 0.5);
         EffectManager.instance().playAudio(param1.hurtSound);
         this._hurtEffect.setupTarget(this._mc);
      }
      
      private function addHurtNumber(param1:int, param2:int, param3:int) : void
      {
         if(!ConstData.openNum || !visible)
         {
            return;
         }
         EffectManager.instance().addBattleNum(param1,param2,x + width * 0.5,y,param3);
         if(param1 == 1)
         {
            EffectManager.instance().addPicPopWord("PIC_CRIT_WORD",x + width * 0.5,y);
         }
      }
      
      public function get mcHurt() : MovieClip
      {
         return this._mc.mc.mcAreaHurt;
      }
      
      public function get isDead() : Boolean
      {
         return this._totalHP <= 0;
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return false;
      }
      
      public function addTargets(param1:IRole) : void
      {
      }
      
      public function cleanTargets() : void
      {
      }
      
      public function hitBack() : void
      {
      }
      
      override public function destroy() : void
      {
         this._mc.destroy();
         this._mc = null;
         this.onRole.removeAll();
         this.onRole = null;
         this._hurtEffect.destroy();
         this._hurtEffect = null;
         super.destroy();
      }
   }
}

