package mogames.gameData.role
{
   import mogames.gameData.heroSkill.HurtSkillVO;
   
   public class HeroDrakanVO extends HeroGameVO
   {
      public function HeroDrakanVO()
      {
         allSkills = [];
         allSkills.push(new HurtSkillVO(1021,this,0));
         allSkills.push(new HurtSkillVO(1022,this,1));
         allSkills.push(new HurtSkillVO(1023,this,2));
         allSkills.push(new HurtSkillVO(1024,this,3));
         allSkills.push(new HurtSkillVO(1025,this,4));
         updateKeySkills();
         super(1005);
      }
   }
}

