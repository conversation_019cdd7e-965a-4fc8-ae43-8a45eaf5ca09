package mogames.gameLine
{
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameObj.display.CitrusMC;
   import utils.MethodUtil;
   
   public class ComicPlayer
   {
      private static var _instance:ComicPlayer;
      
      private var _mc:CitrusMC;
      
      private var _okFunc:Function;
      
      private var _sound:String;
      
      private var _name:String;
      
      private var _last:String;
      
      private var _btnSkip:SimpleButton;
      
      private var _btnNext:SimpleButton;
      
      private var _active:Boolean;
      
      private var _labels:Array = ["one","two","three"];
      
      public function ComicPlayer()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : ComicPlayer
      {
         if(!_instance)
         {
            _instance = new ComicPlayer();
         }
         return _instance;
      }
      
      public function init(param1:String, param2:String, param3:String, param4:Function) : void
      {
         this._okFunc = param4;
         this._sound = param2;
         this._name = param1;
         this._last = param3;
         this._mc = new CitrusMC();
         MethodUtil.setMousable(this._mc,false);
         Layers.topLayer.addChild(this._mc);
         this._btnNext = AssetManager.newBtnRes("UI_BTN_NEXT_CLIP",772,542);
         Layers.topLayer.addChild(this._btnNext);
         this._btnSkip = AssetManager.newBtnRes("UI_BTN_SKIP_CLIP",867,539);
         Layers.topLayer.addChild(this._btnSkip);
         this._btnNext.addEventListener(MouseEvent.CLICK,this.onNext,false,0,true);
         this._btnSkip.addEventListener(MouseEvent.CLICK,this.onSkip,false,0,true);
         this._mc.setupMC(GameInLoader.instance().findAsset(param1));
         this._mc.changeAnimation("one");
         EffectManager.instance().playBGM(param2);
      }
      
      private function onNext(param1:MouseEvent) : void
      {
         if(this._mc.curLabel == this._last)
         {
            this.handlerComicEnd();
            return;
         }
         var _loc2_:int = int(this._labels.indexOf(this._mc.curLabel));
         if(_loc2_ != -1)
         {
            this._mc.changeAnimation(this._labels[_loc2_ + 1],false);
         }
      }
      
      private function onSkip(param1:MouseEvent) : void
      {
         this.handlerComicEnd();
      }
      
      private function handlerComicEnd(param1:String = "") : void
      {
         if(this._active)
         {
            return;
         }
         this._active = true;
         Layers.ceLayer.sound.stopSound(this._sound);
         this._okFunc();
         this.clean();
      }
      
      private function clean() : void
      {
         GameInLoader.instance().cleanRes(this._name);
         this._btnSkip.removeEventListener(MouseEvent.CLICK,this.onSkip);
         MethodUtil.removeMe(this._btnSkip);
         this._btnNext.removeEventListener(MouseEvent.CLICK,this.onNext);
         MethodUtil.removeMe(this._btnNext);
         this._mc.destroy();
         this._mc = null;
         this._btnSkip = null;
         this._btnNext = null;
         this._okFunc = null;
         _instance = null;
      }
   }
}

