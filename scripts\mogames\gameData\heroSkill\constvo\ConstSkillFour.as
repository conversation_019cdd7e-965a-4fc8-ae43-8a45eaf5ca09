package mogames.gameData.heroSkill.constvo
{
   import mogames.gameData.heroSkill.SkillLevelVO;
   
   public class ConstSkillFour extends ConstSkillVO
   {
      public function ConstSkillFour(param1:int, param2:String, param3:String, param4:String, param5:String)
      {
         super(param1,param2,param3,param4,param5);
      }
      
      override protected function initCondition() : void
      {
         super.initCondition();
         _lvVec.push(new SkillLevelVO(0,4,800));
         _lvVec.push(new SkillLevelVO(1,9,1600));
         _lvVec.push(new SkillLevelVO(2,14,2400));
         _lvVec.push(new SkillLevelVO(3,19,3200));
         _lvVec.push(new SkillLevelVO(4,24,4000));
         _lvVec.push(new SkillLevelVO(5,29,4800));
         _lvVec.push(new SkillLevelVO(6,34,5600));
         _lvVec.push(new SkillLevelVO(7,39,6400));
         _lvVec.push(new SkillLevelVO(8,44,7200));
         _lvVec.push(new SkillLevelVO(9,49,8000));
      }
   }
}

