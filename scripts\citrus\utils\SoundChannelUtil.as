package citrus.utils
{
   import flash.events.Event;
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundTransform;
   import flash.utils.ByteArray;
   
   public class SoundChannelUtil
   {
      private static var _soundCheck:Sound;
      
      private static var soundChannel:SoundChannel;
      
      private static var _silentSound:Sound;
      
      private static var silentChannel:SoundChannel;
      
      private static var _silentSoundTransform:SoundTransform = new SoundTransform(0,0);
      
      public function SoundChannelUtil()
      {
         super();
      }
      
      public static function hasAvailableChannel() : Boolean
      {
         soundChannel = soundCheck.play(0,0,silentST);
         if(soundChannel != null)
         {
            soundChannel.stop();
            soundChannel = null;
            return true;
         }
         return false;
      }
      
      public static function maxAvailableChannels() : uint
      {
         var _loc1_:Vector.<SoundChannel> = new Vector.<SoundChannel>();
         var _loc2_:* = 0;
         while((soundChannel = soundCheck.play(0,0,silentST)) != null)
         {
            _loc1_.push(soundChannel);
         }
         _loc2_ = _loc1_.length;
         while((soundChannel = _loc1_.pop()) != null)
         {
            soundChannel.stop();
         }
         _loc1_.length = 0;
         return _loc2_;
      }
      
      public static function get silentST() : SoundTransform
      {
         return _silentSoundTransform;
      }
      
      public static function get soundCheck() : Sound
      {
         if(!_soundCheck)
         {
            _soundCheck = generateSound();
         }
         return _soundCheck;
      }
      
      public static function get silentSound() : Sound
      {
         if(!_silentSound)
         {
            _silentSound = generateSound(2048,0);
         }
         return _silentSound;
      }
      
      public static function playSilentSound() : Boolean
      {
         if(silentChannel)
         {
            return false;
         }
         silentChannel = silentSound.play(0,int.MAX_VALUE,silentST);
         if(silentChannel)
         {
            silentChannel.addEventListener(Event.SOUND_COMPLETE,silentComplete);
            return true;
         }
         return false;
      }
      
      public static function stopSilentSound() : void
      {
         if(silentChannel)
         {
            silentChannel.stop();
            silentChannel.removeEventListener(Event.SOUND_COMPLETE,silentComplete);
            silentChannel = null;
         }
      }
      
      private static function generateSound(param1:int = 1, param2:Number = 1) : Sound
      {
         var _loc3_:Sound = new Sound();
         var _loc4_:ByteArray = new ByteArray();
         var _loc5_:int = 0;
         while(_loc5_ < param1)
         {
            _loc4_.writeFloat(param2);
            _loc5_++;
         }
         _loc4_.position = 0;
         _loc3_.loadPCMFromByteArray(_loc4_,1,"float",false,44100);
         return _loc3_;
      }
      
      private static function silentComplete(param1:Event) : void
      {
         silentChannel = silentSound.play(0,int.MAX_VALUE,silentST);
      }
   }
}

