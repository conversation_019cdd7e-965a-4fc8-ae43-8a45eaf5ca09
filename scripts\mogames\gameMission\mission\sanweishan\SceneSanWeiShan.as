package mogames.gameMission.mission.sanweishan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.enemy.qingniao.BOSSQingNiao;
   
   public class SceneSan<PERSON>ei<PERSON>han extends BossScene
   {
      public function SceneSanWeiShan(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_BATTLE0");
         setWinPosition(new Rectangle(200,170,480,140),new Point(700,386));
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this.addBOSS();
         startBossBattle();
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSQingNiao();
         _boss.x = 1200;
         _boss.y = 200;
         _boss.initData(30601,30601,206);
         _boss.target = _mission.onePlayer;
         add(_boss);
      }
   }
}

