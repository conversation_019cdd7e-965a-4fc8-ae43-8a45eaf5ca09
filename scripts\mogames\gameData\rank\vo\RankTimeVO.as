package mogames.gameData.rank.vo
{
   import mogames.gameData.base.Sint;
   
   public class RankTimeVO
   {
      public var rank:Sint;
      
      public var nick:String;
      
      public var score:Sint;
      
      public var hurt:Sint;
      
      public var uid:String;
      
      public var saveIndex:int;
      
      public function RankTimeVO()
      {
         super();
         this.rank = new Sint();
         this.score = new Sint();
         this.hurt = new Sint();
      }
      
      public function parseData(param1:Object) : void
      {
         this.uid = param1.uId;
         this.nick = param1.userName;
         this.score.v = param1.score;
         this.rank.v = param1.rank;
         this.saveIndex = param1.index;
         if(param1.extra is Array)
         {
            this.parseExtra(param1.extra);
         }
      }
      
      private function parseExtra(param1:Array) : void
      {
         this.hurt.v = int(param1[0]);
      }
      
      public function get sortIndex() : int
      {
         return this.rank.v;
      }
   }
}

