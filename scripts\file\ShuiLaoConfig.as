package file
{
   import mogames.gameData.fuben.shuilao.ShuiLaoVO;
   
   public class ShuiLaoConfig
   {
      private static var _instance:ShuiLaoConfig;
      
      public var fbList:Array;
      
      public function ShuiLaoConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : ShuiLaoConfig
      {
         if(!_instance)
         {
            _instance = new ShuiLaoConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.fbList = [];
         this.fbList.push(new ShuiLaoVO(4001,[10000,14001,14002,13301,13305]));
         this.fbList.push(new ShuiLaoVO(4002,[10000,14001,14002,13302,13304]));
         this.fbList.push(new ShuiLaoVO(4003,[10000,14001,14002,13311]));
         this.fbList.push(new ShuiLaoVO(4004,[10000,14001,14002,16010]));
      }
   }
}

