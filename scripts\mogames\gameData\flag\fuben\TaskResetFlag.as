package mogames.gameData.flag.fuben
{
   import mogames.gameData.MasterProxy;
   
   public class TaskResetFlag extends FBBaseFlag
   {
      public function TaskResetFlag(param1:int, param2:int)
      {
         super(param1,param2);
      }
      
      override public function get status() : String
      {
         if(MasterProxy.instance().gameVIP.v < 4)
         {
            return "VIP4开启重置";
         }
         if(isFree)
         {
            return "免费重置：" + leftFree + "次";
         }
         return "消耗10金锭";
      }
   }
}

