package file
{
   import flash.utils.Dictionary;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.drop.vo.EquipRewardVO;
   
   public class GiftConfig
   {
      private static var _instance:GiftConfig;
      
      public var rewardDic:Dictionary;
      
      public var timeDic:Dictionary;
      
      public function GiftConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : GiftConfig
      {
         if(!_instance)
         {
            _instance = new GiftConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.rewardDic = new Dictionary();
         this.timeDic = new Dictionary();
         this.rewardDic["FIRST_PAY"] = [new BaseRewardVO(14150,5),new BaseRewardVO(14009,10),new BaseRewardVO(16711,2),new BaseRewardVO(13303,10),new BaseRewardVO(13108,10),new EquipRewardVO(23006,3)];
         this.rewardDic["TOWER0"] = [new BaseRewardVO(14001,10),new BaseRewardVO(14002,5)];
         this.rewardDic["TOWER1"] = [new BaseRewardVO(13303,10),new BaseRewardVO(13313,10),new BaseRewardVO(13317,10)];
         this.rewardDic["TOWER2"] = [new BaseRewardVO(14009,5),new BaseRewardVO(14150,2),new BaseRewardVO(16711,1),new BaseRewardVO(14021,5)];
      }
      
      public function openActivity(param1:String) : void
      {
         var _loc2_:* = param1;
         switch(0)
         {
         }
      }
   }
}

