package file
{
   import citrus.input.controllers.Keyboard;
   import mogames.Layers;
   
   public class KeyConfig
   {
      private static var _instance:KeyConfig;
      
      public static var CHANNEL_ONE:int = 1;
      
      public function KeyConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : KeyConfig
      {
         if(!_instance)
         {
            _instance = new KeyConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         var _loc1_:Keyboard = Layers.ceLayer.input.keyboard;
         _loc1_.resetAllKeyActions();
         _loc1_.addKeyAction("UP",Keyboard.W,1);
         _loc1_.addKeyAction("DOWN",Keyboard.S,1);
         _loc1_.addKeyAction("LEFT",Keyboard.A,1);
         _loc1_.addKeyAction("RIGHT",Keyboard.D,1);
         _loc1_.addKeyAction("ATTACK",Keyboard.J,1);
         _loc1_.addKeyAction("JUMP",Keyboard.K,1);
         _loc1_.addKeyAction("WUSHUANG",Keyboard.SPACE,1);
         _loc1_.addKeyAction("COMBIME",Keyboard.Q,1);
         _loc1_.addKeyAction("FABAO",Keyboard.N,1);
         _loc1_.addKeyAction("INDEX_ONE",Keyboard.H,1);
         _loc1_.addKeyAction("INDEX_TWO",Keyboard.U,1);
         _loc1_.addKeyAction("INDEX_THREE",Keyboard.I,1);
         _loc1_.addKeyAction("INDEX_FOUR",Keyboard.O,1);
         _loc1_.addKeyAction("INDEX_FIVE",Keyboard.L,1);
         _loc1_.addKeyAction("UP",Keyboard.UP,2);
         _loc1_.addKeyAction("DOWN",Keyboard.DOWN,2);
         _loc1_.addKeyAction("LEFT",Keyboard.LEFT,2);
         _loc1_.addKeyAction("RIGHT",Keyboard.RIGHT,2);
         _loc1_.addKeyAction("ATTACK",Keyboard.NUMPAD_1,2);
         _loc1_.addKeyAction("JUMP",Keyboard.NUMPAD_2,2);
         _loc1_.addKeyAction("WUSHUANG",Keyboard.NUMPAD_0,2);
         _loc1_.addKeyAction("COMBIME",Keyboard.NUMPAD_9,2);
         _loc1_.addKeyAction("FABAO",Keyboard.NUMPAD_3,2);
         _loc1_.addKeyAction("INDEX_ONE",Keyboard.NUMPAD_4,2);
         _loc1_.addKeyAction("INDEX_TWO",Keyboard.NUMPAD_5,2);
         _loc1_.addKeyAction("INDEX_THREE",Keyboard.NUMPAD_6,2);
         _loc1_.addKeyAction("INDEX_FOUR",Keyboard.NUMPAD_7,2);
         _loc1_.addKeyAction("INDEX_FIVE",Keyboard.NUMPAD_8,2);
      }
      
      public function actionName(param1:int, param2:int) : String
      {
         var _loc5_:Object = null;
         var _loc3_:Keyboard = Layers.ceLayer.input.keyboard;
         var _loc4_:Vector.<Object> = _loc3_.getActionsByKey(param1);
         for each(_loc5_ in _loc4_)
         {
            if(_loc5_.channel == param2)
            {
               return _loc5_.name;
            }
         }
         return "";
      }
   }
}

