package mogames.gameData.base
{
   import utils.MathUtil;
   
   public class SArray
   {
      private var list:Array;
      
      public function SArray(param1:Array)
      {
         super();
         this.arrList = param1;
      }
      
      public function indexOf(param1:int) : int
      {
         var _loc2_:int = 0;
         var _loc3_:int = int(this.list.length);
         while(_loc2_ < _loc3_)
         {
            if(this.list[_loc2_].v == param1)
            {
               return _loc2_;
            }
            _loc2_++;
         }
         return -1;
      }
      
      public function splice(param1:int) : void
      {
         this.list.splice(param1,1);
      }
      
      public function push(param1:int) : void
      {
         this.list.push(new Sint(param1));
      }
      
      public function split(param1:String, param2:String) : void
      {
         var _loc3_:Array = MathUtil.arrStringToNum(param1.split(param2));
         this.arrList = _loc3_;
      }
      
      public function join(param1:String) : String
      {
         var _loc2_:Array = [];
         var _loc3_:int = 0;
         var _loc4_:int = int(this.list.length);
         while(_loc3_ < _loc4_)
         {
            _loc2_.push(this.list[_loc3_].v);
            _loc3_++;
         }
         return _loc2_.join(param1);
      }
      
      public function get arrList() : Array
      {
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         var _loc3_:int = int(this.list.length);
         while(_loc2_ < _loc3_)
         {
            _loc1_.push(this.list[_loc2_].v);
            _loc2_++;
         }
         return _loc1_;
      }
      
      public function set arrList(param1:Array) : void
      {
         this.list = [];
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         while(_loc2_ < _loc3_)
         {
            this.list.push(new Sint(param1[_loc2_]));
            _loc2_++;
         }
      }
      
      public function setElement(param1:int, param2:int) : void
      {
         this.list[param1].v = param2;
      }
      
      public function findElement(param1:int) : int
      {
         return this.list[param1].v;
      }
      
      public function addValue(param1:int) : void
      {
         this.list[param1].v += 1;
      }
      
      public function get length() : int
      {
         return this.list.length;
      }
   }
}

