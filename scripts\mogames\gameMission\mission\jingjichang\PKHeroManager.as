package mogames.gameMission.mission.jingjichang
{
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameRole.hero.BaseHero;
   
   public class PKHeroManager
   {
      public static var onePlayer:BaseHero;
      
      private static var _instance:PKHeroManager;
      
      public var teams:Array;
      
      public function PKHeroManager()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : PKHeroManager
      {
         if(!_instance)
         {
            _instance = new PKHeroManager();
         }
         return _instance;
      }
      
      public static function clean() : void
      {
         if(!_instance)
         {
            return;
         }
         _instance.destroy();
         _instance = null;
      }
      
      public function init(param1:Array) : void
      {
         HeroProxy.instance().initBattleData();
         this.teams = param1;
         var _loc2_:int = 0;
         while(_loc2_ < this.teams.length)
         {
            this.teams[_loc2_].onUpdate.add(this.listenHero);
            _loc2_++;
         }
      }
      
      public function checkAllDead() : Boolean
      {
         var _loc1_:int = 0;
         while(_loc1_ < this.teams.length)
         {
            if(!this.teams[_loc1_].isDead())
            {
               return false;
            }
            _loc1_++;
         }
         return true;
      }
      
      public function checkLeftOne() : Boolean
      {
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         while(_loc2_ < this.teams.length)
         {
            if(!this.teams[_loc2_].isDead())
            {
               _loc1_++;
            }
            _loc2_++;
         }
         return _loc1_ == 1;
      }
      
      private function listenHero(param1:String) : void
      {
         if(param1 != "onDead")
         {
            return;
         }
         if(this.checkAllDead())
         {
            EventManager.dispatchEvent(UIEvent.TEAM_EVENT,{"type":"teamDead"});
         }
      }
      
      private function destroy() : void
      {
         this.teams = null;
         HeroProxy.instance().cleanBattleData();
         HeroProxy.instance().initBattleData();
         onePlayer = null;
      }
   }
}

