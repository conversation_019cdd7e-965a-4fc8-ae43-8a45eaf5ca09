package mogames.gameMission.mission.saitaisui
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.qilinshan.BaseCloud;
   import mogames.gameObj.trap.TrapJinJi;
   import mogames.gameRole.enemy.BOSSSaiTaiSui;
   
   public class SceneSaiTaiSui extends BossScene
   {
      private var _traps:Array = [{
         "skin":"MC_TRAP_JIN_JI01",
         "width":302,
         "height":38,
         "posX":73,
         "posY":512
      },{
         "skin":"MC_TRAP_JIN_JI02",
         "width":372,
         "height":40,
         "posX":454,
         "posY":510
      },{
         "skin":"MC_TRAP_JIN_JI03",
         "width":146,
         "height":40,
         "posX":888,
         "posY":510
      }];
      
      private var _list:Array = [];
      
      private var _realBOSS:BOSSSaiTaiSui;
      
      public function SceneSaiTaiSui(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         setWinPosition(new Rectangle(384,212,666,174),new Point(711,426));
         EffectManager.instance().playBGM("BGM_BATTLE1");
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.layoutCloud();
         this.layoutTraps();
         this.addBOSS();
         this.startBossBattle();
      }
      
      private function layoutTraps() : void
      {
         var _loc1_:Object = null;
         var _loc2_:TrapJinJi = null;
         for each(_loc1_ in this._traps)
         {
            _loc2_ = new TrapJinJi(_loc1_.skin,_loc1_.width,_loc1_.height);
            Layers.addCEChild(_loc2_);
            _loc2_.x = _loc1_.posX;
            _loc2_.y = _loc1_.posY;
            _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",2,2);
            _loc2_.createHitHurt(1000,1,false);
            this._list.push(_loc2_);
         }
      }
      
      private function layoutCloud() : void
      {
         var _loc1_:BaseCloud = new BaseCloud("MC_LARGE_CLOUD_CLIP",{
            "width":265,
            "height":26,
            "group":2,
            "oneWay":true
         });
         _loc1_.setLocation(480,308);
         _loc1_.setTime(5,5);
         Layers.addCEChild(_loc1_);
      }
      
      override protected function addBOSS() : void
      {
         this._realBOSS = new BOSSSaiTaiSui();
         this._realBOSS.x = 771;
         this._realBOSS.y = 200;
         this._realBOSS.initData(40091,40091,125);
         this._realBOSS.target = _mission.curTarget;
         this._realBOSS.setInverted(true);
         add(this._realBOSS);
         this._realBOSS.onRole.add(listenBOSS);
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         setHeroEnable(true);
         this._realBOSS.activeEnemy(false);
      }
      
      override protected function handlerWin() : void
      {
         this.destroyTraps();
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":this._realBOSS,
            "rect":_dropRect,
            "hero":this._realBOSS.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override protected function listenLeave() : void
      {
         setRoleEnable(false);
         Layers.setKeyEnable(false);
         MissionManager.instance().handlerWin(false);
      }
      
      private function destroyTraps() : void
      {
         var _loc1_:TrapJinJi = null;
         for each(_loc1_ in this._list)
         {
            _loc1_.kill = true;
         }
         this._list = null;
      }
      
      override public function destroy() : void
      {
         this._traps = null;
         this._list = null;
         super.destroy();
      }
   }
}

