package file
{
   import mogames.gameData.drop.vo.BaoDaiVO;
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.drop.vo.CardRewardVO;
   
   public class BaoDaiConfig
   {
      private static var _instance:BaoDaiConfig;
      
      private var _normals:Array;
      
      private var _extras:Array;
      
      public var normalReward:BaseRewardVO;
      
      public var extraReward:BaseRewardVO;
      
      public function BaoDaiConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : BaoDaiConfig
      {
         if(!_instance)
         {
            _instance = new BaoDaiConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._normals = [];
         this._normals[0] = new BaoDaiVO(45,[new BaseRewardVO(16757,2),new BaseRewardVO(16762,2),new BaseRewardVO(16759,2),new BaseRewardVO(14009,2),new BaseRewardVO(14045,2),new BaseRewardVO(16711,1),new BaseRewardVO(14002,3),new BaseRewardVO(14120,3),new BaseRewardVO(14121,1),new BaseRewardVO(50000,1)]);
         this._normals[1] = new BaoDaiVO(45,[new BaseRewardVO(14125,1),new BaseRewardVO(16721,1),new BaseRewardVO(16722,1),new BaseRewardVO(16750,1),new BaseRewardVO(16752,1),new BaseRewardVO(16755,1),new BaseRewardVO(16756,1),new BaseRewardVO(16760,1),new BaseRewardVO(14030,1),new BaseRewardVO(14101,1)]);
         this._normals[2] = new BaoDaiVO(10,[new CardRewardVO(70041,1),new CardRewardVO(70046,1),new CardRewardVO(70051,1),new CardRewardVO(70056,1),new CardRewardVO(70061,1),new CardRewardVO(70066,1),new CardRewardVO(70071,1),new CardRewardVO(70076,1),new CardRewardVO(70081,1),new CardRewardVO(70086,1),new CardRewardVO(70091,1),new CardRewardVO(70096,1),new CardRewardVO(70101,1),new CardRewardVO(70106,1),new CardRewardVO(70111,1),new CardRewardVO(70116,1),new CardRewardVO(70121,1),new CardRewardVO(70126,1),new CardRewardVO(70131,1),new CardRewardVO(70136,1),new CardRewardVO(70141,1),new CardRewardVO(70146,1),new CardRewardVO(70151,1),new CardRewardVO(70156,1),new CardRewardVO(70161,1),new CardRewardVO(70166,1),new CardRewardVO(70171,1),new CardRewardVO(70176,1),new CardRewardVO(70181,1)]);
         this._extras = [];
         this._extras[0] = new BaoDaiVO(46,[new BaseRewardVO(16757,2),new BaseRewardVO(16762,2),new BaseRewardVO(16759,2),new BaseRewardVO(14009,2),new BaseRewardVO(14045,2),new BaseRewardVO(16711,1),new BaseRewardVO(14002,3),new BaseRewardVO(14120,3),new BaseRewardVO(14121,1),new BaseRewardVO(50000,1)]);
         this._extras[1] = new BaoDaiVO(43,[new BaseRewardVO(14125,1),new BaseRewardVO(16721,1),new BaseRewardVO(16722,1),new BaseRewardVO(16750,1),new BaseRewardVO(16752,1),new BaseRewardVO(16755,1),new BaseRewardVO(16756,1),new BaseRewardVO(16760,1),new BaseRewardVO(14030,1),new BaseRewardVO(14101,1)]);
         this._extras[2] = new BaoDaiVO(1,[new BaseRewardVO(14107,1)]);
         this._extras[3] = new BaoDaiVO(4,[new CardRewardVO(70951,1),new CardRewardVO(70956,1),new CardRewardVO(70961,1),new CardRewardVO(70966,1),new CardRewardVO(70971,1),new CardRewardVO(70976,1),new CardRewardVO(70981,1),new BaseRewardVO(14008,1),new BaseRewardVO(14000,1)]);
         this._extras[4] = new BaoDaiVO(1,[new BaseRewardVO(10095,1),new BaseRewardVO(10094,1)]);
         this._extras[5] = new BaoDaiVO(5,[new BaseRewardVO(14007,1),new CardRewardVO(70045,1),new CardRewardVO(70050,1),new CardRewardVO(70055,1),new CardRewardVO(70060,1),new CardRewardVO(70065,1),new CardRewardVO(70070,1),new CardRewardVO(70075,1),new CardRewardVO(70080,1),new CardRewardVO(70085,1),new CardRewardVO(70090,1),new CardRewardVO(70095,1),new CardRewardVO(70100,1),new CardRewardVO(70105,1),new CardRewardVO(70110,1),new CardRewardVO(70115,1),new CardRewardVO(70120,1),new CardRewardVO(70125,1),new CardRewardVO(70130,1),new CardRewardVO(70135,1),new CardRewardVO(70140,1),new CardRewardVO(70145,1),new CardRewardVO(70150,1),new CardRewardVO(70155,1),new CardRewardVO(70160,1),new CardRewardVO(70165,1),new CardRewardVO(70170,1),new CardRewardVO(70175,1),new CardRewardVO(70180,1),new CardRewardVO(70185,1)]);
         this.normalReward = new BaseRewardVO(14002,1);
         this.extraReward = new BaseRewardVO(16721,1);
      }
      
      public function randBaoDai(param1:Boolean) : BaseRewardVO
      {
         var _loc2_:Array = param1 ? this._normals : this._extras;
         var _loc3_:int = Math.random() * 100 + 1;
         var _loc4_:int = 0;
         var _loc5_:int = int(_loc2_.length);
         while(_loc4_ < _loc5_)
         {
            if(_loc3_ <= this.countPer(_loc4_ + 1,_loc2_))
            {
               return _loc2_[_loc4_].randReward;
            }
            _loc4_++;
         }
         return null;
      }
      
      public function get randTen() : Array
      {
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         while(_loc2_ < 9)
         {
            _loc1_[_loc2_] = this.randBaoDai(false);
            _loc2_++;
         }
         if(this.checkCard(_loc1_))
         {
            _loc1_[_loc1_.length] = this.randBaoDai(false);
         }
         else
         {
            _loc1_[_loc1_.length] = this._extras[5].randReward;
         }
         return _loc1_;
      }
      
      private function checkCard(param1:Array) : Boolean
      {
         var _loc2_:BaseRewardVO = null;
         for each(_loc2_ in param1)
         {
            if(_loc2_ is CardRewardVO)
            {
               return true;
            }
         }
         return false;
      }
      
      private function countPer(param1:int, param2:Array) : int
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         while(_loc4_ < param1)
         {
            _loc3_ += param2[_loc4_].rate;
            _loc4_++;
         }
         return _loc3_;
      }
   }
}

