package mogames.gameData.buff
{
   import file.BuffConfig;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.buff.team.EXPBuff;
   
   public class TeamBuffProxy
   {
      private static var _instance:TeamBuffProxy;
      
      private var _list:Vector.<BaseTeamBuff>;
      
      public function TeamBuffProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : TeamBuffProxy
      {
         if(!_instance)
         {
            _instance = new TeamBuffProxy();
         }
         return _instance;
      }
      
      public static function clean() : void
      {
         if(!_instance)
         {
            return;
         }
         _instance.destroyBuff();
         _instance = null;
      }
      
      public function init() : void
      {
         this._list = new Vector.<BaseTeamBuff>();
      }
      
      public function addBuff(param1:BuffVO) : void
      {
         var _loc2_:BaseTeamBuff = null;
         var _loc3_:BaseTeamBuff = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.buffID.v == param1.id.v)
            {
               _loc2_.setBuffVO(param1);
               return;
            }
         }
         _loc3_ = BuffConfig.instance().newTeamBuff(param1);
         this._list.push(_loc3_);
         EventManager.dispatchEvent(UIEvent.TEAM_BUFF_EVENT,{
            "type":"addTeamBuff",
            "data":_loc3_
         });
      }
      
      public function removeBuff(param1:BaseTeamBuff) : void
      {
         var _loc2_:int = int(this._list.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._list.splice(_loc2_,1);
         }
      }
      
      public function get expBuff() : EXPBuff
      {
         var _loc1_:BaseTeamBuff = null;
         for each(_loc1_ in this._list)
         {
            if(_loc1_.buffID.v == 10000)
            {
               return _loc1_ as EXPBuff;
            }
         }
         return null;
      }
      
      public function get hasBuff() : Boolean
      {
         return this._list.length > 0;
      }
      
      private function destroyBuff() : void
      {
         var _loc1_:BaseTeamBuff = null;
         if(!this._list)
         {
            return;
         }
         for each(_loc1_ in this._list)
         {
            if(_loc1_)
            {
               _loc1_.destroy();
            }
         }
         this._list = null;
      }
   }
}

