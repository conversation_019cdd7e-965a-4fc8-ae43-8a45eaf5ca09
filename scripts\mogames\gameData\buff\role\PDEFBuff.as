package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class PDEFBuff extends BaseBuff
   {
      private var addPDEF:int;
      
      public function PDEFBuff()
      {
         super(1010);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         this.addPDEF = _roleVO.totalPDEF.v * _buffVO.argDic.per * 0.01;
         _roleVO.skillPDEF.v += this.addPDEF;
         _roleVO.updatePDEF();
         if(_role.isReady)
         {
            EffectManager.instance().addHeadWord("物防提升！",_role.x,_role.y - _role.height * 0.5);
         }
      }
      
      override protected function onEnd() : void
      {
         _roleVO.skillPDEF.v -= this.addPDEF;
         _roleVO.updatePDEF();
         destroy();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         if(!_buffVO.argDic.skin)
         {
            return null;
         }
         _skin.setupSequence(AssetManager.findAnimation(_buffVO.argDic.skin),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

