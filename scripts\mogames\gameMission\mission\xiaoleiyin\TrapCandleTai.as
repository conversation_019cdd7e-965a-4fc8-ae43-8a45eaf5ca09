package mogames.gameMission.mission.xiaoleiyin
{
   import citrus.core.CitrusObject;
   import citrus.objects.CitrusSprite;
   import mogames.Layers;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.co.HurtEffect;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameRole.co.IRole;
   
   public class TrapCandleTai extends CitrusSprite implements IRole
   {
      private var _label:Array = ["red","yellow","blue","green","purple"];
      
      private var _mc:CitrusMC;
      
      private var _hurtEffect:HurtEffect;
      
      private var _allTargets:Vector.<CitrusObject>;
      
      private var _hitNum:int;
      
      public var index:int;
      
      public function TrapCandleTai()
      {
         super("TrapCandleLight",{
            "width":68,
            "height":172,
            "group":2
         });
         registration = "center";
         this.createView();
         this._hurtEffect = new HurtEffect();
      }
      
      private function createView() : void
      {
         this._mc = new CitrusMC();
         this._mc.setupMC(GameInLoader.instance().findAsset("MC_LA_ZHU_TAI"));
         this.view = this._mc;
         BattleMediator.instance().onHeroATK.add(this.listenHurt);
         this.index = Math.random() * this._label.length;
         this.updateFire();
      }
      
      private function listenHurt(param1:BaseHitVO) : void
      {
         if(Boolean(param1.source) && param1.source.hasTargets(this))
         {
            return;
         }
         if(!this._mc.mc.mcAreaHurt || !this._mc.mc.mcAreaHurt.hitTestObject(param1.range))
         {
            return;
         }
         if(param1.source)
         {
            param1.source.addTargets(this);
         }
         ++this._hitNum;
         if(this._hitNum >= 2)
         {
            this.updateFire();
         }
         this._hurtEffect.setupTarget(this._mc);
         EffectManager.instance().addBMCEffect(param1.hurtSkin,Layers.ceEffectLayer,x,y);
         EffectManager.instance().playAudio(param1.hurtSound);
      }
      
      private function updateFire() : void
      {
         ++this.index;
         if(this.index >= this._label.length)
         {
            this.index = 0;
         }
         this._mc.changeAnimation(this._label[this.index],true);
         this._hitNum = 0;
      }
      
      public function hasTargets(param1:IRole) : Boolean
      {
         return false;
      }
      
      public function addTargets(param1:IRole) : void
      {
      }
      
      public function cleanTargets() : void
      {
      }
      
      public function hitBack() : void
      {
      }
      
      override public function destroy() : void
      {
         this._mc.destroy();
         this._mc = null;
         super.destroy();
      }
   }
}

