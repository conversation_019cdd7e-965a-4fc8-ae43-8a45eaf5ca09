package mogames.gameData.base
{
   import mogames.gamePKG.PKGManager;
   import utils.MathUtil;
   import utils.TxtUtil;
   
   public class Snum
   {
      private var real:String;
      
      private var hash:String;
      
      private var salt:String;
      
      private var code:String;
      
      public function Snum(param1:Number = 0)
      {
         super();
         this.code = TxtUtil.newCode();
         this.v = param1;
      }
      
      public function set v(param1:Number) : void
      {
         this.real = param1.toString();
         this.salt = int(MathUtil.randomNum(10000,99999)).toString();
         this.hash = TxtUtil.hash(param1 + this.salt,this.code);
      }
      
      public function get v() : Number
      {
         if(this.hash != TxtUtil.hash(this.real + this.salt,this.code))
         {
            PKGManager.handlerCheat(new Sint(1));
            return -1;
         }
         return Number(this.real);
      }
   }
}

