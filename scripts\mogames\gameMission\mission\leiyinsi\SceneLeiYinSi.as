package mogames.gameMission.mission.leiyinsi
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameEffect.WinEffect;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameRole.enemy.BOSSJiaYe;
   import mogames.gameRole.enemy.BossANan;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusRender;
   
   public class SceneLeiYinSi extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      private var _pTrigger4:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _index:int;
      
      private var _traps:Array;
      
      private var _boss0:BOSSJiaYe;
      
      private var _boss1:BossANan;
      
      public function SceneLeiYinSi(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_DANGER2");
         setWinPosition(new Rectangle(4130,134,512,118),new Point(4390,384));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2059,1);
            TaskProxy.instance().addTask(11040);
         };
         super.handlerInit();
         this.layoutEnemies();
         this.layoutTraps();
         if(!FlagProxy.instance().isComplete(2059))
         {
            showDialog("STORY0086",func);
         }
      }
      
      private function layoutTraps() : void
      {
         var _loc2_:TrapFoXiang = null;
         this._traps = [];
         var _loc1_:int = 0;
         while(_loc1_ < 5)
         {
            _loc2_ = new TrapFoXiang();
            Layers.addCEChild(_loc2_);
            _loc2_.x = 475 + _loc1_ * 960;
            _loc2_.y = 375;
            this._traps[_loc1_] = _loc2_;
            _loc1_++;
         }
      }
      
      private function layoutEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[163,242,150,100],[676,98,150,100]],32011,this.triggerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[996,98,150,100],[1385,242,150,100]],32012,this.triggerEnd);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1965,242,150,100],[2610,98,150,100]],32013,this.triggerEnd);
         this._eTrigger3 = new EnemyTrigger(_mission,[[3516,242,150,100],[3003,242,150,100]],32014,this.triggerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(464,413),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1441,413),1,new Rectangle(0,0,1920,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2400,413),1,new Rectangle(960,0,1920,600));
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(3364,413),1,new Rectangle(1920,0,1920,600));
         this._pTrigger4 = new LocalTriggerX(_mission.onePlayer,new Point(4309,450),1,new Rectangle(3840,0,960,600));
         this._pTrigger2.setBlock(false,false,true,true);
         this._pTrigger3.setBlock(false,false,true,true);
         this._pTrigger4.setBlock(false,false,true,true);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = function func():void
         {
            _pTrigger1.start();
            _traps[_index].start();
         };
         this._pTrigger1.okFunc = function func():void
         {
            _pTrigger2.start();
            triggerTrap();
         };
         this._pTrigger2.okFunc = function func():void
         {
            _pTrigger3.start();
            triggerTrap();
         };
         this._pTrigger3.okFunc = function func():void
         {
            _pTrigger4.start();
            triggerTrap();
         };
         this._pTrigger4.okFunc = function func():void
         {
            addBOSS();
            triggerTrap();
         };
         this._pTrigger0.start();
      }
      
      private function triggerTrap() : void
      {
         ++this._index;
         if(this._index < this._traps.length)
         {
            this._traps[this._index].start();
         }
         if(this._index >= 1)
         {
            this._traps[this._index - 1].stop();
         }
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      override protected function addBOSS() : void
      {
         this._boss0 = new BOSSJiaYe();
         this._boss0.x = 3909;
         this._boss0.y = 413;
         this._boss0.initData(30531,30531);
         this._boss0.target = _mission.curTarget;
         add(this._boss0);
         this._boss1 = new BossANan();
         this._boss1.x = 4733;
         this._boss1.y = 413;
         this._boss1.initData(30541,30541,166);
         this._boss1.target = _mission.curTarget;
         add(this._boss1);
         this._boss0.activeEnemy(false);
         this._boss1.activeEnemy(false);
         _boss = this._boss1;
         CitrusRender.instance().add(this.checkWin);
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      private function checkWin() : void
      {
         if(this._boss0.isDead && this._boss1.isDead)
         {
            this.showWin();
            TaskProxy.instance().setComplete(11040);
            CitrusRender.instance().remove(this.checkWin);
         }
      }
      
      private function showWin() : void
      {
         Layers.lockGame = true;
         EffectManager.instance().stopAllSound();
         var _loc1_:WinEffect = new WinEffect();
         _loc1_.start();
         _winTimer.setTimeOut(3,handlerWin);
         _isWin = true;
      }
      
      override public function destroy() : void
      {
         CitrusRender.instance().remove(this.checkWin);
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         if(this._pTrigger4)
         {
            this._pTrigger4.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         this._pTrigger4 = null;
         this._boss0 = null;
         this._boss1 = null;
         this._traps.length = 0;
         this._traps = null;
         super.destroy();
      }
   }
}

