package mogames.gameMission.mission.jingjichang
{
   import flash.utils.setTimeout;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.pk.RolePKProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionFactory;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.jingjichang.co.JingJiChangMission;
   import mogames.gameUI.map.AreaManager;
   import mogames.gameUI.pk.PKLoseModule;
   import mogames.gameUI.pk.PKMainModule;
   import mogames.gameUI.pk.PKWinModule;
   
   public class PKMissionManager
   {
      private static var _instance:PKMissionManager;
      
      public var curMission:BaseMission;
      
      public var isLose:Boolean;
      
      public var isWin:Boolean;
      
      public function PKMissionManager()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         EventManager.addEventListener(UIEvent.TEAM_EVENT,this.listenAllDead,false,0,true);
      }
      
      public static function instance() : PKMissionManager
      {
         if(!_instance)
         {
            _instance = new PKMissionManager();
         }
         return _instance;
      }
      
      public static function clean() : void
      {
         if(!_instance)
         {
            return;
         }
         _instance.destroy();
         _instance = null;
      }
      
      public function initMission() : void
      {
         BattleMediator.instance().init();
         this.curMission = MissionFactory.instance().newMission(8);
         this.curMission.initMission(8);
      }
      
      public function handlerWin() : void
      {
         if(this.isLose)
         {
            return;
         }
         this.isWin = true;
         RolePKProxy.instance().handlerWin();
         setTimeout(PKWinModule.instance().init,1000);
      }
      
      public function quitMission() : void
      {
         EffectManager.instance().stopAllSound();
         clean();
         AreaManager.instance().showMap();
         PKMainModule.instance().init();
      }
      
      private function listenAllDead(param1:UIEvent) : void
      {
         if(param1.data.type != "teamDead")
         {
            return;
         }
         if(this.isWin)
         {
            return;
         }
         this.isLose = true;
         (this.curMission as JingJiChangMission).enemySwitcher.cleanSwitch();
         RolePKProxy.instance().handlerLose();
         setTimeout(PKLoseModule.instance().init,1000);
         EffectManager.instance().stopAllSound();
         EffectManager.instance().playAudio("MISSION_LOSE");
      }
      
      private function destroy() : void
      {
         EventManager.removeEventListener(UIEvent.TEAM_EVENT,this.listenAllDead);
         this.curMission.cleanMission();
         this.curMission = null;
         BattleMediator.clean();
         PKHeroManager.clean();
         GameInLoader.instance().clean();
         this.isLose = false;
         this.isWin = false;
      }
   }
}

