package mogames.gameMission.mission.heishuihe
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.TrapFallCi;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.enemy.BOSSTuoLong;
   import mogames.gameRole.enemy.BOSSZhenTuoLong;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.dialog.StoryDialogModule;
   
   public class SceneHeiShuiHe02 extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _trapTimer:CitrusTimer;
      
      private var _trapArg:Object = {
         "hurt":new Sint(150),
         "hurtTime":new Snum(1.5),
         "interval":new Snum(6)
      };
      
      private var _traps:Array;
      
      private var _failBoss:BOSSTuoLong;
      
      private var _failPos:Point;
      
      private var _enemyArgs:Array = [{
         "id":new Sint(2015),
         "attID":new Sint(20151),
         "aiID":new Sint(20151),
         "dropID":new Sint(53)
      },{
         "id":new Sint(2016),
         "attID":new Sint(20161),
         "aiID":new Sint(20161),
         "dropID":new Sint(53)
      }];
      
      public function SceneHeiShuiHe02(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         sceneType = 1;
         setWinPosition(new Rectangle(2184,159,435,116),new Point(2400,415));
         this._trapTimer = new CitrusTimer(true);
      }
      
      override protected function handlerInit() : void
      {
         super.handlerInit();
         this.initFallTrap();
         this.initEnemies();
         this.addFailBOSS();
         this._trapTimer.setInterval(this._trapArg.interval.v + 2,0,this.activeTrap,null,false);
         this._trapTimer.startNone();
      }
      
      private function initFallTrap() : void
      {
         var _loc1_:int = 0;
         var _loc2_:TrapFallCi = null;
         this._traps = [];
         _loc1_ = 0;
         while(_loc1_ < 7)
         {
            _loc2_ = new TrapFallCi();
            _loc2_.x = 458 + _loc1_ * 365;
            _loc2_.y = -420;
            _loc2_.createHitEffect("EFFECT_GUN_ATK","GUNHURT",0,0);
            _loc2_.createHitHurt(this._trapArg.hurt.v,this._trapArg.hurtTime.v,false);
            add(_loc2_);
            this._traps[_loc1_] = _loc2_;
            _loc1_++;
         }
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[396,310,150,100],[711,310,150,100]],12021,this.triggerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[1190,310,150,100],[1500,310,150,100]],12022,this.triggerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(301,480),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1004,480),1,new Rectangle(0,0,1920,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2286,480),1);
         this._pTrigger1.setBlock(false,false,false,true);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this.activeFailBOSS;
         this._pTrigger0.start();
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function activeTrap() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = Math.random() * 5 + 3;
         var _loc2_:Array = this._traps.slice();
         var _loc4_:int = 0;
         while(_loc4_ < _loc1_)
         {
            _loc3_ = Math.random() * _loc2_.length;
            _loc2_[_loc3_].showTuCi();
            _loc2_.splice(_loc3_,1);
            _loc4_++;
         }
      }
      
      private function addFailBOSS() : void
      {
         this._failBoss = new BOSSTuoLong();
         this._failBoss.x = 2640;
         this._failBoss.y = 270;
         this._failBoss.initData(30171,30171);
         this._failBoss.target = _mission.onePlayer;
         add(this._failBoss);
         this._failBoss.onRole.add(this.listenDead);
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSZhenTuoLong();
         _boss.x = 2640;
         _boss.y = 270;
         _boss.initData(30181,30181,54);
         (_boss as BOSSZhenTuoLong).enemyList = this._enemyArgs.slice();
         _boss.target = _mission.onePlayer;
         add(_boss);
         this._failBoss.kill = true;
         startBossBattle();
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,_boss.x,_boss.y);
      }
      
      private function activeFailBOSS() : void
      {
         setHeroEnable(true);
         this._failBoss.activeEnemy(false);
         EffectManager.instance().playBGM("BGM_BATTLE0");
      }
      
      private function listenDead(param1:String) : void
      {
         if(param1 != "onDead" || HeroManager.instance().checkAllDead())
         {
            return;
         }
         this._failPos = new Point(this._failBoss.x,this._failBoss.y);
         StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(680,87),"嗷—————我生气了！","BODY_TUO_LONG",this.addBOSS,2,false);
         EffectManager.instance().playBGM("BGM_BATTLE2");
      }
      
      override protected function handlerWin() : void
      {
         this._trapTimer.pause();
         TaskProxy.instance().setComplete(11015);
         super.handlerWin();
      }
      
      override public function destroy() : void
      {
         this._failBoss = null;
         this._trapTimer.destroy();
         this._trapTimer = null;
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         super.destroy();
      }
   }
}

