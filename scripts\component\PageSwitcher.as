package component
{
   import flash.events.MouseEvent;
   import utils.FontUtil;
   import utils.MethodUtil;
   
   public class PageSwitcher
   {
      public var curPage:int;
      
      public var showTxt:Boolean = true;
      
      private var totalPage:int;
      
      private var _allData:Array;
      
      private var pageData:Array;
      
      private var pageNum:int;
      
      private var func:Function;
      
      private var skin:Object;
      
      public function PageSwitcher(param1:int, param2:Function, param3:Object)
      {
         super();
         this.pageNum = param1;
         this.func = param2;
         this.skin = param3;
      }
      
      private function init() : void
      {
         if(this.showTxt)
         {
            this.skin.visible = true;
         }
         this.skin.btnPrev.addEventListener(MouseEvent.CLICK,this.onPrev,false,0,true);
         this.skin.btnNext.addEventListener(MouseEvent.CLICK,this.onNext,false,0,true);
      }
      
      private function onNext(param1:MouseEvent) : void
      {
         ++this.curPage;
         this.updateBtn();
         this.updateData();
         this.func();
      }
      
      private function onPrev(param1:MouseEvent) : void
      {
         --this.curPage;
         this.updateBtn();
         this.updateData();
         this.func();
      }
      
      private function updateBtn() : void
      {
         if(this.totalPage == 1)
         {
            MethodUtil.enableBtn(this.skin.btnPrev,false);
            MethodUtil.enableBtn(this.skin.btnNext,false);
         }
         else
         {
            MethodUtil.enableBtn(this.skin.btnPrev,this.curPage > 1);
            MethodUtil.enableBtn(this.skin.btnNext,this.curPage < this.totalPage);
         }
         if(this.skin.txtPage != null)
         {
            FontUtil.setText(this.skin.txtPage,this.curPage + "/" + this.totalPage);
         }
      }
      
      private function updateData() : void
      {
         var _loc1_:int = (this.curPage - 1) * this.pageNum;
         var _loc2_:int = Math.min(this.curPage * this.pageNum,this._allData.length);
         this.pageData = this._allData.slice(_loc1_,_loc2_);
      }
      
      public function gotoPage(param1:int) : void
      {
         this.curPage = Math.min(param1,this.totalPage);
         this.updateBtn();
         this.updateData();
         this.func();
      }
      
      public function refresh() : void
      {
         this.curPage = Math.min(this.curPage,this.totalPage);
         this.updateBtn();
         this.updateData();
         this.func();
      }
      
      public function del(param1:*) : void
      {
         var _loc2_:int = int(this._allData.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._allData.splice(_loc2_,1);
         }
         this.refresh();
      }
      
      public function add(param1:*) : void
      {
         var _loc2_:int = int(this._allData.indexOf(param1));
         if(_loc2_ == -1)
         {
            this._allData.push(param1);
         }
         this.refresh();
      }
      
      public function get curData() : Array
      {
         return this.pageData;
      }
      
      public function set data(param1:Array) : void
      {
         this._allData = param1;
         this.curPage = 1;
         this.totalPage = Math.ceil(this._allData.length / this.pageNum);
         if(this.totalPage == 0)
         {
            this.totalPage = 1;
         }
         this.updateData();
         this.updateBtn();
         this.func();
         this.init();
         if(!this.showTxt)
         {
            this.skin.visible = this._allData.length > this.pageNum;
         }
      }
      
      public function get length() : int
      {
         if(!this._allData)
         {
            return 0;
         }
         return this._allData.length;
      }
      
      public function get allData() : Array
      {
         return this._allData;
      }
      
      public function destroy() : void
      {
         this.skin.btnPrev.removeEventListener(MouseEvent.CLICK,this.onPrev);
         this.skin.btnNext.removeEventListener(MouseEvent.CLICK,this.onNext);
         this.skin = null;
      }
   }
}

