package mogames.gameData.fuben.daily
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.mall.vo.MallMoneyVO;
   
   public class DailyFBVO
   {
      public var id:Sint;
      
      public var frame:int;
      
      public var name:String;
      
      public var rewards:Array;
      
      public function DailyFBVO(param1:int, param2:int, param3:String, param4:Array)
      {
         super();
         this.id = new Sint(param1);
         this.frame = param2;
         this.name = param3;
         this.rewards = param4;
      }
      
      public function get mallVO() : MallMoneyVO
      {
         return null;
      }
   }
}

