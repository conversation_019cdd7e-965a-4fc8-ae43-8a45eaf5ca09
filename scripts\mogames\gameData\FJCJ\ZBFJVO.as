package mogames.gameData.FJCJ
{
   import file.GoodConfig;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class ZBFJVO
   {
      public var id:Sint;
      
      public var result:Sint;
      
      public var needGold:Sint;
      
      public function ZBFJVO(param1:int, param2:int, param3:int)
      {
         super();
         this.id = new Sint(param1);
         this.result = new Sint(param2);
         this.needGold = new Sint(param3);
      }
      
      public function useStuff() : void
      {
         if(MasterProxy.instance().gameGold.v < this.needGold.v)
         {
            return;
         }
         MasterProxy.instance().changeGold(-this.needGold.v);
      }
      
      public function get constGood() : ConstGoodVO
      {
         return GoodConfig.instance().findConstGood(this.result.v);
      }
      
      public function get newGood() : GameGoodVO
      {
         return GoodConfig.instance().newGood(this.result.v);
      }
   }
}

