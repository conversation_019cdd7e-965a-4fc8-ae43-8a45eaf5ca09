package mogames.gameData.heroSkill.sunwukong
{
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.role.HeroGameVO;
   
   public class SkillHYJJVO extends HurtSkillVO
   {
      public function SkillHYJJVO(param1:int, param2:HeroGameVO, param3:int = 0)
      {
         super(param1,param2,param3);
      }
      
      override protected function updateNeedMP() : void
      {
         needMP.v = countVO.baseMP.v + int(level.v * 0.33);
      }
   }
}

