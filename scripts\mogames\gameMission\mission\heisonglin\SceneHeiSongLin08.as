package mogames.gameMission.mission.heisonglin
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.StoneTrap;
   import mogames.gameRole.enemy.BOSSHuangFengGuai;
   
   public class SceneHeiSongLin08 extends BossScene
   {
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      public function SceneHeiSongLin08(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [StoneTrap];
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this.initLeftEnemies();
         this.initRightEnemies();
      }
      
      private function initRightEnemies() : void
      {
         if(_mission.hasMark("enemy8082"))
         {
            return;
         }
         this._eTrigger1 = new EnemyTrigger(_mission,[[1156,370,150,100],[1730,90,150,100]],8082,null);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1520,470),1,new Rectangle(960,0,960,600));
         this._pTrigger1.setBlock(false,false,true,false);
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger1.okFunc = this.okFunc1;
         this._pTrigger1.start();
      }
      
      private function initLeftEnemies() : void
      {
         if(_mission.hasMark("enemy8081"))
         {
            return;
         }
         this._eTrigger0 = new EnemyTrigger(_mission,[[206,150,150,100],[507,370,150,100]],8081,unlock);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,470),1,new Rectangle(0,0,1400,600));
         this._pTrigger0.setBlock(false,false,false,true);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger0.okFunc = this.okFunc0;
         this._pTrigger0.start();
      }
      
      private function okFunc1() : void
      {
         this.startBOSS();
         _mission.setMark("enemy8082");
      }
      
      private function okFunc0() : void
      {
         _mission.setMark("enemy8081");
      }
      
      private function startBOSS() : void
      {
         this.addBOSS();
         startBossBattle("BGM_BATTLE1");
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHuangFengGuai();
         _boss.x = 1340;
         _boss.y = 420;
         _boss.initData(30043,30043,30);
         _boss.target = _mission.curTarget;
         add(_boss);
         _dropRect = new Rectangle(1276,330,370,140);
      }
      
      override protected function handlerWin() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
         _boss.kill = true;
         unlock();
         EffectManager.instance().playBGM("BGM_SUNSHINE");
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         this._eTrigger0 = null;
         this._pTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger1 = null;
         super.destroy();
      }
   }
}

