package mogames.gameData.drop.vo
{
   import file.GoodConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.GameEquipVO;
   import mogames.gameData.good.GameGoodVO;
   import utils.MathUtil;
   
   public class EquipDropVO extends BaseDropVO
   {
      public var quality:Sint;
      
      private var _maxATT:Sint;
      
      private var _maxVALUE:Sint;
      
      public function EquipDropVO(param1:int, param2:int = 0, param3:int = 0, param4:int = 0, param5:int = 0)
      {
         super(param1,1,param3);
         this.quality = new Sint(param2);
         this._maxATT = new Sint(param4);
         this._maxVALUE = new Sint(param5);
      }
      
      override public function get isDrop() : Boolean
      {
         var _loc1_:int = _per.v;
         if(MasterProxy.instance().isVIP)
         {
            _loc1_ += MasterProxy.instance().gameVIP.v * 40;
         }
         return MathUtil.checkOdds(_loc1_);
      }
      
      public function get maxATT() : Boolean
      {
         return this._maxATT.v != ConstData.DATA_NUM0.v;
      }
      
      public function get maxVALUE() : Boolean
      {
         return this._maxVALUE.v != ConstData.DATA_NUM0.v;
      }
      
      override public function newGood() : GameGoodVO
      {
         var _loc1_:GameEquipVO = GoodConfig.instance().newGood(id.v) as GameEquipVO;
         _loc1_.initNewEquip(this.quality.v,this.maxATT,this.maxVALUE);
         return _loc1_;
      }
   }
}

