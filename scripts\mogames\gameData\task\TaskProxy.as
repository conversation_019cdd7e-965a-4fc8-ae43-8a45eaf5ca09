package mogames.gameData.task
{
   import file.TaskConfig;
   import file.TaskDailyConfig;
   import mogames.Layers;
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.task.vo.BaseTask;
   import mogames.gameData.task.vo.EnemyTask;
   import mogames.gameData.task.vo.GoodTask;
   import mogames.gameEffect.EffectManager;
   import mogames.gamePKG.PKGManager;
   
   public class TaskProxy
   {
      private static var _instance:TaskProxy;
      
      private var allTasks:Array;
      
      public var taskHandler:TaskHandler;
      
      public function TaskProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.taskHandler = new TaskHandler();
         this.startNew();
      }
      
      public static function instance() : TaskProxy
      {
         if(!_instance)
         {
            _instance = new TaskProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.allTasks = [];
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:* = null;
         var _loc4_:Array = null;
         var _loc5_:BaseTask = null;
         this.startNew();
         if(!param1 || param1 == "")
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc3_ in _loc2_)
         {
            _loc4_ = _loc3_.split("H");
            _loc5_ = TaskConfig.instance().newTask(int(_loc4_[0]));
            if(_loc5_)
            {
               _loc5_.loadData = _loc4_;
               this.allTasks.push(_loc5_);
            }
         }
      }
      
      public function get saveData() : String
      {
         var _loc2_:BaseTask = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.allTasks)
         {
            if(_loc2_)
            {
               _loc1_.push(_loc2_.saveData);
            }
         }
         return _loc1_.join("T");
      }
      
      public function dailyRefresh() : void
      {
         if(!FlagProxy.instance().isComplete(1001))
         {
            return;
         }
         this.refreshDaily();
         PKGManager.saveNoMulit();
      }
      
      public function refreshDaily() : void
      {
         var _loc2_:Sint = null;
         this.removeDailyTask();
         var _loc1_:Array = TaskDailyConfig.instance().randomDaily(5);
         for each(_loc2_ in _loc1_)
         {
            this.addTask(_loc2_.v,false,true);
         }
      }
      
      public function resetDaily() : void
      {
         var _loc2_:BaseTask = null;
         var _loc1_:Array = this.getTabTasks(ConstData.TASK_TAB_DAILY);
         for each(_loc2_ in _loc1_)
         {
            _loc2_.reset();
         }
      }
      
      public function addTask(param1:int, param2:Boolean = true, param3:Boolean = false, param4:Boolean = false) : void
      {
         if(this.hasTask(param1) && !param3)
         {
            return;
         }
         var _loc5_:BaseTask = TaskConfig.instance().newTask(param1);
         if(param4)
         {
            this.allTasks.push(_loc5_);
         }
         else
         {
            this.allTasks.unshift(_loc5_);
         }
         if(param2)
         {
            PKGManager.saveData();
            EffectManager.instance().playAudio("NEW_FUNCTION");
            EffectManager.instance().addBMCEffect("EFFECT_NEW_TASK_CLIP",Layers.tipLayer,480,200,0.5,false);
         }
      }
      
      public function removeTask(param1:int) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = int(this.allTasks.length);
         while(_loc2_ < _loc3_)
         {
            if(this.allTasks[_loc2_].constTask.id.v == param1)
            {
               this.allTasks.splice(_loc2_,1);
               return;
            }
            _loc2_++;
         }
      }
      
      public function removeDailyTask() : void
      {
         var _loc1_:int = 0;
         while(_loc1_ < this.allTasks.length)
         {
            if(this.isDailyTask(this.allTasks[_loc1_].constTask.id.v))
            {
               this.allTasks.splice(_loc1_,1);
               _loc1_--;
            }
            _loc1_++;
         }
      }
      
      public function checkEnemyTask(param1:int) : void
      {
         var _loc2_:BaseTask = null;
         for each(_loc2_ in this.allTasks)
         {
            if(_loc2_ is EnemyTask)
            {
               (_loc2_ as EnemyTask).updateEnemy(param1);
            }
         }
      }
      
      public function checkGoodTask() : void
      {
         var _loc1_:BaseTask = null;
         for each(_loc1_ in this.allTasks)
         {
            if(_loc1_ is GoodTask)
            {
               _loc1_.checkComplete();
            }
         }
      }
      
      public function checkNeedGood(param1:int) : Boolean
      {
         var _loc2_:BaseTask = null;
         for each(_loc2_ in this.allTasks)
         {
            if(_loc2_ is GoodTask && (_loc2_ as GoodTask).checkNeed(param1))
            {
               return true;
            }
         }
         return false;
      }
      
      public function setComplete(param1:int, param2:Boolean = true) : void
      {
         var _loc3_:BaseTask = null;
         for each(_loc3_ in this.allTasks)
         {
            if(_loc3_.constTask.id.v == param1)
            {
               _loc3_.setComplete();
            }
         }
      }
      
      public function hasTask(param1:int) : Boolean
      {
         var _loc2_:BaseTask = null;
         for each(_loc2_ in this.allTasks)
         {
            if(_loc2_.constTask.id.v == param1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function isTaskFinish(param1:int) : Boolean
      {
         var _loc2_:BaseTask = null;
         for each(_loc2_ in this.allTasks)
         {
            if(_loc2_.constTask.id.v == param1 && _loc2_.isComplete)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get isComplete() : Boolean
      {
         var _loc1_:BaseTask = null;
         for each(_loc1_ in this.allTasks)
         {
            if(_loc1_.isComplete)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get isDailyGet() : Boolean
      {
         var _loc2_:BaseTask = null;
         var _loc1_:Array = this.getTabTasks(ConstData.TASK_TAB_DAILY);
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_.isGet)
            {
               return true;
            }
         }
         return false;
      }
      
      public function get isTaskTip() : Boolean
      {
         var _loc1_:Array = this.getTabTasks(ConstData.TASK_TAB_DAILY);
         if(_loc1_.length <= 0)
         {
            return this.isComplete;
         }
         return this.isComplete && !this.isDailyGet;
      }
      
      public function get hasUnComplete() : Boolean
      {
         var _loc1_:BaseTask = null;
         for each(_loc1_ in this.allTasks)
         {
            if(!_loc1_.isComplete)
            {
               return true;
            }
         }
         return false;
      }
      
      public function isDailyTask(param1:int) : Boolean
      {
         return TaskConfig.instance().findTaskTab(param1) == ConstData.TASK_TAB_DAILY;
      }
      
      public function getTabTasks(param1:String) : Array
      {
         var _loc3_:BaseTask = null;
         var _loc2_:Array = [];
         for each(_loc3_ in this.allTasks)
         {
            if(TaskConfig.instance().findTaskTab(_loc3_.constTask.id.v) == param1)
            {
               _loc2_.push(_loc3_);
            }
         }
         return _loc2_;
      }
   }
}

