package file
{
   import mogames.gameData.fabao.FBDZVO;
   import mogames.gameData.forge.NeedVO;
   
   public class FBDZConfig
   {
      private static var _instance:FBDZConfig;
      
      private var _dzVec:Vector.<FBDZVO>;
      
      public function FBDZConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : FBDZConfig
      {
         if(!_instance)
         {
            _instance = new FBDZConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._dzVec = new Vector.<FBDZVO>();
         this._dzVec.push(new FBDZVO(23006,23007,88888,[new NeedVO(13110,20),new NeedVO(13108,20),new NeedVO(13107,20)]));
      }
      
      public function findDZVO(param1:int) : FBDZVO
      {
         var _loc2_:FBDZVO = null;
         for each(_loc2_ in this._dzVec)
         {
            if(_loc2_.oldFB.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

