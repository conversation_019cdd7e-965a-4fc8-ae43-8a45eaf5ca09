package file
{
   import mogames.gameData.fuben.daily.DailyFBVO;
   
   public class DailyFBConfig
   {
      private static var _instance:DailyFBConfig;
      
      public var list:Array;
      
      public function DailyFBConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : DailyFBConfig
      {
         if(!_instance)
         {
            _instance = new DailyFBConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.list = [];
         this.list.push(new DailyFBVO(110,1,"试炼场",[10011]));
         this.list.push(new DailyFBVO(111,2,"寻宝",[10000,16702,16721,14008,14107]));
         this.list.push(new DailyFBVO(113,3,"黄泉路",[10000,14120,14121,14125]));
         this.list.push(new DailyFBVO(114,4,"赛太岁",[12007,13111,13112,13113,13114,13115]));
         this.list.push(new DailyFBVO(116,5,"冰麒麟",[16764,10025,10026,13407,13408,13409]));
         this.list.push(new DailyFBVO(117,6,"火凤凰",[16765,10031,10032,13410,13411,13412]));
         this.list.push(new DailyFBVO(118,7,"玄武",[16766,10034,13413,13414,13415]));
      }
   }
}

