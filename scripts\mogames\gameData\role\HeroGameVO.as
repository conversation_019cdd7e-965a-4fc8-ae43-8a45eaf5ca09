package mogames.gameData.role
{
   import com.hexagonstar.util.debug.Debug;
   import file.FashionSkillConfig;
   import file.GoodConfig;
   import file.HeroConfig;
   import flash.display.MovieClip;
   import mogames.AssetManager;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.ConstData;
   import mogames.gameData.MasterProxy;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.SkinInfo;
   import mogames.gameData.base.Snum;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.good.GameATTVO;
   import mogames.gameData.good.GameEquipVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.heroSkill.BaseSkillVO;
   import mogames.gameData.heroSkill.HurtSkillVO;
   import mogames.gameData.role.vo.HeroCountVO;
   import mogames.gameData.role.vo.RoleGameVO;
   import mogames.gamePKG.PKGManager;
   import org.osflash.signals.Signal;
   
   public class HeroGameVO extends RoleGameVO
   {
      public static const MAX_LEVEL:Sint = new Sint(57);
      
      public static const MAX_SP:Sint = new Sint(200);
      
      protected var _hpArg0:Sint = new Sint(40);
      
      protected var _mpArg0:Sint = new Sint(15);
      
      protected var _atkArg0:Sint = new Sint(5);
      
      public var equipHP:Sint;
      
      public var equipMP:Sint;
      
      public var equipATK:Sint;
      
      public var equipPDEF:Sint;
      
      public var equipMDEF:Sint;
      
      public var equipMISS:Sint;
      
      public var equipCRIT:Sint;
      
      public var equipLUCK:Sint;
      
      public var extraHP:Sint;
      
      public var extraMP:Sint;
      
      public var extraATK:Sint;
      
      public var extraPDEF:Sint;
      
      public var extraMDEF:Sint;
      
      public var perHP:Sint;
      
      public var perMP:Sint;
      
      public var perATK:Sint;
      
      public var perPDEF:Sint;
      
      public var perMDEF:Sint;
      
      public var level:Sint;
      
      public var curEXP:Sint;
      
      public var totalEXP:Sint;
      
      public var curSP:Sint;
      
      public var isFashion:int;
      
      public var wuqi:GameEquipVO;
      
      public var kuijia:GameEquipVO;
      
      public var shoushi:GameEquipVO;
      
      public var fabao:GameFabaoVO;
      
      public var shenbing:GameEquipVO;
      
      public var shizhuang:GameEquipVO;
      
      public var chenghao:GameATTVO;
      
      public var teamSequence:int = 0;
      
      public var keySkills:Array;
      
      public var allSkills:Array;
      
      protected var _danyaos:Vector.<HeroDanYaoVO>;
      
      protected var _countVO:HeroCountVO;
      
      public var onUpdate:Signal;
      
      private var _hparg0:Sint = new Sint(150);
      
      private var _hparg1:Sint = new Sint(40);
      
      private var _hparg2:Snum = new Snum(0.6);
      
      private var _mparg0:Sint = new Sint(100);
      
      private var _mparg1:Sint = new Sint(15);
      
      private var _mparg2:Snum = new Snum(0.3);
      
      private var _atkarg0:Sint = new Sint(10);
      
      private var _atkarg1:Sint = new Sint(5);
      
      private var _atkarg2:Snum = new Snum(0.8);
      
      private var _exparg0:Sint = new Sint(3);
      
      private var _exparg1:Sint = new Sint(80);
      
      private var _exparg2:Sint = new Sint(1);
      
      protected var lastLv:int;
      
      public function HeroGameVO(param1:int)
      {
         super(param1);
         this.onUpdate = new Signal();
         this.equipHP = new Sint();
         this.equipMP = new Sint();
         this.equipATK = new Sint();
         this.equipPDEF = new Sint();
         this.equipMDEF = new Sint();
         this.equipMISS = new Sint();
         this.equipCRIT = new Sint();
         this.equipLUCK = new Sint();
         this.extraHP = new Sint();
         this.extraMP = new Sint();
         this.extraATK = new Sint();
         this.extraPDEF = new Sint();
         this.extraMDEF = new Sint();
         this.perHP = new Sint();
         this.perMP = new Sint();
         this.perATK = new Sint();
         this.perPDEF = new Sint();
         this.perMDEF = new Sint();
         this.curSP = new Sint();
         this.curEXP = new Sint();
         this.totalEXP = new Sint();
         this.level = new Sint();
         this.isFashion = 1;
         this.initConstData(param1);
         this.setLevel(1);
         this.initBattleData();
         this._danyaos = new Vector.<HeroDanYaoVO>();
      }
      
      protected function initConstData(param1:int) : void
      {
         constVO = HeroConfig.instance().findConstRole(param1);
         assetVO = HeroConfig.instance().findRoleAsset(param1);
         this._countVO = HeroConfig.instance().findRoleCount(param1);
         baseCRIT.v = this._countVO.baseCRIT.v;
         baseMISS.v = this._countVO.baseMISS.v;
         baseMOVE.v = this._countVO.baseMOVE.v;
         baseRUN.v = this._countVO.baseRUN.v;
         baseLUCK.v = this._countVO.baseLUCK.v;
      }
      
      public function initBattleData() : void
      {
         curHP.v = totalHP.v;
         curMP.v = totalMP.v;
         this.curSP.v = 0;
         if(this.fabao != null)
         {
            this.fabao.initBattleData();
         }
      }
      
      public function cleanBattleData() : void
      {
         cleanBuff();
         this.cleanSkill();
         this.onUpdate.removeAll();
         if(this.fabao != null)
         {
            this.fabao.initBattleData();
         }
      }
      
      protected function cleanSkill() : void
      {
         skillHP.v = 0;
         skillMP.v = 0;
         skillATK.v = 0;
         skillPDEF.v = 0;
         skillMDEF.v = 0;
         skillCRIT.v = 0;
         skillMISS.v = 0;
         skillMOVE.v = 0;
         skillRUN.v = 0;
         this.updateAll();
      }
      
      public function setLevel(param1:int, param2:Boolean = true) : void
      {
         this.level.v = param1;
         baseHP.v = this._hparg0.v + this._hparg1.v * this.level.v * this._countVO.argHP.v * this._hparg2.v;
         baseMP.v = (this._mparg0.v + this._mparg1.v * this.level.v * this._countVO.argMP.v) * this._mparg2.v;
         baseATK.v = this._atkarg0.v + this._atkarg1.v * this.level.v * this._countVO.argATK.v * this._atkarg2.v;
         basePDEF.v = this._countVO.basePDEF.v + (this.level.v - 1) * this._countVO.argPDEF.v;
         baseMDEF.v = this._countVO.baseMDEF.v + (this.level.v - 1) * this._countVO.argMDEF.v;
         this.totalEXP.v = this.countTotalExp(this.level.v);
         if(param2)
         {
            this.updateAll();
         }
         this.updateSkill();
         EventManager.dispatchEvent(UIEvent.MENU_TIP_EVENT,{"type":"updateSkillBtn"});
      }
      
      protected function countTotalExp(param1:int) : int
      {
         if(param1 < 26)
         {
            return Math.pow(param1,this._exparg0.v) + this._exparg1.v * (param1 + this._exparg2.v);
         }
         return Math.pow(param1,3) * 1.5 + Math.pow(param1,2) * 5;
      }
      
      public function addEXP(param1:int, param2:Boolean = true) : void
      {
         this.lastLv = this.level.v;
         this.curEXP.v += param1;
         if(this.curEXP.v >= this.totalEXP.v && this.isMaxLevel)
         {
            this.curEXP.v = this.totalEXP.v;
         }
         else
         {
            while(this.curEXP.v >= this.totalEXP.v)
            {
               this.curEXP.v -= this.totalEXP.v;
               if(!this.isMaxLevel)
               {
                  this.setLevel(this.level.v + ConstData.DATA_NUM1.v);
                  FlagProxy.instance().changeValue(id.v * 100,1);
               }
            }
         }
         this.onUpdate.dispatch("updateEXP");
         if(this.lastLv != this.level.v)
         {
            this.onUpdate.dispatch("updateLevel");
         }
      }
      
      public function countExpNeed(param1:int) : int
      {
         var _loc2_:int = 0;
         var _loc3_:int = this.level.v;
         while(_loc3_ < param1)
         {
            _loc2_ += this.countTotalExp(_loc3_);
            _loc3_++;
         }
         return _loc2_ - this.curEXP.v;
      }
      
      public function refresh() : void
      {
         this.initBattleData();
         this.onUpdate.dispatch("updateMP");
         this.onUpdate.dispatch("updateHP");
      }
      
      public function handlerDanRevive(param1:Number, param2:Number) : void
      {
         curHP.v = totalHP.v * param1;
         curMP.v += totalMP.v * param2;
         if(curMP.v >= totalMP.v)
         {
            curMP.v = totalMP.v;
         }
         if(this.fabao != null)
         {
            this.fabao.initBattleData();
         }
         this.onUpdate.dispatch("updateMP");
         this.onUpdate.dispatch("updateHP");
      }
      
      public function revive() : void
      {
         curHP.v = 1;
         this.onUpdate.dispatch("onRevive");
         cleanBuff();
         this.cleanSkill();
      }
      
      public function addChengHao(param1:int) : void
      {
         if(param1 == 0)
         {
            this.chenghao.destroy();
            this.chenghao = null;
         }
         else
         {
            this.chenghao = GoodConfig.instance().newGood(param1) as GameATTVO;
         }
         this.updateEquip();
      }
      
      public function addEquip(param1:GameEquipVO, param2:Boolean = true) : void
      {
         var _loc3_:int = param1.constEquip.bodyPart;
         var _loc4_:GameEquipVO = this.findBodyEquip(_loc3_);
         this.removeEquip(_loc4_,false);
         this.setBodyEquip(param1,_loc3_);
         if(param2)
         {
            this.updateEquip();
         }
         BagProxy.instance().removeEquip(param1);
      }
      
      public function removeEquip(param1:GameEquipVO, param2:Boolean = true) : void
      {
         if(!param1)
         {
            return;
         }
         param1.owner = null;
         this.setBodyEquip(null,param1.constEquip.bodyPart);
         if(param2)
         {
            this.updateEquip();
         }
         BagProxy.instance().addDirect(param1 as GameGoodVO);
      }
      
      public function updateEquip(param1:Boolean = true) : void
      {
         var _loc3_:GameEquipVO = null;
         this.equipHP.v = 0;
         this.equipMP.v = 0;
         this.equipATK.v = 0;
         this.equipPDEF.v = 0;
         this.equipMDEF.v = 0;
         this.equipMISS.v = 0;
         this.equipCRIT.v = 0;
         this.equipLUCK.v = 0;
         this.perHP.v = 0;
         this.perMP.v = 0;
         this.perATK.v = 0;
         this.perPDEF.v = 0;
         this.perMDEF.v = 0;
         var _loc2_:Array = this.getEquipList();
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_)
            {
               _loc3_.addEquipATT();
            }
         }
         if(this.chenghao)
         {
            this.chenghao.addEquipATT(this);
         }
         if(param1)
         {
            this.updateAll();
         }
         this.updateSkill();
      }
      
      override public function changeHP(param1:int) : void
      {
         super.changeHP(param1);
         this.onUpdate.dispatch("updateHP");
         if(isDead())
         {
            this.onUpdate.dispatch("onDead");
         }
      }
      
      override public function changeMP(param1:int) : void
      {
         super.changeMP(param1);
         this.onUpdate.dispatch("updateMP");
      }
      
      public function addSP(param1:int) : void
      {
         if(this.isMaxSP || this.isSPBuff)
         {
            return;
         }
         this.curSP.v += param1;
         if(this.curSP.v >= MAX_SP.v)
         {
            this.curSP.v = MAX_SP.v;
         }
         this.onUpdate.dispatch("updateSP");
      }
      
      public function cleanSP() : void
      {
         this.curSP.v = ConstData.DATA_NUM0.v;
         this.onUpdate.dispatch("updateSP");
      }
      
      public function eatDanYao(param1:int, param2:int = 1, param3:Boolean = true) : void
      {
         var _loc4_:HeroDanYaoVO = null;
         var _loc5_:HeroDanYaoVO = null;
         for each(_loc4_ in this._danyaos)
         {
            if(_loc4_ && _loc4_.constVO.id.v == param1)
            {
               _loc4_.changeValue(param2);
               if(param3)
               {
                  this.updateDanYao();
               }
               return;
            }
         }
         _loc5_ = new HeroDanYaoVO(param1);
         _loc5_.owner = this;
         _loc5_.changeValue(param2);
         this._danyaos.push(_loc5_);
         if(param3)
         {
            this.updateDanYao();
         }
      }
      
      public function updateDanYao(param1:Boolean = true) : void
      {
         var _loc2_:HeroDanYaoVO = null;
         this.extraHP.v = 0;
         this.extraMP.v = 0;
         this.extraATK.v = 0;
         for each(_loc2_ in this._danyaos)
         {
            if(_loc2_)
            {
               _loc2_.addATT();
            }
         }
         if(param1)
         {
            this.updateAll();
         }
      }
      
      public function updateSkill() : void
      {
         var _loc1_:BaseSkillVO = null;
         for each(_loc1_ in this.allSkills)
         {
            if(_loc1_ is HurtSkillVO)
            {
               (_loc1_ as HurtSkillVO).updateHurt();
            }
         }
      }
      
      override public function updateAll() : void
      {
         super.updateAll();
         this.updateLUCK();
      }
      
      public function get ownHP() : int
      {
         var _loc1_:int = baseHP.v + this.equipHP.v + this.extraHP.v;
         if(this.perHP.v != 0)
         {
            _loc1_ += _loc1_ * this.perHP.v * ConstData.DATA_NUM001.v;
         }
         if(MasterProxy.instance().isVIP)
         {
            _loc1_ += _loc1_ * MasterProxy.instance().gameVIP.v * 0.08;
         }
         return _loc1_;
      }
      
      public function get ownMP() : int
      {
         var _loc1_:int = baseMP.v + this.equipMP.v + this.extraMP.v;
         if(this.perMP.v != 0)
         {
            _loc1_ += _loc1_ * this.perMP.v * ConstData.DATA_NUM001.v;
         }
         return _loc1_;
      }
      
      public function get ownATK() : int
      {
         var _loc1_:int = baseATK.v + this.equipATK.v + this.extraATK.v;
         if(this.perATK.v != 0)
         {
            _loc1_ += _loc1_ * this.perATK.v * ConstData.DATA_NUM001.v;
         }
         return _loc1_;
      }
      
      public function get ownPDEF() : int
      {
         var _loc1_:int = basePDEF.v + this.equipPDEF.v + this.extraPDEF.v;
         if(this.perPDEF.v != 0)
         {
            _loc1_ += _loc1_ * this.perPDEF.v * ConstData.DATA_NUM001.v;
         }
         if(MasterProxy.instance().isVIP)
         {
            _loc1_ += _loc1_ * MasterProxy.instance().gameVIP.v * 0.04;
         }
         return _loc1_;
      }
      
      public function get ownMDEF() : int
      {
         var _loc1_:int = baseMDEF.v + this.equipMDEF.v + this.extraMDEF.v;
         if(this.perMDEF.v != 0)
         {
            _loc1_ += _loc1_ * this.perMDEF.v * ConstData.DATA_NUM001.v;
         }
         if(MasterProxy.instance().isVIP)
         {
            _loc1_ += _loc1_ * MasterProxy.instance().gameVIP.v * 0.04;
         }
         return _loc1_;
      }
      
      public function get ownMISS() : int
      {
         return baseMISS.v + this.equipMISS.v;
      }
      
      public function get ownCRIT() : int
      {
         return baseCRIT.v + this.equipCRIT.v;
      }
      
      public function get ownLUCK() : int
      {
         return baseLUCK.v + this.equipLUCK.v;
      }
      
      override public function updateHP() : void
      {
         totalHP.v = this.ownHP + skillHP.v;
      }
      
      override public function updateMP() : void
      {
         totalMP.v = this.ownMP + skillMP.v;
      }
      
      override public function updateATK() : void
      {
         totalATK.v = this.ownATK + skillATK.v;
      }
      
      override public function updatePDEF() : void
      {
         totalPDEF.v = this.ownPDEF + skillPDEF.v;
      }
      
      override public function updateMDEF() : void
      {
         totalMDEF.v = this.ownMDEF + skillMDEF.v;
      }
      
      override public function updateMISS() : void
      {
         totalMISS.v = this.ownMISS + skillMISS.v;
      }
      
      override public function updateCRIT() : void
      {
         totalCRIT.v = this.ownCRIT + skillCRIT.v;
      }
      
      override public function updateLUCK() : void
      {
         totalLUCK.v = this.ownLUCK + skillLUCK.v;
      }
      
      public function updateKeySkills() : void
      {
         this.keySkills = this.allSkills.slice();
         this.keySkills.sortOn("keyIndex",Array.NUMERIC);
      }
      
      public function findBodyEquip(param1:int) : GameEquipVO
      {
         var _loc3_:GameEquipVO = null;
         var _loc2_:Array = this.getEquipList();
         for each(_loc3_ in _loc2_)
         {
            if(Boolean(_loc3_) && _loc3_.constEquip.bodyPart == param1)
            {
               return _loc3_;
            }
         }
         return null;
      }
      
      public function setBodyEquip(param1:GameEquipVO, param2:int) : void
      {
         if(param1)
         {
            param1.owner = this;
         }
         switch(param2)
         {
            case 0:
               this.wuqi = param1;
               break;
            case 1:
               this.kuijia = param1;
               break;
            case 2:
               this.shoushi = param1;
               break;
            case 3:
               this.fabao = param1 as GameFabaoVO;
               break;
            case 4:
               this.shenbing = param1;
               break;
            case 5:
               this.shizhuang = param1;
         }
      }
      
      public function danNum(param1:int) : int
      {
         var _loc2_:HeroDanYaoVO = null;
         for each(_loc2_ in this._danyaos)
         {
            if(_loc2_.constVO.id.v == param1)
            {
               return _loc2_.total.v;
            }
         }
         return 0;
      }
      
      public function checkHasDan(param1:int) : Boolean
      {
         var _loc2_:HeroDanYaoVO = null;
         for each(_loc2_ in this._danyaos)
         {
            if(_loc2_.constVO.id.v == param1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function findSkill(param1:int) : BaseSkillVO
      {
         var _loc2_:BaseSkillVO = null;
         for each(_loc2_ in this.allSkills)
         {
            if(_loc2_.constVO.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get isLearnSkill() : Boolean
      {
         var _loc1_:BaseSkillVO = null;
         for each(_loc1_ in this.allSkills)
         {
            if(_loc1_.canLearn)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getEquipList() : Array
      {
         return [this.wuqi,this.kuijia,this.shoushi,this.fabao,this.shenbing,this.shizhuang];
      }
      
      public function get battleRate() : int
      {
         return (this.ownHP - 150) / this._countVO.argHP.v + (this.ownMP / 0.3 - 100) / this._countVO.argMP.v * 0.5 + (this.ownATK - 10) / this._countVO.argATK.v * 3 + (this.ownPDEF - this._countVO.basePDEF.v) / this._countVO.argPDEF.v * 10 + (this.ownMDEF - this._countVO.baseMDEF.v) / this._countVO.argMDEF.v * 10 + this.ownCRIT / this._countVO.baseCRIT.v * 100 + this.ownMISS / this._countVO.baseMISS.v * 100 + this.ownLUCK * 5;
      }
      
      public function get isMaxLevel() : Boolean
      {
         return this.level.v >= MAX_LEVEL.v;
      }
      
      public function get isMaxSP() : Boolean
      {
         return this.curSP.v >= MAX_SP.v;
      }
      
      public function get isSPBuff() : Boolean
      {
         return hasBuff(2000);
      }
      
      public function get chenghaoSkin() : Vector.<SkinInfo>
      {
         if(this.chenghao)
         {
            return AssetManager.findAnimation(this.chenghao.constVO.iname);
         }
         return null;
      }
      
      public function get bodySkin() : MovieClip
      {
         if(this.isFashion && this.shizhuang && this.shizhuang.constEquip.mcSkin != "")
         {
            return AssetManager.newMCRes(this.shizhuang.constEquip.mcSkin);
         }
         if(Boolean(this.kuijia) && this.kuijia.constEquip.mcSkin != "")
         {
            return AssetManager.newMCRes(this.kuijia.constEquip.mcSkin);
         }
         return AssetManager.newMCRes(assetVO.clothSkin);
      }
      
      public function get weaponSkin() : MovieClip
      {
         if(this.isFashion && this.shenbing && this.shenbing.constEquip.mcSkin != "")
         {
            return AssetManager.newMCRes(this.shenbing.constEquip.mcSkin);
         }
         if(Boolean(this.wuqi) && this.wuqi.constEquip.mcSkin != "")
         {
            return AssetManager.newMCRes(this.wuqi.constEquip.mcSkin);
         }
         return AssetManager.newMCRes(assetVO.wuqiSkin);
      }
      
      public function get weaponSkinBack() : MovieClip
      {
         if(this.isFashion && this.shenbing && this.shenbing.constEquip.mcSkin != "")
         {
            return AssetManager.newMCRes(this.shenbing.constEquip.mcSkin + "_BACK");
         }
         if(Boolean(this.wuqi) && this.wuqi.constEquip.mcSkin != "")
         {
            return AssetManager.newMCRes(this.wuqi.constEquip.mcSkin + "_BACK");
         }
         return AssetManager.newMCRes(assetVO.wuqiSkin + "_BACK");
      }
      
      public function get skillSkin() : MovieClip
      {
         return AssetManager.newMCRes(assetVO.skillSkin);
      }
      
      public function get isPig() : Boolean
      {
         return id.v == 1003;
      }
      
      public function get chenghaoID() : int
      {
         if(this.chenghao)
         {
            return this.chenghao.constVO.id.v;
         }
         return 0;
      }
      
      public function get fashionSkill() : int
      {
         if(!this.shenbing || !this.shizhuang)
         {
            return 0;
         }
         return FashionSkillConfig.instance().findSkill(this.shenbing.constEquip.id.v,this.shizhuang.constEquip.id.v);
      }
      
      public function parseLoadData(param1:String) : void
      {
         var _loc2_:Array = param1.split("H");
         this.level.v = int(_loc2_[0]);
         this.curEXP.v = int(_loc2_[1]);
         this.isFashion = int(_loc2_[2]);
         if(int(_loc2_[3]))
         {
            this.chenghao = GoodConfig.instance().newGood(int(_loc2_[3])) as GameATTVO;
         }
         this.parseEquipData(_loc2_[4]);
         this.parseSkillData(_loc2_[5]);
         this.teamSequence = int(_loc2_[6]);
         if(_loc2_[7])
         {
            this.parseExtraData(_loc2_[7]);
         }
         this.setLevel(this.level.v,false);
         this.updateEquip(false);
         this.updateDanYao(false);
         this.updateAll();
      }
      
      public function collectSaveData() : String
      {
         var _loc1_:Array = [];
         _loc1_[0] = this.level.v;
         _loc1_[1] = this.curEXP.v;
         _loc1_[2] = this.isFashion;
         _loc1_[3] = this.chenghao == null ? "None" : this.chenghao.constVO.id.v + "";
         _loc1_[4] = this.collectEquipData();
         _loc1_[5] = this.collectSkillData();
         _loc1_[6] = this.teamSequence;
         _loc1_[7] = this.collectExtra();
         return _loc1_.join("H");
      }
      
      protected function parseSkillData(param1:String) : void
      {
         var _loc3_:* = null;
         var _loc4_:Array = null;
         var _loc2_:Array = param1.split("S");
         for each(_loc3_ in _loc2_)
         {
            _loc4_ = _loc3_.split("A");
            this.findSkill(int(_loc4_[0])).parseLoadData(_loc4_);
         }
      }
      
      protected function parseEquipData(param1:String) : void
      {
         var _loc3_:* = null;
         var _loc4_:Array = null;
         var _loc5_:GameEquipVO = null;
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("E");
         for each(_loc3_ in _loc2_)
         {
            _loc4_ = _loc3_.split("B");
            _loc5_ = GoodConfig.instance().newGood(int(_loc4_[0])) as GameEquipVO;
            if(!_loc5_)
            {
               Debug.trace("未解析出的装备ID：" + int(_loc4_[0]));
            }
            else
            {
               _loc5_.parseLoadData(_loc4_);
               this.setBodyEquip(_loc5_,_loc5_.constEquip.bodyPart);
               if(_loc5_.constEquip.mcURL != "")
               {
                  PKGManager.addURL(_loc5_.constEquip.mcURL);
               }
            }
         }
      }
      
      protected function parseExtraData(param1:String) : void
      {
         var _loc5_:Array = null;
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("X");
         var _loc3_:int = 0;
         var _loc4_:int = int(_loc2_.length);
         while(_loc3_ < _loc4_)
         {
            _loc5_ = _loc2_[_loc3_].split("Y");
            this.eatDanYao(int(_loc5_[0]),int(_loc5_[1]),false);
            _loc3_++;
         }
      }
      
      protected function collectExtra() : String
      {
         var _loc2_:HeroDanYaoVO = null;
         var _loc3_:String = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._danyaos)
         {
            if(_loc2_)
            {
               _loc3_ = _loc2_.constVO.id.v + "Y" + _loc2_.total.v;
               _loc1_.push(_loc3_);
            }
         }
         return _loc1_.join("X");
      }
      
      protected function collectSkillData() : String
      {
         var _loc2_:BaseSkillVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.allSkills)
         {
            _loc1_.push(_loc2_.collectSaveData());
         }
         return _loc1_.join("S");
      }
      
      public function collectEquipData() : String
      {
         var _loc3_:GameEquipVO = null;
         var _loc1_:Array = [];
         var _loc2_:Array = this.getEquipList();
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_)
            {
               _loc1_.push(_loc3_.collectSaveData());
            }
         }
         return _loc1_.join("E");
      }
   }
}

