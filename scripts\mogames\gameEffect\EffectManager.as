package mogames.gameEffect
{
   import com.greensock.TweenLite;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.ColorTransform;
   import flash.geom.Point;
   import flash.utils.setTimeout;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.gameData.ConstData;
   import mogames.gameEffect.co.FallBitmap;
   import mogames.gameEffect.co.SpreadEffect;
   import mogames.gameObj.display.BMCSprite;
   import mogames.gameObj.display.CitrusMC;
   import mogames.gameObj.display.PicSprite;
   import mogames.gamePool.PSFactory;
   import utils.FontUtil;
   import utils.MethodUtil;
   
   public class EffectManager
   {
      private static var _instance:EffectManager;
      
      private var _lastBGM:String;
      
      private var _headMC:MovieClip;
      
      private var _goNames:Array = ["EFFECT_GO_UP_CLIP","EFFECT_GO_DOWN_CLIP","EFFECT_GO_LEFT_CLIP","EFFECT_GO_RIGHT_CLIP"];
      
      private var _goPoses:Array = [[480,90],[480,520],[100,300],[850,300]];
      
      public function EffectManager()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this._lastBGM = "";
         this._headMC = AssetManager.newMCRes("EFFECT_HEAD_WORD_CLIP");
      }
      
      public static function instance() : EffectManager
      {
         if(!_instance)
         {
            _instance = new EffectManager();
         }
         return _instance;
      }
      
      public function addHeadWord(param1:String, param2:int, param3:int, param4:int = -1, param5:int = 1) : void
      {
         var data:BitmapData = null;
         var pic:PicSprite = null;
         var destroy:Function = null;
         var value:String = param1;
         var posX:int = param2;
         var posY:int = param3;
         var dir:int = param4;
         var $scale:int = param5;
         destroy = function():void
         {
            data.dispose();
            pic.recyle();
         };
         FontUtil.setHtml(this._headMC.txtWord,value);
         this._headMC.width *= $scale;
         this._headMC.height *= $scale;
         data = new BitmapData(this._headMC.width,this._headMC.height,true,16777215);
         data.draw(this._headMC);
         pic = PSFactory.instance().newPicSprite();
         pic.init(data);
         pic.x = posX;
         pic.y = posY;
         Layers.ceEffectLayer.addChild(pic);
         TweenLite.to(pic,1.5,{
            "y":posY + 100 * dir,
            "alpha":0,
            "onComplete":destroy
         });
      }
      
      public function addBattleNum(param1:int, param2:int, param3:int, param4:int, param5:int) : void
      {
         var _loc6_:FallBitmap = FallFactory.instance().newFallBitmap();
         _loc6_.bitmapData = FallFactory.instance().newHurtNumber(param1,String(param2));
         _loc6_.x = param3;
         _loc6_.y = param4;
         _loc6_.start(param5);
         Layers.ceEffectLayer.addChild(_loc6_);
      }
      
      public function addFallEffect(param1:String, param2:int, param3:int, param4:int = 0) : void
      {
         var _loc5_:FallBitmap = FallFactory.instance().newFallBitmap();
         _loc5_.bitmapData = AssetManager.findPicRes(param1);
         _loc5_.x = param2;
         _loc5_.y = param3;
         _loc5_.start(param4);
         Layers.ceEffectLayer.addChild(_loc5_);
      }
      
      public function addSkinSpread(param1:int, param2:Point, param3:Array, param4:Number = 1) : void
      {
         var _loc5_:SpreadEffect = new SpreadEffect();
         _loc5_.addSkinSpread(param1,param2,param3,param4);
      }
      
      public function addCircleSpread(param1:int, param2:Point, param3:int = 16711680) : void
      {
         var _loc4_:SpreadEffect = new SpreadEffect();
         _loc4_.addCircleSpread(param1,param2,param3);
      }
      
      public function addMCEffect(param1:String, param2:DisplayObjectContainer, param3:int = 0, param4:int = 0, param5:String = "", param6:Boolean = false) : void
      {
         var cmc:CitrusMC = null;
         var destroy:Function = null;
         var name:String = param1;
         var container:DisplayObjectContainer = param2;
         var posX:int = param3;
         var posY:int = param4;
         var sound:String = param5;
         var canPause:Boolean = param6;
         destroy = function(param1:String):void
         {
            cmc.destroy();
         };
         var mc:MovieClip = AssetManager.newMCRes(name,posX,posY);
         if(!mc)
         {
            return;
         }
         cmc = new CitrusMC();
         cmc.canPause = canPause;
         cmc.setupMC(mc,destroy);
         cmc.changeAnimation(ConstData.MC_START,false);
         container.addChild(cmc);
         if(sound != "")
         {
            this.playAudio(sound);
         }
      }
      
      public function addMCEffect0(param1:MovieClip, param2:String = "", param3:Boolean = false) : void
      {
         var cmc:CitrusMC = null;
         var destroy:Function = null;
         var mc:MovieClip = param1;
         var sound:String = param2;
         var canPause:Boolean = param3;
         destroy = function(param1:String):void
         {
            cmc.destroy();
         };
         if(!mc)
         {
            return;
         }
         cmc = new CitrusMC();
         cmc.canPause = canPause;
         cmc.setupMC(mc,destroy);
         cmc.changeAnimation(ConstData.MC_START,false);
         Layers.effectLayer.addChild(cmc);
         if(sound != "")
         {
            this.playAudio(sound);
         }
      }
      
      public function addBMCEffect(param1:String, param2:DisplayObjectContainer, param3:int, param4:int, param5:Number = 0.5, param6:Boolean = true) : void
      {
         if(!param1 || param1 == "")
         {
            return;
         }
         var _loc7_:BMCSprite = BMCFactory.instance().newBMC();
         _loc7_.canPause = param6;
         _loc7_.x = param3;
         _loc7_.y = param4;
         _loc7_.setupSequence(AssetManager.findAnimation(param1));
         _loc7_.fpsRatio = param5;
         param2.addChild(_loc7_);
      }
      
      public function addFlyGold(param1:Object, param2:int, param3:int) : void
      {
         var targetX:int = 0;
         var targetY:int = 0;
         var pic:PicSprite = null;
         var func:Function = null;
         var target:Object = param1;
         var posX:int = param2;
         var posY:int = param3;
         func = function():void
         {
            TweenLite.to(pic,0.3,{
               "x":targetX,
               "y":targetY,
               "scaleX":0.5,
               "scaleY":0.5,
               "alpha":0,
               "onComplete":pic.recyle
            });
         };
         targetX = int(target.x);
         targetY = int(target.y);
         pic = PSFactory.instance().newPicSprite();
         pic.init(AssetManager.findPicRes("TONG_QIAN"));
         pic.x = posX;
         pic.y = posY;
         Layers.ceEffectLayer.addChild(pic);
         TweenLite.to(pic,0.2,{"y":posY - 100});
         TweenLite.delayedCall(0.2,func);
      }
      
      public function addGOEffect(param1:int, param2:int = 0, param3:int = 0) : void
      {
         if(param2 == 0)
         {
            param2 = int(this._goPoses[param1][0]);
         }
         if(param3 == 0)
         {
            param3 = int(this._goPoses[param1][1]);
         }
         var _loc4_:BMCSprite = BMCFactory.instance().newBMC();
         _loc4_.x = param2;
         _loc4_.y = param3;
         _loc4_.setupSequence(AssetManager.findAnimation(this._goNames[param1]),true);
         Layers.effectLayer.addChild(_loc4_);
         setTimeout(_loc4_.recyle,2000);
      }
      
      public function addPicFloatWord(param1:String, param2:int, param3:int) : void
      {
         var bs:PicSprite = null;
         var func:Function = null;
         var name:String = param1;
         var posX:int = param2;
         var posY:int = param3;
         func = function():void
         {
            TweenLite.to(bs,0.5,{
               "y":bs.y - 40,
               "alpha":0,
               "onComplete":bs.destroy
            });
         };
         bs = new PicSprite();
         bs.init(AssetManager.findPicRes(name));
         bs.x = posX;
         bs.y = posY;
         Layers.effectLayer.addChild(bs);
         setTimeout(func,1500);
      }
      
      public function addPicPopWord(param1:String, param2:int, param3:int, param4:Number = 0.5) : void
      {
         var bs:PicSprite = null;
         var destroy:Function = null;
         var name:String = param1;
         var posX:int = param2;
         var posY:int = param3;
         var value:Number = param4;
         destroy = function():void
         {
            TweenLite.to(bs,0.3,{
               "y":bs.y - 10,
               "alpha":0,
               "onComplete":bs.recyle
            });
         };
         bs = PSFactory.instance().newPicSprite();
         bs.init(AssetManager.findPicRes(name));
         bs.x = posX;
         bs.y = posY;
         bs.scaleX = 0.1;
         bs.scaleY = 0.1;
         bs.alpha = 0;
         Layers.ceEffectLayer.addChild(bs);
         TweenLite.to(bs,0.2,{
            "scaleX":1,
            "scaleY":1,
            "alpha":1
         });
         TweenLite.delayedCall(value,destroy);
      }
      
      public function addTransEffect() : void
      {
         var bitmap:Bitmap = null;
         var onDestroy:Function = null;
         onDestroy = function():void
         {
            MethodUtil.removeMe(bitmap);
            Layers.lockGame = false;
            Layers.ceLayer.input.keyboard.enabled = true;
         };
         Layers.lockGame = true;
         Layers.ceLayer.input.keyboard.enabled = false;
         bitmap = AssetManager.newBitmap("PIC_TRANS_CLIP",0,-7);
         Layers.topLayer.addChild(bitmap);
         TweenLite.to(bitmap,0.5,{
            "x":-1525,
            "onComplete":onDestroy
         });
      }
      
      public function addScreenEffect(param1:uint, param2:Number = 0.5) : void
      {
         var sp:Sprite = null;
         var destroy:Function = null;
         var color:uint = param1;
         var ms:Number = param2;
         destroy = function():void
         {
            MethodUtil.removeMe(sp);
            sp = null;
         };
         sp = MethodUtil.drawRect(Layers.gameW,Layers.gameH,1,color);
         Layers.topLayer.addChild(sp);
         TweenLite.to(sp,ms,{
            "alpha":0,
            "onComplete":destroy
         });
      }
      
      public function playBGM(param1:String) : void
      {
         if(this._lastBGM == param1 || param1 == "")
         {
            return;
         }
         if(this._lastBGM != "")
         {
            Layers.ceLayer.sound.stopSound(this._lastBGM);
         }
         Layers.ceLayer.sound.playSound(param1);
         this._lastBGM = param1;
      }
      
      public function playAudio(param1:String) : void
      {
         if(!param1 || param1 == "")
         {
            return;
         }
         Layers.ceLayer.sound.playSound(param1);
      }
      
      public function stopSound(param1:String) : void
      {
         Layers.ceLayer.sound.stopSound(param1);
      }
      
      public function stopAllSound() : void
      {
         Layers.ceLayer.sound.stopAllPlayingSounds();
         this._lastBGM = "";
      }
      
      public function addColor(param1:DisplayObject, param2:int, param3:int, param4:int) : void
      {
         var _loc5_:ColorTransform = new ColorTransform();
         _loc5_.redOffset = param2;
         _loc5_.greenOffset = param3;
         _loc5_.blueOffset = param4;
         param1.transform.colorTransform = _loc5_;
      }
      
      public function removeColor(param1:DisplayObject) : void
      {
         if(!param1)
         {
            return;
         }
         var _loc2_:ColorTransform = new ColorTransform();
         param1.transform.colorTransform = _loc2_;
      }
   }
}

