package mogames.gameMission.mission.npc.huaguoshan
{
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.HeroManager;
   import mogames.gameUI.mission.BattleHeroTopModule;
   import mogames.gameUI.mission.BattleMenuModule;
   import mogames.gameUI.mission.BossHPManager;
   import mogames.gameUI.mission.HeroHitModule;
   import mogames.gameUI.mission.HeroLeftModule;
   import mogames.gameUI.mission.HeroRightModule;
   import mogames.gameUI.prompt.MiniMsgMediator;
   
   public class HuaGuoShanMission extends BaseMission
   {
      public function HuaGuoShanMission()
      {
         super();
      }
      
      override protected function layoutUI() : void
      {
         HeroLeftModule.instance().init();
         if(HeroManager.isDouble)
         {
            HeroRightModule.instance().init();
         }
         BattleMenuModule.instance().init(null);
         BattleHeroTopModule.instance().init();
         HeroHitModule.instance().init();
         BossHPManager.instance().init();
      }
      
      override protected function handlerTouchDoor(param1:int, param2:int, param3:int) : void
      {
         if(param1 == 0)
         {
            throw new Error("未能找到场景！");
         }
         if(param1 == 9999)
         {
            quitMission();
            return;
         }
         EffectManager.instance().addTransEffect();
         _heroPos.x = param2;
         _heroPos.y = param3;
         changeScene(param1);
      }
      
      public function enterShuiLianDong() : void
      {
         var _loc2_:HeroGameVO = null;
         var _loc1_:Array = HeroProxy.instance().teamHero;
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_.level.v < 25)
            {
               MiniMsgMediator.instance().showAutoMsg("等级达到25级以上方可进入水帘洞！");
               return;
            }
         }
         this.handlerTouchDoor(11,100,390);
      }
      
      public function enterHuaGuoShan() : void
      {
         this.handlerTouchDoor(2,150,780);
      }
   }
}

