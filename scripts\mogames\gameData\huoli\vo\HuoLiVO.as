package mogames.gameData.huoli.vo
{
   import mogames.gameData.base.Sint;
   import utils.TxtUtil;
   
   public class HuoLiVO
   {
      public var id:Sint;
      
      public var cur:Sint;
      
      public var score:Sint;
      
      private var _total:Sint;
      
      private var _title:String;
      
      public function HuoLiVO(param1:int, param2:int, param3:int, param4:String)
      {
         super();
         this.id = new Sint(param1);
         this.score = new Sint(param2);
         this._total = new Sint(param3);
         this._title = param4;
         this.cur = new Sint();
      }
      
      public function addNum() : void
      {
         if(this.isComplete)
         {
            return;
         }
         this.cur.v += 1;
         if(this.cur.v >= this._total.v)
         {
            this.cur.v = this._total.v;
         }
      }
      
      public function get isComplete() : Bo<PERSON>an
      {
         return this.cur.v >= this._total.v;
      }
      
      public function get title() : String
      {
         if(this.isComplete)
         {
            return TxtUtil.setColor(this._title,"99ff00");
         }
         return TxtUtil.setColor(this._title,"FFFFFF");
      }
      
      public function get numStr() : String
      {
         if(this.isComplete)
         {
            return TxtUtil.setColor("（" + this.cur.v + "/" + this._total.v + " ）","99ff00");
         }
         return TxtUtil.setColor("（" + this.cur.v + "/" + this._total.v + " ）","FFFFFF");
      }
      
      public function get scoreStr() : String
      {
         return this.score.v + "点活跃度";
      }
      
      public function get saveData() : String
      {
         return [this.id.v,this.cur.v].join("H");
      }
   }
}

