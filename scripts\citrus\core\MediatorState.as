package citrus.core
{
   import citrus.datastructures.PoolObject;
   import citrus.objects.APhysicsObject;
   import citrus.physics.APhysicsEngine;
   import citrus.system.Component;
   import citrus.system.Entity;
   import citrus.system.components.ViewComponent;
   import citrus.view.ACitrusView;
   
   public final class MediatorState
   {
      private var _objects:Vector.<CitrusObject> = new Vector.<CitrusObject>();
      
      private var _poolObjects:Vector.<PoolObject> = new Vector.<PoolObject>();
      
      private var _view:ACitrusView;
      
      private var _istate:IState;
      
      private var _garbage:Array = [];
      
      private var _numObjects:uint = 0;
      
      public function MediatorState(param1:IState)
      {
         super();
         this._istate = param1;
      }
      
      public function destroy() : void
      {
         var _loc1_:PoolObject = null;
         var _loc2_:CitrusObject = null;
         for each(_loc1_ in this._poolObjects)
         {
            _loc1_.destroy();
         }
         this._poolObjects.length = 0;
         this._numObjects = this._objects.length;
         while(true)
         {
            _loc2_ = this._objects.pop();
            if(_loc2_ == null)
            {
               break;
            }
            this.removeImmediately(_loc2_);
         }
         this._numObjects = this._objects.length = 0;
         this._view.destroy();
         this._objects = null;
         this._poolObjects = null;
         this._view = null;
      }
      
      public function get view() : ACitrusView
      {
         return this._view;
      }
      
      public function set view(param1:ACitrusView) : void
      {
         this._view = param1;
      }
      
      public function update(param1:Number) : void
      {
         var _loc2_:CitrusObject = null;
         var _loc4_:CitrusObject = null;
         var _loc5_:PoolObject = null;
         this._numObjects = this._objects.length;
         var _loc3_:Number = 0;
         while(_loc3_ < this._numObjects)
         {
            _loc2_ = this._objects.shift();
            if(_loc2_.kill)
            {
               this._garbage.push(_loc2_);
            }
            else
            {
               this._objects.push(_loc2_);
               if(_loc2_.updateCallEnabled)
               {
                  _loc2_.update(param1);
               }
            }
            _loc3_++;
         }
         while(true)
         {
            _loc4_ = this._garbage.shift();
            if(_loc4_ == null)
            {
               break;
            }
            this.removeImmediately(_loc4_);
         }
         for each(_loc5_ in this._poolObjects)
         {
            _loc5_.updatePhysics(param1);
         }
         if(this._view)
         {
            this._view.update(param1);
         }
      }
      
      public function add(param1:CitrusObject) : CitrusObject
      {
         var _loc2_:CitrusObject = null;
         if(param1 is Entity)
         {
            throw new Error("Object named: " + param1.name + " is an entity and should be added to the state via addEntity method.");
         }
         for each(_loc2_ in this.objects)
         {
            if(param1 == _loc2_)
            {
               throw new Error(param1.name + " is already added to the state.");
            }
         }
         if(param1 is APhysicsObject)
         {
            (param1 as APhysicsObject).addPhysics();
         }
         if(param1 is APhysicsEngine)
         {
            this._objects.unshift(param1);
         }
         else
         {
            this._objects.push(param1);
         }
         this._view.addArt(param1);
         return param1;
      }
      
      public function addEntity(param1:Entity) : Entity
      {
         var _loc2_:CitrusObject = null;
         var _loc3_:Vector.<Component> = null;
         var _loc4_:ViewComponent = null;
         for each(_loc2_ in this.objects)
         {
            if(param1 == _loc2_)
            {
               throw new Error(param1.name + " is already added to the state.");
            }
         }
         this._objects.push(param1);
         _loc3_ = param1.lookupComponentsByType(ViewComponent);
         if(_loc3_.length > 0)
         {
            for each(_loc4_ in _loc3_)
            {
               this._view.addArt(_loc4_);
            }
         }
         return param1;
      }
      
      public function addPoolObject(param1:PoolObject) : PoolObject
      {
         if(param1.isCitrusObjectPool)
         {
            param1.citrus_internal::state = this._istate;
            this._poolObjects.push(param1);
            return param1;
         }
         return null;
      }
      
      public function remove(param1:CitrusObject) : void
      {
         param1.kill = true;
      }
      
      public function removeImmediately(param1:CitrusObject) : void
      {
         var _loc3_:Vector.<Component> = null;
         var _loc4_:ViewComponent = null;
         if(param1 == null)
         {
            return;
         }
         var _loc2_:int = int(this._objects.indexOf(param1));
         if(_loc2_ != -1)
         {
            this._objects.splice(_loc2_,1);
         }
         param1.kill = true;
         if(param1 is Entity)
         {
            _loc3_ = (param1 as Entity).lookupComponentsByType(ViewComponent);
            if(_loc3_.length > 0)
            {
               for each(_loc4_ in _loc3_)
               {
                  this._view.removeArt(_loc4_);
               }
            }
         }
         else
         {
            this._view.removeArt(param1);
         }
         param1.destroy();
         --this._numObjects;
      }
      
      public function getObjectByName(param1:String) : CitrusObject
      {
         var object:CitrusObject = null;
         var poolObject:PoolObject = null;
         var found:Boolean = false;
         var name:String = param1;
         for each(object in this._objects)
         {
            if(object.name == name)
            {
               return object;
            }
         }
         if(this._poolObjects.length > 0)
         {
            found = false;
            for each(poolObject in this._poolObjects)
            {
               poolObject.foreachRecycled(function(param1:*):Boolean
               {
                  if(param1 is CitrusObject && param1["name"] == name)
                  {
                     object = param1;
                     found = true;
                     return true;
                  }
                  return false;
               });
               if(found)
               {
                  return object;
               }
            }
         }
         return null;
      }
      
      public function getObjectsByName(param1:String) : Vector.<CitrusObject>
      {
         var objects:Vector.<CitrusObject> = null;
         var object:CitrusObject = null;
         var poolObject:PoolObject = null;
         var name:String = param1;
         objects = new Vector.<CitrusObject>();
         for each(object in this._objects)
         {
            if(object.name == name)
            {
               objects.push(object);
            }
         }
         if(this._poolObjects.length > 0)
         {
            for each(poolObject in this._poolObjects)
            {
               poolObject.foreachRecycled(function(param1:*):Boolean
               {
                  if(param1 is CitrusObject && param1["name"] == name)
                  {
                     objects.push(param1 as CitrusObject);
                  }
                  return false;
               });
            }
         }
         return objects;
      }
      
      public function getFirstObjectByType(param1:Class) : CitrusObject
      {
         var object:CitrusObject = null;
         var poolObject:PoolObject = null;
         var found:Boolean = false;
         var type:Class = param1;
         for each(object in this._objects)
         {
            if(object is type)
            {
               return object;
            }
         }
         if(this._poolObjects.length > 0)
         {
            found = false;
            for each(poolObject in this._poolObjects)
            {
               poolObject.foreachRecycled(function(param1:*):Boolean
               {
                  if(param1 is type)
                  {
                     object = param1;
                     found = true;
                     return true;
                  }
                  return false;
               });
               if(found)
               {
                  return object;
               }
            }
         }
         return null;
      }
      
      public function getObjectsByType(param1:Class) : Vector.<CitrusObject>
      {
         var objects:Vector.<CitrusObject> = null;
         var object:CitrusObject = null;
         var poolObject:PoolObject = null;
         var type:Class = param1;
         objects = new Vector.<CitrusObject>();
         for each(object in this._objects)
         {
            if(object is type)
            {
               objects.push(object);
            }
         }
         if(this._poolObjects.length > 0)
         {
            for each(poolObject in this._poolObjects)
            {
               poolObject.foreachRecycled(function(param1:*):Boolean
               {
                  if(param1 is type)
                  {
                     objects.push(param1 as CitrusObject);
                  }
                  return false;
               });
            }
         }
         return objects;
      }
      
      public function killAllObjects(param1:Array) : void
      {
         var _loc2_:CitrusObject = null;
         var _loc3_:CitrusObject = null;
         for each(_loc2_ in this._objects)
         {
            _loc2_.kill = true;
            for each(_loc3_ in param1)
            {
               if(_loc2_ == _loc3_)
               {
                  _loc3_.kill = false;
                  param1.splice(param1.indexOf(_loc3_),1);
                  break;
               }
            }
         }
      }
      
      public function get objects() : Vector.<CitrusObject>
      {
         return this._objects;
      }
   }
}

