package citrus.objects.platformer.box2d
{
   import Box2D.Collision.b2Manifold;
   import Box2D.Dynamics.Contacts.b2Contact;
   import Box2D.Dynamics.b2Body;
   import citrus.objects.Box2DPhysicsObject;
   import citrus.physics.box2d.Box2DUtils;
   import citrus.physics.box2d.IBox2DPhysicsObject;
   
   public class Platform extends Box2DPhysicsObject
   {
      private var _oneWay:Boolean = false;
      
      public function Platform(param1:String, param2:Object = null)
      {
         super(param1,param2);
      }
      
      public function get oneWay() : Boolean
      {
         return this._oneWay;
      }
      
      public function set oneWay(param1:Boolean) : void
      {
         if(this._oneWay == param1)
         {
            return;
         }
         this._oneWay = _preContactCallEnabled = param1;
      }
      
      override protected function defineBody() : void
      {
         super.defineBody();
         _bodyDef.type = b2Body.b2_staticBody;
      }
      
      override protected function defineFixture() : void
      {
         super.defineFixture();
         _fixtureDef.restitution = 0;
      }
      
      override public function handlePreSolve(param1:b2Contact, param2:b2Manifold) : void
      {
         var _loc3_:Number = NaN;
         var _loc4_:IBox2DPhysicsObject = null;
         var _loc5_:Number = NaN;
         var _loc6_:Number = NaN;
         if(this._oneWay)
         {
            _loc3_ = 0;
            _loc4_ = Box2DUtils.CollisionGetOther(this,param1);
            if(!_loc4_.height)
            {
               return;
            }
            _loc3_ = _loc4_.height / 2;
            _loc5_ = _loc4_.y + _loc3_;
            _loc6_ = Math.sin(_body.GetAngle()) / Math.cos(_body.GetAngle());
            if(_loc5_ >= _loc6_ * (_loc4_.x - x) + y - height / 2)
            {
               param1.SetEnabled(false);
            }
         }
      }
   }
}

