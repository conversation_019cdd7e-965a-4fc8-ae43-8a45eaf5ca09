package mogames.gameEffect.co
{
   import flash.display.DisplayObject;
   import mogames.gameSystem.CitrusRender;
   import utils.FilterFactory;
   
   public class StoicEffect
   {
      private var _list:Array = [[FilterFactory.getGlowFilter(16777215,5,5,10)],[]];
      
      private var _count:int;
      
      private var _index:int;
      
      private var _target:DisplayObject;
      
      public function StoicEffect()
      {
         super();
      }
      
      public function play(param1:DisplayObject) : void
      {
         this._target = param1;
         this._count = 0;
         this._index = 0;
         CitrusRender.instance().add(this.update);
      }
      
      public function stop() : void
      {
         if(this._target)
         {
            this._target.filters = [];
         }
         CitrusRender.instance().remove(this.update);
      }
      
      public function update() : void
      {
         if(!this._target)
         {
            this.destroy();
         }
         if(this._index >= 2)
         {
            this._index = 0;
         }
         if(this._count % 3 == 0)
         {
            this._target.filters = this._list[this._index];
            ++this._index;
            this._count = 0;
         }
         ++this._count;
      }
      
      public function destroy() : void
      {
         if(this._target)
         {
            this._target.filters = [];
         }
         CitrusRender.instance().remove(this.update);
         this._list = null;
         this._target = null;
      }
   }
}

