package mogames.gameData.rank
{
   import mogames.gameData.role.HeroGameVO;
   
   public class RankATKHandler extends RankBRHandler
   {
      public function RankATKHandler()
      {
         super();
         rankIDs = [1712,1713,1714,1715,1716];
         _msg = "初始化攻击排行榜";
      }
      
      override protected function newScore(param1:HeroGameVO) : int
      {
         return param1.ownATK;
      }
   }
}

