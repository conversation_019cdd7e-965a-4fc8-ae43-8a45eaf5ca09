package citrus.view
{
   import citrus.core.CitrusObject;
   import citrus.utils.LoadManager;
   import flash.utils.Dictionary;
   
   public class ACitrusView
   {
      public var loadManager:LoadManager;
      
      public var camera:ACitrusCamera;
      
      protected var _viewObjects:Dictionary = new Dictionary();
      
      protected var _root:*;
      
      protected var _viewInterface:Class;
      
      public function ACitrusView(param1:*, param2:Class)
      {
         super();
         this._root = param1;
         this._viewInterface = param2;
         this.loadManager = new LoadManager();
      }
      
      public function destroy() : void
      {
         this.camera.destroy();
         this.loadManager.destroy();
      }
      
      public function update(param1:Number) : void
      {
      }
      
      public function addArt(param1:Object) : void
      {
         if(!(param1 is this._viewInterface))
         {
            return;
         }
         var _loc2_:Object = this.createArt(param1);
         if(_loc2_)
         {
            this._viewObjects[param1] = _loc2_;
         }
         if(_loc2_["content"] == null)
         {
            this.loadManager.add(_loc2_,param1 as CitrusObject);
         }
      }
      
      public function removeArt(param1:Object) : void
      {
         if(!(param1 is this._viewInterface))
         {
            return;
         }
         this.destroyArt(param1);
         delete this._viewObjects[param1];
      }
      
      public function getArt(param1:Object) : Object
      {
         if(!param1 is this._viewInterface)
         {
            throw new Error("The object " + param1 + " does not have a graphical counterpart because it does not implement " + this._viewInterface + ".");
         }
         return this._viewObjects[param1];
      }
      
      public function getObjectFromArt(param1:Object) : Object
      {
         var _loc2_:Object = null;
         for(_loc2_ in this._viewObjects)
         {
            if(this._viewObjects[_loc2_] == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      protected function createArt(param1:Object) : Object
      {
         return null;
      }
      
      protected function updateArt(param1:Object, param2:Object) : void
      {
      }
      
      protected function destroyArt(param1:Object) : void
      {
      }
   }
}

