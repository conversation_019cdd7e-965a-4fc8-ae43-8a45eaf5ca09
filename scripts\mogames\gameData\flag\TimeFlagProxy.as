package mogames.gameData.flag
{
   import com.hexagonstar.util.debug.Debug;
   import mogames.gameData.flag.mission.TimeMissionFlag;
   
   public class TimeFlagProxy
   {
      private static var _instance:TimeFlagProxy;
      
      public static var missionIDs:Array = [100,300,500,700,800,900,1000,1100,1200,1300,1400,1500,1600,1700,1800,1900,2000,2100,2200,2300,2400,2500,2600,2700,2800,2900,3000];
      
      private var _flags:Vector.<TimeMissionFlag>;
      
      public function TimeFlagProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : TimeFlagProxy
      {
         if(!_instance)
         {
            _instance = new TimeFlagProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         var _loc1_:int = 0;
         this._flags = null;
         this._flags = new Vector.<TimeMissionFlag>();
         for each(_loc1_ in missionIDs)
         {
            this._flags.push(new TimeMissionFlag(_loc1_));
         }
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:Array = null;
         var _loc4_:* = null;
         var _loc5_:TimeMissionFlag = null;
         Debug.trace("通关时间数据：" + param1);
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc4_ in _loc2_)
         {
            _loc3_ = _loc4_.split("H");
            _loc5_ = this.findFlag(int(_loc3_[0]));
            if(_loc5_)
            {
               _loc5_.loadData = _loc3_;
            }
         }
      }
      
      public function get saveData() : String
      {
         var _loc2_:TimeMissionFlag = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._flags)
         {
            _loc1_.push(_loc2_.saveData);
         }
         return _loc1_.join("T");
      }
      
      public function recordFlag(param1:int, param2:int, param3:int) : void
      {
         Debug.trace("记录关卡时间：" + [param1,param2,param3].join(","));
         var _loc4_:TimeMissionFlag = this.findFlag(param1);
         if(!_loc4_)
         {
            return;
         }
         _loc4_.recordTime(param2);
         _loc4_.recordHurt(param3);
      }
      
      public function findFlag(param1:int) : TimeMissionFlag
      {
         var _loc2_:TimeMissionFlag = null;
         for each(_loc2_ in this._flags)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

