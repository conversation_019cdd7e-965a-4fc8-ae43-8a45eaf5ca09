package mogames.gameData.drop.vo
{
   import mogames.gameData.good.GameGoodVO;
   
   public class ListDropVO extends BaseDropVO
   {
      private var _list:Array;
      
      public function ListDropVO(param1:Array)
      {
         super(0,0,0);
         this._list = param1;
      }
      
      override public function get isDrop() : <PERSON><PERSON>an
      {
         return true;
      }
      
      override public function newGood() : GameGoodVO
      {
         var _loc1_:int = Math.random() * 1000;
         var _loc2_:int = 0;
         var _loc3_:int = int(this._list.length);
         while(_loc2_ < _loc3_)
         {
            if(this.checkDrop(_loc2_,_loc1_))
            {
               return this._list[_loc2_].newGood();
            }
            _loc2_++;
         }
         return null;
      }
      
      private function checkDrop(param1:int, param2:int) : Bo<PERSON>an
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         while(_loc4_ <= param1)
         {
            _loc3_ += this._list[_loc4_].per;
            _loc4_++;
         }
         if(param2 <= _loc3_)
         {
            return true;
         }
         return false;
      }
   }
}

