package mogames.gameMission.mission.jingjichang
{
   import flash.events.KeyboardEvent;
   import mogames.Layers;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameEffect.EffectManager;
   import mogames.gameSystem.CitrusTimer;
   import mogames.gameUI.FrameModule;
   import mogames.gameUI.mission.co.HeroTopItem;
   import utils.TxtUtil;
   
   public class PKTopHeroModule extends FrameModule
   {
      private static var _instance:PKTopHeroModule;
      
      public var list:Array;
      
      private var _items:Vector.<HeroTopItem>;
      
      private var _activeItem:HeroTopItem;
      
      private var _changeTimer:CitrusTimer;
      
      public function PKTopHeroModule()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : PKTopHeroModule
      {
         if(!_instance)
         {
            _instance = new PKTopHeroModule();
         }
         return _instance;
      }
      
      public static function clean() : void
      {
         if(!_instance)
         {
            return;
         }
         _instance.destroy();
         _instance = null;
      }
      
      public function init(param1:Array) : void
      {
         var _loc4_:HeroTopItem = null;
         this._items = new Vector.<HeroTopItem>();
         this.list = param1;
         var _loc2_:int = 0;
         var _loc3_:int = int(this.list.length);
         while(_loc2_ < _loc3_)
         {
            _loc4_ = new HeroTopItem(_loc2_);
            _loc4_.initData(this.list[_loc2_]);
            _loc4_.setKeyVisible(_loc3_ >= 2);
            addFrame(_loc4_,100 * _loc2_,0);
            this._items.push(_loc4_);
            _loc4_.addEventListener(UIEvent.HERO_EVENT,this.onDead,false,0,true);
            _loc2_++;
         }
         this._changeTimer = new CitrusTimer();
         this.registerItem(0);
         Layers.gameStage.addEventListener(KeyboardEvent.KEY_UP,this.onSwitch);
         _active = true;
      }
      
      private function onSwitch(param1:KeyboardEvent) : void
      {
         if([49,50,51,52,53].indexOf(param1.keyCode) == -1)
         {
            return;
         }
         if(!this.checkKeySwich() || !Layers.ceLayer.input.keyboard.enabled)
         {
            return;
         }
         if(PKHeroManager.instance().checkLeftOne())
         {
            return;
         }
         var _loc2_:int = int(param1.keyCode % 49);
         if(_loc2_ < this.list.length && _loc2_ != this._activeItem.index)
         {
            if(this._changeTimer.running)
            {
               EffectManager.instance().playAudio("ERROR");
               EffectManager.instance().addHeadWord(TxtUtil.setColor("角色切换冷却中！"),PKHeroManager.onePlayer.x,PKHeroManager.onePlayer.y - 50,-1);
               return;
            }
            if(Boolean(PKHeroManager.onePlayer) && !PKHeroManager.onePlayer.isSwitch)
            {
               EffectManager.instance().playAudio("ERROR");
               EffectManager.instance().addHeadWord(TxtUtil.setColor("当前状态无法切换角色！"),PKHeroManager.onePlayer.x,PKHeroManager.onePlayer.y - 50,-1);
               return;
            }
            if(this.switchTarget(_loc2_))
            {
               this._changeTimer.setInterval(1,3,this.handlerColdDown);
               this.handlerColdDown();
            }
         }
      }
      
      private function onDead(param1:UIEvent) : void
      {
         if(param1.data.type == "onDead")
         {
            this.handlerHeroDead(param1.target as HeroTopItem);
         }
      }
      
      private function handlerHeroDead(param1:HeroTopItem) : void
      {
         if(PKHeroManager.instance().checkAllDead())
         {
            return;
         }
         if(param1 != this._activeItem)
         {
            return;
         }
         var _loc2_:int = 0;
         var _loc3_:int = int(this.list.length);
         while(_loc2_ < _loc3_)
         {
            if(!this.list[_loc2_].isDead() && this.switchTarget(_loc2_,1))
            {
               return;
            }
            _loc2_++;
         }
      }
      
      public function registerItem(param1:int) : void
      {
         if(this._activeItem)
         {
            this._activeItem.setActive(false);
         }
         this._activeItem = null;
         this._activeItem = this._items[param1];
         this._activeItem.setActive(true);
      }
      
      private function checkKeySwich() : Boolean
      {
         return Boolean(PKHeroManager.onePlayer) && PKHeroManager.onePlayer.isReady;
      }
      
      private function switchTarget(param1:int, param2:int = 0) : Boolean
      {
         if(Boolean(this._activeItem) && this._activeItem.index == param1)
         {
            return false;
         }
         this.registerItem(param1);
         EventManager.dispatchEvent(UIEvent.TEAM_EVENT,{
            "type":"onSwitch",
            "data":param1,
            "sType":param2
         });
         return true;
      }
      
      private function handlerColdDown() : void
      {
         var _loc1_:int = int(this._items.length);
         var _loc2_:int = 0;
         while(_loc2_ < _loc1_)
         {
            this._items[_loc2_].showTime(this._changeTimer.leftTime);
            _loc2_++;
         }
      }
      
      private function destroyItem() : void
      {
         var _loc1_:int = int(this._items.length);
         var _loc2_:int = 0;
         while(_loc2_ < _loc1_)
         {
            this._items[_loc2_].destroy();
            _loc2_++;
         }
         this._items = null;
         this._activeItem = null;
      }
      
      override protected function destroy() : void
      {
         if(!_active)
         {
            return;
         }
         Layers.gameStage.removeEventListener(KeyboardEvent.KEY_UP,this.onSwitch);
         this._changeTimer.destroy();
         this.destroyItem();
      }
   }
}

