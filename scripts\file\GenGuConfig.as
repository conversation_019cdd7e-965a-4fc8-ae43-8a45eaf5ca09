package file
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.constvo.ConstDanYaoVO;
   
   public class GenGuConfig
   {
      private static var _instance:GenGuConfig;
      
      private var _list:Array;
      
      public function GenGuConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : GenGuConfig
      {
         if(!_instance)
         {
            _instance = new GenGuConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._list = [];
         this._list[0] = [new Sint(16201),new Sint(16202),new Sint(16203),new Sint(16204),new Sint(16205),new Sint(16211),new Sint(16212),new Sint(16213),new Sint(16214),new Sint(16215),new Sint(16221),new Sint(16222),new Sint(16223),new Sint(16224),new Sint(16225)];
         this._list[1] = [new Sint(16301),new Sint(16302),new Sint(16303),new Sint(16304),new Sint(16305),new Sint(16311),new Sint(16312),new Sint(16313),new Sint(16314),new Sint(16315),new Sint(16321),new Sint(16322),new Sint(16323),new Sint(16324),new Sint(16325)];
         this._list[2] = [new Sint(16401),new Sint(16402),new Sint(16403),new Sint(16404),new Sint(16405),new Sint(16411),new Sint(16412),new Sint(16413),new Sint(16414),new Sint(16415),new Sint(16421),new Sint(16422),new Sint(16423),new Sint(16424),new Sint(16425)];
         this._list[3] = [new Sint(16501),new Sint(16502),new Sint(16503),new Sint(16504),new Sint(16505),new Sint(16511),new Sint(16512),new Sint(16513),new Sint(16514),new Sint(16515),new Sint(16521),new Sint(16522),new Sint(16523),new Sint(16524),new Sint(16525)];
         this._list[4] = [new Sint(16601),new Sint(16602),new Sint(16603),new Sint(16604),new Sint(16605),new Sint(16611),new Sint(16612),new Sint(16613),new Sint(16614),new Sint(16615),new Sint(16621),new Sint(16622),new Sint(16623),new Sint(16624),new Sint(16625)];
      }
      
      public function findDanVO(param1:int, param2:int, param3:int) : ConstDanYaoVO
      {
         var _loc7_:ConstDanYaoVO = null;
         var _loc4_:Array = this._list[param1];
         var _loc5_:int = 0;
         var _loc6_:int = int(_loc4_.length);
         while(_loc5_ < _loc6_)
         {
            _loc7_ = GoodConfig.instance().findConstGood(_loc4_[_loc5_].v) as ConstDanYaoVO;
            if(_loc7_.wuxing.v == param2 && _loc7_.addType.v == param3)
            {
               return _loc7_;
            }
            _loc5_++;
         }
         return null;
      }
      
      public function findList(param1:int) : Array
      {
         return this._list[param1];
      }
   }
}

