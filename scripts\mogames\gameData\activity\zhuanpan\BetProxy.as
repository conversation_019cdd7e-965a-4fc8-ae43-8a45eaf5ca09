package mogames.gameData.activity.zhuanpan
{
   import mogames.gameData.drop.vo.BaseRewardVO;
   import mogames.gameData.drop.vo.CardRewardVO;
   
   public class BetProxy
   {
      private static var _instance:BetProxy;
      
      private var _list:Array;
      
      public function BetProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : BetProxy
      {
         if(!_instance)
         {
            _instance = new BetProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this._list = [];
         this._list[this._list.length] = new BetVO(101,new BaseRewardVO(14055,1));
         this._list[this._list.length] = new BetVO(102,new BaseRewardVO(14063,1));
         this._list[this._list.length] = new BetVO(103,new BaseRewardVO(14124,1));
         this._list[this._list.length] = new BetVO(204,new BaseRewardVO(14050,1));
         this._list[this._list.length] = new BetVO(205,new CardRewardVO(70978,1));
         this._list[this._list.length] = new BetVO(206,new BaseRewardVO(16762,50));
         this._list[this._list.length] = new BetVO(307,new BaseRewardVO(14064,2));
         this._list[this._list.length] = new BetVO(308,new BaseRewardVO(10091,1));
      }
      
      public function get saveData() : String
      {
         var _loc2_:BetVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._list)
         {
            _loc1_[_loc1_.length] = _loc2_.saveData;
         }
         return _loc1_.join("T");
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:Array = null;
         var _loc4_:* = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc4_ in _loc2_)
         {
            _loc3_ = _loc4_.split("H");
            this.findVO(int(_loc3_[0])).loadData = _loc3_;
         }
      }
      
      public function get randIndex() : int
      {
         var _loc1_:Array = this.enabledIndex(1);
         if(_loc1_.length <= 0)
         {
            _loc1_ = this.enabledIndex(2);
         }
         if(_loc1_.length <= 0)
         {
            _loc1_ = this.enabledIndex(3);
         }
         var _loc2_:int = _loc1_.length * Math.random();
         return _loc1_[_loc2_].position - 1;
      }
      
      public function get betList() : Array
      {
         return this._list;
      }
      
      private function enabledIndex(param1:int) : Array
      {
         var _loc3_:BetVO = null;
         var _loc2_:Array = [];
         for each(_loc3_ in this._list)
         {
            if(!_loc3_.isGet && _loc3_.index == param1)
            {
               _loc2_[_loc2_.length] = _loc3_;
            }
         }
         return _loc2_;
      }
      
      private function findVO(param1:int) : BetVO
      {
         var _loc2_:BetVO = null;
         for each(_loc2_ in this._list)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

