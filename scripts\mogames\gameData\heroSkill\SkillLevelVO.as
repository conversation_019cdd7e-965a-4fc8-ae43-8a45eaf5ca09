package mogames.gameData.heroSkill
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.role.HeroGameVO;
   import utils.TxtUtil;
   
   public class SkillLevelVO
   {
      public var level:Sint;
      
      public var needLv:Sint;
      
      public var needGold:Sint;
      
      public function SkillLevelVO(param1:int, param2:int, param3:int)
      {
         super();
         this.level = new Sint(param1);
         this.needLv = new Sint(param2);
         this.needGold = new Sint(param3);
      }
      
      public function parseNeedStr(param1:HeroGameVO) : String
      {
         var _loc2_:Array = [];
         var _loc3_:String = "";
         if(this.needLv.v > 0)
         {
            _loc3_ = param1.level.v < this.needLv.v ? "ff0000" : "99ff00";
            _loc2_.push(TxtUtil.setColor("人物达到" + this.needLv.v + "级",_loc3_));
         }
         _loc3_ = MasterProxy.instance().gameGold.v < this.needGold.v ? "ff0000" : "99ff00";
         _loc2_.push(TxtUtil.setColor("铜钱X" + this.needGold.v,_loc3_));
         return _loc2_.join("<br>");
      }
      
      public function useStuff() : void
      {
         MasterProxy.instance().changeGold(-this.needGold.v);
      }
      
      public function checkLearn(param1:HeroGameVO) : Boolean
      {
         return param1.level.v >= this.needLv.v && MasterProxy.instance().gameGold.v >= this.needGold.v;
      }
   }
}

