package mogames.gameData.pk.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.TimeVO;
   import mogames.gameData.flag.FBFlagProxy;
   import mogames.gamePKG.GameProxy;
   
   public class RolePKVO
   {
      public var score:Sint;
      
      public var allWin:Sint;
      
      public var allLose:Sint;
      
      public var curWin:Sint;
      
      public var curLose:Sint;
      
      public var maxWin:Sint;
      
      public var maxLose:Sint;
      
      public var lastR:int;
      
      public var pkNum:Sint;
      
      public var lastPKNum:int;
      
      public function RolePKVO()
      {
         super();
         this.score = new Sint(100);
         this.allWin = new Sint();
         this.allLose = new Sint();
         this.curWin = new Sint();
         this.curLose = new Sint();
         this.maxWin = new Sint();
         this.maxLose = new Sint();
         this.pkNum = new Sint();
         this.lastR = 2;
      }
      
      public function get saveData() : String
      {
         return [this.score.v,this.allWin.v,this.allLose.v,this.curWin.v,this.curLose.v,this.maxWin.v,this.maxLose.v,this.lastR,this.pkNum.v,this.lastPKNum].join("H");
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc2_:Array = param1.split("H");
         this.score.v = int(_loc2_[0]);
         this.allWin.v = int(_loc2_[1]);
         this.allLose.v = int(_loc2_[2]);
         this.curWin.v = int(_loc2_[3]);
         this.curLose.v = int(_loc2_[4]);
         this.maxWin.v = int(_loc2_[5]);
         this.maxLose.v = int(_loc2_[6]);
         this.lastR = int(_loc2_[7]);
         if(_loc2_[8])
         {
            this.pkNum.v = int(_loc2_[8]);
         }
         if(_loc2_[9])
         {
            this.lastPKNum = int(_loc2_[9]);
         }
      }
      
      public function weekRefresh() : void
      {
         if(!this.checkRefresh())
         {
            return;
         }
         this.score.v = 100;
         this.pkNum.v = 0;
         this.lastPKNum = 0;
      }
      
      public function addLastPKNum() : void
      {
         this.pkNum.v += 1;
         var _loc1_:TimeVO = GameProxy.instance().curTS;
         if(_loc1_.week.v == 0)
         {
            ++this.lastPKNum;
         }
      }
      
      private function checkRefresh() : Boolean
      {
         var _loc1_:TimeVO = GameProxy.instance().newTS;
         var _loc2_:TimeVO = GameProxy.instance().curTS;
         if(_loc1_.week.v == 2)
         {
            return true;
         }
         if(_loc1_.totalDay - _loc2_.totalDay >= 7)
         {
            return true;
         }
         if(_loc1_.week.v > 2)
         {
            if(_loc2_.week.v < 2 || _loc2_.week.v > _loc1_.week.v)
            {
               return true;
            }
            return false;
         }
         return false;
      }
      
      public function setWin() : void
      {
         this.allWin.v += 1;
         if(this.lastR == 1 || this.lastR == 2)
         {
            this.curWin.v += 1;
         }
         if(this.curWin.v >= this.maxWin.v)
         {
            this.maxWin.v = this.curWin.v;
         }
         this.lastR = 1;
         this.curLose.v = 0;
      }
      
      public function setLose() : void
      {
         this.allLose.v += 1;
         if(this.lastR == 0 || this.lastR == 2)
         {
            this.curLose.v += 1;
         }
         if(this.curLose.v >= this.maxLose.v)
         {
            this.maxLose.v = this.curLose.v;
         }
         this.lastR = 0;
         this.curWin.v = 0;
      }
      
      public function get leftStr() : String
      {
         return FBFlagProxy.instance().findFlag(112).leftFree + "次";
      }
      
      public function get zhanji() : String
      {
         return this.allWin.v + "胜" + this.allLose.v + "负";
      }
      
      public function scoreRefresh() : void
      {
         this.score.v = 100;
      }
   }
}

