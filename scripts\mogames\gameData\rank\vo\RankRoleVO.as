package mogames.gameData.rank.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameData.role.HeroHorseVO;
   import mogames.gameData.role.HeroMonkeyVO;
   import mogames.gameData.role.HeroPigVO;
   
   public class RankRoleVO
   {
      public var rank:Sint;
      
      public var nick:String;
      
      public var level:Sint;
      
      public var hp:Sint;
      
      public var atk:Sint;
      
      public var pdef:Sint;
      
      public var mdef:Sint;
      
      public var br:Sint;
      
      public var score:Sint;
      
      public var uid:String;
      
      public var saveIndex:int;
      
      public var heroID:int;
      
      public var heroData:String;
      
      public var heroVO:HeroGameVO;
      
      public function RankRoleVO()
      {
         super();
         this.rank = new Sint();
         this.level = new Sint();
         this.hp = new Sint();
         this.atk = new Sint();
         this.pdef = new Sint();
         this.mdef = new Sint();
         this.br = new Sint();
         this.score = new Sint();
      }
      
      public function parseData(param1:Object) : void
      {
         this.uid = param1.uId;
         this.nick = param1.userName;
         this.score.v = param1.score;
         this.rank.v = param1.rank;
         this.saveIndex = param1.index;
         if(param1.extra is Array)
         {
            this.parseExtra(param1.extra);
         }
         else if(param1.extra is String)
         {
            this.parseExtra(String(param1.extra).split(","));
         }
      }
      
      private function parseExtra(param1:Array) : void
      {
         this.level.v = param1[0];
         this.hp.v = param1[1];
         this.atk.v = param1[2];
         this.pdef.v = param1[3];
         this.mdef.v = param1[4];
         this.br.v = param1[5];
      }
      
      private function initHeroVO() : void
      {
         switch(this.heroID)
         {
            case 1001:
               this.heroVO = new HeroMonkeyVO();
               break;
            case 1002:
               this.heroVO = new HeroHorseVO();
               break;
            case 1003:
               this.heroVO = new HeroPigVO();
         }
         this.heroVO.parseLoadData(this.heroData);
      }
      
      public function get sortIndex() : int
      {
         return this.rank.v;
      }
   }
}

