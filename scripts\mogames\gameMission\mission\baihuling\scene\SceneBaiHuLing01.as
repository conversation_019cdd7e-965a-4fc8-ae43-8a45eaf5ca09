package mogames.gameMission.mission.baihuling.scene
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.baihuling.BaiHuLingMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameSystem.CitrusLock;
   
   public class SceneBaiHuLing01 extends BaseScene
   {
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _eTrigger2:EnemyTrigger;
      
      private var _eTrigger3:EnemyTrigger;
      
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _pTrigger3:LocalTriggerX;
      
      public function SceneBaiHuLing01(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         (_mission as BaiHuLingMission).playerBGM();
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            initEnemies();
            FlagProxy.instance().setValue(2010,1);
            TaskProxy.instance().addTask(12006);
         };
         _mission.cleanLoadUI();
         if(FlagProxy.instance().isComplete(2010))
         {
            this.initEnemies();
         }
         else
         {
            showDialog("STORY0018",func);
         }
      }
      
      private function initEnemies() : void
      {
         if(_mission.hasMark("enemy701"))
         {
            return;
         }
         _mission.setMark("enemy701");
         CitrusLock.instance().lock(new Rectangle(0,0,960,1200),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[106,950,152,100]],7011,this.triggerEnd0);
         this._eTrigger1 = new EnemyTrigger(_mission,[[960,950,152,100],[1162,950,152,100]],7012,this.triggerEnd1);
         this._eTrigger2 = new EnemyTrigger(_mission,[[1974,125,152,100],[2444,511,152,100]],7013,unlock);
         this._eTrigger3 = new EnemyTrigger(_mission,[[24,270,152,100],[674,311,152,100],[1226,493,152,100]],7014,null);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(570,1053),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1530,1053),1);
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(2280,580),1,new Rectangle(1920,0,960,1200));
         this._pTrigger2.disY = 100;
         this._pTrigger2.setBlock(false,false,true,false);
         this._pTrigger3 = new LocalTriggerX(_mission.onePlayer,new Point(470,363),-1);
         this._pTrigger3.disY = 100;
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger2.trigger = this._eTrigger2;
         this._pTrigger3.trigger = this._eTrigger3;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this._pTrigger3.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd0() : void
      {
         unlock();
         CitrusLock.instance().lock(new Rectangle(0,0,1820,1200),false,false,false,true);
         EffectManager.instance().addGOEffect(3);
      }
      
      private function triggerEnd1() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._eTrigger2)
         {
            this._eTrigger2.destroy();
         }
         if(this._eTrigger3)
         {
            this._eTrigger3.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         if(this._pTrigger2)
         {
            this._pTrigger2.destroy();
         }
         if(this._pTrigger3)
         {
            this._pTrigger3.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._eTrigger2 = null;
         this._eTrigger3 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._pTrigger3 = null;
         super.destroy();
      }
   }
}

