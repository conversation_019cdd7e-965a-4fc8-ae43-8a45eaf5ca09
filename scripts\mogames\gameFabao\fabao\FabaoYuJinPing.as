package mogames.gameFabao.fabao
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameFabao.view.FabaoYuJinPingView;
   
   public class FabaoYuJinPing extends BaseFabao
   {
      public function FabaoYuJinPing(param1:GameFabaoVO)
      {
         super(param1,20,54);
         this.createFabaoData();
         createView(new FabaoYuJinPingView());
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(30),
            "keepTime":new Snum(5),
            "maxPer":new Sint(3),
            "minPer":new Sint(2)
         },{
            "cdTime":new Snum(30),
            "keepTime":new Snum(6),
            "maxPer":new Sint(4),
            "minPer":new Sint(2)
         },{
            "cdTime":new Snum(30),
            "keepTime":new Snum(6),
            "maxPer":new Sint(5),
            "minPer":new Sint(3)
         },{
            "cdTime":new Snum(30),
            "keepTime":new Snum(6),
            "maxPer":new Sint(6),
            "minPer":new Sint(3)
         },{
            "cdTime":new Snum(30),
            "keepTime":new Snum(6),
            "maxPer":new Sint(7),
            "minPer":new Sint(3)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      public function addBuff() : void
      {
         if(!owner || owner.isDead)
         {
            return;
         }
         owner.addBuff(new BuffVO(1006,_fabaoData.keepTime.v,{
            "maxPer":_fabaoData.maxPer,
            "minPer":_fabaoData.minPer
         }));
      }
   }
}

