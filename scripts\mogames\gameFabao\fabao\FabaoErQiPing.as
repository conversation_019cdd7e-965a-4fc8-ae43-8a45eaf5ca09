package mogames.gameFabao.fabao
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameFabao.view.FabaoErQiPingView;
   
   public class FabaoErQiPing extends BaseFabao
   {
      public function FabaoErQiPing(param1:GameFabaoVO)
      {
         super(param1,64,82);
         this.createFabaoData();
         createView(new FabaoErQiPingView());
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(36),
            "cureValue":new Sint(100)
         },{
            "cdTime":new Snum(34),
            "cureValue":new Sint(150)
         },{
            "cdTime":new Snum(32),
            "cureValue":new Sint(200)
         },{
            "cdTime":new Snum(30),
            "cureValue":new Sint(250)
         },{
            "cdTime":new Snum(28),
            "cureValue":new Sint(300)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      override public function followTarget() : void
      {
         if(_fabaoView.isSkill)
         {
            return;
         }
         super.followTarget();
      }
      
      override public function handlerSkill() : void
      {
         this.x = owner.x;
         super.handlerSkill();
      }
      
      public function handlerSkillOne() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         owner.roleVO.changeMP(_fabaoData.cureValue.v);
      }
   }
}

