package mogames.gameMission.mission.huangfengdong
{
   import citrus.objects.CitrusSprite;
   import citrus.view.ICitrusArt;
   import file.GoodConfig;
   import flash.display.Bitmap;
   import flash.geom.Point;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameObj.DropObject;
   import utils.MethodUtil;
   
   public class BaseYaocao extends CitrusSprite
   {
      private var _vo:GameGoodVO;
      
      private var _bitmap:Bitmap = AssetManager.newBitmap(this._vo.constVO.iname);
      
      private var _index:int;
      
      public var pickFunc:Function;
      
      public function BaseYaocao(param1:int, param2:int, param3:Function)
      {
         this._index = param2;
         this.pickFunc = param3;
         this._vo = GoodConfig.instance().newGood(param1);
         super("baseYaocao",{
            "width":this._bitmap.width,
            "height":this._bitmap.height,
            "group":2
         });
         registration = "center";
         this.view = this._bitmap;
      }
      
      override public function handleArtReady(param1:ICitrusArt) : void
      {
         super.handleArtReady(param1);
         BattleMediator.instance().onHeroATK.add(this.listenHurt);
      }
      
      private function listenHurt(param1:BaseHitVO) : void
      {
         if(!this._bitmap.hitTestObject(param1.range) || kill)
         {
            return;
         }
         Layers.addCEChild(new DropObject(this._vo,x,y - this._bitmap.height,10,new Point(0,-1)));
         EffectManager.instance().addBMCEffect(param1.hurtSkin,Layers.ceEffectLayer,x,y);
         EffectManager.instance().playAudio(param1.hurtSound);
         EffectManager.instance().addCircleSpread(Math.random() * 5 + 5,new Point(x,y),4291598592);
         this.pickFunc(this._index);
         kill = true;
      }
      
      override public function destroy() : void
      {
         this._vo = null;
         MethodUtil.removeMe(this._bitmap);
         BattleMediator.instance().onHeroATK.remove(this.listenHurt);
         super.destroy();
      }
   }
}

