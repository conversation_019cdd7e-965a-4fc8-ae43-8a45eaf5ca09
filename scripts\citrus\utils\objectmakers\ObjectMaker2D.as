package citrus.utils.objectmakers
{
   import citrus.core.*;
   import citrus.objects.CitrusSprite;
   import citrus.utils.objectmakers.tmx.TmxLayer;
   import citrus.utils.objectmakers.tmx.TmxMap;
   import citrus.utils.objectmakers.tmx.TmxObject;
   import citrus.utils.objectmakers.tmx.TmxObjectGroup;
   import citrus.utils.objectmakers.tmx.TmxTileSet;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.getDefinitionByName;
   
   public class ObjectMaker2D
   {
      public function ObjectMaker2D()
      {
         super();
      }
      
      public static function FromMovieClip(param1:MovieClip, param2:Boolean = true, param3:uint = 1) : Array
      {
         var _loc6_:MovieClip = null;
         var _loc8_:Class = null;
         var _loc9_:Object = null;
         var _loc10_:Number = NaN;
         var _loc11_:String = null;
         var _loc12_:CitrusObject = null;
         var _loc13_:CitrusEngine = null;
         param1.gotoAndStop(param3);
         var _loc4_:Array = [];
         var _loc5_:Number = param1.numChildren;
         var _loc7_:Number = 0;
         while(_loc7_ < _loc5_)
         {
            _loc6_ = param1.getChildAt(_loc7_) as MovieClip;
            if(_loc6_)
            {
               if(_loc6_.className)
               {
                  _loc8_ = getDefinitionByName(_loc6_.className) as Class;
                  _loc9_ = {};
                  if(_loc6_.params)
                  {
                     _loc9_ = _loc6_.params;
                  }
                  _loc9_.x = _loc6_.x;
                  _loc9_.y = _loc6_.y;
                  _loc10_ = _loc6_.rotation;
                  _loc6_.rotation = 0;
                  _loc9_.width = _loc6_.width;
                  _loc9_.height = _loc6_.height;
                  _loc6_.rotation = _loc10_;
                  _loc9_.rotation = _loc6_.rotation;
                  for(_loc11_ in _loc6_)
                  {
                     if(_loc11_ != "componentInspectorSetting" && _loc11_ != "className")
                     {
                        _loc9_[_loc11_] = _loc6_[_loc11_];
                     }
                  }
                  _loc12_ = new _loc8_(_loc6_.name,_loc9_);
                  _loc4_.push(_loc12_);
               }
            }
            _loc7_++;
         }
         if(param2)
         {
            _loc13_ = CitrusEngine.getInstance();
            for each(_loc12_ in _loc4_)
            {
               _loc13_.state.add(_loc12_);
            }
         }
         return _loc4_;
      }
      
      public static function FromTiledMap(param1:XML, param2:Array, param3:Boolean = true) : Array
      {
         var _loc6_:Object = null;
         var _loc7_:CitrusEngine = null;
         var _loc8_:CitrusObject = null;
         var _loc4_:Array = [];
         var _loc5_:TmxMap = new TmxMap(param1);
         for each(_loc6_ in _loc5_.layers_ordered)
         {
            if(_loc6_ is TmxLayer)
            {
               addTiledLayer(_loc5_,_loc6_ as TmxLayer,param2,_loc4_);
            }
            else
            {
               if(!(_loc6_ is TmxObjectGroup))
               {
                  throw new Error("Found layer type not supported.");
               }
               addTiledObjectgroup(_loc6_ as TmxObjectGroup,_loc4_);
            }
         }
         _loc7_ = CitrusEngine.getInstance();
         if(param3)
         {
            for each(_loc8_ in _loc4_)
            {
               _loc7_.state.add(_loc8_);
            }
         }
         return _loc4_;
      }
      
      private static function addTiledLayer(param1:TmxMap, param2:TmxLayer, param3:Array, param4:Array) : void
      {
         var _loc5_:Number = 0;
         var _loc6_:Number = 0;
         var _loc7_:Number = 0;
         var _loc10_:Object = null;
         var _loc11_:Bitmap = null;
         var _loc12_:Boolean = false;
         var _loc14_:Array = null;
         var _loc16_:* = 0;
         var _loc21_:Array = null;
         var _loc22_:String = null;
         var _loc24_:TmxTileSet = null;
         var _loc25_:Bitmap = null;
         var _loc26_:String = null;
         var _loc27_:Bitmap = null;
         var _loc28_:Number = 0;
         var _loc29_:Boolean = false;
         var _loc30_:Number = 0;
         var _loc31_:* = 0;
         var _loc32_:* = false;
         var _loc33_:* = false;
         var _loc34_:* = false;
         var _loc35_:int = 0;
         var _loc36_:int = 0;
         var _loc37_:Number = NaN;
         var _loc38_:Number = NaN;
         _loc5_ = 536870912;
         _loc6_ = 1073741824;
         _loc7_ = 2147483648;
         var _loc8_:uint = uint(~(_loc7_ | _loc6_ | _loc5_));
         var _loc9_:Number = Math.PI * 0.5;
         var _loc13_:Rectangle = new Rectangle();
         _loc13_.width = param1.tileWidth;
         _loc13_.height = param1.tileHeight;
         _loc14_ = param2.tileGIDs;
         var _loc15_:uint = _loc14_.length;
         var _loc17_:Matrix = new Matrix();
         var _loc18_:BitmapData = new BitmapData(param1.tileWidth,param1.tileHeight,true,0);
         var _loc19_:Rectangle = new Rectangle(0,0,param1.tileWidth,param1.tileHeight);
         var _loc20_:Point = new Point();
         var _loc23_:BitmapData = new BitmapData(param1.width * param1.tileWidth,param1.height * param1.tileHeight,true,0);
         var _loc39_:int = 0;
         var _loc40_:* = param1.tileSets;
         while(true)
         {
            for each(_loc24_ in _loc40_)
            {
               _loc21_ = _loc24_.imageSource.split("/");
               _loc22_ = _loc21_[_loc21_.length - 1];
               for each(_loc27_ in param3)
               {
                  _loc29_ = false;
                  if(_loc22_ == _loc27_.name)
                  {
                     _loc29_ = true;
                     _loc11_ = _loc27_;
                     break;
                  }
               }
               if(!_loc29_ || _loc11_ == null)
               {
                  break;
               }
               _loc12_ ||= _loc11_.smoothing;
               _loc24_.image = _loc11_.bitmapData;
               _loc28_ = 0;
               while(_loc28_ < _loc15_)
               {
                  _loc16_ = uint(_loc14_[_loc28_].length);
                  _loc30_ = 0;
                  while(_loc30_ < _loc16_)
                  {
                     _loc31_ = uint(_loc14_[_loc28_][_loc30_]);
                     _loc32_ = (_loc31_ & _loc7_) != 0;
                     _loc33_ = (_loc31_ & _loc6_) != 0;
                     _loc34_ = (_loc31_ & _loc5_) != 0;
                     _loc31_ &= _loc8_;
                     if(_loc31_ != 0)
                     {
                        _loc35_ = (_loc31_ - 1) / _loc24_.numCols;
                        _loc36_ = (_loc31_ - 1) % _loc24_.numCols;
                        _loc13_.x = _loc36_ * param1.tileWidth;
                        _loc13_.y = _loc35_ * param1.tileHeight;
                        _loc20_.x = _loc30_ * param1.tileWidth;
                        _loc20_.y = _loc28_ * param1.tileHeight;
                        if(_loc34_ || _loc32_ || _loc33_)
                        {
                           _loc37_ = _loc13_.x + _loc13_.width * 0.5;
                           _loc38_ = _loc13_.y + _loc13_.height * 0.5;
                           _loc17_.identity();
                           _loc17_.translate(-_loc37_,-_loc38_);
                           if(_loc34_)
                           {
                              if(_loc32_)
                              {
                                 _loc17_.rotate(_loc9_);
                                 if(_loc33_)
                                 {
                                    _loc17_.scale(1,-1);
                                 }
                              }
                              else
                              {
                                 _loc17_.rotate(-_loc9_);
                                 if(!_loc33_)
                                 {
                                    _loc17_.scale(1,-1);
                                 }
                              }
                           }
                           else
                           {
                              if(_loc32_)
                              {
                                 _loc17_.scale(-1,1);
                              }
                              if(_loc33_)
                              {
                                 _loc17_.scale(1,-1);
                              }
                           }
                           _loc17_.translate(_loc37_,_loc38_);
                           _loc17_.translate(-_loc13_.x,-_loc13_.y);
                           _loc18_.fillRect(_loc19_,0);
                           _loc18_.draw(_loc11_.bitmapData,_loc17_,null,null,_loc19_);
                           _loc23_.copyPixels(_loc18_,_loc19_,_loc20_);
                        }
                        else
                        {
                           _loc23_.copyPixels(_loc11_.bitmapData,_loc13_,_loc20_);
                        }
                     }
                     _loc30_++;
                  }
                  _loc28_++;
               }
            }
            _loc25_ = new Bitmap(_loc23_);
            _loc25_.smoothing = _loc12_;
            _loc10_ = {};
            _loc10_.view = _loc25_;
            _loc18_.dispose();
            for(_loc26_ in param2.properties)
            {
               _loc10_[_loc26_] = param2.properties[_loc26_];
            }
            param4.push(new CitrusSprite(param2.name,_loc10_));
            return;
         }
         throw new Error("ObjectMaker didn\'t find an image name corresponding to the tileset imagesource name: " + _loc24_.imageSource + ", add its name to your bitmap.");
      }
      
      private static function addTiledObjectgroup(param1:TmxObjectGroup, param2:Array) : void
      {
         var _loc3_:Class = null;
         var _loc4_:CitrusObject = null;
         var _loc5_:Object = null;
         var _loc6_:TmxObject = null;
         var _loc7_:String = null;
         for each(_loc6_ in param1.objects)
         {
            _loc3_ = getDefinitionByName(_loc6_.type) as Class;
            _loc5_ = {};
            for(_loc7_ in _loc6_.custom)
            {
               _loc5_[_loc7_] = _loc6_.custom[_loc7_];
            }
            _loc5_.x = _loc6_.x + _loc6_.width * 0.5;
            _loc5_.y = _loc6_.y + _loc6_.height * 0.5;
            _loc5_.width = _loc6_.width;
            _loc5_.height = _loc6_.height;
            _loc5_.rotation = _loc6_.rotation;
            if(_loc6_.points != null)
            {
               _loc5_.points = _loc6_.points;
            }
            _loc4_ = new _loc3_(_loc6_.name,_loc5_);
            param2.push(_loc4_);
         }
      }
      
      public static function FromGleed(param1:XML, param2:Boolean = true, param3:String = "group", param4:String = "citrus.objects.CitrusSprite") : Array
      {
         var _loc7_:Object = null;
         var _loc8_:String = null;
         var _loc10_:XML = null;
         var _loc11_:XML = null;
         var _loc12_:Number = NaN;
         var _loc13_:Number = NaN;
         var _loc14_:String = null;
         var _loc15_:String = null;
         var _loc16_:XML = null;
         var _loc17_:Class = null;
         var _loc18_:CitrusObject = null;
         var _loc19_:Number = NaN;
         var _loc20_:Number = NaN;
         var _loc21_:Number = NaN;
         var _loc22_:Number = NaN;
         var _loc23_:Number = NaN;
         var _loc5_:Number = 0;
         var _loc6_:Array = [];
         var _loc9_:CitrusEngine = CitrusEngine.getInstance();
         for each(_loc10_ in param1.Layers.Layer)
         {
            for each(_loc11_ in _loc10_.Items.Item)
            {
               _loc8_ = <EMAIL>();
               _loc12_ = Number(_loc11_.Position.X.toString());
               _loc13_ = Number(_loc11_.Position.Y.toString());
               _loc14_ = _loc11_.texture_filename.toString();
               if(_loc14_ != "")
               {
                  _loc19_ = Number(_loc11_.Origin.X.toString());
                  _loc20_ = Number(_loc11_.Origin.Y.toString());
                  _loc21_ = Number(_loc11_.Rotation.toString()) * 180 / Math.PI;
                  _loc7_ = {
                     "x":_loc12_,
                     "y":_loc13_,
                     "width":_loc19_ * 2,
                     "height":_loc20_ * 2,
                     "rotation":_loc21_,
                     "registration":"center"
                  };
                  _loc14_ = Replace(_loc14_,"\\","/");
                  _loc7_.view = _loc14_;
               }
               else
               {
                  _loc22_ = Number(_loc11_.Width.toString());
                  _loc23_ = Number(_loc11_.Height.toString());
                  _loc7_ = {
                     "x":_loc12_ + _loc22_ / 2,
                     "y":_loc13_ + _loc23_ / 2,
                     "width":_loc22_,
                     "height":_loc23_
                  };
               }
               if(param3)
               {
                  _loc7_[param3] = _loc5_;
               }
               _loc15_ = param4;
               for each(_loc16_ in _loc11_.CustomProperties.Property)
               {
                  if(<EMAIL>() == "className")
                  {
                     _loc15_ = _loc16_.string.toString();
                  }
                  else
                  {
                     _loc7_[<EMAIL>()] = _loc16_.string.toString();
                  }
               }
               _loc17_ = getDefinitionByName(_loc15_) as Class;
               _loc18_ = new _loc17_(_loc8_,_loc7_);
               if(param2)
               {
                  _loc9_.state.add(_loc18_);
               }
               _loc6_.push(_loc18_);
            }
            _loc5_++;
         }
         return _loc6_;
      }
      
      public static function FromLevelArchitect(param1:XML, param2:Boolean = true) : Array
      {
         var objectXML:XML = null;
         var params:Object = null;
         var paramXML:XML = null;
         var className:String = null;
         var theObject:CitrusObject = null;
         var theClass:Class = null;
         var levelData:XML = param1;
         var addToCurrentState:Boolean = param2;
         var array:Array = [];
         var state:IState = CitrusEngine.getInstance().state;
         for each(objectXML in levelData.CitrusObject)
         {
            params = {};
            for each(paramXML in objectXML.Property)
            {
               params[paramXML.@name] = paramXML.toString();
            }
            className = objectXML.@className;
            try
            {
               theClass = getDefinitionByName(className) as Class;
            }
            catch(e:Error)
            {
               if(e.errorID == 1065)
               {
                  throw new Error("You (yes, YOU) must import and create a reference to the " + className + " class somewhere in your code. The Level Architect cannot create objects unless they are compiled into the SWF.");
               }
               throw e;
            }
            theObject = new theClass(objectXML.@name,params);
            array.push(theObject);
            if(addToCurrentState)
            {
               state.add(theObject);
            }
         }
         return array;
      }
      
      private static function Replace(param1:String, param2:String, param3:String) : String
      {
         return param1.split(param2).join(param3);
      }
   }
}

