package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   import mogames.gameRole.HeroManager;
   import mogames.gameRole.co.BaseRole;
   import mogames.gameRole.enemy.BaseEnemy;
   import mogames.gameRole.hero.BaseHero;
   
   public class CandleBuff extends BaseBuff
   {
      private var _hurt:int;
      
      public function CandleBuff(param1:int)
      {
         super(param1,true,true);
      }
      
      override public function setBuffVO(param1:BuffVO) : void
      {
         super.setBuffVO(param1);
         this._hurt = param1.argDic.hurt.v;
      }
      
      override protected function startInterval() : void
      {
         _buffTimer.setInterval(0.5,_buffVO.sec.v,this.onBuff,onEnd);
      }
      
      override protected function onBuff() : void
      {
         if(_roleVO.isDead())
         {
            return;
         }
         if(_role is BaseEnemy)
         {
            this.handlerHurtHero();
         }
      }
      
      private function handlerHurtHero() : void
      {
         if(!_role)
         {
            return;
         }
         this.onHurtRole(HeroManager.onePlayer);
         this.onHurtRole(HeroManager.twoPlayer);
      }
      
      private function onHurtRole(param1:BaseHero) : void
      {
         if(!param1 || param1.isDead)
         {
            return;
         }
         this.handlerHurt(param1);
         if(param1.petActive)
         {
            this.handlerHurt(param1.ownPet);
         }
      }
      
      private function handlerHurt(param1:BaseRole) : void
      {
         if(Math.abs(param1.x - _role.x) > _role.width)
         {
            return;
         }
         param1.roleVO.changeHP(-this._hurt);
         param1.addHurtNumber(0,this._hurt,0);
         EffectManager.instance().addBMCEffect("EFFECT_FIRE_BOOM_CLIP",Layers.ceEffectLayer,param1.x,param1.y);
         EffectManager.instance().playAudio("FIRE_EXPLORE");
      }
      
      override public function get buffSkin() : BMCSprite
      {
         _skin.setupSequence(AssetManager.findAnimation("EFFECT_CANDLE_BUFF" + buffID.v),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height;
         return _skin;
      }
   }
}

