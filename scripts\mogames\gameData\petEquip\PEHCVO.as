package mogames.gameData.petEquip
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.good.GameEquipVO;
   import mogames.gameData.good.constvo.ConstEquipVO;
   
   public class PEHCVO extends BaseUseVO
   {
      public var clipID:Sint;
      
      public var equipID:Sint;
      
      public function PEHCVO(param1:int, param2:int, param3:int, param4:Array = null)
      {
         super(param3,param4);
         this.clipID = new Sint(param1);
         this.equipID = new Sint(param2);
      }
      
      public function get constVO() : ConstEquipVO
      {
         return GoodConfig.instance().findConstGood(this.equipID.v) as ConstEquipVO;
      }
      
      public function handlerHC() : GameEquipVO
      {
         useStuff();
         var _loc1_:GameEquipVO = GoodConfig.instance().newGood(this.equipID.v) as GameEquipVO;
         _loc1_.initNewEquip(3,false,false);
         return _loc1_;
      }
   }
}

