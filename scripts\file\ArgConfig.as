package file
{
   import mogames.gameData.base.ArgVO;
   
   public class ArgConfig
   {
      private static var _instance:ArgConfig;
      
      private var _normals:Vector.<ArgVO>;
      
      private var _fashions:Vector.<ArgVO>;
      
      public function ArgConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : ArgConfig
      {
         if(!_instance)
         {
            _instance = new ArgConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._normals = new Vector.<ArgVO>();
         this._normals.push(new ArgVO(0,1));
         this._normals.push(new ArgVO(1,1.2));
         this._normals.push(new ArgVO(2,1.4));
         this._normals.push(new ArgVO(3,1.6));
         this._normals.push(new ArgVO(4,2.5));
         this._fashions = new Vector.<ArgVO>();
         this._fashions.push(new ArgVO(0,1));
         this._fashions.push(new ArgVO(1,1.5));
         this._fashions.push(new ArgVO(2,2));
         this._fashions.push(new ArgVO(3,2.5));
         this._fashions.push(new ArgVO(4,3));
      }
      
      public function findQuality(param1:int, param2:Boolean = false) : ArgVO
      {
         var _loc4_:ArgVO = null;
         var _loc3_:Vector.<ArgVO> = param2 ? this._fashions : this._normals;
         for each(_loc4_ in _loc3_)
         {
            if(_loc4_.id.v == param1)
            {
               return _loc4_;
            }
         }
         return null;
      }
   }
}

