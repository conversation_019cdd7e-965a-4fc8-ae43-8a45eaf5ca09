package mogames.gameData.good
{
   import mogames.gameData.good.constvo.ConstEquipVO;
   import mogames.gameData.good.constvo.ConstFabaoVO;
   import mogames.gameSystem.CitrusTimer;
   
   public class GameFabaoVO extends GameEquipVO
   {
      public var constFabao:ConstFabaoVO;
      
      public var isCD:Boolean;
      
      private var _timer:CitrusTimer;
      
      public function GameFabaoVO(param1:ConstFabaoVO)
      {
         this.constFabao = param1;
         super(param1 as ConstEquipVO);
         this._timer = new CitrusTimer();
      }
      
      public function startCD(param1:Number) : void
      {
         var func:Function = null;
         var time:Number = param1;
         func = function():void
         {
            isCD = false;
         };
         this.isCD = true;
         this._timer.setTimeOut(time,func);
      }
      
      public function get leftTime() : int
      {
         return this._timer.leftTime;
      }
      
      public function initBattleData() : void
      {
         this.isCD = false;
         this._timer.pause();
      }
      
      override public function destroy() : void
      {
         this._timer.destroy();
         this._timer = null;
         super.destroy();
      }
   }
}

