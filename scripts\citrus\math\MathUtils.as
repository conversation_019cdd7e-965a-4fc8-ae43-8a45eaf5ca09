package citrus.math
{
   import flash.display.DisplayObject;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class MathUtils
   {
      public function MathUtils()
      {
         super();
      }
      
      public static function DistanceBetweenTwoPoints(param1:Number, param2:Number, param3:Number, param4:Number) : Number
      {
         var _loc5_:Number = param1 - param2;
         var _loc6_:Number = param3 - param4;
         return Math.sqrt(_loc5_ * _loc5_ + _loc6_ * _loc6_);
      }
      
      public static function RotateAroundInternalPoint(param1:DisplayObject, param2:Point, param3:Number) : void
      {
         var _loc4_:Matrix = param1.transform.matrix;
         var _loc5_:Point = param2;
         _loc5_ = _loc4_.transformPoint(_loc5_);
         RotateAroundExternalPoint(param1,_loc5_,param3);
      }
      
      public static function RotateAroundExternalPoint(param1:DisplayObject, param2:Point, param3:Number) : void
      {
         var _loc4_:Matrix = param1.transform.matrix;
         _loc4_.translate(-param2.x,-param2.y);
         _loc4_.rotate(param3 * (Math.PI / 180));
         _loc4_.translate(param2.x,param2.y);
         param1.transform.matrix = _loc4_;
      }
      
      public static function rotatePoint(param1:Number, param2:Number, param3:Number, param4:Point = null) : Point
      {
         var _loc5_:Number = Math.cos(param3);
         var _loc6_:Number = Math.sin(param3);
         if(param4)
         {
            param4.setTo(param1 * _loc5_ + param2 * _loc6_,-param1 * _loc6_ + param2 * _loc5_);
            return null;
         }
         return new Point(param1 * _loc5_ + param2 * _loc6_,-param1 * _loc6_ + param2 * _loc5_);
      }
      
      public static function lineEquation(param1:Point, param2:Point) : Object
      {
         var _loc3_:Number = (param2.y - param1.y) / (param2.x - param1.x);
         var _loc4_:Number = param1.y - _loc3_ * param1.x;
         return {
            "m":_loc3_,
            "b":_loc4_
         };
      }
      
      public static function lerp(param1:Number, param2:Number, param3:Number) : Number
      {
         return param1 + (param2 - param1) * param3;
      }
      
      public static function createAABB(param1:Number, param2:Number, param3:Number, param4:Number, param5:Number = 0) : Rectangle
      {
         var _loc9_:Boolean = false;
         var _loc10_:Boolean = false;
         var _loc6_:Rectangle = new Rectangle(param1,param2,param3,param4);
         if(param5 == 0)
         {
            return _loc6_;
         }
         var _loc7_:Number = Math.cos(param5);
         var _loc8_:Number = Math.sin(param5);
         if(_loc8_ < 0)
         {
            _loc8_ = -_loc8_;
            _loc10_ = false;
         }
         else
         {
            _loc10_ = true;
         }
         if(_loc7_ < 0)
         {
            _loc7_ = -_loc7_;
            _loc9_ = false;
         }
         else
         {
            _loc9_ = true;
         }
         _loc6_.width = param4 * _loc8_ + param3 * _loc7_;
         _loc6_.height = param4 * _loc7_ + param3 * _loc8_;
         if(_loc9_)
         {
            if(_loc10_)
            {
               _loc6_.x -= param4 * _loc8_;
            }
            else
            {
               _loc6_.y -= param3 * _loc8_;
            }
         }
         else if(_loc10_)
         {
            _loc6_.x -= param3 * _loc7_ + param4 * _loc8_;
            _loc6_.y -= param4 * _loc7_;
         }
         else
         {
            _loc6_.x -= param3 * _loc7_;
            _loc6_.y -= param3 * _loc8_ + param4 * _loc7_;
         }
         return _loc6_;
      }
      
      public static function createAABBData(param1:Number, param2:Number, param3:Number, param4:Number, param5:Number = 0, param6:Object = null) : Object
      {
         var _loc11_:Boolean = false;
         var _loc12_:Boolean = false;
         if(param6 == null)
         {
            param6 = {
               "offsetX":0,
               "offsetY":0,
               "rect":new Rectangle()
            };
         }
         param6.rect.setTo(param1,param2,param3,param4);
         var _loc7_:Number = 0;
         var _loc8_:Number = 0;
         if(param5 == 0)
         {
            param6.offsetX = 0;
            param6.offsetY = 0;
            return param6;
         }
         var _loc9_:Number = Math.cos(param5);
         var _loc10_:Number = Math.sin(param5);
         if(_loc10_ < 0)
         {
            _loc10_ = -_loc10_;
            _loc12_ = false;
         }
         else
         {
            _loc12_ = true;
         }
         if(_loc9_ < 0)
         {
            _loc9_ = -_loc9_;
            _loc11_ = false;
         }
         else
         {
            _loc11_ = true;
         }
         param6.rect.width = param4 * _loc10_ + param3 * _loc9_;
         param6.rect.height = param4 * _loc9_ + param3 * _loc10_;
         if(_loc11_)
         {
            if(_loc12_)
            {
               _loc7_ -= param4 * _loc10_;
            }
            else
            {
               _loc8_ -= param3 * _loc10_;
            }
         }
         else if(_loc12_)
         {
            _loc7_ -= param3 * _loc9_ + param4 * _loc10_;
            _loc8_ -= param4 * _loc9_;
         }
         else
         {
            _loc7_ -= param3 * _loc9_;
            _loc8_ -= param3 * _loc10_ + param4 * _loc9_;
         }
         param6.rect.x += param6.offsetX = _loc7_;
         param6.rect.y += param6.offsetY = _loc8_;
         return param6;
      }
      
      public static function angleBetween(param1:Number, param2:Number, param3:Number) : Boolean
      {
         var _loc4_:Number = Math.PI * 2;
         param1 = (_loc4_ + param1 % _loc4_) % _loc4_;
         param2 = (_loc4_ * 100 + param2) % _loc4_;
         param3 = (_loc4_ * 100 + param3) % _loc4_;
         if(param2 < param3)
         {
            return param2 <= param1 && param1 <= param3;
         }
         return param2 <= param1 || param1 <= param3;
      }
      
      public static function linesIntersection(param1:Number, param2:Number, param3:Number, param4:Number, param5:Number, param6:Number, param7:Number, param8:Number, param9:Boolean = true) : Point
      {
         var _loc10_:Point = null;
         var _loc11_:Number = NaN;
         var _loc12_:Number = NaN;
         var _loc13_:Number = NaN;
         var _loc14_:Number = NaN;
         var _loc15_:Number = NaN;
         var _loc16_:Number = NaN;
         _loc11_ = param4 - param2;
         _loc13_ = param1 - param3;
         _loc15_ = param3 * param2 - param1 * param4;
         _loc12_ = param8 - param6;
         _loc14_ = param5 - param7;
         _loc16_ = param7 * param6 - param5 * param8;
         var _loc17_:Number = _loc11_ * _loc14_ - _loc12_ * _loc13_;
         if(_loc17_ == 0)
         {
            return null;
         }
         _loc10_ = new Point();
         _loc10_.x = (_loc13_ * _loc16_ - _loc14_ * _loc15_) / _loc17_;
         _loc10_.y = (_loc12_ * _loc15_ - _loc11_ * _loc16_) / _loc17_;
         if(param9)
         {
            if(pow2(_loc10_.x - param3) + pow2(_loc10_.y - param4) > pow2(param1 - param3) + pow2(param2 - param4))
            {
               return null;
            }
            if(pow2(_loc10_.x - param1) + pow2(_loc10_.y - param2) > pow2(param1 - param3) + pow2(param2 - param4))
            {
               return null;
            }
            if(pow2(_loc10_.x - param7) + pow2(_loc10_.y - param8) > pow2(param5 - param7) + pow2(param6 - param8))
            {
               return null;
            }
            if(pow2(_loc10_.x - param5) + pow2(_loc10_.y - param6) > pow2(param5 - param7) + pow2(param6 - param8))
            {
               return null;
            }
         }
         return _loc10_;
      }
      
      public static function pow2(param1:Number) : Number
      {
         return param1 * param1;
      }
      
      public static function clamp01(param1:Number) : Number
      {
         return param1 < 0 ? 0 : (param1 > 1 ? 1 : param1);
      }
      
      public static function randomInt(param1:int, param2:int) : int
      {
         return Math.floor(Math.random() * (1 + param2 - param1)) + param1;
      }
      
      public static function getBestFitRatio(param1:Rectangle, param2:Rectangle) : Number
      {
         if(param2.height / param2.width > param1.height / param1.width)
         {
            return param2.width / param1.width;
         }
         return param2.height / param1.height;
      }
      
      public static function getFillRatio(param1:Rectangle, param2:Rectangle) : Number
      {
         if(param2.height / param2.width > param1.height / param1.width)
         {
            return param2.height / param1.height;
         }
         return param2.width / param1.width;
      }
      
      public static function getArrayRandomItem(param1:Array) : *
      {
         return param1[randomInt(0,param1.length - 1)];
      }
      
      public static function getNextInArray(param1:*, param2:Array) : *
      {
         var _loc3_:int = param2.lastIndexOf(param1) + 1;
         if(_loc3_ >= param2.length)
         {
            _loc3_ = 0;
         }
         return param2[_loc3_];
      }
      
      public static function getPreviousInArray(param1:*, param2:Array) : *
      {
         var _loc3_:int = param2.lastIndexOf(param1) - 1;
         if(_loc3_ < 0)
         {
            _loc3_ = int(param2.length - 1);
         }
         return param2[_loc3_];
      }
      
      public static function getRandomColor(param1:uint = 0, param2:uint = 255, param3:Boolean = false, param4:Boolean = false) : uint
      {
         var _loc8_:* = 0;
         param2 = param2 > 255 ? 255 : param2;
         param1 = param1 > 255 ? 255 : param1;
         var _loc5_:uint = uint(MathUtils.randomInt(param1,param2));
         var _loc6_:uint = uint(MathUtils.randomInt(param1,param2));
         var _loc7_:uint = uint(MathUtils.randomInt(param1,param2));
         if(!param3)
         {
            return _loc5_ << 16 | _loc6_ << 8 | _loc7_;
         }
         _loc8_ = param4 ? uint(MathUtils.randomInt(0,255)) : 255;
         return _loc8_ << 24 | _loc5_ << 16 | _loc6_ << 8 | _loc7_;
      }
      
      public static function colorLerp(param1:uint, param2:uint, param3:Number) : uint
      {
         var _loc4_:Number = 1 - param3;
         var _loc5_:uint = uint(param1 >> 24 & 255);
         var _loc6_:uint = uint(param1 >> 16 & 255);
         var _loc7_:uint = uint(param1 >> 8 & 255);
         var _loc8_:uint = uint(param1 & 255);
         var _loc9_:uint = uint(param2 >> 24 & 255);
         var _loc10_:uint = uint(param2 >> 16 & 255);
         var _loc11_:uint = uint(param2 >> 8 & 255);
         var _loc12_:uint = uint(param2 & 255);
         var _loc13_:uint = _loc5_ * _loc4_ + _loc9_ * param3;
         var _loc14_:uint = _loc6_ * _loc4_ + _loc10_ * param3;
         var _loc15_:uint = _loc7_ * _loc4_ + _loc11_ * param3;
         var _loc16_:uint = _loc8_ * _loc4_ + _loc12_ * param3;
         return uint(_loc13_ << 24 | _loc14_ << 16 | _loc15_ << 8 | _loc16_);
      }
      
      public static function abs(param1:Number) : Number
      {
         return param1 < 0 ? -param1 : param1;
      }
      
      public static function logx(param1:Number, param2:Number = 10) : Number
      {
         return Math.log(param1) / Math.log(param2);
      }
      
      public static function easeInQuad(param1:Number, param2:Number, param3:Number, param4:Number) : Number
      {
         return param3 * (param1 = param1 / param4) * param1 + param2;
      }
      
      public static function easeOutQuad(param1:Number, param2:Number, param3:Number, param4:Number) : Number
      {
         return -param3 * (param1 = param1 / param4) * (param1 - 2) + param2;
      }
      
      public static function easeInCubic(param1:Number, param2:Number, param3:Number, param4:Number) : Number
      {
         return param3 * (param1 = param1 / param4) * param1 * param1 + param2;
      }
      
      public static function easeOutCubic(param1:Number, param2:Number, param3:Number, param4:Number) : Number
      {
         return param3 * ((param1 = param1 / param4 - 1) * param1 * param1 + 1) + param2;
      }
      
      public static function easeInQuart(param1:Number, param2:Number, param3:Number, param4:Number) : Number
      {
         return param3 * (param1 = param1 / param4) * param1 * param1 * param1 + param2;
      }
      
      public static function easeOutQuart(param1:Number, param2:Number, param3:Number, param4:Number) : Number
      {
         return -param3 * ((param1 = param1 / param4 - 1) * param1 * param1 * param1 - 1) + param2;
      }
   }
}

