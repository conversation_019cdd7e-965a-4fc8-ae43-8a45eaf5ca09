package mogames.gameData.forge
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.fabao.BaseUseVO;
   import mogames.gameData.good.GameGoodVO;
   
   public class ForgeVO extends BaseUseVO
   {
      public var level:Sint;
      
      public var per:Sint;
      
      public var needVO:NeedVO;
      
      public function ForgeVO(param1:int, param2:int, param3:int, param4:NeedVO)
      {
         super(param3);
         this.level = new Sint(param1);
         this.per = new Sint(param2);
         this.needVO = param4;
         needList = [param4];
      }
      
      public function get stone() : GameGoodVO
      {
         return this.needVO.needGood;
      }
      
      public function get stoneStr() : String
      {
         return this.needVO.needStr;
      }
   }
}

