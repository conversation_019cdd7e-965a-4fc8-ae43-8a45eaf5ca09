package mogames.gameFabao.view
{
   import mogames.gameData.ConstRole;
   import mogames.gameEffect.EffectManager;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoZhaoYaoJing;
   
   public class FabaoZhaoYaoJingView extends FabaoBaseView
   {
      private var _fabao:FabaoZhaoYaoJing;
      
      public function FabaoZhaoYaoJingView()
      {
         super();
      }
      
      override protected function updateCurrentFrame(param1:int) : void
      {
         this.addSound(param1);
         super.updateCurrentFrame(param1);
         if(!mcATK)
         {
            return;
         }
         if(curStatus == ConstRole.SKILL_ONE)
         {
            this._fabao.dispatchSkillOne();
         }
      }
      
      override public function setOwner(param1:BaseFabao) : void
      {
         super.setOwner(param1);
         this._fabao = param1 as FabaoZhaoYaoJing;
      }
      
      override protected function addSound(param1:int) : void
      {
         if(param1 == 40)
         {
            EffectManager.instance().playAudio("SKILL_ZHAO_YAO_JING");
         }
      }
   }
}

