package citrus.view.spriteview
{
   import citrus.physics.APhysicsEngine;
   import citrus.view.ACitrusView;
   import citrus.view.ISpriteView;
   import flash.display.Sprite;
   
   public class SpriteView extends ACitrusView
   {
      private var _viewRoot:Sprite;
      
      public function SpriteView(param1:Sprite)
      {
         super(param1,ISpriteView);
         this._viewRoot = new Sprite();
         param1.addChild(this._viewRoot);
         var _loc2_:int = 0;
         while(_loc2_ < 6)
         {
            this._viewRoot.addChild(new Sprite());
            _loc2_++;
         }
         camera = new SpriteCamera(this._viewRoot);
      }
      
      public function get viewRoot() : Sprite
      {
         return this._viewRoot;
      }
      
      override public function destroy() : void
      {
         this._viewRoot.removeChildren();
         if(this._viewRoot.parent)
         {
            this._viewRoot.parent.removeChild(this._viewRoot);
         }
         this._viewRoot = null;
         super.destroy();
      }
      
      override public function update(param1:Number) : void
      {
         var _loc2_:SpriteArt = null;
         super.update(param1);
         for each(_loc2_ in _viewObjects)
         {
            if(_loc2_.group != _loc2_.citrusObject.group)
            {
               this.updateGroupForSprite(_loc2_);
            }
            if(_loc2_.updateArtEnabled)
            {
               _loc2_.update(this);
            }
         }
         if(camera.enabled)
         {
            camera.update();
         }
      }
      
      override protected function createArt(param1:Object) : Object
      {
         var _loc2_:ISpriteView = param1 as ISpriteView;
         if(param1 is APhysicsEngine)
         {
            param1.view = SpritePhysicsDebugView;
         }
         var _loc3_:SpriteArt = new SpriteArt(_loc2_);
         _loc3_.update(this);
         this.updateGroupForSprite(_loc3_);
         return _loc3_;
      }
      
      override protected function destroyArt(param1:Object) : void
      {
         var _loc2_:SpriteArt = _viewObjects[param1];
         _loc2_.destroy();
         _loc2_.parent.removeChild(_loc2_);
      }
      
      private function updateGroupForSprite(param1:SpriteArt) : void
      {
         if(param1.citrusObject.group > this._viewRoot.numChildren + 100)
         {
         }
         while(param1.citrusObject.group >= this._viewRoot.numChildren)
         {
            this._viewRoot.addChild(new Sprite());
         }
         Sprite(this._viewRoot.getChildAt(param1.citrusObject.group)).addChild(param1);
      }
   }
}

