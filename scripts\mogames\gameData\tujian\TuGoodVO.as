package mogames.gameData.tujian
{
   import file.GoodConfig;
   import mogames.gameData.good.constvo.ConstEquipVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   
   public class TuGoodVO
   {
      public var id:int;
      
      public var constVO:ConstGoodVO;
      
      public var num:int;
      
      protected var _quality:int;
      
      public var isEquip:Boolean;
      
      public function TuGoodVO(param1:int, param2:int = 1, param3:int = 0)
      {
         super();
         this.id = param1;
         this.num = param2;
         this.constVO = GoodConfig.instance().findConstGood(param1);
         this.isEquip = this.constVO is ConstEquipVO;
         this._quality = this.isEquip ? param3 : this.constVO.quality.v;
      }
      
      public function get curQuality() : int
      {
         return this._quality;
      }
      
      public function get tipVO() : Object
      {
         if(this.isEquip)
         {
            return {
               "equip":this.constVO,
               "quality":this._quality
            };
         }
         return this.constVO;
      }
      
      public function get tipID() : int
      {
         return this.isEquip ? 2001 : 1001;
      }
   }
}

