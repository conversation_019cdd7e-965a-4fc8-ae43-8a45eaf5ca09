package mogames.gameFabao
{
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameFabao.fabao.BaseFabao;
   import mogames.gameFabao.fabao.FabaoBiShuiZhu;
   import mogames.gameFabao.fabao.FabaoDongHuangZhong;
   import mogames.gameFabao.fabao.FabaoErQiPing;
   import mogames.gameFabao.fabao.FabaoFuYaoSuo;
   import mogames.gameFabao.fabao.FabaoJinNao;
   import mogames.gameFabao.fabao.FabaoLingLongDing;
   import mogames.gameFabao.fabao.FabaoWeiBJS;
   import mogames.gameFabao.fabao.FabaoYuJinPing;
   import mogames.gameFabao.fabao.FabaoYuShouDai;
   import mogames.gameFabao.fabao.FabaoZhaoYaoJing;
   import mogames.gameFabao.fabao.FabaoZhenBJS;
   import mogames.gameFabao.fabao.FabaoZhenYaoTa;
   import mogames.gameFabao.fabao.FabaoZiJinLing;
   import mogames.gameFabao.fabao.FabaoZiZhuLan;
   
   public class FabaoFactory
   {
      public function FabaoFactory()
      {
         super();
      }
      
      public static function newFabao(param1:GameFabaoVO) : BaseFabao
      {
         switch(param1.constFabao.id.v)
         {
            case 23001:
               return new FabaoFuYaoSuo(param1);
            case 23002:
               return new FabaoZhaoYaoJing(param1);
            case 23003:
               return new FabaoYuJinPing(param1);
            case 23004:
               return new FabaoBiShuiZhu(param1);
            case 23005:
               return new FabaoZhenYaoTa(param1);
            case 23006:
               return new FabaoWeiBJS(param1);
            case 23007:
               return new FabaoZhenBJS(param1);
            case 23008:
               return new FabaoYuShouDai(param1);
            case 23009:
               return new FabaoZiJinLing(param1);
            case 23010:
               return new FabaoLingLongDing(param1);
            case 23011:
               return new FabaoZiZhuLan(param1);
            case 23012:
               return new FabaoErQiPing(param1);
            case 23013:
               return new FabaoJinNao(param1);
            case 23014:
               return new FabaoDongHuangZhong(param1);
            default:
               return null;
         }
      }
   }
}

