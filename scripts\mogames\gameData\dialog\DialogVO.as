package mogames.gameData.dialog
{
   import mogames.gameData.role.HeroProxy;
   
   public class DialogVO
   {
      public var type:int;
      
      public var dialog:String;
      
      private var _name:String;
      
      private var _iname:String;
      
      public function DialogVO(param1:int, param2:String, param3:String, param4:String)
      {
         super();
         this.type = param1;
         this._name = param2;
         this._iname = param3;
         this.dialog = param4;
      }
      
      public function get iname() : String
      {
         if(this._name != "MASTER")
         {
            return this._iname;
         }
         var _loc1_:int = int(HeroProxy.instance().teamHero[0].constVO.roleID.v);
         switch(_loc1_)
         {
            case 1001:
               return "BODY_SUN_WU_KONG" + this._iname;
            case 1002:
               return "BODY_LONG_MA" + this._iname;
            case 1003:
               return "BODY_ZHU_BA_JIE" + this._iname;
            case 1004:
               return "BODY_SHA_SENG" + this._iname;
            case 1005:
               return "BODY_XIAO_LONG_NV" + this._iname;
            default:
               return "";
         }
      }
      
      public function get name() : String
      {
         if(this._name == "MASTER")
         {
            return HeroProxy.instance().teamHero[0].assetVO.name;
         }
         return this._name;
      }
   }
}

