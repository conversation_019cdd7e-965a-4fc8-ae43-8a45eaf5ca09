package mogames.gameData.heroSkill.constvo
{
   import mogames.gameData.heroSkill.SkillLevelVO;
   
   public class ConstSkillTwo extends ConstSkillVO
   {
      public function ConstSkillTwo(param1:int, param2:String, param3:String, param4:String, param5:String)
      {
         super(param1,param2,param3,param4,param5);
      }
      
      override protected function initCondition() : void
      {
         super.initCondition();
         _lvVec.push(new SkillLevelVO(0,2,400));
         _lvVec.push(new SkillLevelVO(1,7,800));
         _lvVec.push(new SkillLevelVO(2,12,1200));
         _lvVec.push(new SkillLevelVO(3,17,1600));
         _lvVec.push(new SkillLevelVO(4,22,2000));
         _lvVec.push(new SkillLevelVO(5,27,2400));
         _lvVec.push(new SkillLevelVO(6,32,2800));
         _lvVec.push(new SkillLevelVO(7,37,3200));
         _lvVec.push(new SkillLevelVO(8,42,3600));
         _lvVec.push(new SkillLevelVO(9,47,4000));
      }
   }
}

