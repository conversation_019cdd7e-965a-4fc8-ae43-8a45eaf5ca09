package citrus.utils.objectmakers.tmx
{
   public class TmxMap
   {
      private static const TILE_LAYER_NAME:String = "layer";
      
      private static const OBJECT_LAYER_NAME:String = "objectgroup";
      
      public var version:String;
      
      public var orientation:String;
      
      public var width:uint;
      
      public var height:uint;
      
      public var tileWidth:uint;
      
      public var tileHeight:uint;
      
      public var properties:TmxPropertySet = null;
      
      public var tileSets:Object;
      
      public var layers_ordered:Array;
      
      public function TmxMap(param1:XML)
      {
         var _loc2_:XML = null;
         this.tileSets = {};
         this.layers_ordered = [];
         super();
         this.version = !!param1.@version ? param1.@version : "unknown";
         this.orientation = !!param1.@orientation ? param1.@orientation : "orthogonal";
         this.width = param1.@width;
         this.height = param1.@height;
         this.tileWidth = param1.@tilewidth;
         this.tileHeight = param1.@tileheight;
         for each(_loc2_ in param1.properties)
         {
            this.properties = !!this.properties ? this.properties.extend(_loc2_) : new TmxPropertySet(_loc2_);
         }
         for each(_loc2_ in param1.tileset)
         {
            this.tileSets[_loc2_.@name] = new TmxTileSet(_loc2_,this);
         }
         for each(_loc2_ in param1.children())
         {
            if(_loc2_.name() == TILE_LAYER_NAME)
            {
               this.layers_ordered.push(new TmxLayer(_loc2_,this));
            }
            else if(_loc2_.name() == OBJECT_LAYER_NAME)
            {
               this.layers_ordered.push(new TmxObjectGroup(_loc2_,this));
            }
         }
      }
      
      public function getTileSet(param1:String) : TmxTileSet
      {
         return this.tileSets[param1] as TmxTileSet;
      }
      
      public function getGidOwner(param1:int) : TmxTileSet
      {
         var _loc2_:TmxTileSet = null;
         for each(_loc2_ in this.tileSets)
         {
            if(_loc2_.hasGid(param1))
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

