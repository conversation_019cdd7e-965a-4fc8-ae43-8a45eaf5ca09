package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameEffect.EffectManager;
   import mogames.gameObj.display.BMCSprite;
   
   public class MDEFDeBuff extends BaseBuff
   {
      private var _reMDEF:int;
      
      public function MDEFDeBuff()
      {
         super(1015,true);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         this._reMDEF = _roleVO.totalMDEF.v * _buffVO.argDic.per * 0.01;
         _roleVO.skillMDEF.v -= this._reMDEF;
         _roleVO.updateMDEF();
         if(_role.isReady)
         {
            EffectManager.instance().addHeadWord("魔防降低！",_role.x,_role.y - _role.height);
         }
      }
      
      override protected function onEnd() : void
      {
         _roleVO.skillMDEF.v += this._reMDEF;
         _roleVO.updateMDEF();
         destroy();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         if(!_buffVO.argDic.skin)
         {
            return null;
         }
         _skin.setupSequence(AssetManager.findAnimation(_buffVO.argDic.skin),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

