package mogames.gameMission.mission.npc.huaguoshan
{
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameUI.pet.MiniPetModule;
   import utils.MethodUtil;
   
   public class SceneShuiLianDong extends BaseScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _btnPet:SimpleButton;
      
      public function SceneShuiLianDong(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_GAOLAOZHUANG");
      }
      
      override protected function handlerInit() : void
      {
         this.layoutPetBtn();
         this.initEnemies();
      }
      
      private function initEnemies() : void
      {
         this._eTrigger0 = new EnemyTrigger(_mission,[[193,316,150,100],[960,316,150,100]],211);
         this._eTrigger1 = new EnemyTrigger(_mission,[[960,316,150,100],[1300,316,150,100]],212,this.triggerEnd1);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,450),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(900,450),1);
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger0.okFunc = this._pTrigger1.start;
         this._pTrigger0.start();
      }
      
      private function triggerEnd1() : void
      {
         addLeaveDoor(1200,388);
      }
      
      private function layoutPetBtn() : void
      {
         this._btnPet = AssetManager.newButtonRes("BTN_PET_CLIP",887,16);
         Layers.frameLayer.addChild(this._btnPet);
         this._btnPet.addEventListener(MouseEvent.CLICK,this.onPet,false,0,true);
      }
      
      private function removePetBtn() : void
      {
         this._btnPet.removeEventListener(MouseEvent.CLICK,this.onPet);
         MethodUtil.removeMe(this._btnPet);
         this._btnPet = null;
      }
      
      private function onPet(param1:MouseEvent) : void
      {
         MiniPetModule.instance().init();
      }
      
      override protected function listenLeave() : void
      {
         (_mission as HuaGuoShanMission).enterHuaGuoShan();
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger0)
         {
            this._eTrigger0.destroy();
         }
         if(this._eTrigger1)
         {
            this._eTrigger1.destroy();
         }
         if(this._pTrigger0)
         {
            this._pTrigger0.destroy();
         }
         if(this._pTrigger1)
         {
            this._pTrigger1.destroy();
         }
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this.removePetBtn();
         super.destroy();
      }
   }
}

