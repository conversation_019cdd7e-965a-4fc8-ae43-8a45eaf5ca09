package mogames.gameData.huoli
{
   import file.HuoLiConfig;
   import mogames.gameData.huoli.vo.HuoLiRewardVO;
   import mogames.gameData.huoli.vo.HuoLiVO;
   
   public class HuoLiProxy
   {
      private static var _instance:HuoLiProxy;
      
      public var huolis:Array;
      
      public function HuoLiProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : HuoLiProxy
      {
         if(!_instance)
         {
            _instance = new HuoLiProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.huolis = HuoLiConfig.instance().newHuoLis();
      }
      
      public function set loadData(param1:String) : void
      {
         var _loc3_:* = null;
         var _loc4_:Array = null;
         var _loc5_:HuoLiVO = null;
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         for each(_loc3_ in _loc2_)
         {
            _loc4_ = _loc3_.split("H");
            _loc5_ = this.findVO(int(_loc4_[0]));
            if(_loc5_)
            {
               _loc5_.cur.v = int(_loc4_[1]);
            }
         }
      }
      
      public function get saveData() : String
      {
         var _loc2_:HuoLiVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this.huolis)
         {
            _loc1_.push(_loc2_.saveData);
         }
         return _loc1_.join("T");
      }
      
      public function dailyRefresh() : void
      {
         this.huolis = null;
         this.startNew();
      }
      
      public function findVO(param1:int) : HuoLiVO
      {
         var _loc2_:HuoLiVO = null;
         for each(_loc2_ in this.huolis)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function setNum(param1:int) : void
      {
         var _loc2_:HuoLiVO = null;
         for each(_loc2_ in this.huolis)
         {
            if(_loc2_.id.v == param1)
            {
               _loc2_.addNum();
            }
         }
      }
      
      public function get curScore() : int
      {
         var _loc1_:int = 0;
         var _loc2_:HuoLiVO = null;
         for each(_loc2_ in this.huolis)
         {
            if(_loc2_.isComplete)
            {
               _loc1_ += _loc2_.score.v;
            }
         }
         return int(Math.min(100,_loc1_));
      }
      
      public function get hasReward() : Boolean
      {
         var _loc2_:HuoLiRewardVO = null;
         var _loc1_:Array = HuoLiConfig.instance().rewards;
         for each(_loc2_ in _loc1_)
         {
            if(_loc2_.canGet && !_loc2_.hasGet)
            {
               return true;
            }
         }
         return false;
      }
   }
}

