package citrus.view.spriteview
{
   import citrus.core.CitrusObject;
   import citrus.objects.CitrusSprite;
   import flash.display.MovieClip;
   
   public class SpriteDebugArt extends MovieClip
   {
      public function SpriteDebugArt()
      {
         super();
      }
      
      public function initialize(param1:CitrusObject) : void
      {
         var _loc2_:CitrusSprite = param1 as CitrusSprite;
         if(_loc2_)
         {
            graphics.lineStyle(1,2236962);
            graphics.beginFill(8947848);
            graphics.drawRect(0,0,_loc2_.width,_loc2_.height);
            graphics.endFill();
         }
      }
   }
}

