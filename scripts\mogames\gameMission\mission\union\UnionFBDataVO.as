package mogames.gameMission.mission.union
{
   import mogames.gameData.ConstData;
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   
   public class UnionFBDataVO
   {
      public var id:int;
      
      public var hpID:int;
      
      public var getID:int;
      
      public var numID:int;
      
      public var totalHP:Number;
      
      public var rewards:Array;
      
      private var _curHP:Sint;
      
      public function UnionFBDataVO(param1:int, param2:int, param3:int, param4:int, param5:int, param6:Array)
      {
         super();
         this.id = param1;
         this.hpID = param4;
         this.getID = param2;
         this.numID = param3;
         this.totalHP = param5;
         this._curHP = new Sint(this.totalHP);
         this.rewards = param6;
      }
      
      public function handlerNum() : void
      {
         if(FlagProxy.instance().isComplete(this.numID))
         {
            return;
         }
         FlagProxy.instance().changeValue(this.numID,1);
      }
      
      public function setGet() : void
      {
         FlagProxy.instance().setValue(this.getID,1);
      }
      
      public function get hasGet() : Boolean
      {
         return FlagProxy.instance().isComplete(this.getID);
      }
      
      public function get isFinish() : Boolean
      {
         return this.curHP == ConstData.DATA_NUM0.v;
      }
      
      public function set curHP(param1:int) : void
      {
         if(param1 <= 0)
         {
            param1 = 0;
         }
         this._curHP.v = param1;
      }
      
      public function get curHP() : int
      {
         return this._curHP.v;
      }
      
      public function get numStatus() : String
      {
         return "剩余免费挑战次数：" + FlagProxy.instance().findFlag(this.numID).left;
      }
   }
}

