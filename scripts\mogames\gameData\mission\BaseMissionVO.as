package mogames.gameData.mission
{
   import flash.geom.Point;
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   
   public class BaseMissionVO
   {
      public var id:Sint;
      
      public var name:String;
      
      public var sID:Sint;
      
      public var heroPos:Point;
      
      public var curFlag:Sint;
      
      public var openFlag:Sint;
      
      public var resURLs:Array;
      
      public var infor:String;
      
      public function BaseMissionVO(param1:int, param2:String, param3:int, param4:Point, param5:Array, param6:int = 0, param7:int = 0, param8:String = "")
      {
         super();
         this.id = new Sint(param1);
         this.name = param2;
         this.sID = new Sint(param3);
         this.heroPos = param4;
         this.resURLs = param5;
         this.curFlag = new Sint(param6);
         this.openFlag = new Sint(param7);
         this.infor = param8;
      }
      
      public function setComplete() : void
      {
         if(this.curFlag.v == 0)
         {
            return;
         }
         FlagProxy.instance().setValue(this.curFlag.v,1);
      }
      
      public function isComplete() : Boolean
      {
         if(this.curFlag.v == 0)
         {
            return true;
         }
         return FlagProxy.instance().isComplete(this.curFlag.v);
      }
      
      public function isOpen() : Boolean
      {
         if(this.openFlag.v == 0)
         {
            return true;
         }
         return FlagProxy.instance().isComplete(this.openFlag.v);
      }
   }
}

