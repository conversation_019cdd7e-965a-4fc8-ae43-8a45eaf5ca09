package mogames.gameEffect.co
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.geom.ColorTransform;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import utils.MethodUtil;
   
   public class BodyShadow
   {
      private var _info:Object;
      
      private var _color:ColorTransform;
      
      private var _target:DisplayObject;
      
      private var _list:Vector.<Bitmap>;
      
      private var _running:Boolean;
      
      private var _frameIndex:int;
      
      private var _bitmapPool:Vector.<Bitmap>;
      
      public var isAnimation:Boolean = false;
      
      public var isColor:Boolean = false;
      
      public var alphaRate:Number = 0.05;
      
      public var interval:int = 6;
      
      public var type:int;
      
      public function BodyShadow(param1:DisplayObject = null)
      {
         super();
         this._target = param1;
         this._info = new Object();
         this._list = new Vector.<Bitmap>();
         this._bitmapPool = new Vector.<Bitmap>();
      }
      
      public function setTarget(param1:DisplayObject) : void
      {
         this._color = new ColorTransform(1,1,1,1,-100,0,255);
         this._target = param1;
      }
      
      private function ef(param1:Event) : void
      {
         var _loc2_:Bitmap = null;
         var _loc5_:Bitmap = null;
         if(!this._target || !this._target.parent || !this._target.visible)
         {
            this.cleanImgs();
            return;
         }
         var _loc3_:int = int(this._list.length);
         var _loc4_:int = 0;
         while(_loc4_ < _loc3_)
         {
            _loc2_ = this._list[_loc4_];
            _loc2_.alpha -= this.alphaRate;
            if(_loc2_.alpha <= 0)
            {
               this.returnBitmap(_loc2_);
               this._list.splice(_loc4_,1);
               _loc4_--;
               _loc3_ = int(this._list.length);
            }
            _loc4_++;
         }
         ++this._frameIndex;
         if(this._frameIndex % this.interval == 0)
         {
            if(this.isAnimation)
            {
               this.draw();
            }
            _loc5_ = this.findBitmap();
            _loc5_.bitmapData = this._info.bitmapData;
            _loc5_.scaleX = this._target.scaleX;
            _loc5_.x = this._target.x + this._info.x * _loc5_.scaleX;
            _loc5_.y = this._target.y + this._info.y;
            _loc5_.alpha = 0.9;
            _loc5_.visible = true;
            this._list[this._list.length] = _loc5_;
            this._frameIndex = 0;
         }
      }
      
      public function start() : void
      {
         if(this._running)
         {
            return;
         }
         this.draw();
         this._running = true;
         Layers.gameStage.addEventListener(Event.ENTER_FRAME,this.ef);
      }
      
      public function stop() : void
      {
         this._running = false;
         this._frameIndex = 0;
         Layers.gameStage.removeEventListener(Event.ENTER_FRAME,this.ef);
         this.cleanImgs();
      }
      
      private function draw() : void
      {
         var _loc6_:BitmapData = null;
         var _loc1_:Rectangle = this._target.getBounds(this._target);
         var _loc2_:int = Math.round(_loc1_.x);
         var _loc3_:int = Math.round(_loc1_.y);
         if(_loc1_.isEmpty())
         {
            _loc1_.width = 1;
            _loc1_.height = 1;
         }
         var _loc4_:BitmapData = new BitmapData(Math.ceil(_loc1_.width),Math.ceil(_loc1_.height),true,0);
         if(this.isColor)
         {
            _loc4_.draw(this._target,new Matrix(1,0,0,1,-_loc2_,-_loc3_),this._color,null,null);
         }
         else
         {
            _loc4_.draw(this._target,new Matrix(1,0,0,1,-_loc2_,-_loc3_),null,null,null);
         }
         var _loc5_:Rectangle = _loc4_.getColorBoundsRect(4278190080,0,false);
         if(!_loc5_.isEmpty() && (_loc4_.width != _loc5_.width || _loc4_.height != _loc5_.height))
         {
            _loc6_ = new BitmapData(_loc5_.width,_loc5_.height,true,0);
            _loc6_.copyPixels(_loc4_,_loc5_,new Point());
            _loc4_.dispose();
            _loc4_ = _loc6_;
            _loc2_ += _loc5_.x;
            _loc3_ += _loc5_.y;
         }
         this._info.bitmapData = _loc4_;
         this._info.x = _loc2_;
         this._info.y = _loc3_;
      }
      
      private function cleanImgs() : void
      {
         var _loc1_:Bitmap = null;
         if(!this._list || this._list.length <= 0)
         {
            return;
         }
         for each(_loc1_ in this._list)
         {
            this.returnBitmap(_loc1_);
         }
         this._list.length = 0;
      }
      
      private function findBitmap() : Bitmap
      {
         if(this._bitmapPool.length > 0)
         {
            return this._bitmapPool.pop();
         }
         var _loc1_:Bitmap = new Bitmap();
         this._target.parent.addChild(_loc1_);
         this._target.parent.setChildIndex(this._target,this._target.parent.numChildren - 1);
         return _loc1_;
      }
      
      private function returnBitmap(param1:Bitmap) : void
      {
         param1.scaleY = 1;
         param1.scaleX = 1;
         param1.alpha = 1;
         if(param1.bitmapData != null)
         {
            param1.bitmapData.dispose();
         }
         param1.visible = false;
         this._bitmapPool.push(param1);
      }
      
      private function destroyImg() : void
      {
         var _loc1_:Bitmap = null;
         if(!this._list || this._list.length <= 0)
         {
            return;
         }
         for each(_loc1_ in this._list)
         {
            if(_loc1_.bitmapData != null)
            {
               _loc1_.bitmapData.dispose();
            }
            MethodUtil.removeMe(_loc1_);
         }
      }
      
      public function destroy() : void
      {
         this.stop();
         this.destroyImg();
         this._target = null;
         this._list = null;
         this._bitmapPool = null;
      }
   }
}

