package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameObj.display.BMCSprite;
   import mogames.gameRole.hero.BaseHero;
   
   public class ReverseDebuff extends BaseBuff
   {
      public function ReverseDebuff()
      {
         super(1004,true);
      }
      
      override public function setBuffVO(param1:BuffVO) : void
      {
         _buffVO = param1;
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         (_role as BaseHero).isReverse = true;
      }
      
      override protected function onEnd() : void
      {
         (_role as BaseHero).isReverse = false;
         destroy();
      }
      
      override public function get buffSkin() : BMCSprite
      {
         _skin.setupSequence(AssetManager.findAnimation("EFFECT_BUFF_REVERSE_CLIP"),true);
         _skin.x = _role.width * 0.5;
         _skin.y = -_skin.height;
         return _skin;
      }
   }
}

