package mogames.gameMission.mission.npc.jingjiling
{
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import mogames.AssetManager;
   import mogames.Layers;
   import mogames.event.EventManager;
   import mogames.event.UIEvent;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BaseScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameObj.box2d.npc.BaseNPC;
   import mogames.gameUI.card.CardFuncModule;
   import mogames.gameUI.heishi.HeiShiModule;
   import mogames.gameUI.jinjiling.FJCJMainModule;
   import mogames.gameUI.jinjiling.ZBXLModule;
   import mogames.gameUI.mall.MallModule;
   import mogames.gameUI.petEquip.PEMainModule;
   import utils.MethodUtil;
   
   public class SceneJingJiLing extends BaseScene
   {
      private var _npc108:BaseNPC;
      
      private var _npc109:BaseNPC;
      
      private var _npc110:BaseNPC;
      
      private var _npc111:BaseNPC;
      
      private var _npc112:BaseNPC;
      
      private var _btnMall:SimpleButton;
      
      public function SceneJingJiLing(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_JINGJILING");
         EventManager.addEventListener(UIEvent.NPC_EVENT,this.onNPC,false,0,true);
      }
      
      override protected function handlerInit() : void
      {
         this.initNPC();
         super.handlerInit();
         setHeroLock(true);
         this.initMallBtn();
      }
      
      private function initMallBtn() : void
      {
         this._btnMall = AssetManager.newButtonRes("BTN_MALL_CLIP",887,28);
         Layers.frameLayer.addChild(this._btnMall);
         this._btnMall.addEventListener(MouseEvent.CLICK,this.onMall,false,0,true);
      }
      
      private function removeMallBtn() : void
      {
         this._btnMall.removeEventListener(MouseEvent.CLICK,this.onMall);
         MethodUtil.removeMe(this._btnMall);
         this._btnMall = null;
      }
      
      private function onMall(param1:MouseEvent) : void
      {
         MallModule.instance().init();
      }
      
      private function initNPC() : void
      {
         this._npc108 = new BaseNPC(108,"MC_NPC_CLIP108",200,120);
         this._npc108.x = 324;
         this._npc108.y = 454;
         add(this._npc108);
         this._npc109 = new BaseNPC(109,"MC_NPC_CLIP109",200,120);
         this._npc109.x = 870;
         this._npc109.y = 454;
         add(this._npc109);
         this._npc110 = new BaseNPC(110,"MC_NPC_CLIP110",200,120);
         this._npc110.x = 500;
         this._npc110.y = 445;
         add(this._npc110);
         this._npc111 = new BaseNPC(111,"MC_NPC_CLIP111",200,120);
         this._npc111.x = 678;
         this._npc111.y = 444;
         add(this._npc111);
         this._npc112 = new BaseNPC(112,"MC_NPC_CLIP112",200,120);
         this._npc112.x = 1050;
         this._npc112.y = 444;
         add(this._npc112);
      }
      
      private function onNPC(param1:UIEvent) : void
      {
         if(param1.data.type != "onNPC")
         {
            return;
         }
         setHeroEnable(false);
         switch(param1.data.data)
         {
            case 108:
               ZBXLModule.instance().init(enabledKey);
               break;
            case 109:
               FJCJMainModule.instance().init(enabledKey);
               break;
            case 110:
               HeiShiModule.instance().init(enabledKey);
               break;
            case 111:
               PEMainModule.instance().init(enabledKey);
               break;
            case 112:
               CardFuncModule.instance().init(enabledKey);
         }
      }
      
      private function handlerHeiShi() : void
      {
      }
      
      override public function destroy() : void
      {
         this.removeMallBtn();
         EventManager.removeEventListener(UIEvent.NPC_EVENT,this.onNPC);
         this._npc108 = null;
         this._npc109 = null;
         this._npc110 = null;
         this._npc111 = null;
         this._npc112 = null;
         super.destroy();
      }
   }
}

