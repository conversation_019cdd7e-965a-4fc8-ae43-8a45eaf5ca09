package mogames.gameData.forge
{
   import file.GoodConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.GameGoodVO;
   import mogames.gameData.good.constvo.ConstGoodVO;
   import utils.TxtUtil;
   
   public class NeedVO
   {
      public var id:Sint;
      
      public var num:Sint;
      
      public function NeedVO(param1:int, param2:int)
      {
         super();
         this.id = new Sint(param1);
         this.num = new Sint(param2);
      }
      
      public function get isLack() : Boolean
      {
         return BagProxy.instance().getGoodNum(this.id.v) < this.num.v;
      }
      
      public function get needStr() : String
      {
         var _loc1_:int = BagProxy.instance().getGoodNum(this.id.v);
         var _loc2_:String = _loc1_ + "/" + this.num.v;
         if(_loc1_ < this.num.v)
         {
            return TxtUtil.setColor(_loc2_,"ff0000");
         }
         return TxtUtil.setColor(_loc2_,"99ff00");
      }
      
      public function get askStr() : String
      {
         var _loc1_:ConstGoodVO = GoodConfig.instance().findConstGood(this.id.v);
         return TxtUtil.setColor(_loc1_.name + "X" + this.num.v,ConstData.GOOD_COLOR1[_loc1_.quality.v]);
      }
      
      public function get needName() : String
      {
         return GoodConfig.instance().findConstGood(this.id.v).name;
      }
      
      public function get needGood() : GameGoodVO
      {
         return GoodConfig.instance().newGood(this.id.v);
      }
   }
}

