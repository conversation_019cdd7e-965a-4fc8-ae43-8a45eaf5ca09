package file
{
   import mogames.gameData.role.vo.RoleWXVO;
   
   public class WuXingConfig
   {
      private static var _instance:WuXingConfig;
      
      private var list:Vector.<RoleWXVO>;
      
      public function WuXingConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.list = new Vector.<RoleWXVO>();
         this.list.push(new RoleWXVO(0,1000,1000,1000,1000,1000,1000));
         this.list.push(new RoleWXVO(1,1000,450,1000,600,600,600));
         this.list.push(new RoleWXVO(2,1000,700,500,700,700,1100));
         this.list.push(new RoleWXVO(3,1000,700,700,500,1100,700));
         this.list.push(new RoleWXVO(4,1000,1100,700,700,500,700));
         this.list.push(new RoleWXVO(5,1000,700,700,1100,700,500));
      }
      
      public static function instance() : WuXingConfig
      {
         if(!_instance)
         {
            _instance = new WuXingConfig();
         }
         return _instance;
      }
      
      public function findWuXingVO(param1:int) : RoleWXVO
      {
         var _loc2_:RoleWXVO = null;
         for each(_loc2_ in this.list)
         {
            if(_loc2_.mine.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

