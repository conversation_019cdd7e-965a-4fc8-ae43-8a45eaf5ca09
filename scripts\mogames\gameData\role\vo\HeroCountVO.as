package mogames.gameData.role.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   
   public class HeroCountVO
   {
      public var roleID:Sint;
      
      public var argHP:Snum;
      
      public var argMP:Snum;
      
      public var argATK:Snum;
      
      public var argPDEF:Snum;
      
      public var argMDEF:Snum;
      
      public var argCRIT:Snum;
      
      public var argMISS:Snum;
      
      public var basePDEF:Sint;
      
      public var baseMDEF:Sint;
      
      public var baseCRIT:Sint;
      
      public var baseMISS:Sint;
      
      public var baseLUCK:Sint;
      
      public var baseMOVE:Snum;
      
      public var baseRUN:Snum;
      
      public function HeroCountVO(param1:int, param2:Number, param3:Number, param4:Number, param5:Number, param6:Number, param7:int, param8:int, param9:int, param10:int, param11:int, param12:Number, param13:Number)
      {
         super();
         this.roleID = new Sint(param1);
         this.argHP = new Snum(param2);
         this.argMP = new Snum(param3);
         this.argATK = new Snum(param4);
         this.argPDEF = new Snum(param5);
         this.argMDEF = new Snum(param6);
         this.basePDEF = new Sint(param7);
         this.baseMDEF = new Sint(param8);
         this.baseCRIT = new Sint(param9);
         this.baseMISS = new Sint(param10);
         this.baseLUCK = new Sint(param11);
         this.baseMOVE = new Snum(param12);
         this.baseRUN = new Snum(param13);
      }
   }
}

