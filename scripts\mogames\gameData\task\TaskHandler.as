package mogames.gameData.task
{
   import file.StoryConfig;
   import mogames.gameUI.dialog.DialogMediator;
   import mogames.gameUI.task.TaskModule;
   
   public class TaskHandler
   {
      public function TaskHandler()
      {
         super();
      }
      
      public function handlerGetReward(param1:int) : void
      {
         switch(param1)
         {
            case 11003:
               this.handlerTask11003();
         }
      }
      
      public function handlerComplete(param1:int) : void
      {
      }
      
      private function handlerTask11003() : void
      {
         var func:Function = null;
         func = function():void
         {
            TaskProxy.instance().addTask(12004);
         };
         TaskModule.clean();
         DialogMediator.instance().init(StoryConfig.instance().findDialog("STORY0038"),func);
      }
   }
}

