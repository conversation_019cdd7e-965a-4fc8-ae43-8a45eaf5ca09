package mogames.gameMission.mission.shuangchaling
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.setTimeout;
   import mogames.Layers;
   import mogames.event.UIEvent;
   import mogames.gameData.bag.BagProxy;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameData.task.TaskProxy;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.StoneTrap;
   import mogames.gameRole.enemy.BOSSYinJiangJun;
   import mogames.gameSystem.CitrusLock;
   import mogames.gameSystem.CitrusRender;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.PromptMediator;
   import utils.TxtUtil;
   
   public class SceneSCL extends BossScene
   {
      private var _pTrigger0:LocalTriggerX;
      
      private var _pTrigger1:LocalTriggerX;
      
      private var _pTrigger2:LocalTriggerX;
      
      private var _eTrigger0:EnemyTrigger;
      
      private var _eTrigger1:EnemyTrigger;
      
      private var _npc:SaveNPC;
      
      public function SceneSCL(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [StoneTrap];
         super(param1,param2);
         EffectManager.instance().playBGM("BGM_SUNSHINE");
         setWinPosition(new Rectangle(1684,400,576,72),new Point(1933,396));
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            TaskProxy.instance().addTask(11001);
         };
         super.handlerInit();
         this.initEnemies();
         this.initNPC();
         if(!FlagProxy.instance().isComplete(102))
         {
            showDialog("STORY0025",func);
         }
      }
      
      private function initGuide() : void
      {
         if(FlagProxy.instance().isComplete(11))
         {
            return;
         }
         (_mission.onePlayer.roleVO as HeroGameVO).addSP(200);
         MiniMsgMediator.instance().showMsg("头像闪动按" + TxtUtil.setColor("【空格】","99ff00") + "释放无双（加攻加移速）",480,50);
         FlagProxy.instance().setValue(11,1);
         CitrusRender.instance().add(this.checkRelease);
      }
      
      private function checkRelease() : void
      {
         if(_mission.onePlayer.isDead)
         {
            return;
         }
         if((_mission.onePlayer.roleVO as HeroGameVO).curSP.v == 0)
         {
            CitrusRender.instance().remove(this.checkRelease);
            MiniMsgMediator.clean();
         }
      }
      
      private function initNPC() : void
      {
         if(FlagProxy.instance().isComplete(102))
         {
            return;
         }
         this._npc = new SaveNPC("MC_STORY_NPC_CLIP101",168,186);
         this._npc.x = 1690;
         this._npc.y = 276;
         add(this._npc);
         this._npc.mcNPC.addEventListener(UIEvent.NPC_EVENT,this.onNPC,false,0,true);
      }
      
      private function initEnemies() : void
      {
         CitrusLock.instance().lock(new Rectangle(0,0,960,600),false,false,false,true);
         this._eTrigger0 = new EnemyTrigger(_mission,[[355,220,548,224]],1011,this.triggerEnd);
         this._eTrigger1 = new EnemyTrigger(_mission,[[820,200,550,220]],1012,this.triggerEnd);
         this._pTrigger0 = new LocalTriggerX(_mission.onePlayer,new Point(480,480),1);
         this._pTrigger1 = new LocalTriggerX(_mission.onePlayer,new Point(1000,480),1,new Rectangle(600,0,1060,600));
         this._pTrigger2 = new LocalTriggerX(_mission.onePlayer,new Point(1730,480),1,new Rectangle(1160,0,1240,600));
         this._pTrigger0.trigger = this._eTrigger0;
         this._pTrigger1.trigger = this._eTrigger1;
         this._pTrigger1.setBlock(false,false,true,true);
         this._pTrigger2.setBlock(false,false,true,false);
         this._pTrigger0.okFunc = this.onTrigger0;
         this._pTrigger1.okFunc = this._pTrigger2.start;
         this._pTrigger2.okFunc = this.showStory;
         this._pTrigger0.start();
      }
      
      private function onTrigger0() : void
      {
         this.initGuide();
         this._pTrigger1.start();
      }
      
      private function triggerEnd() : void
      {
         unlock();
         EffectManager.instance().addGOEffect(3);
      }
      
      private function onNPC(param1:UIEvent) : void
      {
         if(!BagProxy.instance().hasItem(10012))
         {
            MiniMsgMediator.instance().showAutoMsg("需要" + TxtUtil.setColor("牢笼钥匙") + "！",480,200);
            return;
         }
         this.handlerSave();
      }
      
      private function handlerSave() : void
      {
         var func0:Function = null;
         var func1:Function = null;
         func0 = function():void
         {
            showDialog("STORY0027",func1);
         };
         func1 = function():void
         {
            var showPrompt:Function = null;
            showPrompt = function():void
            {
               Layers.lockGame = false;
               EffectManager.instance().addScreenEffect(16777215);
               PromptMediator.instance().showPrompt(TxtUtil.setColor("【装备强化】","99ff00") + "功能已经开放！<br>（花果山找刘伯钦）",null,null,true,"NEW_FUNCTION",false);
               TaskProxy.instance().addTask(11024,true,false,true);
            };
            _npc.disappear();
            setTimeout(showPrompt,1000);
         };
         MiniMsgMediator.clean();
         BagProxy.instance().delItemByID(10012);
         MiniMsgMediator.instance().showAutoMsg("使用了牢笼钥匙！",480,200,"");
         FlagProxy.instance().setValue(102,1);
         TaskProxy.instance().setComplete(11001);
         Layers.lockGame = true;
         this._npc.changeAnimation("open",false);
         this._npc.openFunc = func0;
      }
      
      private function showStory() : void
      {
         var func:Function = null;
         func = function():void
         {
            FlagProxy.instance().setValue(2001,1);
            startBossBattle("BGM_BATTLE0");
         };
         this.addBOSS();
         if(FlagProxy.instance().isComplete(2001))
         {
            this.startBossBattle("BGM_BATTLE0");
         }
         else
         {
            showDialog("STORY0008",func);
         }
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         super.startBossBattle(param1);
         if(!_mission.missionVO.isComplete())
         {
            MiniMsgMediator.instance().showMsg("五行相克：" + TxtUtil.setColor("【金】","ffff00") + "克" + TxtUtil.setColor("【木】","99ff00"),480,30);
         }
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSYinJiangJun();
         _boss.x = 2200;
         _boss.y = 450;
         _boss.initData(30021,30021,2);
         _boss.target = _mission.curTarget;
         add(_boss);
      }
      
      override protected function handlerWin() : void
      {
         MiniMsgMediator.clean();
         if(!FlagProxy.instance().isComplete(102) && TaskProxy.instance().hasTask(11001))
         {
            BagProxy.instance().addPileItem(10012);
            PromptMediator.instance().showPrompt("获得牢笼钥匙，赶紧解救刘伯钦！<br>" + TxtUtil.setColor("（点击牢笼即可解救）","99ff00"),this.onWin,null,true,"KEY_DROP");
         }
         else
         {
            this.onWin();
         }
      }
      
      private function onWin() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
         addLeaveDoor(_doorPos.x,_doorPos.y);
      }
      
      override public function destroy() : void
      {
         MiniMsgMediator.clean();
         CitrusRender.instance().remove(this.checkRelease);
         this._eTrigger0.destroy();
         this._eTrigger1.destroy();
         this._pTrigger0.destroy();
         this._pTrigger1.destroy();
         this._pTrigger2.destroy();
         this._eTrigger0 = null;
         this._eTrigger1 = null;
         this._pTrigger0 = null;
         this._pTrigger1 = null;
         this._pTrigger2 = null;
         this._npc = null;
         super.destroy();
      }
   }
}

