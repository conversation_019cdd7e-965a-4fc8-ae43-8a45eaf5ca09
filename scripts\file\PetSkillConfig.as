package file
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.heroSkill.SkillCountVO;
   import mogames.gameData.heroSkill.constvo.ConstSkillVO;
   import mogames.gameData.pet.vo.PetGameVO;
   import mogames.gameData.petSkill.PetSkillCountVO;
   import mogames.gameData.petSkill.PetSkillVO;
   import mogames.gameData.petSkill.active.PetConstSKillOne;
   import mogames.gameData.petSkill.active.PetConstSkillFive;
   import mogames.gameData.petSkill.active.PetConstSkillFour;
   import mogames.gameData.petSkill.active.PetConstSkillSix;
   import mogames.gameData.petSkill.active.PetConstSkillThree;
   import mogames.gameData.petSkill.active.PetConstSkillTwo;
   import mogames.gameData.petSkill.passive.PetSkillJianRenVO;
   import mogames.gameData.petSkill.passive.PetSkillManLiVO;
   import mogames.gameData.petSkill.passive.PetSkillRebornVO;
   import mogames.gameData.petSkill.passive.PetSkillRenXingVO;
   import mogames.gameData.petSkill.passive.PetSkillShenJiVO;
   import mogames.gameData.petSkill.passive.PetSkillXiXieVO;
   import mogames.gameData.petSkill.passive.PetSkillXuRuoVO;
   import mogames.gameData.petSkill.passive.PetSkillZhiMingVO;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill1;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill10;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill2;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill3;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill4;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill5;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill6;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill7;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill8;
   import mogames.gameData.petSkill.passive.constvo.PetConstPSkill9;
   
   public class PetSkillConfig
   {
      private static var _instance:PetSkillConfig;
      
      private var _skillVec:Vector.<ConstSkillVO>;
      
      private var _countVec:Vector.<SkillCountVO>;
      
      public function PetSkillConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : PetSkillConfig
      {
         if(!_instance)
         {
            _instance = new PetSkillConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._skillVec = new Vector.<ConstSkillVO>();
         this._skillVec.push(new PetConstPSkill1(100,"回春","ICON_XI_XUE","普攻时根据攻击力恢复生命。"));
         this._skillVec.push(new PetConstPSkill2(101,"蛮力","ICON_MAN_LI","增加宠物物理攻击。"));
         this._skillVec.push(new PetConstPSkill3(102,"坚韧","ICON_JIAN_REN","增加宠物物理防御。"));
         this._skillVec.push(new PetConstPSkill4(103,"韧性","ICON_REN_XING","增加宠物魔法防御。"));
         this._skillVec.push(new PetConstPSkill5(104,"致命","ICON_ZHI_MING","提高爆击概率。"));
         this._skillVec.push(new PetConstPSkill6(105,"重生","ICON_CHONG_SHENG","战斗死亡时有几率复活，并恢复一半气血。"));
         this._skillVec.push(new PetConstPSkill7(106,"虚弱","ICON_XU_LUO","普攻时有几率降低目标的攻击能力。"));
         this._skillVec.push(new PetConstPSkill8(107,"神迹","ICON_SHEN_JI","有概率不受异常状态影响。"));
         this._skillVec.push(new PetConstPSkill9(108,"命之庇护","ICON_BI_HU","每秒恢复其跟随角色的生命。"));
         this._skillVec.push(new PetConstPSkill10(109,"魔之庇佑","ICON_BI_YOU","每秒恢复其跟随角色的法力。"));
         this._skillVec.push(new PetConstSKillOne(11011,"火浪","ICON_HUO_LANG","skillone","手臂一甩串出火浪，对前方怪物造成伤害"));
         this._skillVec.push(new PetConstSkillTwo(11012,"旋炎","ICON_XUAN_YAN","skilltwo","释放一个带旋转的火球，对怪物造成持续伤害"));
         this._skillVec.push(new PetConstSkillThree(11013,"落陨","ICON_LUO_YUN","skillthree","释放出一个大陨石,对附近怪物造成伤害"));
         this._skillVec.push(new PetConstSkillFour(11014,"火漩涡","ICON_HUOXUAN_WO","skillfour","释放出一个火漩涡，怪物在范围内受到每秒伤害"));
         this._skillVec.push(new PetConstSkillFive(11015,"烈岩爆","ICON_LIEYAN_BAO","skillfive","在目标怪物位置释放带火岩石，爆炸后造成伤害"));
         this._skillVec.push(new PetConstSkillSix(11016,"鬼炎","ICON_GUI_YAN","skillsix","召唤出一个鬼炎飞向怪物后剧烈爆炸，怪物受到伤害并晕眩"));
         this._skillVec.push(new PetConstSKillOne(11021,"刀叶","ICON_DAO_YE","skillone","幻化出一片叶子旋转向前，对前方怪物造成伤害"));
         this._skillVec.push(new PetConstSkillTwo(11022,"毒粉末","ICON_DUFEN_MO","skilltwo","将毒粉末撒向前方，对怪物造成持续伤害"));
         this._skillVec.push(new PetConstSkillThree(11023,"冲击","ICON_CHONG_JI","skillthree","使出全身力气向前冲撞,对附近怪物造成伤害"));
         this._skillVec.push(new PetConstSkillFour(11024,"麻痹粉","ICON_MABI_FEN","skillfour","将麻痹粉撒向前方，对怪物造成伤害并麻痹"));
         this._skillVec.push(new PetConstSkillFive(11025,"突桩","ICON_TU_ZHUANG","skillfive","前方范围内的怪物脚下突出木桩，对怪物造成伤害"));
         this._skillVec.push(new PetConstSkillSix(11026,"叶风暴","ICON_YEFENG_BAO","skillsix","身体快速旋转形成移动风暴，怪物受到2次伤害"));
         this._skillVec.push(new PetConstSKillOne(11031,"奇袭","ICON_QI_XI","skillone","向前窜出并返回，窜出时身上带金色的残影，对怪物造成伤害"));
         this._skillVec.push(new PetConstSkillTwo(11032,"雷金术","ICON_LEI_JIN_SHU","skilltwo","空中落下一道金色闪电，对身前怪物造成伤害"));
         this._skillVec.push(new PetConstSkillThree(11033,"金锥箭","ICON_JIN_ZHUI_JIAN","skillthree","凝聚出一道金箭，对单个怪物造成伤害并眩晕"));
         this._skillVec.push(new PetConstSkillFour(11034,"碎金爆","ICON_SUI_JIN_BAO","skillfour","天空掉落一道金色光柱碎裂爆炸，对怪物造成伤害并击退"));
         this._skillVec.push(new PetConstSkillFive(11035,"金光乍现","ICON_JIN_GUANG_ZHA_XIAN","skillfive","张嘴一吼身体周围发出强烈的光亮对范围内的怪物造成伤害"));
         this._skillVec.push(new PetConstSkillSix(11036,"流金网","ICON_LIU_JIN_WANG","skillsix","召唤出一个大网向前盖去，网内怪物收到伤害并麻痹"));
         this._skillVec.push(new PetConstSKillOne(11041,"泡沫光线","ICON_PAO_MO","skillone","喷射大量的泡沫攻击前方敌人，对敌人造成伤害并降低速度"));
         this._skillVec.push(new PetConstSkillTwo(11042,"冰锥","ICON_BING_ZHUI","skilltwo","双手凝聚出一个冰锥向前飞出，对怪物造成伤害并击退"));
         this._skillVec.push(new PetConstSkillThree(11043,"水龙","ICON_SHUI_LONG","skillthree","背后召唤出一条水龙向前飞出，对直线上的敌人造成伤害"));
         this._skillVec.push(new PetConstSkillFour(11044,"玄冰冻气","ICON_XUAN_BING_DONG_QI","skillfour","四周释放出冻气，对敌人造成伤害并冰冻"));
         this._skillVec.push(new PetConstSkillFive(11045,"暴风雪","ICON_BAO_FENG_XUE","skillfive","前方召唤出暴风雪，对敌人造成伤害并冰冻"));
         this._skillVec.push(new PetConstSKillOne(11061,"冰爪狂啸","ICON_BING_ZHUA_KUANG_XIAO","skillone","抬爪向前猛抓，对怪物造成伤害并击退"));
         this._skillVec.push(new PetConstSkillTwo(11062,"寒之冲撞","ICON_HAN_BING_CHONG_ZHUANG","skilltwo","快速向前飞奔，对直线上怪物造成伤害并击飞"));
         this._skillVec.push(new PetConstSkillThree(11063,"寒冰柱","ICON_HAN_BING_ZHU","skillthree","在面前召唤出一道冰柱，对怪物造成伤害并冰冻"));
         this._skillVec.push(new PetConstSkillFour(11064,"狂霜降雪","ICON_KUANG_SHUANG_JIANG_XUE","skillfour","前方范围内飘落雪花，对范围内的怪物造成伤害并减速"));
         this._skillVec.push(new PetConstSkillFive(11065,"冰霜玄地","ICON_BING_SHUANG_XUAN_DI","skillfive","地面散发出寒气，对四周怪物造成伤害并冰冻"));
         this._skillVec.push(new PetConstSKillOne(11071,"炎羽","ICON_HUO_YU","skillone","甩动单翅，飞出一根冒火羽毛，对怪物造成伤害并击退"));
         this._skillVec.push(new PetConstSkillTwo(11072,"烈焰展翅","ICON_LIE_YAN_ZHAN_CHI","skilltwo","化身火影向前穿出，对直线上的怪物造成伤害并晕眩"));
         this._skillVec.push(new PetConstSkillThree(11073,"火炎","ICON_HUO_YAN","skillthree","快速煽动双翅膀发出一道火龙卷向前移动，对怪物造成伤害并击飞"));
         this._skillVec.push(new PetConstSkillFour(11074,"炎爆","ICON_YAN_BAO","skillfour","向前发出一个火球并爆炸，对怪物造成伤害并晕眩"));
         this._skillVec.push(new PetConstSkillFive(11075,"欲火焚身","ICON_YU_HUO_FEN_SHEN","skillfive","前方地面召唤出幽火，对怪物造成多次伤害并减速"));
         this._skillVec.push(new PetConstSKillOne(11081,"破空击","ICON_PO_KONG_JI","skillone","砸向地面，对怪物造成伤害并击倒"));
         this._skillVec.push(new PetConstSkillTwo(11082,"沙浴","ICON_SHA_YU","skilltwo","向前放出沙暴，对怪物造成持续伤害。"));
         this._skillVec.push(new PetConstSkillThree(11083,"玄冥灭","ICON_XUAN_MING_MIE","skillthree","向前射出玄冥光线，对怪物造成伤害并减速"));
         this._skillVec.push(new PetConstSkillFour(11084,"尘埃","ICON_CHEN_AI","skillfour","自身四周释放尘埃，对怪物造成伤害并减防"));
         this._skillVec.push(new PetConstSkillFive(11085,"撼动大地","ICON_HAN_DONG_DA_DI","skillfive","撼动大地，对怪物造成伤害并眩晕"));
         this._countVec = new Vector.<SkillCountVO>();
         this._countVec.push(new PetSkillCountVO(11011,{"hurt":new Sint(35)}));
         this._countVec.push(new PetSkillCountVO(11012,{"hurt":new Sint(20)}));
         this._countVec.push(new PetSkillCountVO(11013,{"hurt":new Sint(45)}));
         this._countVec.push(new PetSkillCountVO(11014,{"hurt":new Sint(23)}));
         this._countVec.push(new PetSkillCountVO(11015,{"hurt":new Sint(50)}));
         this._countVec.push(new PetSkillCountVO(11016,{"hurt":new Sint(55)}));
         this._countVec.push(new PetSkillCountVO(11021,{"hurt":new Sint(35)}));
         this._countVec.push(new PetSkillCountVO(11022,{"hurt":new Sint(19)}));
         this._countVec.push(new PetSkillCountVO(11023,{"hurt":new Sint(46)}));
         this._countVec.push(new PetSkillCountVO(11024,{"hurt":new Sint(49)}));
         this._countVec.push(new PetSkillCountVO(11025,{"hurt":new Sint(51)}));
         this._countVec.push(new PetSkillCountVO(11026,{"hurt":new Sint(42)}));
         this._countVec.push(new PetSkillCountVO(11031,{"hurt":new Sint(28)}));
         this._countVec.push(new PetSkillCountVO(11032,{"hurt":new Sint(54)}));
         this._countVec.push(new PetSkillCountVO(11033,{"hurt":new Sint(46)}));
         this._countVec.push(new PetSkillCountVO(11034,{"hurt":new Sint(62)}));
         this._countVec.push(new PetSkillCountVO(11035,{"hurt":new Sint(30)}));
         this._countVec.push(new PetSkillCountVO(11036,{"hurt":new Sint(69)}));
         this._countVec.push(new PetSkillCountVO(11041,{"hurt":new Sint(44)}));
         this._countVec.push(new PetSkillCountVO(11042,{"hurt":new Sint(63)}));
         this._countVec.push(new PetSkillCountVO(11043,{"hurt":new Sint(58)}));
         this._countVec.push(new PetSkillCountVO(11044,{"hurt":new Sint(80)}));
         this._countVec.push(new PetSkillCountVO(11045,{"hurt":new Sint(65)}));
         this._countVec.push(new PetSkillCountVO(11061,{"hurt":new Sint(86)}));
         this._countVec.push(new PetSkillCountVO(11062,{"hurt":new Sint(88)}));
         this._countVec.push(new PetSkillCountVO(11063,{"hurt":new Sint(90)}));
         this._countVec.push(new PetSkillCountVO(11064,{"hurt":new Sint(110)}));
         this._countVec.push(new PetSkillCountVO(11065,{"hurt":new Sint(130)}));
         this._countVec.push(new PetSkillCountVO(11071,{"hurt":new Sint(101)}));
         this._countVec.push(new PetSkillCountVO(11072,{"hurt":new Sint(99)}));
         this._countVec.push(new PetSkillCountVO(11073,{"hurt":new Sint(140)}));
         this._countVec.push(new PetSkillCountVO(11074,{"hurt":new Sint(130)}));
         this._countVec.push(new PetSkillCountVO(11075,{"hurt":new Sint(55)}));
         this._countVec.push(new PetSkillCountVO(11081,{"hurt":new Sint(120)}));
         this._countVec.push(new PetSkillCountVO(11082,{"hurt":new Sint(78)}));
         this._countVec.push(new PetSkillCountVO(11083,{"hurt":new Sint(140)}));
         this._countVec.push(new PetSkillCountVO(11084,{"hurt":new Sint(155)}));
         this._countVec.push(new PetSkillCountVO(11085,{"hurt":new Sint(130)}));
      }
      
      public function newPassiveSkills(param1:PetGameVO) : Array
      {
         var _loc2_:Array = [];
         _loc2_.push(this.newPassive(100,param1));
         _loc2_.push(this.newPassive(101,param1));
         _loc2_.push(this.newPassive(102,param1));
         _loc2_.push(this.newPassive(103,param1));
         _loc2_.push(this.newPassive(104,param1));
         _loc2_.push(this.newPassive(105,param1));
         _loc2_.push(this.newPassive(106,param1));
         _loc2_.push(this.newPassive(107,param1));
         return _loc2_;
      }
      
      public function newPassive(param1:int, param2:PetGameVO) : PetSkillVO
      {
         switch(param1)
         {
            case 100:
               return new PetSkillXiXieVO(100,param2);
            case 101:
               return new PetSkillManLiVO(101,param2);
            case 102:
               return new PetSkillJianRenVO(102,param2);
            case 103:
               return new PetSkillRenXingVO(103,param2);
            case 104:
               return new PetSkillZhiMingVO(104,param2);
            case 105:
               return new PetSkillRebornVO(105,param2);
            case 106:
               return new PetSkillXuRuoVO(106,param2);
            case 107:
               return new PetSkillShenJiVO(107,param2);
            default:
               return null;
         }
      }
      
      public function findConstSkill(param1:int) : ConstSkillVO
      {
         var _loc2_:ConstSkillVO = null;
         for each(_loc2_ in this._skillVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function findSkillCount(param1:int) : SkillCountVO
      {
         var _loc2_:SkillCountVO = null;
         for each(_loc2_ in this._countVec)
         {
            if(_loc2_.id.v == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
   }
}

