package mogames.gameData.task.vo
{
   import mogames.gameData.ConstData;
   import mogames.gameData.task.constvo.ConstTask;
   
   public class StoryTask extends BaseTask
   {
      public function StoryTask(param1:ConstTask)
      {
         super(param1);
      }
      
      override public function get isComplete() : Boolean
      {
         return _flag.v == ConstData.DATA_NUM1.v;
      }
      
      override public function get targetStr() : String
      {
         return constTask.target;
      }
      
      override public function get saveData() : String
      {
         return constTask.id.v + "H" + _flag.v;
      }
      
      override public function set loadData(param1:Array) : void
      {
         _flag.v = int(param1[1]);
      }
   }
}

