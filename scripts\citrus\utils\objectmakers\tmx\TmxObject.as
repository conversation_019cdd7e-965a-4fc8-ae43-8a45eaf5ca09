package citrus.utils.objectmakers.tmx
{
   public class TmxObject
   {
      public var group:TmxObjectGroup;
      
      public var name:String;
      
      public var type:String;
      
      public var x:int;
      
      public var y:int;
      
      public var width:int;
      
      public var height:int;
      
      public var rotation:int;
      
      public var gid:int;
      
      public var custom:TmxPropertySet;
      
      public var shared:TmxPropertySet;
      
      public var points:Array;
      
      public var shapeType:String;
      
      public function TmxObject(param1:XML, param2:TmxObjectGroup)
      {
         var _loc3_:XML = null;
         var _loc4_:XMLList = null;
         var _loc5_:TmxTileSet = null;
         var _loc6_:Array = null;
         var _loc7_:* = 0;
         var _loc8_:Number = 0;
         var _loc9_:Array = null;
         var _loc10_:Object = null;
         super();
         if(!param1)
         {
            return;
         }
         this.group = param2;
         this.name = param1.@name;
         this.type = param1.@type;
         this.x = param1.@x;
         this.y = param1.@y;
         this.width = param1.@width;
         this.height = param1.@height;
         this.rotation = param1.@rotation;
         this.shared = null;
         this.gid = -1;
         if(<EMAIL> != 0)
         {
            this.gid = param1.@gid;
            for each(_loc5_ in this.group.map.tileSets)
            {
               this.shared = _loc5_.getPropertiesByGid(this.gid);
               if(this.shared)
               {
                  break;
               }
            }
         }
         for each(_loc3_ in param1.properties)
         {
            this.custom = !!this.custom ? this.custom.extend(_loc3_) : new TmxPropertySet(_loc3_);
         }
         _loc4_ = param1.children();
         var _loc11_:int = 0;
         var _loc12_:* = _loc4_;
         for each(_loc3_ in _loc12_)
         {
            this.shapeType = _loc3_.@name;
            this.points = [];
            _loc6_ = String(_loc3_.@points).split(" ");
            _loc7_ = _loc6_.length;
            _loc8_ = 0;
            while(_loc8_ < _loc7_)
            {
               _loc9_ = _loc6_[_loc8_].split(",");
               _loc10_ = {
                  "x":int(_loc9_[0]),
                  "y":int(_loc9_[1])
               };
               this.points.push(_loc10_);
               _loc8_++;
            }
         }
      }
   }
}

