package mogames.gameData.heishi
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.mall.vo.BaseMallVO;
   import mogames.gameData.mall.vo.MallMoneyVO;
   import mogames.gameData.mall.vo.MallTicketVO;
   
   public class MallHeiShiVO
   {
      public var id:Sint;
      
      public var zhekou:int;
      
      public var oldPrice:String;
      
      public var newPrice:String;
      
      public var mallVO:BaseMallVO;
      
      public function MallHeiShiVO(param1:int, param2:int, param3:int, param4:BaseMallVO)
      {
         super();
         this.id = new Sint(param1);
         this.zhekou = param2;
         this.mallVO = param4;
         this.oldPrice = "原价：" + param3 + this.priceType;
         this.newPrice = "现价：" + param4.price.v + this.priceType;
      }
      
      private function get priceType() : String
      {
         if(this.mallVO is MallMoneyVO)
         {
            return "金锭";
         }
         if(this.mallVO is MallTicketVO)
         {
            return "银锭";
         }
         return "";
      }
   }
}

