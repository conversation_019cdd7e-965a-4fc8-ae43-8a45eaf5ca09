package mogames.gameData.drop.vo
{
   import file.GoodConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.GameEquipVO;
   import mogames.gameData.good.GameGoodVO;
   
   public class EquipRewardVO extends BaseRewardVO
   {
      private var _quality:Sint;
      
      public function EquipRewardVO(param1:int, param2:int)
      {
         this._quality = new Sint(param2);
         super(param1,1);
      }
      
      public function get quality() : int
      {
         return this._quality.v;
      }
      
      override public function newGood() : GameGoodVO
      {
         var _loc1_:GameEquipVO = GoodConfig.instance().newGood(id.v) as GameEquipVO;
         _loc1_.initNewEquip(this._quality.v,true,true);
         return _loc1_ as GameGoodVO;
      }
   }
}

