package mogames.gameData.bag
{
   import file.GoodConfig;
   import mogames.gameData.ConstData;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.good.GameCardVO;
   import mogames.gameData.good.GameGoodVO;
   
   public class CardProxy
   {
      private static var _instance:CardProxy;
      
      public function CardProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : CardProxy
      {
         if(!_instance)
         {
            _instance = new CardProxy();
         }
         return _instance;
      }
      
      public function addCard(param1:int, param2:int) : void
      {
         var _loc3_:Array = null;
         var _loc7_:GameGoodVO = null;
         _loc3_ = this.cardList;
         var _loc4_:int = 0;
         var _loc5_:int = int(_loc3_.length);
         while(_loc4_ < _loc5_)
         {
            _loc7_ = _loc3_[_loc4_];
            if(_loc7_.constVO.id.v == param1)
            {
               _loc7_.amount.v += param2;
               return;
            }
            _loc4_++;
         }
         var _loc6_:GameGoodVO = GoodConfig.instance().newGood(param1);
         _loc6_.amount.v = param2;
         _loc3_.push(_loc6_);
      }
      
      public function useCard(param1:int, param2:int) : void
      {
         var _loc4_:GameCardVO = null;
         var _loc3_:Array = this.cardList;
         var _loc5_:int = 0;
         var _loc6_:int = int(_loc3_.length);
         while(_loc5_ < _loc6_)
         {
            _loc4_ = _loc3_[_loc5_];
            if(_loc4_.constVO.id.v == param1)
            {
               _loc4_.amount.v -= param2;
               if(_loc4_.amount.v <= 0)
               {
                  _loc3_.splice(_loc5_,1);
               }
               return;
            }
            _loc5_++;
         }
      }
      
      public function delCard(param1:GameCardVO) : void
      {
         var _loc2_:Array = this.cardList;
         var _loc3_:int = int(_loc2_.indexOf(param1));
         if(_loc3_ != -1)
         {
            _loc2_.splice(_loc3_,1);
            FlagProxy.instance().changeValue(param1.constVO.id.v,-param1.amount.v);
         }
         param1.destroy();
      }
      
      public function countCardNum(param1:int) : int
      {
         var _loc3_:GameCardVO = null;
         var _loc2_:Array = this.cardList;
         for each(_loc3_ in _loc2_)
         {
            if(_loc3_.constVO.id.v == param1)
            {
               return _loc3_.amount.v;
            }
         }
         return 0;
      }
      
      public function countCard(param1:int) : Array
      {
         var _loc5_:GameCardVO = null;
         var _loc2_:Array = [];
         var _loc3_:Array = this.cardList;
         var _loc4_:* = param1 == 4;
         for each(_loc5_ in _loc3_)
         {
            if(_loc5_.isBoss == _loc4_)
            {
               _loc2_.push(_loc5_);
            }
         }
         return _loc2_;
      }
      
      public function get cardList() : Array
      {
         return BagProxy.instance().findList(ConstData.GOOD_CARD);
      }
   }
}

