package mogames.gameData.pk.vo
{
   import file.PKGConfig;
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.vo.BaseRewardVO;
   
   public class PKRewardVO
   {
      public var oneRank:Sint;
      
      public var twoRank:Sint;
      
      public var score:Sint;
      
      public var rewardOne:Array;
      
      public var rewardTwo:Array;
      
      public function PKRewardVO(param1:int, param2:int, param3:int, param4:Array, param5:Array)
      {
         super();
         this.oneRank = new Sint(param1);
         this.twoRank = new Sint(param2);
         this.score = new Sint(param3);
         this.rewardOne = param4;
         this.rewardTwo = param5;
      }
      
      public function inRate(param1:int) : Boolean
      {
         return param1 >= this.oneRank.v && param1 <= this.twoRank.v;
      }
      
      public function countReward(param1:int) : Array
      {
         var _loc2_:Array = param1 >= this.score.v ? this.rewardOne.slice() : this.rewardTwo.slice();
         var _loc3_:Array = PKGConfig.instance().findReward(_loc2_[0].id.v);
         if(!_loc3_ || _loc3_.length <= 0)
         {
            return _loc2_;
         }
         var _loc4_:Array = _loc2_.slice();
         _loc4_.shift();
         var _loc5_:BaseRewardVO = _loc3_[int(Math.random() * _loc3_.length)];
         _loc4_.push(_loc5_);
         return _loc4_;
      }
   }
}

