package mogames.gameData.pk.vo
{
   import mogames.gameData.pk.enemy.PKEnemyVO;
   import mogames.gameData.role.vo.RoleGameVO;
   
   public class PKEnemyGameVO extends RoleGameVO
   {
      public var owner:PKEnemyVO;
      
      public function PKEnemyGameVO(param1:PKEnemyVO)
      {
         super(param1.id.v);
         this.owner = param1;
         constVO = this.owner.constVO;
         assetVO = this.owner.assetVO;
         this.initData();
      }
      
      public function initData() : void
      {
         baseHP.v = this.owner.totalHP.v * 3;
         baseATK.v = this.owner.totalATK.v;
         baseMP.v = this.owner.totalMP.v;
         basePDEF.v = this.owner.totalPDEF.v;
         baseMDEF.v = this.owner.totalMDEF.v;
         baseCRIT.v = this.owner.totalCRIT.v;
         baseMISS.v = this.owner.totalMISS.v;
         baseMOVE.v = this.owner.totalMOVE.v;
         baseRUN.v = this.owner.totalRUN.v;
         updateAll();
         curHP.v = totalHP.v;
      }
   }
}

