package mogames.gameMission.mission.jingjichang.co
{
   import file.MissionConfig;
   import flash.utils.setTimeout;
   import mogames.Layers;
   import mogames.event.UIEvent;
   import mogames.gameData.pk.PKDataHandler;
   import mogames.gameData.pk.RolePKProxy;
   import mogames.gameData.role.HeroGameVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameLoad.GameInLoader;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.MissionFactory;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.mission.jingjichang.PKEnemySwitcher;
   import mogames.gameMission.mission.jingjichang.PKHeroManager;
   import mogames.gameMission.mission.jingjichang.PKMissionManager;
   import mogames.gameMission.mission.jingjichang.PKTopHeroModule;
   import mogames.gameRole.RoleFactory;
   import mogames.gameRole.hero.BaseHero;
   import mogames.gameUI.mission.HeroLeftModule;
   
   public class JingJiChangMission extends BaseMission
   {
      public var pkHandler:PKDataHandler;
      
      public var enemySwitcher:PKEnemySwitcher;
      
      public function JingJiChangMission()
      {
         super(false);
         this.pkHandler = RolePKProxy.instance().pkHandler;
         PKHeroManager.instance().init(RolePKProxy.instance().pkTeams);
      }
      
      override public function initMission(param1:int) : void
      {
         _missionVO = MissionConfig.instance().findMissionVO(param1);
         GameInLoader.instance().init(this.loadPKURLs,_missionVO.resURLs);
         GameInLoader.instance().isAuto = false;
      }
      
      private function loadPKURLs() : void
      {
         var func:Function = null;
         func = function():void
         {
            GameInLoader.instance().init(layoutFirstScene,pkHandler.mcURL,1,true);
            GameInLoader.instance().isAuto = false;
         };
         if(!this.pkHandler.mcURL || this.pkHandler.mcURL.length <= 0)
         {
            layoutFirstScene();
            return;
         }
         setTimeout(func,100);
      }
      
      public function layoutEnemy() : void
      {
         this.enemySwitcher = new PKEnemySwitcher();
         this.enemySwitcher.init(this.pkHandler.enemyTeams);
         this.enemySwitcher.startSwitcher();
      }
      
      override protected function onTeam(param1:UIEvent) : void
      {
         switch(param1.data.type)
         {
            case "onSwitch":
               this.removePlayer();
               this.switchPlayer(param1.data.data);
               BattleMediator.instance().onSwitch.dispatch(onePlayer);
               EffectManager.instance().playAudio("SWITCH");
               EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,onePlayer.x,onePlayer.y);
               if(param1.data.sType == 1)
               {
                  onePlayer.handlerWudi(3);
               }
               break;
            case "teamDead":
               if(Layers.camera)
               {
                  Layers.camera.enabled = false;
                  break;
               }
         }
      }
      
      override protected function layoutUI() : void
      {
         HeroLeftModule.instance().init();
         PKTopHeroModule.instance().init(RolePKProxy.instance().pkTeams);
      }
      
      override protected function changeScene(param1:int) : void
      {
         _sceneVO = MissionConfig.instance().findSceneVO(param1);
         Layers.ceLayer.state = MissionFactory.instance().newScene(_sceneVO.id.v,_sceneVO,PKMissionManager.instance().curMission);
      }
      
      override public function addHero() : void
      {
         var _loc4_:BaseHero = null;
         _heroVec.length = 0;
         var _loc1_:Array = PKHeroManager.instance().teams;
         var _loc2_:int = 0;
         var _loc3_:int = int(_loc1_.length);
         while(_loc2_ < _loc3_)
         {
            _loc4_ = RoleFactory.instance().newHero(_loc1_[_loc2_].id.v);
            _loc4_.roleEnabled = false;
            Layers.addCEChild(_loc4_);
            _heroVec[_loc2_] = _loc4_;
            _loc4_.roleVO.skillHP.v += (_loc4_.roleVO as HeroGameVO).ownHP;
            _loc4_.roleVO.updateHP();
            _loc4_.roleVO.curHP.v = _loc4_.roleVO.totalHP.v;
            _loc2_++;
         }
      }
      
      override public function initPlayer() : void
      {
         var _loc1_:BaseHero = null;
         for each(_loc1_ in _heroVec)
         {
            _loc1_.x = _heroPos.x;
            _loc1_.y = _heroPos.y;
         }
         this.switchPlayer(activeIndex);
      }
      
      override protected function switchPlayer(param1:int = 0) : void
      {
         onePlayer = _heroVec[param1];
         onePlayer.x = _heroPos.x;
         onePlayer.y = _heroPos.y;
         onePlayer.roleEnabled = true;
         if(Layers.camera)
         {
            Layers.camera.target = onePlayer;
         }
         activeIndex = param1;
         PKHeroManager.onePlayer = onePlayer;
         HeroLeftModule.instance().registerVO(onePlayer.roleVO as HeroGameVO);
      }
      
      override protected function removePlayer() : void
      {
         if(!onePlayer || !onePlayer.isReady)
         {
            return;
         }
         onePlayer.roleEnabled = false;
         _heroPos.x = onePlayer.x;
         _heroPos.y = onePlayer.y;
      }
      
      override public function get curTarget() : BaseHero
      {
         return onePlayer;
      }
      
      override public function cleanScene() : void
      {
         HeroLeftModule.clean();
         PKTopHeroModule.clean();
         Layers.ceLayer.destroyState();
         this.enemySwitcher.destroy();
         this.enemySwitcher = null;
      }
   }
}

