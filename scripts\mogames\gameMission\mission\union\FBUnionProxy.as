package mogames.gameMission.mission.union
{
   import mogames.gameData.drop.vo.BaseRewardVO;
   
   public class FBUnionProxy
   {
      private static var _instance:FBUnionProxy;
      
      public var curData:UnionFBDataVO;
      
      private var _fubens:Array;
      
      public function FBUnionProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("instance");
         }
         this.init();
      }
      
      public static function instance() : FBUnionProxy
      {
         if(!_instance)
         {
            _instance = new FBUnionProxy();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this._fubens = [];
         this._fubens[this._fubens.length] = new UnionFBDataVO(1,234,235,193,100,[new BaseRewardVO(16711,3),new BaseRewardVO(16723,10),new BaseRewardVO(14130,10),new BaseRewardVO(14131,10),new BaseRewardVO(14132,10)]);
         this._fubens[this._fubens.length] = new UnionFBDataVO(2,236,237,198,100,[new BaseRewardVO(16711,4),new BaseRewardVO(16820,8),new BaseRewardVO(16819,8),new BaseRewardVO(14021,3),new BaseRewardVO(14039,3)]);
      }
      
      public function findData(param1:int) : UnionFBDataVO
      {
         var _loc2_:UnionFBDataVO = null;
         for each(_loc2_ in this._fubens)
         {
            if(_loc2_.hpID == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function get idList() : Array
      {
         var _loc2_:UnionFBDataVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._fubens)
         {
            _loc1_[_loc1_.length] = _loc2_.hpID;
         }
         return _loc1_;
      }
      
      public function get fubens() : Array
      {
         return this._fubens;
      }
   }
}

