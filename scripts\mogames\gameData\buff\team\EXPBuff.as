package mogames.gameData.buff.team
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.buff.BaseTeamBuff;
   import mogames.gameData.buff.BuffVO;
   
   public class EXPBuff extends BaseTeamBuff
   {
      public var _per:Sint = new Sint();
      
      public function EXPBuff(param1:<PERSON>olean = false)
      {
         super(10000,param1);
      }
      
      override public function setBuffVO(param1:BuffVO) : void
      {
         super.setBuffVO(param1);
         this._per.v = param1.argDic.per.v;
      }
      
      public function countExp(param1:int) : int
      {
         return Math.ceil(param1 + param1 * this._per.v * 0.01);
      }
      
      override public function get tip() : String
      {
         return "关卡获得经验提高" + this._per.v + "%";
      }
   }
}

