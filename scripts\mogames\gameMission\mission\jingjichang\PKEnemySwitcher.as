package mogames.gameMission.mission.jingjichang
{
   import flash.geom.Point;
   import mogames.Layers;
   import mogames.gameEffect.EffectManager;
   import mogames.gamePK.BasePKEnemy;
   import mogames.gameRole.co.BaseRole;
   import mogames.gameSystem.CitrusRender;
   import mogames.gameSystem.CitrusTimer;
   
   public class PKEnemySwitcher
   {
      public var enemyList:Vector.<BasePKEnemy>;
      
      public var activeEnemy:BasePKEnemy;
      
      public var isSwitch:Boolean;
      
      private var _timer:CitrusTimer;
      
      private var _last:Point;
      
      public function PKEnemySwitcher()
      {
         super();
      }
      
      public function init(param1:Array) : void
      {
         var _loc2_:BasePKEnemy = null;
         this.isSwitch = false;
         this._timer = new CitrusTimer();
         this.enemyList = new Vector.<BasePKEnemy>();
         for each(_loc2_ in param1)
         {
            _loc2_.x = 1330;
            _loc2_.y = 308;
            _loc2_.setEnabled(false);
            _loc2_.onRole.add(this.listenDead);
            Layers.addCEChild(_loc2_);
            this.enemyList.push(_loc2_);
         }
         param1[0].target = PKHeroManager.onePlayer;
         param1[0].setEnabled(true);
         this.activeEnemy = param1[0];
         this._last = new Point();
      }
      
      public function startSwitcher() : void
      {
         if(this.enemyList.length <= 1)
         {
            return;
         }
         CitrusRender.instance().add(this.checkSwitch);
         this.updateTimer();
      }
      
      private function updateTimer() : void
      {
         var okFunc:Function = null;
         okFunc = function():void
         {
            isSwitch = true;
         };
         this._timer.setTimeOut(int(Math.random() * 10 + 10),okFunc,false);
         this._timer.startNone();
      }
      
      private function listenDead(param1:String) : void
      {
         if(param1 != "onDead")
         {
            return;
         }
         this.isSwitch = true;
         this._timer.reset(true);
      }
      
      private function checkSwitch() : void
      {
         if(this.extraAlives <= 0)
         {
            this.cleanSwitch();
            return;
         }
         if(!this.isSwitch)
         {
            return;
         }
         if(!this.activeEnemy.isDead && !this.activeEnemy.isSwitch)
         {
            return;
         }
         var _loc1_:Vector.<BasePKEnemy> = this.enemyList.slice();
         var _loc2_:int = int(this.enemyList.indexOf(this.activeEnemy));
         _loc1_.splice(_loc2_,1);
         var _loc3_:BaseRole = this.activeEnemy.target;
         this.activeEnemy.setEnabled(false);
         this._last.setTo(this.activeEnemy.x,this.activeEnemy.y - 50);
         this.activeEnemy = _loc1_[int(Math.random() * _loc1_.length)];
         this.activeEnemy.setEnabled(true);
         this.activeEnemy.x = this._last.x;
         this.activeEnemy.y = this._last.y;
         this.activeEnemy.target = _loc3_;
         EffectManager.instance().playAudio("SWITCH");
         EffectManager.instance().addBMCEffect("EFFECT_SMOKE_BOOM",Layers.ceEffectLayer,this.activeEnemy.x,this.activeEnemy.y);
         this.isSwitch = false;
         this.updateTimer();
      }
      
      private function get extraAlives() : int
      {
         var _loc1_:int = 0;
         var _loc2_:BasePKEnemy = null;
         for each(_loc2_ in this.enemyList)
         {
            if(_loc2_ != this.activeEnemy)
            {
               if(!_loc2_.isDead)
               {
                  _loc1_++;
               }
            }
         }
         return _loc1_;
      }
      
      public function get allDead() : Boolean
      {
         var _loc1_:BasePKEnemy = null;
         for each(_loc1_ in this.enemyList)
         {
            if(!_loc1_.isDead)
            {
               return false;
            }
         }
         return true;
      }
      
      public function cleanSwitch() : void
      {
         CitrusRender.instance().remove(this.checkSwitch);
         this._timer.pause();
      }
      
      private function destroyEnemies() : void
      {
         var _loc1_:BasePKEnemy = null;
         for each(_loc1_ in this.enemyList)
         {
            if(!_loc1_.kill)
            {
               _loc1_.kill = true;
            }
         }
      }
      
      public function destroy() : void
      {
         this.cleanSwitch();
         this.destroyEnemies();
         this._timer.destroy();
         this._timer = null;
         this.enemyList = null;
         this.activeEnemy = null;
      }
   }
}

