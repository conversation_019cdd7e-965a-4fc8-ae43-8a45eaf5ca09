package mogames.gameMission.mission.huoyanshan
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameRole.enemy.BOSSTieShan;
   import mogames.gameSystem.CitrusRender;
   import mogames.gameUI.dialog.StoryDialogModule;
   
   public class SceneHuoYanShan04 extends BossScene
   {
      public function SceneHuoYanShan04(param1:BaseSceneVO, param2:BaseMission)
      {
         super(param1,param2);
         _dropRect = new Rectangle(405,204,428,124);
      }
      
      override protected function handlerInit() : void
      {
         var func:Function = null;
         func = function():void
         {
            startBossBattle("BGM_BATTLE0");
            FlagProxy.instance().setValue(2036,1);
         };
         if(_mission.hasMark("battleTieShan"))
         {
            return;
         }
         this.addBOSS();
         if(!FlagProxy.instance().isComplete(2036))
         {
            showDialog("STORY0062",func);
         }
         else
         {
            this.startBossBattle("BGM_BATTLE0");
         }
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSTieShan();
         _boss.x = 850;
         _boss.y = 300;
         _boss.initData(30281,30281);
         _boss.target = _mission.onePlayer;
         add(_boss);
      }
      
      override protected function startBossBattle(param1:String = "") : void
      {
         setHeroEnable(true);
         _boss.activeEnemy(false);
         _boss.aiEnabled = true;
         EffectManager.instance().playBGM(param1);
         CitrusRender.instance().add(this.checkWin);
      }
      
      private function checkWin() : void
      {
         if(_boss.roleVO.hpPer <= 0.4)
         {
            _boss.aiEnabled = false;
            CitrusRender.instance().remove(this.checkWin);
            StoryDialogModule.instance().showDialog(new Point(1258,92),new Point(680,87),"好你个泼猴，今我先放过你！！","BODY_TIE_SHAN",null,2,false);
            _boss.setSysDead();
            EffectManager.instance().playBGM("BGM_DANGER1");
            _mission.setMark("battleTieShan");
         }
      }
      
      override public function destroy() : void
      {
         CitrusRender.instance().remove(this.checkWin);
         super.destroy();
      }
   }
}

