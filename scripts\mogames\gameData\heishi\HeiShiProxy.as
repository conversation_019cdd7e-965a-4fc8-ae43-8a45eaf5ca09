package mogames.gameData.heishi
{
   import file.MallHeiShiConfig;
   
   public class HeiShiProxy
   {
      private static var _instance:HeiShiProxy;
      
      public var flags:Array;
      
      public function HeiShiProxy()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
      }
      
      public static function instance() : HeiShiProxy
      {
         if(!_instance)
         {
            _instance = new HeiShiProxy();
         }
         return _instance;
      }
      
      public function startNew() : void
      {
         this.flags = null;
         this.flags = [];
         this.flags[0] = new HeiShiFlag();
         this.flags[1] = new HeiShiFlag();
         this.flags[2] = new HeiShiFlag();
         this.flags[3] = new HeiShiFlag();
         this.flags[4] = new HeiShiFlag();
         var _loc1_:Array = MallHeiShiConfig.instance().newList();
         var _loc2_:int = 0;
         while(_loc2_ < 5)
         {
            this.flags[_loc2_].id.v = _loc1_[_loc2_].id.v;
            _loc2_++;
         }
      }
      
      public function get saveData() : String
      {
         var _loc1_:Array = [];
         var _loc2_:int = 0;
         while(_loc2_ < 5)
         {
            _loc1_[_loc2_] = this.flags[_loc2_].saveData;
            _loc2_++;
         }
         return _loc1_.join("T");
      }
      
      public function set loadData(param1:String) : void
      {
         this.startNew();
         if(!param1)
         {
            return;
         }
         var _loc2_:Array = param1.split("T");
         var _loc3_:int = 0;
         while(_loc3_ < 5)
         {
            this.flags[_loc3_].loadData = _loc2_[_loc3_];
            _loc3_++;
         }
      }
      
      public function dailyRefresh() : void
      {
         this.startNew();
      }
   }
}

