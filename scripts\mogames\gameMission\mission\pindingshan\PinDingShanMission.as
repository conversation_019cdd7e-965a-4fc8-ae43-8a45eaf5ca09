package mogames.gameMission.mission.pindingshan
{
   import mogames.gameData.base.Sint;
   import mogames.gameMission.mission.BaseMission;
   
   public class PinDingShanMission extends BaseMission
   {
      private var _randomList:Array;
      
      public var newList:Array;
      
      public function PinDingShanMission()
      {
         super();
         this.createYaoCao();
      }
      
      private function createYaoCao() : void
      {
         var _loc1_:Array = null;
         this._randomList = [];
         this._randomList.push([new Sint(),new Sint(),new Sint(15003),new Sint(10005)]);
         this._randomList.push([new Sint(),new Sint(),new Sint(15002),new Sint(10006)]);
         this._randomList.push([new Sint(),new Sint(),new Sint(15001),new Sint(10005)]);
         this._randomList.push([new Sint(),new Sint(),new Sint(15003),new Sint(10006)]);
         this._randomList.push([new Sint(),new Sint(),new Sint(15002),new Sint(10005)]);
         this._randomList.push([new Sint(),new Sint(),new Sint(15001),new Sint(10006)]);
         this.newList = [];
         for each(_loc1_ in this._randomList)
         {
            this.newList.push(_loc1_[int(Math.random() * _loc1_.length)]);
         }
      }
      
      override public function cleanMission() : void
      {
         this._randomList = null;
         this.newList = [];
         super.cleanMission();
      }
   }
}

