package mogames.gameData.pet.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.drop.UseHandler;
   import mogames.gameData.fabao.BaseUseVO;
   
   public class PetEvolveCostVO extends BaseUseVO
   {
      public var evolve:Sint;
      
      public var petID:Sint;
      
      public function PetEvolveCostVO(param1:int, param2:int, param3:int, param4:Array)
      {
         this.evolve = new Sint(param2);
         this.petID = new Sint(param1);
         super(param3,param4);
      }
      
      public function get needStr() : String
      {
         var _loc1_:Array = [goldStr,UseHandler.instance().parseNeed(needList)];
         return _loc1_.join(",");
      }
   }
}

