package component
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class RadioSwitcher
   {
      private var _radios:Array;
      
      private var _funcs:Array;
      
      private var _index:int;
      
      public function RadioSwitcher(param1:Array, param2:Array, param3:int = -1)
      {
         super();
         this._radios = param1;
         this._funcs = param2;
         this.defaultRadios();
         this.initRadios();
         if(param3 != -1)
         {
            this._radios[param3].dispatchEvent(new MouseEvent(MouseEvent.MOUSE_DOWN));
         }
      }
      
      private function onSelected(param1:MouseEvent) : void
      {
         var _loc2_:MovieClip = param1.target as MovieClip;
         this._index = this._radios.indexOf(_loc2_);
         this.defaultRadios();
         _loc2_.gotoAndStop(2);
         _loc2_.mouseEnabled = false;
         this._funcs[this._index]();
      }
      
      private function defaultRadios() : void
      {
         var _loc1_:MovieClip = null;
         for each(_loc1_ in this._radios)
         {
            _loc1_.gotoAndStop(1);
            _loc1_.mouseEnabled = true;
         }
      }
      
      private function initRadios() : void
      {
         var _loc1_:MovieClip = null;
         for each(_loc1_ in this._radios)
         {
            _loc1_.mouseChildren = false;
            _loc1_.buttonMode = true;
            _loc1_.addEventListener(MouseEvent.MOUSE_DOWN,this.onSelected,false,0,true);
         }
      }
      
      private function destroyRadios() : void
      {
         var _loc1_:MovieClip = null;
         for each(_loc1_ in this._radios)
         {
            _loc1_.removeEventListener(MouseEvent.MOUSE_DOWN,this.onSelected);
         }
      }
      
      public function changeIndex(param1:int) : void
      {
         this._radios[param1].dispatchEvent(new MouseEvent(MouseEvent.MOUSE_DOWN));
      }
      
      public function get selectIndex() : int
      {
         return this._index;
      }
      
      public function destroy() : void
      {
         this.destroyRadios();
         this._radios = null;
         this._funcs = null;
      }
   }
}

