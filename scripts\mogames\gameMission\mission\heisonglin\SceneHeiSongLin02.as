package mogames.gameMission.mission.heisonglin
{
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mogames.Layers;
   import mogames.gameData.mission.BaseSceneVO;
   import mogames.gameEffect.EffectManager;
   import mogames.gameMission.BattleMediator;
   import mogames.gameMission.co.BossScene;
   import mogames.gameMission.mission.BaseMission;
   import mogames.gameMission.trigger.EnemyTrigger;
   import mogames.gameMission.trigger.LocalTriggerX;
   import mogames.gameObj.trap.StoneTrap;
   import mogames.gameRole.enemy.BOSSHeiXiongJing;
   
   public class SceneHeiSongLin02 extends BossScene
   {
      private var _eTrigger:EnemyTrigger;
      
      private var _pTrigger:LocalTriggerX;
      
      public function SceneHeiSongLin02(param1:BaseSceneVO, param2:BaseMission)
      {
         var _loc3_:Array = [StoneTrap];
         super(param1,param2);
      }
      
      override protected function handlerInit() : void
      {
         _mission.cleanLoadUI();
         this.initEnemies();
      }
      
      protected function initEnemies() : void
      {
         if(_mission.hasMark("enemy802"))
         {
            return;
         }
         _mission.setMark("enemy802");
         this.addBOSS();
         this._eTrigger = new EnemyTrigger(_mission,[[190,370,150,100],[668,370,150,100],[1000,370,150,100]],8021,null);
         this._pTrigger = new LocalTriggerX(_mission.onePlayer,new Point(500,470),1);
         this._pTrigger.trigger = this._eTrigger;
         this._pTrigger.okFunc = this.handlerStart;
         this._pTrigger.start();
      }
      
      override protected function addBOSS() : void
      {
         _boss = new BOSSHeiXiongJing();
         _boss.x = 930;
         _boss.y = 470;
         _boss.initData(30032,30032,27);
         _boss.target = _mission.curTarget;
         add(_boss);
         _dropRect = new Rectangle(600,330,370,140);
      }
      
      private function handlerStart() : void
      {
         startBossBattle("BGM_BATTLE1");
      }
      
      override protected function handlerWin() : void
      {
         BattleMediator.instance().onBattle.dispatch({
            "type":"onBossDrop",
            "boss":_boss,
            "rect":_dropRect,
            "hero":_boss.target
         });
         Layers.lockGame = false;
         EffectManager.instance().playBGM("BGM_SUNSHINE");
      }
      
      override public function destroy() : void
      {
         if(this._eTrigger)
         {
            this._eTrigger.destroy();
         }
         if(this._pTrigger)
         {
            this._pTrigger.destroy();
         }
         this._eTrigger = null;
         this._pTrigger = null;
         super.destroy();
      }
   }
}

