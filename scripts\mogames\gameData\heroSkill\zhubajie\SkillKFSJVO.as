package mogames.gameData.heroSkill.zhubajie
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.heroSkill.BaseSkillVO;
   import mogames.gameData.role.HeroGameVO;
   
   public class SkillKFSJVO extends BaseSkillVO
   {
      public var cureHP:Sint = new Sint();
      
      public function SkillKFSJVO(param1:int, param2:HeroGameVO, param3:int = 0)
      {
         super(param1,param2,param3);
      }
      
      override public function setLevel(param1:int, param2:Boolean = false) : void
      {
         super.setLevel(param1);
         this.cureHP.v = countVO.argDic.arg0.v + countVO.argDic.arg1.v * level.v;
      }
   }
}

