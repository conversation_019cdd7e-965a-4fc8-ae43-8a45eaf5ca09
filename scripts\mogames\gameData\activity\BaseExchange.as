package mogames.gameData.activity
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import mogames.gameData.MasterProxy;
   import mogames.gameUI.prompt.MiniMsgMediator;
   import mogames.gameUI.prompt.MsgMediator;
   
   public class BaseExchange
   {
      protected var _okFunc:Function;
      
      protected var _loader:URLLoader;
      
      protected var _req:URLRequest;
      
      protected var _uid:Number;
      
      protected var KEY:String;
      
      public function BaseExchange(param1:Function)
      {
         super();
         this._okFunc = param1;
         this._loader = new URLLoader();
         this._loader.addEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this._loader.addEventListener(Event.COMPLETE,this.loaderCompleteHandler);
         this._uid = Number(MasterProxy.instance().serverUID);
      }
      
      public function startExchange(... rest) : void
      {
      }
      
      protected function onLoadBack(param1:Object) : void
      {
         MsgMediator.clean();
      }
      
      protected function countKey(param1:Array) : String
      {
         return "";
      }
      
      private function loaderCompleteHandler(param1:Event) : void
      {
         this.onLoadBack(this._loader.data);
      }
      
      private function errorHandler(param1:IOErrorEvent) : void
      {
         MsgMediator.clean();
         MiniMsgMediator.instance().showAutoMsg("网络异常，请重试！");
      }
      
      public function destroy() : void
      {
         this._loader.removeEventListener(IOErrorEvent.IO_ERROR,this.errorHandler);
         this._loader.removeEventListener(Event.COMPLETE,this.loaderCompleteHandler);
         this._loader = null;
         this._loader = null;
      }
   }
}

