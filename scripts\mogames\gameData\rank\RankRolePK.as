package mogames.gameData.rank
{
   import mogames.gameData.MasterProxy;
   import mogames.gameData.pk.RolePKProxy;
   import mogames.gameData.rank.vo.RankRolePKVO;
   import mogames.gameData.role.HeroProxy;
   import mogames.gamePKG.GameProxy;
   
   public class RankRolePK extends RankHandler
   {
      public function RankRolePK()
      {
         super([1826]);
         _msg = "处理数据中";
      }
      
      override public function parseRank(param1:Array) : Array
      {
         var _loc5_:RankRolePKVO = null;
         if(!param1 || param1.length <= 0)
         {
            return [];
         }
         var _loc2_:int = 0;
         var _loc3_:int = int(param1.length);
         var _loc4_:Array = [];
         while(_loc2_ < _loc3_)
         {
            _loc5_ = new RankRolePKVO();
            _loc5_.parseData(param1[_loc2_]);
            _loc4_[_loc2_] = _loc5_;
            _loc2_++;
         }
         _loc4_.sortOn("sortIndex",Array.NUMERIC);
         return _loc4_;
      }
      
      override public function submitRank(param1:Function) : void
      {
         if(MasterProxy.instance().totalMoney <= 0 && RolePKProxy.instance().pkVO.score.v > 700)
         {
            GameProxy.instance().isCheat.v = 8200;
            if(param1 != null)
            {
               param1();
            }
            return;
         }
         super.submitRank(param1);
      }
      
      override protected function get rankData() : Array
      {
         return [this.newRankData()];
      }
      
      protected function newRankData() : Object
      {
         return {
            "rId":rankIDs[0],
            "score":RolePKProxy.instance().pkVO.score.v,
            "extra":[HeroProxy.instance().totalBR]
         };
      }
      
      public function get hasReward() : Boolean
      {
         var _loc1_:int = findMyRank(0);
         if(_loc1_ == 0)
         {
            return false;
         }
         return _loc1_ <= 1000;
      }
   }
}

