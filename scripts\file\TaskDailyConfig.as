package file
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.flag.FlagProxy;
   import mogames.gameData.task.constvo.MissionTaskVO;
   
   public class TaskDailyConfig
   {
      private static var _instance:TaskDailyConfig;
      
      private var _tasks:Vector.<MissionTaskVO>;
      
      public function TaskDailyConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : TaskDailyConfig
      {
         if(!_instance)
         {
            _instance = new TaskDailyConfig();
         }
         return _instance;
      }
      
      public function init() : void
      {
         this._tasks = new Vector.<MissionTaskVO>();
         this._tasks.push(new MissionTaskVO(1001,[new Sint(22001),new Sint(22101),new Sint(22201),new Sint(22301),new Sint(22401)]));
         this._tasks.push(new MissionTaskVO(1003,[new Sint(22002),new <PERSON>t(22102),new <PERSON><PERSON>(22202),new Sint(22302),new <PERSON>t(22402)]));
         this._tasks.push(new MissionTaskVO(1005,[new Sint(22003),new Sint(22103),new Sint(22203),new Sint(22303),new Sint(22403)]));
         this._tasks.push(new MissionTaskVO(1007,[new Sint(22004),new Sint(22104),new Sint(22204),new Sint(22304),new Sint(22404)]));
         this._tasks.push(new MissionTaskVO(1008,[new Sint(22005),new Sint(22105),new Sint(22205),new Sint(22305),new Sint(22405)]));
         this._tasks.push(new MissionTaskVO(1009,[new Sint(22006),new Sint(22106),new Sint(22206),new Sint(22306),new Sint(22406)]));
         this._tasks.push(new MissionTaskVO(1010,[new Sint(22007),new Sint(22107),new Sint(22207),new Sint(22307),new Sint(22407),new Sint(23007),new Sint(23107),new Sint(23207),new Sint(23307),new Sint(23407)]));
         this._tasks.push(new MissionTaskVO(1011,[new Sint(22008),new Sint(22108),new Sint(22208),new Sint(22308),new Sint(22408),new Sint(23008),new Sint(23108),new Sint(23208),new Sint(23308),new Sint(23408)]));
         this._tasks.push(new MissionTaskVO(1012,[new Sint(22009),new Sint(22109),new Sint(22209),new Sint(22309),new Sint(22409),new Sint(23009),new Sint(23109),new Sint(23209),new Sint(23309),new Sint(23409)]));
         this._tasks.push(new MissionTaskVO(1013,[new Sint(23010),new Sint(23110),new Sint(23210),new Sint(23310),new Sint(23410)]));
         this._tasks.push(new MissionTaskVO(1014,[new Sint(22011),new Sint(22111),new Sint(22211),new Sint(22311),new Sint(22411),new Sint(23011),new Sint(23111),new Sint(23211),new Sint(23311),new Sint(23411)]));
         this._tasks.push(new MissionTaskVO(1015,[new Sint(22012),new Sint(22112),new Sint(22212),new Sint(22312),new Sint(22412),new Sint(23012),new Sint(23112),new Sint(23212),new Sint(23312),new Sint(23412)]));
         this._tasks.push(new MissionTaskVO(1016,[new Sint(23013),new Sint(23113),new Sint(23213),new Sint(23313),new Sint(23413)]));
         this._tasks.push(new MissionTaskVO(1017,[new Sint(23014),new Sint(23114),new Sint(23214),new Sint(23314),new Sint(23414)]));
         this._tasks.push(new MissionTaskVO(1018,[new Sint(23015),new Sint(23115),new Sint(23215),new Sint(23315),new Sint(23415)]));
         this._tasks.push(new MissionTaskVO(1019,[new Sint(23016),new Sint(23116),new Sint(23216),new Sint(23316),new Sint(23416)]));
         this._tasks.push(new MissionTaskVO(1020,[new Sint(23017),new Sint(23117),new Sint(23217),new Sint(23317),new Sint(23417)]));
         this._tasks.push(new MissionTaskVO(1021,[new Sint(23018),new Sint(23118),new Sint(23218),new Sint(23318),new Sint(23418)]));
         this._tasks.push(new MissionTaskVO(1022,[new Sint(23019),new Sint(23119),new Sint(23219),new Sint(23319),new Sint(23419)]));
         this._tasks.push(new MissionTaskVO(1023,[new Sint(23020),new Sint(23120),new Sint(23220),new Sint(23320),new Sint(23420)]));
         this._tasks.push(new MissionTaskVO(1024,[new Sint(23021),new Sint(23121),new Sint(23221),new Sint(23321),new Sint(23421)]));
         this._tasks.push(new MissionTaskVO(1025,[new Sint(23022),new Sint(23122),new Sint(23222),new Sint(23321),new Sint(23422)]));
         this._tasks.push(new MissionTaskVO(1026,[new Sint(23023),new Sint(23123),new Sint(23223),new Sint(23321),new Sint(23423)]));
         this._tasks.push(new MissionTaskVO(1027,[new Sint(23024),new Sint(23124),new Sint(23224),new Sint(23324),new Sint(23424)]));
         this._tasks.push(new MissionTaskVO(1028,[new Sint(23025),new Sint(23125),new Sint(23225),new Sint(23325),new Sint(23425)]));
         this._tasks.push(new MissionTaskVO(1029,[new Sint(23026),new Sint(23126),new Sint(23226),new Sint(23326),new Sint(23426)]));
         this._tasks.push(new MissionTaskVO(1030,[new Sint(23027),new Sint(23127),new Sint(23227),new Sint(23327),new Sint(23427)]));
         this._tasks.push(new MissionTaskVO(1031,[new Sint(23028),new Sint(23128),new Sint(23228),new Sint(23328),new Sint(23428)]));
         this._tasks.push(new MissionTaskVO(1032,[new Sint(23029),new Sint(23129),new Sint(23229),new Sint(23329),new Sint(23429)]));
         this._tasks.push(new MissionTaskVO(1033,[new Sint(23030),new Sint(23130),new Sint(23230),new Sint(23330),new Sint(23430)]));
      }
      
      private function collectDaily() : Array
      {
         var _loc2_:MissionTaskVO = null;
         var _loc1_:Array = [];
         for each(_loc2_ in this._tasks)
         {
            if(FlagProxy.instance().isComplete(_loc2_.flagID.v))
            {
               _loc1_ = _loc1_.concat(_loc2_.tasks);
            }
         }
         return _loc1_;
      }
      
      public function randomDaily(param1:int) : Array
      {
         var _loc4_:int = 0;
         var _loc5_:Sint = null;
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         var _loc8_:Sint = null;
         var _loc9_:int = 0;
         var _loc2_:Array = this.collectDaily();
         if(_loc2_.length <= 0)
         {
            return [];
         }
         var _loc3_:Array = [[],[],[],[],[]];
         for each(_loc5_ in _loc2_)
         {
            _loc4_ = int(String(_loc5_.v).charAt(2));
            _loc3_[_loc4_].push(_loc5_);
         }
         _loc6_ = [];
         _loc9_ = 0;
         while(_loc9_ < param1)
         {
            _loc7_ = int(Math.random() * 100 + 1);
            if(_loc7_ <= 15)
            {
               _loc8_ = _loc3_[4][int(Math.random() * _loc3_[4].length)];
            }
            else if(_loc7_ <= 35)
            {
               _loc8_ = _loc3_[3][int(Math.random() * _loc3_[3].length)];
            }
            else if(_loc7_ <= 60)
            {
               _loc8_ = _loc3_[2][int(Math.random() * _loc3_[2].length)];
            }
            else if(_loc7_ <= 85)
            {
               _loc8_ = _loc3_[1][int(Math.random() * _loc3_[1].length)];
            }
            else
            {
               _loc8_ = _loc3_[0][int(Math.random() * _loc3_[0].length)];
            }
            _loc6_[_loc9_] = new Sint(_loc8_.v);
            _loc9_++;
         }
         return _loc6_;
      }
   }
}

