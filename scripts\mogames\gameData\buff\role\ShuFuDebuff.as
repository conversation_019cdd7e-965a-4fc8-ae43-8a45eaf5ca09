package mogames.gameData.buff.role
{
   import mogames.AssetManager;
   import mogames.gameData.buff.BaseBuff;
   import mogames.gameObj.display.BMCSprite;
   
   public class ShuFuDebuff extends BaseBuff
   {
      public function ShuFuDebuff()
      {
         super(1007,true,true);
      }
      
      override public function startBuff() : void
      {
         super.startBuff();
         this.onBuff();
      }
      
      override protected function onBuff() : void
      {
         _roleVO.totalMOVE.v = 0;
         _roleVO.totalRUN.v = 0;
      }
      
      override protected function onEnd() : void
      {
         _roleVO.updateSPEED();
         destroy();
      }
      
      override protected function startInterval() : void
      {
         _buffTimer.setInterval(0.01,_buffVO.sec.v,this.onBuff,this.onEnd);
      }
      
      override public function get buffSkin() : BMCSprite
      {
         _skin.setupSequence(AssetManager.findAnimation("EFFECT_BUFF_SHU_FU_CLIP"),true);
         _skin.x = _role.width * 0.5;
         _skin.y = _role.height * 0.5;
         return _skin;
      }
   }
}

