package mogames.gameFabao.fabao
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameData.buff.BuffVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameFabao.view.FabaoFuYaoSuoView;
   import mogames.gameMission.BattleMediator;
   import mogames.gameRole.hero.BaseHero;
   
   public class FabaoFuYaoSuo extends BaseFabao
   {
      private var _hitVO:BaseHitVO;
      
      public function FabaoFuYaoSuo(param1:GameFabaoVO)
      {
         super(param1,40,48);
         this.createFabaoData();
         this.createVO();
         createView(new FabaoFuYaoSuoView());
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(20),
            "keepTime":new Snum(1.5),
            "hurt":new Sint(900)
         },{
            "cdTime":new Snum(20),
            "keepTime":new Snum(1.5),
            "hurt":new Sint(1220)
         },{
            "cdTime":new Snum(20),
            "keepTime":new Snum(1.5),
            "hurt":new Sint(1540)
         },{
            "cdTime":new Snum(20),
            "keepTime":new Snum(1.5),
            "hurt":new Sint(1928)
         },{
            "cdTime":new Snum(20),
            "keepTime":new Snum(1.5),
            "hurt":new Sint(2274)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      private function createVO() : void
      {
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._hitVO.HURT_X = 0;
         this._hitVO.HURT_Y = 0;
         this._hitVO.HURT_SKIN = "EFFECT_GUN_ATK";
         this._hitVO.HURT_SOUND = "GUNHURT";
         this._hitVO.WU_XING.v = _fabaoVO.constFabao.wuxing.v;
      }
      
      override public function followTarget() : void
      {
         if(_fabaoView.isSkill)
         {
            return;
         }
         super.followTarget();
      }
      
      override public function handlerSkill() : void
      {
         this.x = owner.x + owner.width * owner.dirX;
         super.handlerSkill();
      }
      
      public function dispatchSkillOne() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         this._hitVO.init(_fabaoView.mcATK,_fabaoData.hurt.v,1,false);
         this._hitVO.buffList = [new BuffVO(1007,_fabaoData.keepTime.v)];
         if(owner is BaseHero)
         {
            BattleMediator.instance().onHeroATK.dispatch(this._hitVO);
         }
         else
         {
            BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
         }
      }
   }
}

