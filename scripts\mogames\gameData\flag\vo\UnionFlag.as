package mogames.gameData.flag.vo
{
   import mogames.gamePKG.GameProxy;
   
   public class UnionFlag extends NumFlag
   {
      public function UnionFlag(param1:int)
      {
         super(param1,1,false,true);
      }
      
      override public function dailyRefresh() : void
      {
         if(!GameProxy.instance().nextWeek)
         {
            return;
         }
         _cur.v = 0;
      }
   }
}

