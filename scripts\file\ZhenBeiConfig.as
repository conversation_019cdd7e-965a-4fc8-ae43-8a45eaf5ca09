package file
{
   import mogames.gameData.fuben.zhenbei.ZhenBeiVO;
   
   public class ZhenBeiConfig
   {
      private static var _instance:ZhenBeiConfig;
      
      public var fbList:Array;
      
      public function ZhenBeiConfig()
      {
         super();
         if(_instance)
         {
            throw new Error("只能用instance来获取实例");
         }
         this.init();
      }
      
      public static function instance() : ZhenBeiConfig
      {
         if(!_instance)
         {
            _instance = new ZhenBeiConfig();
         }
         return _instance;
      }
      
      private function init() : void
      {
         this.fbList = [];
         this.fbList.push(new ZhenBeiVO(4005,[10000,13316,14001,14002]));
         this.fbList.push(new ZhenBeiVO(4006,[10000,13318,14001,14002]));
         this.fbList.push(new ZhenBeiVO(4007,[10000,13323,14001,14002]));
         this.fbList.push(new ZhenBeiVO(4008,[10000,13324,14001,14002]));
      }
   }
}

