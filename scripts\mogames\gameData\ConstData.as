package mogames.gameData
{
   import flash.net.SharedObject;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   
   public class ConstData
   {
      public static var quality:String = "best";
      
      public static var openAudio:<PERSON>olean = true;
      
      public static var openMusic:<PERSON>olean = true;
      
      public static var openBlood:<PERSON>olean = true;
      
      public static var openNum:Boolean = true;
      
      public static var PK_MODE:Array = ["单挑","组队","对战","争霸"];
      
      public static var isNew:Boolean = true;
      
      public static const MC_START:String = "start";
      
      public static const HP_COLOR:Array = [16711680,16737792,10092288,52479,8996505];
      
      public static const GOOD_PROP:String = "1";
      
      public static const GOOD_EQUIP:String = "2";
      
      public static const GOOD_FASHION:String = "3";
      
      public static const GOOD_PET:String = "4";
      
      public static const GOOD_CLIP:String = "5";
      
      public static const CHENG_HAO:String = "6";
      
      public static const GOOD_CARD:String = "7";
      
      public static const GOOD_FABAO_PAPER:String = "2";
      
      public static const EQUIP_FABAO:String = "3";
      
      public static const GOOD_COLOR0:Array = [16777215,10092288,3394815,16711935,16766720];
      
      public static const GOOD_COLOR1:Array = ["ffffff","99ff00","33ccff","ff00ff","ffd700"];
      
      public static const GOOD_QUALITY:Array = ["普通","精致","稀有","罕见","传说"];
      
      public static const GOOD_QUALITY1:Array = ["白色","绿色","蓝色","紫色","橙色"];
      
      public static const HERB_STATUS:Array = ["幼苗期","成长期","成熟期"];
      
      public static const EQUIP_TYPE:Array = ["武器","衣服","饰品","法宝","神兵","神甲","兽爪","兽盔","兽甲","兽坠"];
      
      public static const TOWER_NAME:Array = ["降妖塔","封魔塔","诛仙塔","灭神塔"];
      
      public static const FLOOR_NAME:Array = ["第一层","第二层","第三层","第四层","第五层"];
      
      public static const WX_ZERO:Sint = new Sint(0);
      
      public static const WX_ONE:Sint = new Sint(1);
      
      public static const WX_TWO:Sint = new Sint(2);
      
      public static const WX_THREE:Sint = new Sint(3);
      
      public static const WX_FOUR:Sint = new Sint(4);
      
      public static const WX_FIVE:Sint = new Sint(5);
      
      public static const WX_NAME:Array = ["无","金","木","水","火","土"];
      
      public static const ATT_NAME:Array = ["生命","法力","攻击"];
      
      public static const PET_QUALITY:Array = ["普通","优秀","精良","极品"];
      
      public static const PET_COLOR:Array = ["ffffff","99ff00","33ccff","ffd700"];
      
      public static const TASK_STORY:String = "1";
      
      public static const TASK_GOOD:String = "2";
      
      public static const TASK_ENEMY:String = "3";
      
      public static const TASK_TAB_MAIN:String = "1";
      
      public static const TASK_TAB_DAILY:String = "2";
      
      public static const DATA_NUM0:Sint = new Sint(0);
      
      public static const DATA_NUM1:Sint = new Sint(1);
      
      public static const DATA_NUM2:Sint = new Sint(2);
      
      public static const DATA_NUM3:Sint = new Sint(3);
      
      public static const DATA_NUM4:Sint = new Sint(4);
      
      public static const DATA_NUM5:Sint = new Sint(5);
      
      public static const DATA_NUM6:Sint = new Sint(6);
      
      public static const DATA_NUM10:Sint = new Sint(10);
      
      public static const DATA_NUM50:Sint = new Sint(50);
      
      public static const DATA_NUM01:Snum = new Snum(0.1);
      
      public static const DATA_NUM04:Snum = new Snum(0.4);
      
      public static const DATA_NUM05:Snum = new Snum(0.5);
      
      public static const DATA_NUM06:Snum = new Snum(0.6);
      
      public static const DATA_NUM07:Snum = new Snum(0.7);
      
      public static const DATA_NUM012:Snum = new Snum(1.2);
      
      public static const DATA_NUM001:Snum = new Snum(0.001);
      
      public function ConstData()
      {
         super();
      }
      
      public static function initLocal() : void
      {
         var _loc1_:SharedObject = SharedObject.getLocal("localData1");
         if(!_loc1_.data.isSave)
         {
            return;
         }
         quality = _loc1_.data.quality;
         openAudio = _loc1_.data.openAuido;
         openMusic = _loc1_.data.openMusic;
         openBlood = _loc1_.data.openBlood;
         openNum = _loc1_.data.openNum;
      }
      
      public static function saveLocal() : void
      {
         var _loc1_:SharedObject = SharedObject.getLocal("localData1");
         _loc1_.data.quality = quality;
         _loc1_.data.openAuido = openAudio;
         _loc1_.data.openMusic = openMusic;
         _loc1_.data.openBlood = openBlood;
         _loc1_.data.openNum = openNum;
         _loc1_.data.isSave = true;
         _loc1_.flush();
      }
   }
}

