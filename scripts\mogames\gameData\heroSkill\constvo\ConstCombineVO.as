package mogames.gameData.heroSkill.constvo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.IconVO;
   
   public class ConstCombineVO extends IconVO
   {
      public var id:Sint;
      
      public var oneID:Sint;
      
      public var twoID:Sint;
      
      public var wuxing:Sint;
      
      public var mcEffect:String;
      
      public function ConstCombineVO(param1:int, param2:int, param3:int, param4:int, param5:String, param6:String, param7:String, param8:String)
      {
         super();
         this.id = new Sint(param1);
         this.oneID = new Sint(param2);
         this.twoID = new Sint(param3);
         this.wuxing = new Sint(param4);
         name = param5;
         iname = param6;
         this.mcEffect = param7;
         infor = param8;
      }
   }
}

