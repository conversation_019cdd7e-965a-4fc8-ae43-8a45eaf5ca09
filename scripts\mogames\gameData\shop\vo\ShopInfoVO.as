package mogames.gameData.shop.vo
{
   import file.ShopConfig;
   
   public class ShopInfoVO
   {
      public var shopID:int;
      
      public var dialog:String;
      
      public var roleFrame:int;
      
      public function ShopInfoVO(param1:int, param2:String, param3:int)
      {
         super();
         this.shopID = param1;
         this.dialog = param2;
         this.roleFrame = param3;
      }
      
      public function get shopList() : Array
      {
         return ShopConfig.instance().findShop(this.shopID);
      }
   }
}

