package mogames.gameData.buff
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.role.vo.RoleGameVO;
   import mogames.gameEffect.BMCFactory;
   import mogames.gameObj.display.BMCSprite;
   import mogames.gameRole.co.BaseRole;
   import mogames.gameSystem.CitrusTimer;
   
   public class BaseBuff
   {
      public var buffID:Sint;
      
      protected var _roleVO:RoleGameVO;
      
      protected var _role:BaseRole;
      
      protected var _buffTimer:CitrusTimer;
      
      protected var _skin:BMCSprite;
      
      protected var _buffVO:BuffVO;
      
      protected var _skinName:String;
      
      public var isDebuff:Boolean;
      
      public var isInterval:Boolean;
      
      public function BaseBuff(param1:int, param2:Boolean = false, param3:Boolean = false)
      {
         super();
         this.buffID = new Sint(param1);
         this.isDebuff = param2;
         this.isInterval = param3;
         this._buffTimer = new CitrusTimer();
         this._skin = BMCFactory.instance().newBMC();
      }
      
      public function setTarget(param1:BaseRole) : void
      {
         this._role = param1;
         this._roleVO = param1.roleVO;
      }
      
      public function setBuffVO(param1:BuffVO) : void
      {
         this._buffVO = param1;
      }
      
      public function startBuff() : void
      {
         if(this.isInterval)
         {
            this.startInterval();
         }
         else
         {
            this.startTimeout();
         }
      }
      
      public function resetBuff(param1:BuffVO) : void
      {
         this.setBuffVO(param1);
         if(this.isInterval)
         {
            this.startInterval();
         }
         else
         {
            this.startTimeout();
         }
      }
      
      protected function startTimeout() : void
      {
         this._buffTimer.setTimeOut(this._buffVO.sec.v,this.onEnd);
      }
      
      protected function startInterval() : void
      {
         this._buffTimer.setInterval(1,this._buffVO.sec.v,this.onBuff,this.onEnd);
      }
      
      protected function onBuff() : void
      {
      }
      
      protected function onEnd() : void
      {
         this.cleanEffect();
         this.destroy();
      }
      
      protected function cleanEffect() : void
      {
      }
      
      public function get buffSkin() : BMCSprite
      {
         return this._skin;
      }
      
      public function set target(param1:BaseRole) : void
      {
         this._role = param1;
      }
      
      public function cleanBuff() : void
      {
         this._buffTimer.destroy();
         this._buffTimer = null;
         if(this._skin)
         {
            this._skin.recyle();
         }
         this._buffVO = null;
         this._skin = null;
         this._role = null;
      }
      
      public function destroy() : void
      {
         this._roleVO.delBuff(this);
         this.cleanBuff();
      }
   }
}

