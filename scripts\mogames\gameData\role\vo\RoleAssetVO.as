package mogames.gameData.role.vo
{
   import mogames.gameData.base.Sint;
   import mogames.gameData.good.IconVO;
   
   public class RoleAssetVO extends IconVO
   {
      public var roleID:Sint;
      
      public var bodySkin:String;
      
      public var clothSkin:String;
      
      public var wuqiSkin:String;
      
      public var skillSkin:String;
      
      public var hitSkin:String;
      
      public var hitSound:String;
      
      public function RoleAssetVO(param1:int, param2:String, param3:String, param4:String, param5:String, param6:String, param7:String = "", param8:String = "", param9:String = "")
      {
         super();
         this.roleID = new Sint(param1);
         name = param2;
         iname = param3;
         this.bodySkin = param6;
         this.clothSkin = param7;
         this.wuqiSkin = param8;
         this.skillSkin = param9;
         this.hitSkin = param4;
         this.hitSound = param5;
      }
   }
}

