package mogames.gameFabao.fabao
{
   import mogames.gameData.ConstRole;
   import mogames.gameData.base.Sint;
   import mogames.gameData.base.Snum;
   import mogames.gameData.battle.BaseHitVO;
   import mogames.gameData.good.GameFabaoVO;
   import mogames.gameFabao.view.FabaoZiZhuLanView;
   import mogames.gameMission.BattleMediator;
   import mogames.gameRole.hero.BaseHero;
   
   public class <PERSON>abaoZ<PERSON><PERSON><PERSON>Lan extends BaseFabao
   {
      private var _hitVO:BaseHitVO;
      
      public function FabaoZiZhuLan(param1:GameFabaoVO)
      {
         super(param1,56,106);
         this.createFabaoData();
         this.createVO();
         createView(new FabaoZiZhuLanView());
      }
      
      override protected function createFabaoData() : void
      {
         _fabaoList = [{
            "cdTime":new Snum(30),
            "hurt":new Sint(1000)
         },{
            "cdTime":new Snum(30),
            "hurt":new Sint(1350)
         },{
            "cdTime":new Snum(30),
            "hurt":new Sint(1700)
         },{
            "cdTime":new Snum(30),
            "hurt":new Sint(2050)
         },{
            "cdTime":new Snum(30),
            "hurt":new Sint(2400)
         }];
         _fabaoData = _fabaoList[_fabaoVO.quality];
      }
      
      private function createVO() : void
      {
         this._hitVO = new BaseHitVO();
         this._hitVO.source = this;
         this._hitVO.HURT_X = 0;
         this._hitVO.HURT_Y = 0;
         this._hitVO.HURT_SKIN = "EFFECT_ZI_ZHU_LAN_BUFF_CLIP";
         this._hitVO.HURT_SOUND = "GUNHURT";
         this._hitVO.WU_XING.v = _fabaoVO.constFabao.wuxing.v;
      }
      
      override public function followTarget() : void
      {
         if(_fabaoView.isSkill)
         {
            return;
         }
         super.followTarget();
      }
      
      override public function handlerSkill() : void
      {
         this.x = owner.x + owner.width * owner.dirX;
         super.handlerSkill();
      }
      
      public function dispatchSkillOne() : void
      {
         if(owner == null || owner.isDead)
         {
            return;
         }
         this._hitVO.init(_fabaoView.mcATK,_fabaoData.hurt.v,1,false);
         this._hitVO.setRigor(2,ConstRole.BUFF_PALSY);
         if(owner is BaseHero)
         {
            BattleMediator.instance().onHeroATK.dispatch(this._hitVO);
         }
         else
         {
            BattleMediator.instance().onEnemyATK.dispatch(this._hitVO);
         }
      }
   }
}

