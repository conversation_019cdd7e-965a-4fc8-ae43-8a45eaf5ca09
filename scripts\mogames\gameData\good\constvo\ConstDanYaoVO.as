package mogames.gameData.good.constvo
{
   import mogames.gameData.base.Sint;
   
   public class ConstDanYaoVO extends ConstGoodVO
   {
      public var addType:Sint;
      
      public var addValue:Sint;
      
      public var wuxing:Sint;
      
      public var maxEat:Sint;
      
      public function ConstDanYaoVO(param1:int, param2:String, param3:String, param4:int, param5:int, param6:int, param7:int, param8:int, param9:int, param10:int, param11:String)
      {
         this.wuxing = new Sint(param4);
         this.maxEat = new Sint(param5);
         this.addType = new Sint(param6);
         this.addValue = new Sint(param7);
         super(param1,param2,param3,param8,param9,param10,param11,true);
      }
   }
}

